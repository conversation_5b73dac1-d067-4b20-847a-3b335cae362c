define("inputfields/choices/userchoicesController", function() {
    return {};
});
define("inputfields/choices/choicesControllerActions", {
    /*
          This is an auto generated file and any modifications to it may result in corruption of the action sequence.
        */
});
define("inputfields/choices/choicesController", ["inputfields/choices/userchoicesController", "inputfields/choices/choicesControllerActions"], function() {
    var controller = require("inputfields/choices/userchoicesController");
    var actions = require("inputfields/choices/choicesControllerActions");
    for (var key in actions) {
        controller[key] = actions[key];
    }
    return controller;
});
