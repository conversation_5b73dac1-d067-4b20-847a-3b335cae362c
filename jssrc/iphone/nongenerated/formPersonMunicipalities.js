function frmPersonMunicipalities_flcSearchHolder_setVisibility(boolean) {
    voltmx.print("### frmPersonMunicipalities_flcSearchHolder_setVisibility");

    function flcSearchHolder_setVisibility() {
        voltmx.print("### frmPersonMunicipalities_flcSearchHolder_setVisibility flcSearchHolder_setVisibility: " + boolean);
        frmPersonMunicipalities.flcSearchHolder.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcSearchHolder_setVisibility, []);
}

function frmPersonMunicipalities_btnBack_setVisibility(boolean) {
    voltmx.print("### frmPersonMunicipalities_btnBack_setVisibility");

    function btnBack_setVisibility() {
        voltmx.print("### frmPersonMunicipalities_btnBack_setVisibility btnBack_setVisibility: " + boolean);
        frmPersonMunicipalities.btnBack.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnBack_setVisibility, []);
}

function frmPersonMunicipalities_init() {
    voltmx.print("#### frmPersonMunicipalities_init");
    //Utility_registerForIdleTimeout();
    frmPersonMunicipalities.onDeviceBack = Global_onDeviceBack;
    frmPersonMunicipalities.txtSearch.text = "";
    frmPersonMunicipalities.txtSearch.placeholder = frmPersonMunicipalities.txtSearch.placeholder + " (" + voltmx.i18n.getLocalizedString("l_3Characterinput") + ")";
    frmPersonMunicipalities.segSearch.widgetDataMap = {
        lbl1: "lbl1"
    };
}

function frmPersonMunicipalities_preShow() {
    Analytics_logScreenView("person-municipalities");
    voltmx.print("#### frmPersonMunicipalities_preShow Search text: " + frmPersonMunicipalities.txtSearch.text);
    frmPersonMunicipalities.segSearch.removeAll();
    frmPersonMunicipalities_flcSearchHolder_setVisibility(true);
    frmPersonMunicipalities_btnBack_setVisibility(false);
    frmPersonMunicipalities.btnSearch.text = voltmx.i18n.getLocalizedString("bt_cancel");
    frmPersonMunicipalities_getMunicipalities();
}

function frmPersonMunicipalities_toggleSearch() {
    if (frmPersonMunicipalities.btnSearch.text == voltmx.i18n.getLocalizedString("l_search")) {
        frmPersonMunicipalities_flcSearchHolder_setVisibility(true);
        frmPersonMunicipalities_btnBack_setVisibility(false);
        frmPersonMunicipalities.btnSearch.text = voltmx.i18n.getLocalizedString("bt_cancel");
    } else {
        frmPersonMunicipalities_flcSearchHolder_setVisibility(false);
        frmPersonMunicipalities_btnBack_setVisibility(true);
        frmPersonMunicipalities.btnSearch.text = voltmx.i18n.getLocalizedString("l_search");
        frmPersonMunicipalities.txtSearch.text = "";
        frmPersonMunicipalities_TextSearch();
    }
}

function frmPersonMunicipalities_clearSearchText() {
    frmPersonMunicipalities.txtSearch.text = "";
    frmPersonMunicipalities.txtSearch.setFocus(true);
    frmPersonMunicipalities_TextSearch();
}

function frmPersonMunicipalities_getMunicipalities() {
    voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_load"), "center", true, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    var getMunicipalities = {
        showCount: 25,
        startRec: 0,
        endRec: 25,
        orderBy: "ORDER BY Name ASC",
        whereClause: "select * from mle_v_municipality_m where (UPPER(name) like '%" + frmPersonMunicipalities.txtSearch.text.toUpperCase() + "%')"
    };
    voltmx.print("### frmPersonMunicipalities_getMunicipalities: get" + getMunicipalities.showCount + " Municipalities");
    voltmx.print("#### frmPersonManualPerson.calDateOfBirth: " + frmPersonManualPerson.calDateOfBirth.dateComponents);
    var wcs = "";
    //if(frmPersonManualPerson.txtYearOfBirth.text !== "" && Global.vars.previousForm == "frmPersonManualPerson"){
    if (Global.vars.previousForm == "frmPersonManualPerson") {
        wcs = Utility_addTimelineToWhereClauseObjectSync(getMunicipalities.whereClause, frmPersonManualPerson.calDateOfBirth.dateComponents, false);
    } else {
        wcs = getMunicipalities.whereClause;
    }
    wcs = wcs + " " + getMunicipalities.orderBy; // + " LIMIT " + getMunicipalities.showCount + " OFFSET " + getMunicipalities.startRec;
    voltmx.print("### Municipalities wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonMunicipalities_successCallback, frmPersonMunicipalities_errorCallback);
}

function frmPersonMunicipalities_successCallback(result) {
    voltmx.print("### Municipalities result: " + JSON.stringify(result));
    // only add records if there were new database records
    if ((result.length > 0)) {
        for (var i = 0;
            ((result) != null) && i < result.length; i++) {
            var v = result[i];
            result[i].lbl1 = result[i].name;
            result[i].orderSequence = Utility_getSequenceOrder(result[i].name, frmPersonMunicipalities.txtSearch.text);
        }
        // Sort
        result.sort((a, b) => a.orderSequence.localeCompare(b.orderSequence, undefined, {
            numeric: true,
            sensitivity: 'base'
        }));
        frmPersonMunicipalities.segSearch.setData(result);
        voltmx.application.dismissLoadingScreen();
    } else {
        var value = [{
            lbl1: voltmx.i18n.getLocalizedString("i_noMunicipalities")
        }];
        frmPersonMunicipalities.segSearch.setData(value);
        voltmx.application.dismissLoadingScreen();
    }
}

function frmPersonMunicipalities_errorCallback(error) {
    voltmx.print("### Municipalities error: " + error);
    voltmx.application.dismissLoadingScreen();
}

function frmPersonMunicipalities_onClick_segment() {
    try {
        var focusedCharacteristic = frmPersonMunicipalities.segSearch.selectedItems;
        voltmx.print("#### frmPersonMunicipalities_onClick_segment focuseditem: " + JSON.stringify(focusedCharacteristic));
        if (voltmx.i18n.getLocalizedString("i_noMunicipalities") === focusedCharacteristic[0].lbl1) {
            return;
        }
        if (Global.vars.previousForm === "frmPersonManualPerson") {
            Global.vars.gCasePersons.birthMunicipNL = focusedCharacteristic[0].code;
            Global.vars.gCasePersons.birthMunicipNLDesc = focusedCharacteristic[0].lbl1; //municipality is put in to place
            frmPersonManualPerson.lblMunicipalityOfBirth.text = focusedCharacteristic[0].lbl1;
            frmPersonManualPerson.show();
        } else if (Global.vars.previousForm === "frmPersonManualAddress") {
            Global.vars.gCasePersons.addresses[0].municipalCode = focusedCharacteristic[0].code;
            Global.vars.gCasePersons.addresses[0].municipality = focusedCharacteristic[0].lbl1;
            frmPersonManualAddress.show();
        } else if (Global.vars.previousForm === "frmPersonLegalEntityAddress") {
            Global.vars.gCaseLegalEntities.addresses[0].municipalCode = focusedCharacteristic[0].code;
            Global.vars.gCaseLegalEntities.addresses[0].municipality = focusedCharacteristic[0].lbl1;
            frmPersonLegalEntityAddress.show();
        }
        frmPersonMunicipalities.txtSearch.text = "";
    } catch (error) {
        voltmx.print("#### frmPersonMunicipalities_onClick_segment error: " + JSON.stringify(error));
        voltmx.print("#### frmPersonMunicipalities_onClick_segment error msg: " + JSON.stringify(error.message));
    }
}

function frmPersonMunicipalities_TextSearch() {
    if (frmPersonMunicipalities.txtSearch.text.length > 2 || frmPersonMunicipalities.txtSearch.text.length === 0) {
        frmPersonMunicipalities_getMunicipalities();
    }
}

function frmPersonMunicipalities_btnback() {
    frmPersonMunicipalities.txtSearch.text = "";
    if (Global.vars.previousForm == "frmPersonManualAddress") {
        frmPersonManualAddress.show();
    } else if (Global.vars.previousForm == "frmPersonManualPerson") {
        frmPersonManualPerson.show();
    }
}