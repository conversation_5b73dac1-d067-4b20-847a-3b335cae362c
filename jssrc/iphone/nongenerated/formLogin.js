var formInitialized = false;
var txtUserChanged = false;
var environments = [{
    name: "production",
    i18n: "l_production"
}, {
    name: "acceptance",
    i18n: "l_acceptance"
}, {
    name: "test",
    i18n: "l_test"
}, {
    name: "development",
    i18n: "l_development"
}];

function frmLogin_flcNext_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcNext_setVisibility");

    function flcNext_setVisibility() {
        voltmx.print("### frmLogin_flcNext_setVisibility flcNext_setVisibility: " + boolean);
        frmLogin.flcNext.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcNext_setVisibility, []);
}

function frmLogin_singleinstance_setVisibility(boolean) {
    voltmx.print("### frmLogin_singleinstance_setVisibility");

    function singleinstance_setVisibility() {
        voltmx.print("### frmLogin_singleinstance_setVisibility singleinstance_setVisibility: " + boolean);
        frmLogin.singleinstance.setVisibility(boolean);
    }
    voltmx.runOnMainThread(singleinstance_setVisibility, []);
}

function frmLogin_fullname_setVisibility(boolean) {
    voltmx.print("### frmLogin_fullname_setVisibility");

    function fullname_setVisibility() {
        voltmx.print("### frmLogin_fullname_setVisibility fullname_setVisibility: " + boolean);
        frmLogin.fullname.setVisibility(boolean);
    }
    voltmx.runOnMainThread(fullname_setVisibility, []);
}

function frmLogin_flcButtonLeft_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcButtonLeft_setVisibility");

    function flcButtonLeft_setVisibility() {
        voltmx.print("### frmLogin_flcButtonLeft_setVisibility flcButtonLeft_setVisibility: " + boolean);
        frmLogin.flcButtonLeft.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcButtonLeft_setVisibility, []);
}

function frmLogin_flcCancel_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcCancel_setVisibility");

    function flcCancel_setVisibility() {
        voltmx.print("### frmLogin_flcCancel_setVisibility flcCancel_setVisibility: " + boolean);
        frmLogin.flcCancel.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcCancel_setVisibility, []);
}

function frmLogin_flcRegister_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcRegister_setVisibility");

    function flcRegister_setVisibility() {
        voltmx.print("### frmLogin_flcRegister_setVisibility flcRegister_setVisibility: " + boolean);
        frmLogin.flcRegister.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcRegister_setVisibility, []);
}

function frmLogin_flcOAuthLogon_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcOAuthLogon_setVisibility");

    function flcOAuthLogon_setVisibility() {
        voltmx.print("### frmLogin_flcOAuthLogon_setVisibility flcOAuthLogon_setVisibility: " + boolean);
        frmLogin.flcOAuthLogon.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcOAuthLogon_setVisibility, []);
}

function frmLogin_flcChoosEnvironment_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcChoosEnvironment_setVisibility");

    function flcChoosEnvironment_setVisibility() {
        voltmx.print("### frmLogin_flcChoosEnvironment_setVisibility flcChoosEnvironment_setVisibility: " + boolean);
        frmLogin.flcChoosEnvironment.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcChoosEnvironment_setVisibility, []);
}

function frmLogin_environment_setVisibility(boolean) {
    voltmx.print("### frmLogin_environment_setVisibility");

    function environment_setVisibility() {
        voltmx.print("### frmLogin_environment_setVisibility environment_setVisibility: " + boolean);
        frmLogin.environment.setVisibility(boolean);
    }
    voltmx.runOnMainThread(environment_setVisibility, []);
}

function frmLogin_hostlocation_setVisibility(boolean) {
    voltmx.print("### frmLogin_hostlocation_setVisibility");

    function hostlocation_setVisibility() {
        voltmx.print("### frmLogin_hostlocation_setVisibility hostlocation_setVisibility: " + boolean);
        frmLogin.hostlocation.setVisibility(boolean);
    }
    voltmx.runOnMainThread(hostlocation_setVisibility, []);
}

function frmLogin_instance_setVisibility(boolean) {
    voltmx.print("### frmLogin_instance_setVisibility");

    function instance_setVisibility() {
        voltmx.print("### frmLogin_instance_setVisibility instance_setVisibility: " + boolean);
        frmLogin.instance.setVisibility(boolean);
    }
    voltmx.runOnMainThread(instance_setVisibility, []);
}

function frmLogin_flcChoosInstance_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcChoosInstance_setVisibility");

    function flcChoosInstance_setVisibility() {
        voltmx.print("### frmLogin_flcChoosInstance_setVisibility flcChoosInstance_setVisibility: " + boolean);
        frmLogin.flcChoosInstance.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcChoosInstance_setVisibility, []);
}

function frmLogin_username_setVisibility(boolean) {
    voltmx.print("### frmLogin_username_setVisibility");

    function username_setVisibility() {
        voltmx.print("### frmLogin_username_setVisibility username_setVisibility: " + boolean);
        frmLogin.username.setVisibility(boolean);
    }
    voltmx.runOnMainThread(username_setVisibility, []);
}

function frmLogin_flcGoogleRegister_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcGoogleRegister_setVisibility");

    function flcGoogleRegister_setVisibility() {
        voltmx.print("### frmLogin_flcGoogleRegister_setVisibility flcGoogleRegister_setVisibility: " + boolean);
        frmLogin.flcGoogleRegister.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcGoogleRegister_setVisibility, []);
}

function frmLogin_flcMicrosoftRegister_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcMicrosoftRegister_setVisibility");

    function flcMicrosoftRegister_setVisibility() {
        voltmx.print("### frmLogin_flcMicrosoftRegister_setVisibility flcMicrosoftRegister_setVisibility: " + boolean);
        frmLogin.flcMicrosoftRegister.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcMicrosoftRegister_setVisibility, []);
}

function frmLogin_flcCognitoRegister_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcCognitoRegister_setVisibility");

    function flcCognitoRegister_setVisibility() {
        voltmx.print("### frmLogin_flcCognitoRegister_setVisibility flcCognitoRegister_setVisibility: " + boolean);
        frmLogin.flcCognitoRegister.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcCognitoRegister_setVisibility, []);
}

function frmLogin_flcMicrosoftAzureIdentityRegister_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcMicrosoftAzureIdentityRegister_setVisibility");

    function flcMicrosoftAzureIdentityRegister_setVisibility() {
        voltmx.print("### frmLogin_flcMicrosoftAzureIdentityRegister_setVisibility flcMicrosoftAzureIdentityRegister_setVisibility: " + boolean);
        frmLogin.flcMicrosoftAzureIdentityRegister.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcMicrosoftAzureIdentityRegister_setVisibility, []);
}

function frmLogin_flcNormalRegister_setVisibility(boolean) {
    voltmx.print("### frmLogin_flcNormalRegister_setVisibility");

    function flcNormalRegister_setVisibility() {
        voltmx.print("### frmLogin_flcNormalRegister_setVisibility flcNormalRegister_setVisibility: " + boolean);
        frmLogin.flcNormalRegister.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcNormalRegister_setVisibility, []);
}

function frmLogin_password_setVisibility(boolean) {
    voltmx.print("### frmLogin_password_setVisibility");

    function password_setVisibility() {
        voltmx.print("### frmLogin_password_setVisibility password_setVisibility: " + boolean);
        frmLogin.password.setVisibility(boolean);
    }
    voltmx.runOnMainThread(password_setVisibility, []);
}

function frmLogin_multiUserDevice_setVisibility(boolean) {
    voltmx.print("### frmLogin_multiUserDevice_setVisibility");

    function multiUserDevice_setVisibility() {
        voltmx.print("### frmLogin_multiUserDevice_setVisibility multiUserDevice_setVisibility: " + boolean);
        frmLogin.multiUserDevice.setVisibility(boolean);
    }
    voltmx.runOnMainThread(multiUserDevice_setVisibility, []);
}

function frmLogin_segUsers_setVisibility(boolean) {
    voltmx.print("### frmLogin_segUsers_setVisibility");

    function segUsers_setVisibility() {
        voltmx.print("### frmLogin_segUsers_setVisibility segUsers_setVisibility: " + boolean);
        frmLogin.segUsers.setVisibility(boolean);
    }
    voltmx.runOnMainThread(segUsers_setVisibility, []);
}

function frmLogin_init() {
    voltmx.print("### frmLogin_init ###");
    //load values
    Global_LoadStoredValues();
    //
    frmLogin.onDeviceBack = Global_onDeviceBack;
    ////Utility_registerForIdleTimeout();
    // set swipe gesture handler
    var swipeSettings = {
        fingers: 1,
        swipedistance: 75,
        swipevelocity: 75
    };
    var swipeGesture = frmLogin.setGestureRecognizer(2, swipeSettings, frmLogin_handleGesture);
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
        frmLogin.environment.lbxList.centerX = 52 + "%";
        frmLogin.hostlocation.lbxList.centerX = 52 + "%";
        frmLogin.multiUserDevice.lbxList.centerX = 52 + "%";
        frmLogin.instance.lbxList.centerX = 52 + "%";
    }
    frmLogin.lblLogInText.text = voltmx.i18n.getLocalizedString("l_loginRegister");
    frmLogin.instance.lbxList.placeholder = voltmx.i18n.getLocalizedString("l_instance");
    frmLogin.instance.lbxList.expandListItemToParentWidth = true;
    frmLogin.multiUserDevice.lbxList.placeholder = voltmx.i18n.getLocalizedString("l_choosevalue");
    frmLogin.environment.lbxList.placeholder = voltmx.i18n.getLocalizedString("l_choosevalue");
    frmLogin.hostlocation.lbxList.placeholder = voltmx.i18n.getLocalizedString("l_choosevalue");
    // RL-384
    frmLogin.instance.lbxList.expandListItemToParentWidth = true;
    frmLogin.multiUserDevice.lbxList.expandListItemToParentWidth = true;
    frmLogin.environment.lbxList.expandListItemToParentWidth = true;
    frmLogin.hostlocation.lbxList.expandListItemToParentWidth = true;
    //
    frmLogin.username.txtInputText.text = "";
    frmLogin.password.text = "";
    frmLogin.password.btnRemovePasswordOnclick = frmLogin_btnRemovePassword;
    frmLogin.segUsers.widgetDataMap = {
        lbl1: "userName"
    };
}

function frmLogin_handleGesture(myWidget, gestureInfo) {
    voltmx.print("#### frmLogin_handleGesture: " + gestureInfo.swipeDirection);
    if (gestureInfo.swipeDirection == 2) {
        voltmx.print("### swipe direction 2");
        if (Global.vars.previousForm != null) {
            //back_to_Settings_Menu();
        }
    }
}

function frmLogin_preShow() {
    Analytics_logScreenView("login");
    var version = appConfig.appVersion;
    var AppType = appConfig.appName.substring(0, appConfig.appName.indexOf(appConfig.appId));
    if (AppType.length === 0) {
        AppType = appConfig.appName;
    }
    var environment = "";
    for (var i in environments) {
        if (environments[i].name == Global.vars.environment) {
            environment = voltmx.i18n.getLocalizedString(environments[i].i18n);
        }
    } //set appropriate apptype
    if (environment !== "") {
        frmLogin.lblVersion.text = AppType + " " + version + " " + environment;
    } else {
        frmLogin.lblVersion.text = AppType + " " + version;
    }
    voltmx.print("#### frmLogin_preShow Global.vars.loggedOut: " + Global.vars.loggedOut);
    voltmx.print("#### frmLogin_preShow Global.vars.firstLogin: " + Global.vars.firstLogin);
    voltmx.print("#### frmLogin_preShow Global.vars.afterLogOnContinue: " + Global.vars.afterLogOnContinue);
    if (formInitialized && Global.vars.loggedOut === "no") {
        voltmx.print("#### frmLogin_preShow skip");
        frmLogin.flcMainPage.setEnabled(true);
        if (Global.vars.gFullName != null && Global.vars.gFullName !== "" && frmLogin.username.txtInputText.text === Global.vars.gEntraUsername && frmLogin.fullname.isVisible && !frmLogin.flcOAuthLogon.isVisible) {
            frmLogin_flcOAuthLogon_setVisibility(true);
            if (!frmLogin.flcMicrosoftAzureIdentityRegister.isVisible) {
                frmLogin_flcMicrosoftAzureIdentityRegister_setVisibility(true);
            }
        }
        return;
    } else {
        voltmx.print("#### frmLogin_preShow continue");
        formInitialized = true;
    }
    voltmx.application.dismissLoadingScreen();
    // start
    Global.vars.afterLogOnContinue = false;
    voltmx.print("### afterLogOnContinue start: " + Global.vars.afterLogOnContinue);
    //
    frmLogin_flcNext_setVisibility(true);
    voltmx.print("### frmLogin_preShow() ###");
    voltmx.print("### frmLogin_preShow previousform: " + Global.vars.previousForm);
    voltmx.print("### frmLogin_preShow multiuserDevice: " + Global.vars.multiUserDevice);
    voltmx.print("### frmLogin_preShow firstLogin: " + Global.vars.firstLogin);
    voltmx.print("### frmLogin_preShow loggedOut: " + Global.vars.loggedOut);
    if (Global.vars.firstLogin === "yes") {
        voltmx.print("### frmLogin_preShow firstLogin so remove cookies");
        identity_removeCookies();
    }
    frmLogin_singleinstance_setVisibility(false);
    frmLogin_fullname_setVisibility(false);
    frmLogin_flcOAuthLogon_setVisibility(true);
    frmLogin_flcMicrosoftAzureIdentityRegister_setVisibility(true);
    if (Global.vars.previousForm === null) {
        frmLogin_flcButtonLeft_setVisibility(false);
        frmLogin_flcCancel_setVisibility(false);
        frmLogin_flcRegister_setVisibility(false);
        frmLogin_flcNext_setVisibility(true);
        frmLogin_flcChoosEnvironment_setVisibility(false);
        frmLogin_environment_setVisibility(false);
        frmLogin_hostlocation_setVisibility(false);
        frmLogin_instance_setVisibility(false);
        frmLogin_flcChoosInstance_setVisibility(false);
    } else {
        frmLogin_flcButtonLeft_setVisibility(true);
        frmLogin_flcCancel_setVisibility(true);
        frmLogin_flcRegister_setVisibility(false);
        frmLogin_flcNext_setVisibility(false);
    }
    if (Global.vars.loggedOut === "yes" && Global.vars.multiUserDevice == "No" && Global.vars.firstLogin == "no") {
        voltmx.print("### frmLogin_preShow 1");
        voltmx.print("### frmLogin_preShow first login false");
        voltmx.print("### frmLogin_preShow first loggedOut true");
        voltmx.print("### frmLogin_preShow Global.vars.gPassword is null");
        frmLogin.flcMainPage.setEnabled(true);
        if (Global.vars.gEntraUsername != null) {
            frmLogin.username.txtInputText.text = Global.vars.gEntraUsername;
        }
        if (Global.vars.gFullName != null && Global.vars.gFullName !== "") {
            frmLogin_fullname_setVisibility(true);
            frmLogin.fullname.lblText.text = Global.vars.gFullName;
            frmLogin_username_setVisibility(false);
        } else {
            frmLogin_username_setVisibility(true);
            frmLogin_fullname_setVisibility(false);
        }
        //frmLogin.password.text = Global.vars.gPassword === null ? "" : Global.vars.gPassword;
        frmLogin_password_setVisibility(false);
        frmLogin_flcNext_setVisibility(false);
        Global.vars.afterLogOnContinue = true;
    } else if (Global.vars.gEntraUsername != null && Global.vars.multiUserDevice == "No" && Global.vars.firstLogin == "no") {
        voltmx.print("### frmLogin_preShow 2");
        frmLogin.username.txtInputText.text = Global.vars.gEntraUsername;
        if (Global.vars.gFullName !== "") {
            frmLogin_fullname_setVisibility(true);
            frmLogin.fullname.lblText.text = Global.vars.gFullName;
            frmLogin_username_setVisibility(false);
        } else {
            frmLogin_username_setVisibility(true);
            frmLogin_fullname_setVisibility(false);
        }
        frmLogin_password_setVisibility(false);
        frmLogin_flcNext_setVisibility(false);
        voltmx.print("### frmLogin_preShow second login");
        frmLogin_onClick_butNext(frmLogin_onClick_butNextCallback);
    } else {
        voltmx.print("### frmLogin_preShow 3");
        voltmx.print("### frmLogin_preShow first login");
        voltmx.print("### frmLogin_preShow Global.vars.gPassword is null");
        voltmx.print("### frmLogin_preShow Global.vars.runningLogonProcess: " + Global.vars.runningLogonProcess);
        if (Global.vars.runningLogonProcess === false) {
            frmLogin.flcMainPage.setEnabled(true);
        }
        voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loading") + "...", "center", false, true, {
            enablemenukey: true,
            enablebackkey: true
        });
        if (Global.vars.gEntraUsername != null) {
            frmLogin.username.txtInputText.text = Global.vars.gEntraUsername;
        }
        //frmLogin.password.text = Global.vars.gPassword === null ? "" : Global.vars.gPassword;
        frmLogin_username_setVisibility(true);
        frmLogin_password_setVisibility(false);
        frmLogin_flcNext_setVisibility(false);
        frmLogin_onClick_butNext(frmLogin_onClick_butNextCallback);
    }
}

function frmLogin_onClick_butNextCallback() {
    var version = appConfig.appVersion;
    var AppType = appConfig.appName.substring(0, appConfig.appName.indexOf(appConfig.appId));
    if (AppType.length === 0) {
        AppType = appConfig.appName;
    }
    var environment = "";
    for (var i in environments) {
        if (environments[i].name == Global.vars.environment) {
            environment = voltmx.i18n.getLocalizedString(environments[i].i18n);
        }
    } //set appropriate apptype
    if (environment !== "") {
        frmLogin.lblVersion.text = AppType + " " + version + " " + environment;
    } else {
        frmLogin.lblVersion.text = AppType + " " + version;
    }
    //set envrironment variables
    frmLogin_setEnvironmentData(Global.vars.preferences.environments);
    Global.vars.afterLogOnContinue = true;
    voltmx.print("### afterLogOnContinue finished: " + Global.vars.afterLogOnContinue);
    // finished
}

function frmLogin_onClick_butNext(callback, btnPress) {
    if (callback === undefined) {
        callback = null;
    }
    if (btnPress == null) {
        btnPress = false;
    }
    voltmx.print("### frmLogin_onClick_butNext");
    voltmx.print("### frmLogin_onClick_butNext is password empty: " + Utility_checkVariableisEmpty(frmLogin.password.text));
    //frmStart.destroy();
    if (voltmx.net.isNetworkAvailable(constants.NETWORK_TYPE_ANY)) {
        var authorized = true;
        var authorized_noAuth = false;
        if (frmLogin.username.txtInputText.text === "" && Global.vars.useMobileAppAzureAD === true) {
            voltmx.application.dismissLoadingScreen();
            if (btnPress === true) {
                voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_sync0003") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
            }
        } else {
            Global.vars.useMobileAppAzureAD = true;
            // set serverInfo
            Global_setServerInfo();
            //choose environment
            Global.vars.chooseEnvironment = true;
            //set tokenlogin
            Global.vars.accessTokenLogin = false;
            voltmx.print("### frmLogin_onClick_butNext MobileAppAzureAD");
            //oAuthLogon
            Global.vars.oAuthServiceProviders = [{
                provider: "MobileAppAzureAD"
            }];
            authorized = true;
            authorized_noAuth = false; //MobileAppAzureAD
            Global.vars.oAuthLogon = true; //MobileAppAzureAD
            //multiuserdevice
            Global.vars.multiUserDevice = "No";
            frmLogin.multiUserDevice.lbxList.selectedKey = Global.vars.multiUserDevice;
            //show preferences
            Global.vars.useRouting = false;
            //set menu setting
            Global_setMenuSettings();
            if (frmLogin.username.txtInputText.text != null) {
                Global.vars.gEntraUsername = frmLogin.username.txtInputText.text;
                Utility_storeSetItem("entrausername", Global.vars.gEntraUsername);
            }
            voltmx.print("### frmLogin_onClick_butNext authorized Global.vars.environment: " + Global.vars.environment);
            try {
                frmLogin.environment.lbxList.selectedKey = Global.vars.environment;
            } catch (err) {}
            try {
                frmLogin.hostlocation.lbxList.selectedKey = Global.vars.hostlocation;
            } catch (err) {}
            voltmx.print("### frmLogin_onClick_butNext Global.vars.oAuthLogon");
            frmLogin.lblLogInText.text = voltmx.i18n.getLocalizedString("l_login"); //i18n
            var environment = Global.vars.environment; //frmLogin.environment.lbxList.selectedKeyValue[0];
            voltmx.print("### frmLogin_onClick_butNext environment: " + environment);
            var user = Utility_storeGetItem("fullname");
            var _environment = "";
            for (var i in environments) {
                if (environments[i].name == environment) {
                    _environment = voltmx.i18n.getLocalizedString(environments[i].i18n);
                    break;
                }
            }
            if (_environment == "") {
                _environment = environment;
            }
            if (user != null) {
                frmLogin.lblLogInText.text = voltmx.i18n.getLocalizedString("l_loginText1") + " " + _environment.toLowerCase() + " " + voltmx.i18n.getLocalizedString("l_loginText2") + " " + "\r\n" + user + "";
            }
            voltmx.print("### frmLogin_onClick_butNext frmLogin.lblLogInText.text: " + frmLogin.lblLogInText.text);
            //set environment
            voltmx.application.dismissLoadingScreen();
            if (callback != null) {
                callback();
            } else {
                Global.vars.afterLogOnContinue = true;
                voltmx.print("### afterLogOnContinue frmLogin_onClick_butNext 0: " + Global.vars.afterLogOnContinue);
            }
        }
    } else {
        voltmx.application.dismissLoadingScreen();
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_noNetworkClose"), frmLogin_confirm_alert, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
    }
}

function frmLogin_confirm_alert(response) {
    if (response) {
        Services_ExitApplicationAfterNoNetwork();
    }
}
/**
 * @function
 *
 * @param result 
 */
function frmLogin_credentialsUserCallback(result) {
    voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("pleaseWait"), "center", true, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    voltmx.print("### frmLogin_credentialsUserCallback result: " + JSON.stringify(result));
    var client = voltmx.sdk.getCurrentInstance();
    var auth_client = client.getIdentityService(Global.vars.selectedOAuthServiceProvider);
    try {
        var isLoginPersisted = auth_client.usePersistedLogin();
        voltmx.print("### frmLogin_credentialsUserCallback isLoginPersisted : " + isLoginPersisted);
    } catch (err) {
        voltmx.print("### frmLogin_credentialsUserCallback isLoginPersisted error : " + JSON.stringify(err));
    }
    var instanceExists = true;
    var userNotFoundDB = false;
    frmLogin.flcMainPage.setEnabled(true);
    Global.vars.runningLogonProcess = false;
    voltmx.print("### frmLogin_credentialsUserCallback Global.vars.runningLogonProcess: " + Global.vars.runningLogonProcess);
    if (result.opstatus === 0 && result.httpStatusCode == 200) {
        Global.vars.mfRetry = 0;
        if (result.messages !== undefined) {
            for (var a in result.messages) {
                if (a == "userNotFoundDB") {
                    userNotFoundDB = true;
                }
            }
        }
        if (userNotFoundDB) {
            voltmx.application.dismissLoadingScreen();
            alert(voltmx.i18n.getLocalizedString("l_loginUserNotFound"));
        } else {
            // save environment
            try {
                if (Global.vars.environment != null) {
                    voltmx.store.setItem("gEnvironment", Global.vars.environment);
                }
            } catch (err) {}
            // save hostlocation
            try {
                if (Global.vars.hostlocation != null) {
                    voltmx.store.setItem("gHostlocation", Global.vars.hostlocation);
                }
            } catch (err) {}
            if (result.instanceAuth !== undefined) {
                voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback result.instanceAuth !== undefined");
                try {
                    for (var i in result.instanceAuth) {
                        var v = result.instanceAuth[i];
                        if (v.instanceId !== undefined) {
                            var exists = false;
                            for (var j in Global.vars.instances) {
                                if (Global.vars.instances[j] == v.instanceId) {
                                    exists = true;
                                }
                            }
                            if (exists === false) {
                                Global.vars.instances.push(v.instanceId);
                            }
                        }
                    }
                    voltmx.store.setItem("instances", Global.vars.instances);
                } catch (err) {
                    Global.vars.instances = [];
                }
                voltmx.print("### frmLogin_credentialsUserCallback result.instanceAuth: " + JSON.stringify(result.instanceAuth));
                Global.vars.instanceAuthorizations = result.instanceAuth;
                voltmx.store.setItem("instanceAuthorizations", Global.vars.instanceAuthorizations);
                //Token
                voltmx.print("### frmLogin_credentialsUserCallback result.sessionToken: " + JSON.stringify(result.sessionToken));
                var mytoken = result.sessionToken;
                if (mytoken !== undefined && mytoken != null) {
                    Global.vars.gMyAuthToken = mytoken;
                } else {
                    Global.vars.gMyAuthToken = null;
                }
                var couchToken = result.couchDbToken;
                voltmx.print("### frmLogin_credentialsUserCallback couchDbToken: " + couchToken);
                if (couchToken !== undefined && couchToken != null) {
                    Global.vars.gCouchAuthToken = couchToken;
                } else {
                    Global.vars.gCouchAuthToken = null;
                }
                //set token expirationtime
                Global.vars.tokenExpirationTime = result.tokenExpirationTime;
                voltmx.print("### frmLogin_credentialsUserCallback Global.vars.tokenExpirationTime: " + Global.vars.tokenExpirationTime);
                if (Global.vars.tokenExpirationTime !== undefined && Global.vars.tokenExpirationTime != null) {
                    voltmx.store.setItem("tokenExpirationTime", Global.vars.tokenExpirationTime);
                } else {
                    Global.vars.tokenExpirationTime = null;
                }
                //save auth block as string hash
                var algo = "sha512";
                var inputstr = JSON.stringify(result.instanceAuth);
                var myHashValue = voltmx.crypto.createHash(algo, inputstr);
                voltmx.print("### frmLogin_onClick_butLogin Logon succes myHashValue: " + myHashValue);
                voltmx.store.setItem("instanceAuthHash", myHashValue);
                if (Global.vars.firstLogin == "yes" && (Global.vars.buildFor == "GEN" || Global.vars.buildFor == "RWS")) {
                    if (frmLogin.multiUserDevice.lbxList.selectedKey !== undefined && frmLogin.multiUserDevice.lbxList.selectedKey != null) {
                        Global.vars.multiUserDevice = frmLogin.multiUserDevice.lbxList.selectedKey;
                        voltmx.store.setItem("MultiUserDevice", Global.vars.multiUserDevice);
                    }
                }
                voltmx.print("#### frmLogin_credentialsUserCallback 2");
                voltmx.print("### frmLogin_onClick_butLogin Logon succes");
                Global.vars.LoggedIn = true;
                voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback entrausername: " + Global.vars.gEntraUsername);
                voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback username: " + Global.vars.gUsername);
                voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback result.email: " + result.email);
                Utility_setIni(Global.vars.gFullName);
                //sync server info
                SyncUtil_saveServerInfo();
                voltmx.application.dismissLoadingScreen();
                //set other user globals
                var _loginUserIncorrect = false;
                voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback Global.vars.multiUserDevice: " + Global.vars.multiUserDevice);
                if (result.email !== undefined) {
                    Global.vars.gEmail = result.email;
                    voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback result.email: " + result.email);
                    if (Global.vars.gUsername == null || Global.vars.gUsername === "" || Utility_isEmptyObject(Global.vars.gUsername) === true) {
                        Global.vars.gUsername = result.email;
                        Utility_storeSetItem("username", Global.vars.gUsername);
                        voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback username2: " + Global.vars.gUsername);
                    } else {
                        if (Global.vars.gEntraUsername !== result.email && Global.vars.firstLogin == "no" && (Global.vars.multiUserDevice == null || Global.vars.multiUserDevice === "No")) {
                            voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback _loginUserIncorrect result.email: " + result.email);
                            voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback _loginUserIncorrect Global.vars.gUsername: " + Global.vars.gUsername);
                            _loginUserIncorrect = true;
                        } else if (Global.vars.gUsername !== result.email && Global.vars.firstLogin == "yes" && (Global.vars.multiUserDevice == null || Global.vars.multiUserDevice === "No")) {
                            Global.vars.gUsername = result.email;
                            Utility_storeSetItem("username", Global.vars.gUsername);
                        }
                    }
                }
                if (_loginUserIncorrect === false) {
                    if (result.fullName !== undefined) {
                        Global.vars.gFullName = result.fullName;
                        Utility_storeSetItem("fullname", Global.vars.gFullName);
                    } else if (result.given_name !== undefined && result.family_name !== undefined) {
                        Global.vars.gFullName = result.given_name + " " + result.family_name;
                    }
                    if (result.oathType !== undefined) {
                        Global.vars.gOfficerOath = result.oathType;
                    }
                    if (result.roles !== undefined) {
                        Global.vars.memberGroups = result.roles;
                    }
                    if (Global.vars.gFullName !== "") {
                        frmLogin_fullname_setVisibility(true);
                        frmLogin.fullname.lblText.text = Global.vars.gFullName;
                    }
                    frmLogin_username_setVisibility(false);
                    frmLogin_password_setVisibility(false);
                    // get user favorite offences
                    voltmx.print("### frmLogin_onClick_butLogin Utility_getFavoriteOffences");
                    Utility_getFavoriteOffences();
                    // get user favorite policies
                    voltmx.print("### frmLogin_onClick_butLogin Utility_getFavoritePolicies");
                    Utility_getFavoritePolicies();
                    //
                    voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback gFullName: " + Global.vars.gFullName);
                    //           voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback gOfficerNumber: " + Global.vars.gOfficerNumber);
                    //           voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback gTeamNumber: " + Global.vars.gTeamNumber);
                    //           voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback gTeamName: " + Global.vars.gTeamName);
                    voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback gOfficerOath: " + Global.vars.gOfficerOath);
                    voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback memberGroups: " + JSON.stringify(Global.vars.memberGroups));
                    //
                    frmLogin_selectInstance();
                } else {
                    voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback l_loginUserIncorrect");
                    //alert(voltmx.i18n.getLocalizedString("l_loginUserIncorrect"));
                    frmLogin_flcMicrosoftAzureIdentityRegister_setVisibility(false);
                    if (Global.vars.inCorrectUserPopupShowed === false) {
                        Global.vars.inCorrectUserPopupShowed = true;
                        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_loginUserIncorrect") + " - gUsername: " + Global.vars.gUsername + " - result.email: " + result.email, frmLogin_handleIncorrectUser, "confirmation", voltmx.i18n.getLocalizedString("l_logOn"), voltmx.i18n.getLocalizedString("l_resetUser"), voltmx.i18n.getLocalizedString("l_info"), null);
                    }
                }
            } else {
                voltmx.print("### frmLogin_onClick_butLogin frmLogin_credentialsUserCallback l_loginUserIncomplete");
                Global_logoutIdentity(Global_exitApplication, frmLogin_errorCallback);
                alert(voltmx.i18n.getLocalizedString("l_loginUserIncomplete"));
                voltmx.application.dismissLoadingScreen();
            }
        }
    } else if (result.opstatus == 1014) {
        voltmx.application.dismissLoadingScreen();
        alert(voltmx.i18n.getLocalizedString("e_ser0001"));
    } else {
        voltmx.application.dismissLoadingScreen();
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0001") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
    }
}

function frmLogin_succesCallback() {
    voltmx.print("### frmLogin_handleIncorrectUser Global_logoutIdentity succesCallback");
    //   Global_resetApp();
    voltmx.print("### frmLogin_handleIncorrectUser Global_logoutIdentity Global.vars.gIdentityProvider : " + Global.vars.gIdentityProvider);
    var client = voltmx.sdk.getCurrentInstance();
    var auth_client = client.getIdentityService(Global.vars.gIdentityProvider);
    try {
        var isLoginPersisted = auth_client.usePersistedLogin();
        voltmx.print("### frmLogin_handleIncorrectUser isLoginPersisted : " + isLoginPersisted);
    } catch (err) {
        voltmx.print("### frmLogin_handleIncorrectUser isLoginPersisted error : " + JSON.stringify(err));
    }
    frmLogin_flcMicrosoftAzureIdentityRegister_setVisibility(true);
    Global.vars.inCorrectUserPopupShowed = false;
    voltmx.application.dismissLoadingScreen();
    //   frmStart.show();
}

function frmLogin_errorCallback() {
    voltmx.print("### frmLogin_handleIncorrectUser Global_logoutIdentity errorCallback");
    frmLogin_flcMicrosoftAzureIdentityRegister_setVisibility(true);
    Global.vars.inCorrectUserPopupShowed = false;
    voltmx.application.dismissLoadingScreen();
}

function frmLogin_handleIncorrectUser(response) {
    var lresponse = response === undefined ? true : response;
    voltmx.print("####  frmLogin_handleIncorrectUser: " + lresponse);
    Global.vars.microsoftAccessToken = null;
    if (lresponse === true) {
        identityService_logout(Global.vars.gIdentityProvider, frmLogin_succesCallback, frmLogin_errorCallback);
    } else {
        identityService_logout(Global.vars.gIdentityProvider, frmInfo_clearUserAndDataStoreItemsCallback, frmInfo_clearUserAndDataStoreItemsErrorCallback);
    }
}

function frmLogin_onTextChange_txtUser() {
    voltmx.print("#### frmLogin_onTextChange_txtUser");
    txtUserChanged = true;
}

function frmLogin_afterLogOn() {
    try {
        voltmx.timer.schedule("afterLogonTimer", frmLogin_afterLogonTimer, 0.5, false);
    } catch (err) {}
}

function frmLogin_afterLogonTimer() {
    try {
        voltmx.timer.cancel("afterLogonTimer");
    } catch (e) {}
    voltmx.print("### afterLogOnContinue afterLogonTimer: " + Global.vars.afterLogOnContinue);
    if (Global.vars.afterLogOnContinue === false) {
        frmLogin_afterLogOn();
    } else {
        Global.vars.afterLogOnContinue = false;
        frmLogin_afterLogOnContinue();
    }
}

function frmLogin_afterLogOnContinue() {
    voltmx.print("### frmLogin_afterLogOn");
    voltmx.print("### afterLogOnContinue frmLogin_afterLogOn: " + Global.vars.afterLogOnContinue);
    frmLogin_changeThemeForInstance();
    frmLogin_setGlobalForInstance();
    //find functions
    var menuitems = [];
    var mobilemodules = [];
    var noFunctions = true;
    var noOfficerId = true;
    var roles = "";
    Global.vars.authorizedMunicipalities = [];
    for (var i in Global.vars.instanceAuthorizations) {
        var v = Global.vars.instanceAuthorizations[i];
        if (v.instanceId == Global.vars.gInstanceId) {
            //voltmx.print("### frmLogin_afterLogOn v: " + JSON.stringify(v));
            voltmx.print("### frmLogin_afterLogOn v.authorizedMunicipalities: " + JSON.stringify(v.authorizedMunicipalities));
            //voltmx.print("### frmLogin_afterLogOn v.functions: " + JSON.stringify(v.functions));
            voltmx.print("### frmLogin_afterLogOn v.municipalityRestricted: " + v.municipalityRestricted);
            voltmx.print("### frmLogin_afterLogOn set functions");
            if (v.functions === undefined) {
                noFunctions = true;
                if (Global.vars.memberGroups.length > 0) {
                    roles = JSON.stringify(Global.vars.memberGroups);
                } else {
                    roles = "Geen";
                }
            } else {
                noFunctions = false;
                Global.vars.officerFunctions = v.functions;
                voltmx.store.setItem("officerfunctions", Global.vars.officerFunctions);
                if (v.municipalityRestricted === true && v.authorizedMunicipalities !== undefined && v.authorizedMunicipalities.length > 0 && v.authorizedMunicipalities[0].officerId !== undefined) {
                    Global.vars.gOfficerNumber = v.authorizedMunicipalities[0].officerId;
                    service_GetUserByOfficerName(Global.vars.gOfficerNumber, v.authorizedMunicipalities[0].municipalityCode, frmLogin_SearchUserCallback, frmLogin_SearchUserErrorcallback);
                    Global.vars.authorizedMunicipalities = v.authorizedMunicipalities;
                    var officerNumber = "";
                    noOfficerId = false;
                    for (var j in Global.vars.authorizedMunicipalities) {
                        var w = Global.vars.authorizedMunicipalities[j];
                        //officerId  = officer_number in mle_user_municipality
                        voltmx.print("### frmLogin_afterLogOn w.officerId: " + w.officerId);
                        if (w.officerId === undefined || w.officerId === null || w.officerId === "") {
                            noOfficerId = true;
                            break;
                        } else {
                            if (officerNumber === "") {
                                officerNumber = w.officerId;
                            }
                        }
                    }
                    Global.vars.gOfficerNumber = officerNumber;
                    Utility_storeSetItem("officernumber", Global.vars.gOfficerNumber);
                    if (v.authorizedMunicipalities[0].officerIdentification !== undefined && v.authorizedMunicipalities[0].officerIdentification != null) {
                        Global.vars.gOfficerIdentification = v.authorizedMunicipalities[0].officerIdentification;
                        Utility_storeSetItem("officerIdentification", Global.vars.gOfficerIdentification);
                    } else {
                        Global.vars.gOfficerIdentification = null;
                        Utility_keychain_remove("officerIdentification");
                    }
                    voltmx.print("### frmLogin_afterLogOn authorizedMunicipalities gOfficerIdentification: " + Global.vars.gOfficerIdentification);
                } else if (v.municipalityRestricted === false && v.officerId !== undefined && v.officerId != null) {
                    Global.vars.gOfficerNumber = v.officerId;
                    service_GetUserByOfficerName(Global.vars.gOfficerNumber, "", frmLogin_SearchUserCallback, frmLogin_SearchUserErrorcallback);
                    noOfficerId = false;
                    Utility_storeSetItem("officernumber", Global.vars.gOfficerNumber);
                    if (v.officerIdentification !== undefined && v.officerIdentification != null) {
                        Global.vars.gOfficerIdentification = v.officerIdentification;
                        Utility_storeSetItem("officerIdentification", Global.vars.gOfficerIdentification);
                    } else {
                        Global.vars.gOfficerIdentification = null;
                        Utility_keychain_remove("officerIdentification");
                    }
                    voltmx.print("### frmLogin_afterLogOn v gOfficerIdentification: " + Global.vars.gOfficerIdentification);
                }
                if (v.teamCode !== undefined) {
                    Global.vars.gTeamNumber = v.teamCode;
                    voltmx.store.setItem("teamnumber", Global.vars.gTeamNumber);
                }
                if (v.teamName !== undefined) {
                    Global.vars.gTeamName = v.teamName;
                }
                voltmx.print("### frmLogin_afterLogOn gOfficerNumber: " + Global.vars.gOfficerNumber);
                voltmx.print("### frmLogin_afterLogOn gTeamNumber: " + Global.vars.gTeamNumber);
                voltmx.print("### frmLogin_afterLogOn gTeamName: " + Global.vars.gTeamName);
                if (v.applicationItems !== undefined && v.applicationItems.MenuItemApp !== undefined) {
                    voltmx.print("### frmLogin_afterLogOn applicationItems: " + JSON.stringify(v.applicationItems.MenuItemApp));
                    menuitems = v.applicationItems.MenuItemApp;
                }
                if (v.applicationItems !== undefined && v.applicationItems.MobileModule !== undefined) {
                    voltmx.print("### frmLogin_afterLogOn applicationItems: " + JSON.stringify(v.applicationItems.MobileModule));
                    mobilemodules = v.applicationItems.MobileModule;
                }
                Global.vars.authorizedCaseTypes = [];
                if (v.authorizedCaseTypes !== undefined && v.authorizedCaseTypes.length > 0) {
                    voltmx.print("### frmLogin_afterLogOn authorizedCaseTypes: " + JSON.stringify(v.authorizedCaseTypes));
                    Global.vars.authorizedCaseTypes = v.authorizedCaseTypes;
                }
            }
        }
    }
    if (noFunctions === true || noOfficerId === true) {
        voltmx.print("### frmLogin_afterLogOn noFunctions or noOfficerId");
        voltmx.print("### frmLogin_afterLogOn Global.vars.afterLogOnContinue: " + Global.vars.afterLogOnContinue);
        Global.vars.gInstanceId = null;
        voltmx.store.removeItem("instance");
        SyncUtil.gInstance = Global.vars.gInstanceId;
        frmLogin_instance_setVisibility(true);
        Global.vars.afterLogOnContinue = true;
        voltmx.print("### frmLogin_afterLogOn Global.vars.gInstanceId: " + Global.vars.gInstanceId);
        if (noFunctions === true) {
            alert(voltmx.i18n.getLocalizedString("l_loginNoFunctions") + roles);
        } else if (noOfficerId === true) {
            alert(voltmx.i18n.getLocalizedString("l_loginNoOfficerId"));
        }
    } else {
        //log on succes now set userdata to case
        CaseData_setUserInformationToCaseInfo();
        //set creationTime for new case on device
        CaseData.caseinfo.creationTime = Utility_getUTCJavascriptDate(null);
        voltmx.print("### frmLogin_afterLogOn CaseData.caseinfo: " + JSON.stringify(CaseData.caseinfo));
        //Go to sync
        SyncUtil.gRegistrationComplete = true;
        //SyncUtil.gAutoStartSync = true;
        //if(Global.vars.microsoftAccessToken != null && Global.vars.oAuthLogon === true && Global.vars.selectedOAuthServiceProvider == "MicrosoftADAL"){
        if (menuitems != null && menuitems !== undefined && menuitems.length > 0) {
            frmLogin_setAuthorizedItemsMenu(menuitems);
        }
        Global.vars.setPersonOnResume = false;
        if (mobilemodules != null && mobilemodules !== undefined && mobilemodules.length > 0) {
            frmLogin_setAuthorizedModules(mobilemodules);
        }
        if (Global.vars.multiUserDevice == "Yes") {
            if (Global.vars.continueAppVersionOK === true) {
                frmSynchronisation_skipSync(); //frmSynchronization.show();
            } else {
                voltmx.print("### frmLogin_afterLogOn version not correct, wait for app to be updated 1");
            }
        } else {
            if (Global.vars.continueAppVersionOK === true) {
                voltmx.print("### frmLogin_afterLogOn frmPinLogin Global.vars.firstLogin: " + Global.vars.firstLogin);
                voltmx.print("### frmLogin_afterLogOn frmPinLogin Global.vars.usePin: " + Global.vars.usePin);
                voltmx.print("### frmLogin_afterLogOn frmPinLogin Global.vars.createPinInThisSession: " + Global.vars.createPinInThisSession);
                if (Global.vars.buildFor === "GEN") {
                    Global.vars.needToUsePin = true;
                }
                if (Global.vars.createPinInThisSession === true) {
                    Global.vars.createPinInThisSession = false;
                    frmSynchronisation_skipSync();
                } else {
                    if (Global.vars.usePin === "yes" || (Global.vars.usePin === null && Global.vars.needToUsePin === false)) {
                        voltmx.print("### frmLogin_afterLogOn frmPinLogin 1");
                        frmPinLogin.show();
                    } else if (Global.vars.needToUsePin === true) {
                        voltmx.print("### frmLogin_afterLogOn frmPinLogin 2");
                        frmPinLogin.show();
                    } else {
                        frmSynchronisation_skipSync(); //frmSynchronization.show();
                    }
                }
            } else {
                voltmx.print("### frmLogin_afterLogOn version not correct, wait for app to be updated 2");
            }
        }
    }
}

function frmLogin_Instance_successcallback(result) {
    voltmx.print("### frmLogin_Instance_successcallback" + JSON.stringify(result));
    //instance name
    if (result[0] !== undefined) {
        frmLogin.singleinstance.lblText.text = result[0].description;
        frmLogin_singleinstance_setVisibility(true);
    }
}

function frmLogin_Instance_errorcallback(error) {
    voltmx.print("### frmLogin_Instance_errorcallback" + JSON.stringify(error));
}

function frmLogin_setEnvironmentData(environmentData) {
    // set environment data
    voltmx.print("### frmLogin_setEnvironmentData environmentData: " + JSON.stringify(environmentData));
    var environmentDataList = [];
    for (var i = 0; i < environmentData.length; i++) {
        environmentDataList.push({
            key: environmentData[i].name,
            value: voltmx.i18n.getLocalizedString(environmentData[i].i18n)
        });
    }
    voltmx.print("### frmLogin_setEnvironmentData environmentDataList: " + JSON.stringify(environmentDataList));
    frmLogin.environment.lbxList.masterDataMap = [environmentDataList, "key", "value"];
    try {
        frmLogin.environment.lbxList.selectedKey = Global.vars.environment;
    } catch (err) {}
}

function frmLogin_setInstances() {
    if (Global.vars.firstLogin == "yes" || Global.vars.gInstanceId === null) {
        frmLogin_instance_setVisibility(false);
    } else {
        voltmx.print("### frmLogin_preShow Global.vars.instances.length: " + Global.vars.instances.length);
        if (Global.vars.instances.length > 0) {
            frmLogin_populateInstances();
        }
        if (Global.vars.gInstanceId != null) {
            frmLogin.instance.lbxList.selectedKey = Global.vars.gInstanceId;
        }
        frmLogin_instance_setVisibility(true);
    }
}

function frmLogin_postShow() {
    voltmx.print("#### frmLogin postshow");
    //voltmx.application.dismissLoadingScreen();
}

function frmLogin_Stop(response) {
    voltmx.print("#### onClick_frmLogin_Stop ###");
    if (response) {
        // cancel application
        Global_exitApplication();
    } else {
        voltmx.application.dismissLoadingScreen();
    }
}
var sleepId = null;

function frmLogin_sleep() {
    if (sleepId) {
        voltmx.timer.cancel(sleepId); // Cancel the timer
        sleepId = null;
    }
    voltmx.print("### frmLogin_AutoLogin frmLogin_sleep: " + JSON.stringify(Global.vars.setServerInfoDone));
    if (Global.vars.setServerInfoDone === true) {
        Global.vars.setServerInfoCallBack = null;
        frmLogin_onClick_butLogin();
    }
}

function frmLogin_AutoLogin() {
    voltmx.print("### frmLogin_AutoLogin: " + JSON.stringify(Global.vars.setServerInfoDone));
    voltmx.application.dismissLoadingScreen();
    if (Global.vars.setServerInfoDone === true) {
        Global.vars.setServerInfoCallBack = null;
        frmLogin_onClick_butLogin();
    } else {
        sleepId = "sleepTimeout"; // Unique ID for the timer
        voltmx.timer.schedule(sleepId, frmLogin_sleep, 1, false); // 1-second single-use timer
    }
}

function frmLogin_checkOnDone_txtUser_butLogin() {
    if (frmLogin.username.txtInputText.text != null && frmLogin.username.txtInputText.text !== "" && txtUserChanged === true && frmLogin.username.isVisible === true && Global.vars.firstLogin === "yes") {
        voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_authenticating_user"), "center", true, true, {
            enablemenukey: true,
            enablebackkey: true
        });
        Global.vars.setServerInfoCallBack = frmLogin_onClick_butLogin;
        frmLogin_onDone_txtUser();
    } else {
        Global.vars.setServerInfoCallBack = null;
        frmLogin_onClick_butLogin();
    }
}

function frmLogin_onDone_txtUser() {
    voltmx.print("#### frmLogin_onDone_txtUser start");
    voltmx.print("#### frmLogin_onDone_txtUser txtUserChanged: " + txtUserChanged);
    if (frmLogin.username.txtInputText.text != null && frmLogin.username.txtInputText.text !== "" && txtUserChanged === true) {
        txtUserChanged = false;
        // user lower case
        frmLogin.username.txtInputText.text = frmLogin.username.txtInputText.text.toLowerCase();
        //trim spaces
        frmLogin.username.txtInputText.text = frmLogin.username.txtInputText.text.replace(/\s/g, "");
        //check if email
        voltmx.print("### frmLogin_onDone_txtUser frmLogin.username.text: " + frmLogin.username.txtInputText.text);
        if (voltmx.string.isValidEmail(frmLogin.username.txtInputText.text) === false) {
            //if(frmLogin.username.txtInputText.text.indexOf("@") == -1){
            voltmx.application.dismissLoadingScreen();
            alert(voltmx.i18n.getLocalizedString("l_userMustBeEmail"));
        } else {
            Global.vars.gEntraUsername = frmLogin.username.txtInputText.text;
            var _gDomain = Utility_getDomainFromEmail(Global.vars.gEntraUsername);
            voltmx.print("### frmLogin_onDone_txtUser _gDomain: " + _gDomain);
            frmLogin_flcChoosEnvironment_setVisibility(false);
            frmLogin_environment_setVisibility(false);
            frmLogin_hostlocation_setVisibility(false);
            if (_gDomain === Global.vars.gDefaultDomain) {
                voltmx.print("### frmLogin_onDone_txtUser 1");
                Global.vars.preferences.environments = [{
                    name: "production",
                    i18n: "l_production"
                }, {
                    name: "acceptance",
                    i18n: "l_acceptance"
                }];
                Global.vars.environment = "production";
                Global.vars.hostlocation = "eu-central-1.rl";
            } else if (_gDomain === "TWYNS") {
                voltmx.print("### frmLogin_onDone_txtUser 1");
                Global.vars.preferences.environments = [{
                    name: "production",
                    i18n: "l_production"
                }, {
                    name: "acceptance",
                    i18n: "l_acceptance"
                }, {
                    name: "test",
                    i18n: "l_test"
                }, {
                    name: "development",
                    i18n: "l_development"
                }];
                Global.vars.environment = "production";
                Global.vars.hostlocation = "eu-central-1.rl";
            } else if (_gDomain === "REDORA") {
                voltmx.print("### frmLogin_onDone_txtUser 1");
                Global.vars.preferences.environments = [{
                    name: "production",
                    i18n: "l_production"
                }, {
                    name: "acceptance",
                    i18n: "l_acceptance"
                }, {
                    name: "test",
                    i18n: "l_test"
                }, {
                    name: "development",
                    i18n: "l_development"
                }];
                Global.vars.environment = "production";
                Global.vars.hostlocation = "eu-central-1.rl";
            } else if (_gDomain === "NSLOGIN" || _gDomain === "TRANSDEV") {
                voltmx.print("### frmLogin_onDone_txtUser 2");
                Global.vars.preferences.environments = [{
                    name: "production",
                    i18n: "l_production"
                }, {
                    name: "acceptance",
                    i18n: "l_acceptance"
                }];
                Global.vars.environment = "production";
                Global.vars.hostlocation = "eu-central-1.pt";
            } else {
                voltmx.print("### frmLogin_onDone_txtUser 3");
                Global.vars.preferences.environments = [{
                    name: "production",
                    i18n: "l_production"
                }, {
                    name: "acceptance",
                    i18n: "l_acceptance"
                }];
                Global.vars.environment = "production";
                Global.vars.hostlocation = "eu-central-1.rl";
            }
            try {
                frmLogin.environment.lbxList.selectedKey = Global.vars.environment;
            } catch (err) {}
            try {
                frmLogin.hostlocation.lbxList.selectedKey = Global.vars.hostlocation;
            } catch (err) {}
            var version = appConfig.appVersion;
            var appType = appConfig.appName.substring(0, appConfig.appName.indexOf(appConfig.appId));
            if (appType.length === 0) {
                appType = appConfig.appName;
            }
            var environment = "";
            for (var i in Global.vars.preferences.environments) {
                if (Global.vars.preferences.environments[i].name == Global.vars.environment) {
                    environment = voltmx.i18n.getLocalizedString(Global.vars.preferences.environments[i].i18n);
                }
            } //set appropriate apptype
            if (environment !== "") {
                frmLogin.lblVersion.text = appType + " " + version + " " + environment;
            } else {
                frmLogin.lblVersion.text = appType + " " + version;
            }
            frmLogin_setEnvironmentData(Global.vars.preferences.environments);
            Global_setServerInfo();
        }
    }
}

function frmLogin_onDone_txtPassword() {
    voltmx.print("#### frmLogin_btnLogin_onClick 3: frmLogin_passwordTrim");
    //trim spaces
    try {
        frmLogin.password.text = frmLogin.password.text.replace(/\s/g, "");
    } catch (err) {}
}

function frmLogin_onClick_butLogin() {
    voltmx.print("#### frmLogin_onClick_butLogin ###");
    Global.vars.setServerInfoCallBack = null;
    if (frmLogin.username.txtInputText.text === "" || voltmx.string.isValidEmail(frmLogin.username.txtInputText.text) === false) {
        voltmx.application.dismissLoadingScreen();
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_sync0003") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
    } else {
        voltmx.print("### frmLogin_onClick_butLogin else");
        if (SyncUtil.gSyncInitComplete === false) {
            voltmx.print("### frmLogin_onClick_butLogin Sync Init not complete ###");
            Utility_dismissLoadingScreen();
            voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_sync0001") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
        } else {
            if (Global.vars.hostlocation !== "l_choose") {
                voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_authenticating_user"), "center", true, true, {
                    enablemenukey: true,
                    enablebackkey: true
                });
                //call service to get sync credentials etc.
                Utility_storeSetItem("entrausername", Global.vars.gEntraUsername);
                frmLogin_MobileAppAzureADLogin();
            } else {
                voltmx.application.dismissLoadingScreen();
                voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_chooseHost") + "", null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
            }
        }
    }
}

function frmLogin_e_sync0001_Confirmation(response) {
    voltmx.print("####  frmLogin_e_sync0001_Confirmation: " + JSON.stringify(response));
    voltmx.application.dismissLoadingScreen();
    if (response) {
        voltmx.print("####  frmLogin_e_sync0001_Confirmation frmLogin_clearUserAndDataStoreItems");
        SyncUtil_emptyDatabase(frmLogin_clearUserAndDataStoreItems);
    }
}

function frmLogin_clearUserAndDataStoreItems() {
    voltmx.print("#### frmLogin_clearUserAndDataStoreItems ###");
    //empty database
    voltmx.store.removeItem("MultiUserDevice");
    Global.vars.multiUserDevice = null;
    frmLogin.multiUserDevice.lbxList.selectedKey = "No";
    voltmx.store.removeItem("instance");
    Global.vars.gInstanceId = null;
    Global.vars.gPassword = null;
    voltmx.store.removeItem("firstLogin");
    Global.vars.firstLogin = "yes";
    voltmx.store.removeItem("loggedOut");
    Global.vars.loggedOut = "yes";
    Global.vars.encryptionInUse = true;
    voltmx.store.setItem("globalInit", Global.vars.encryptionInUse);
    voltmx.store.removeItem("instanceAuthHash");
    //remove pin
    Utility_keychain_remove("pinHash");
    Global.vars.pinHash = null;
    voltmx.store.removeItem("usePin");
    Global.vars.usePin = null;
    Global.vars.resetPin = false;
    Global.vars.createPinAfter = false;
    Global_resetApp();
    //empty instances
    Global.vars.instances = [];
    //clear text fields
    frmLogin.password.text = "";
    frmLogin.username.txtInputText.text = "";
    //go back to start
    frmStart.show();
}

function frmLogin_calluserCallback(result) {
    voltmx.print("### frmLogin_calluserCallback result: " + JSON.stringify(result));
    var instanceExists = true;
    if (result.opstatus === 0 && result.httpStatusCode == 200) {
        Global.vars.mfRetry = 0;
        // save environment
        try {
            voltmx.store.setItem("gEnvironment", Global.vars.environment);
        } catch (err) {
            Global.vars.environment = null;
        }
        try {
            voltmx.store.setItem("gHostlocation", Global.vars.hostlocation);
        } catch (err) {
            Global.vars.hostlocation = null;
        }
        try {
            for (var i in result.instanceAuth) {
                var v = result.instanceAuth[i];
                if (v.instanceId !== undefined) {
                    var exists = false;
                    for (var j in Global.vars.instances) {
                        if (Global.vars.instances[j] == v.instanceId) {
                            exists = true;
                        }
                    }
                    if (exists === false) {
                        Global.vars.instances.push(v.instanceId);
                    }
                }
            }
            voltmx.store.setItem("instances", Global.vars.instances);
        } catch (err) {
            Global.vars.instances = [];
        }
        Global.vars.instanceAuthorizations = result.instanceAuth;
        voltmx.store.setItem("instanceAuthorizations", Global.vars.instanceAuthorizations);
        var mytoken = result.sessionToken;
        if (mytoken != null) {
            Global.vars.gMyAuthToken = mytoken;
        } else {
            Global.vars.gMyAuthToken = null;
        }
        var couchToken = result.couchDbToken;
        voltmx.print("### frmLogin_calluserCallback couchDbToken: " + couchToken);
        if (couchToken != null) {
            Global.vars.gCouchAuthToken = couchToken;
        } else {
            Global.vars.gCouchAuthToken = null;
        }
        //set token expirationtime
        Global.vars.tokenExpirationTime = result.tokenExpirationTime;
        voltmx.print("### frmLogin_calluserCallback Global.vars.tokenExpirationTime: " + Global.vars.tokenExpirationTime);
        voltmx.store.setItem("tokenExpirationTime", Global.vars.tokenExpirationTime);
        //save auth block as string hash
        var algo = "sha512";
        var inputstr = JSON.stringify(result.instanceAuth);
        var myHashValue = voltmx.crypto.createHash(algo, inputstr);
        voltmx.store.setItem("instanceAuthHash", myHashValue);
        voltmx.print("### frmLogin_onClick_butLogin Logon succes myHashValue: " + myHashValue);
        voltmx.store.setItem("instanceAuthHash", myHashValue);
        //Set multiUser
        if (Global.vars.firstLogin == "yes") {
            Global.vars.multiUserDevice = frmLogin.multiUserDevice.lbxList.selectedKey;
            voltmx.store.setItem("MultiUserDevice", Global.vars.multiUserDevice);
        }
        if (Global.vars.firstLogin == "no" && Global.vars.multiUserDevice == "Yes" && Global.vars.gInstanceId != null) {
            instanceExists = false;
            for (var k in Global.vars.instances) {
                if (Global.vars.instances[k] == Global.vars.gInstanceId) {
                    instanceExists = true;
                }
            }
        }
        //
        voltmx.print("#### frmLogin_calluserCallback environment: " + Global.vars.environment);
        //voltmx.print("#### frmLogin_calluserCallback gSyncPassword: " + Global.vars.gSyncPassword);//niet uit service
        //voltmx.print("#### frmLogin_calluserCallback gSyncUsername: " + Global.vars.gSyncUsername);//niet uit service
        voltmx.print("#### frmLogin_calluserCallback Global.vars.gInstanceId: " + Global.vars.gInstanceId);
        //voltmx.print("#### frmLogin_calluserCallback Global.vars.gMyAuthToken: " + Global.vars.gMyAuthToken);
        //voltmx.print("#### frmLogin_calluserCallback Global.vars.gCouchAuthToken: " + Global.vars.gCouchAuthToken);
        if (Global.vars.environment === null || Global.vars.gSyncUsername === null || Global.vars.gSyncPassword === null || Global.vars.gMyAuthToken === null) {
            voltmx.print("### frmLogin_calluserCallback gRegisteredWithSyncserver 1");
            Global.vars.gRegisteredWithSyncserver = false;
            voltmx.print("### Utility_setIni frmLogin_calluserCallback");
            Utility_setIni("");
            gInitialSync = true;
            voltmx.application.dismissLoadingScreen();
            voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0002") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
        } else {
            voltmx.print("### frmLogin_onClick_butLogin Logon succes");
            Global.vars.LoggedIn = true;
            voltmx.print("### Utility_setIni frmLogin_calluserCallback: " + Global.vars.gUsername);
            Utility_setIni(Global.vars.gFullName);
            //sync server info
            SyncUtil_saveServerInfo();
            voltmx.application.dismissLoadingScreen();
            //set other user globals
            if (result.email !== undefined) {
                Global.vars.gEmail = result.email;
            }
            if (result.fullName !== undefined) {
                Global.vars.gFullName = result.fullName;
                Utility_storeSetItem("fullname", Global.vars.gFullName);
            } else if (result.given_name !== undefined && result.family_name !== undefined) {
                Global.vars.gFullName = result.given_name + " " + result.family_name;
            }
            if (result.instanceAuth.officerId !== undefined && result.instanceAuth.officerId != null && result.instanceAuth.municipalityRestricted === false) {
                Global.vars.gOfficerNumber = result.officerId;
            } else if (result.instanceAuth.authorizedMunicipalities !== undefined && result.instanceAuth.authorizedMunicipalities[0].officerId !== undefined && result.instanceAuth.authorizedMunicipalities[0].officerId != null && result.instanceAuth.municipalityRestricted === true) {
                Global.vars.gOfficerNumber = result.authorizedMunicipalities[0].officerId;
            }
            if (result.teamCode !== undefined) {
                Global.vars.gTeamNumber = result.teamCode;
            }
            //Now choose instance
            if (Global.vars.multiUserDevice == "Yes" && instanceExists === false) {
                alert(voltmx.i18n.getLocalizedString("l_userNotInInstance"));
            } else {
                frmLogin_selectInstance();
            }
            // Move on
            frmLogin_afterLogOn();
        }
    } else if (result.opstatus == 1014) {
        voltmx.application.dismissLoadingScreen();
        alert(voltmx.i18n.getLocalizedString("e_ser0001"));
    } else {
        voltmx.application.dismissLoadingScreen();
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0001") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
    }
}
/**
 * @function
 *
 */
function frmLogin_selectInstance() {
    Utility_dismissLoadingScreen();
    voltmx.print("### frmLogin_selectInstance");
    voltmx.print("### frmLogin_selectInstance: " + Global.vars.gInstanceId);
    if (Global.vars.instances.length == 1) {
        voltmx.print("### frmLogin_selectInstance - only 1 instance");
        frmLogin_populateInstances();

        function only_one_instance() {
            voltmx.print("### frmLogin - only_one_instance");
            frmLogin_instance_setVisibility(false);
            voltmx.print("### frmLogin - only_one_instance end");
        }
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            voltmx.runOnMainThread(only_one_instance, []);
        } else {
            only_one_instance();
        }
        Global.vars.gInstanceId = Global.vars.instances[0];
        frmLogin.instance.lbxList.selectedKey = Global.vars.gInstanceId;
        voltmx.store.setItem("instance", Global.vars.gInstanceId);
        SyncUtil.gInstance = Global.vars.gInstanceId;
        frmLogin_afterLogOn();
    } else if (Global.vars.instances.length > 1 && Global.vars.gInstanceId === null) {
        voltmx.print("### frmLogin_selectInstance - multiple instances");
        voltmx.print("### frmLogin_selectInstance instance masterdate before: " + JSON.stringify(frmLogin.instance.lbxList.masterData));
        frmLogin_populateInstances();

        function multiple_instances() {
            voltmx.print("### frmLogin - multiple_instances");
            frmLogin_instance_setVisibility(true);
            frmLogin.instance.setFocus(true);
            frmLogin_flcChoosEnvironment_setVisibility(false);
            frmLogin_environment_setVisibility(false);
            frmLogin_hostlocation_setVisibility(false);
            frmLogin_flcOAuthLogon_setVisibility(false);
            voltmx.print("### frmLogin - multiple_instances end");
        }
        Global.vars.firstLogin = "no";
        voltmx.store.setItem("firstLogin", Global.vars.firstLogin);
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            voltmx.runOnMainThread(multiple_instances, []);
        } else {
            multiple_instances();
        }
    } else if (Global.vars.instances.length > 1 && Global.vars.gInstanceId != null) {
        voltmx.print("### frmLogin_selectInstance - multiple instances 2");
        frmLogin_populateInstances();

        function multiple_instances_2() {
            voltmx.print("### frmLogin - multiple_instances_2");
            frmLogin_instance_setVisibility(false);
            frmLogin_flcChoosInstance_setVisibility(false);
            frmLogin.instance.lbxList.selectedKey = Global.vars.gInstanceId;
            voltmx.print("### frmLogin - multiple_instances_2 end");
        }
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            voltmx.runOnMainThread(multiple_instances_2, []);
        } else {
            multiple_instances_2();
        }
        SyncUtil.gInstance = Global.vars.gInstanceId;
        frmLogin_afterLogOn();
    } else {
        voltmx.print("### frmLogin_selectInstance - no instances");
        alert(voltmx.i18n.getLocalizedString("l_noInstances"));
    }
}

function frmLogin_populateInstances() {
    voltmx.print("### frmLogin_populateInstances instances: " + Global.vars.instances);
    var instanceData = [];
    for (var j in Global.vars.instances) {
        if (Global.vars.instances[j] == "NL0021") {
            instanceData.push({
                key: Global.vars.instances[j],
                value: voltmx.i18n.getLocalizedString("l_NSproduction")
            });
        } else if (Global.vars.instances[j] == "RL0003") {
            instanceData.push({
                key: Global.vars.instances[j],
                value: voltmx.i18n.getLocalizedString("l_NSproductionEducation")
            });
        } else if (Global.vars.instances[j] == "RL0004") {
            instanceData.push({
                key: Global.vars.instances[j],
                value: voltmx.i18n.getLocalizedString("l_OVdemo")
            });
        } else {
            instanceData.push({
                key: Global.vars.instances[j],
                value: Global.vars.instances[j]
            });
        }
    }
    if (Global.vars.instances.length > 1) {
        //kies uw instantie
        voltmx.print("### frmLogin_populateInstances instances length bigger then 1 add choose option before instances");
        instanceData.push({
            key: "AAA",
            value: voltmx.i18n.getLocalizedString("l_choosevalue")
        });
    }
    instanceData.sort((a, b) => a.key.localeCompare(b.key, undefined, {
        numeric: true,
        sensitivity: "base"
    }));
    voltmx.print("### frmLogin_populateInstances instances: " + JSON.stringify(instanceData));
    frmLogin.instance.lbxList.masterDataMap = [instanceData, "key", "value"];
    if (Global.vars.gInstanceId != null) {
        frmLogin.instance.lbxList.selectedKey = Global.vars.gInstanceId;
    }
    if (Global.vars.instances.length > 1) {
        //kies uw instantie
        voltmx.print("### frmLogin_populateInstances instances length bigger then 1");
        voltmx.runOnMainThread(frmLogin_flcChoosInstance_setVisibilityTrue, []);
    } else {
        voltmx.runOnMainThread(frmLogin_flcChoosInstance_setVisibilityFalse, []);
    }
    voltmx.print("### frmLogin_populateInstances instance masterdate after: " + JSON.stringify(frmLogin.instance.lbxList.masterData));
}

function frmLogin_flcChoosInstance_setVisibilityTrue() {
    voltmx.print("### frmLogin_flcChoosInstance_setVisibilityTrue");
    frmLogin_flcChoosInstance_setVisibility(true);
    frmLogin.instance.setEnabled(true);
    frmLogin.instance.imgRight.setVisibility(true);
    voltmx.print("### frmLogin_flcChoosInstance_setVisibilityTrue end");
}

function frmLogin_flcChoosInstance_setVisibilityFalse() {
    voltmx.print("### frmLogin_flcChoosInstance_setVisibilityFalse");
    frmLogin_flcChoosInstance_setVisibility(false);
    frmLogin.instance.setEnabled(false);
    frmLogin.instance.imgRight.setVisibility(false);
    voltmx.print("### frmLogin_flcChoosInstance_setVisibilityFalse end");
}

function frmLogin_onSelection_lbxInstance() {
    voltmx.print("#### frmLogin_onSelection_lbxInstance: " + frmLogin.instance.lbxList.selectedKey);
    if (Global.vars.firstLogin == "no" && Global.vars.previousForm != null) {
        //als je vanuit het menu komt
        if (frmLogin.instance.lbxList.selectedKey !== Global.vars.gInstanceId && frmLogin.instance.lbxList.selectedKeyValue[0] !== "AAA") {
            voltmx.print("### instance changed by user");
            voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_changeInstance"), frmLogin_changeInstance_alertConfirmation, "confirmation", voltmx.i18n.getLocalizedString("bt_yes"), voltmx.i18n.getLocalizedString("bt_no"), "Info", null);
        }
    } else if (frmLogin.instance.lbxList.selectedKey !== null && frmLogin.instance.lbxList.selectedKeyValue[0] !== "AAA") {
        Global.vars.gInstanceId = frmLogin.instance.lbxList.selectedKey;
        voltmx.store.setItem("instance", Global.vars.gInstanceId);
        SyncUtil.gInstance = Global.vars.gInstanceId;
        frmLogin_changeThemeForInstance();
        frmLogin_setGlobalForInstance();
        if (Global.vars.useCognitoLogin === true) {
            voltmx.print("### frmLogin_onSelection_lbxInstance cognito service_LogonAmazonCognito");
            service_LogonAmazonCognito(frmLogin_credentialsUserCallback);
        } else if (Global.vars.useMobileAppAzureAD === true) {
            voltmx.print("### frmLogin_onSelection_lbxInstance useMobileAppAzureAD service_LogonToken");
            service_LogonTokenInstance(Global.vars.microsoftAccessToken, Global.vars.gInstanceId, frmLogin_credentialsUserCallback);
        } else {
            frmLogin_onClick_butNext();
        }
        //frmLogin_afterLogOn();
        //frmLogin_afterInstanceSelect();
    } else {
        voltmx.print("### frmLogin_onSelection_lbxInstance no valid choice is made");
    }
}

function frmLogin_changeThemeForInstance() {
    if (Global.vars.gInstanceId == "RL0003" || Global.vars.gInstanceId == "RL0005") {
        voltmx.theme.setCurrentTheme("REDORAopl", frmLogin_changeThemeForInstanceSuccesCallback, frmLogin_changeThemeForInstanceErrorCallback);
    } else {
        voltmx.theme.setCurrentTheme("REDORA", frmLogin_changeThemeForInstanceSuccesCallback, frmLogin_changeThemeForInstanceErrorCallback);
    }
}

function frmLogin_setGlobalForInstance() {
    if (Global.vars.gInstanceId == "RL0004") {
        Global.vars.useDemo = true;
    } else {
        Global.vars.useDemo = false;
    }
    //   if(Global.vars.gInstanceId == "NL0023" || Global.vars.gInstanceId == "NL0027" || Global.vars.gInstanceId == "NL0028"){
    //     Global.vars.startInFollowPrefered = true;
    //   }else
    if (Global.vars.gInstanceId == "NL0022") {
        Global.vars.startInCardCheck = true;
    }
}

function frmLogin_changeThemeForInstanceSuccesCallback() {
    voltmx.print("### frmLogin_changeThemeForInstanceSuccesCallback");
    if (voltmx.theme.getCurrentTheme() == "REDORA") {
        //Redora
        voltmx.application.setApplicationProperties({
            statusBarColor: "184a83"
        }); //Blue
    } else if (voltmx.theme.getCurrentTheme() == "Egis") {
        //Egis
        voltmx.application.setApplicationProperties({
            statusBarColor: "9ec536"
        }); //Green
    } else if (voltmx.theme.getCurrentTheme() == "Arvoo") {
        //Arvoo
        voltmx.application.setApplicationProperties({
            statusBarColor: "f57f22"
        }); //Orange
    } else if (voltmx.theme.getCurrentTheme() == "REDORAopl") {
        //RedoraOpl
        voltmx.application.setApplicationProperties({
            statusBarColor: "c20015"
        }); //Red
    }
}

function frmLogin_changeThemeForInstanceErrorCallback(error) {
    voltmx.print("### frmLogin_changeThemeForInstanceErrorCallback" + JSON.stringify(error));
}

function frmLogin_changeInstance_alertConfirmation(response) {
    voltmx.print("### frmLogin_changeInstance_alertConfirmation" + response);
    if (response) {
        SyncUtil_emptyDatabase(frmLogin_changeInstance_emptyDatabaseSuccessCallback);
    }
}

function frmLogin_changeInstance_emptyDatabaseSuccessCallback(result) {
    voltmx.print("### frmLogin_changeInstance_emptyDatabaseSuccessCallback: " + JSON.stringify(result));
    Global.vars.gInstanceId = frmLogin.instance.lbxList.selectedKey;
    voltmx.store.setItem("instance", Global.vars.gInstanceId);
    SyncUtil.gInstance = Global.vars.gInstanceId;
    CaseData_setUserInformationToCaseInfo();
    //set creationTime for new case on device
    CaseData.caseinfo.creationTime = Utility_getUTCJavascriptDate(null);
    voltmx.print("### frmLogin_changeInstance_emptyDatabaseSuccessCallback CaseData.caseinfo after SyncInit Logon: " + JSON.stringify(CaseData.caseinfo));
    //Go to sync
    SyncUtil.gRegistrationComplete = true;
    SyncUtil.gAutoStartSync = true;
    if (Global.vars.continueAppVersionOK === true) {
        frmSynchronisation_skipSync(); //frmSynchronization.show();
    } else {
        voltmx.print("### frmLogin_changeInstance_emptyDatabaseSuccessCallback version not correct, wait for app to be updated");
    }
}

function frmLogin_Logon_SingleUser() {
    voltmx.print("### frmLogin_Logon_SingleUser");
    voltmx.print("### frmLogin_Logon_SingleUser Loaded username: " + Global.vars.gUsername);
    voltmx.print("### frmLogin_Logon_SingleUser Loaded password: " + Global.vars.gPassword);
    voltmx.print("### frmLogin_Logon_SingleUser Global.vars.tokenExpirationTime: " + Global.vars.tokenExpirationTime);
    var continueWithoutLogon = false;
    if (Global.vars.tokenExpirationTime != null) {
        var expirationTime = new Date(Global.vars.tokenExpirationTime);
        voltmx.print("### frmLogin_Logon_SingleUser expirationTime UTC to javascriptDate: " + expirationTime);
        var currentDateTime = new Date();
        if (currentDateTime < expirationTime) {
            voltmx.print("### frmLogin_Logon_SingleUser currentDateTime < expirationTime && Global.vars.gMyAuthToken != null");
            //set logged in
            Global.vars.LoggedIn = true;
            //get userdata
            Global.vars.gEmail = Utility_storeGetItem("email");
            Global.vars.gFullName = Utility_storeGetItem("fullname");
            Global.vars.gOfficerNumber = Utility_storeGetItem("officernumber");
            Global.vars.gOfficerIdentification = Utility_storeGetItem("officerIdentification");
            Global.vars.gTeamNumber = voltmx.store.getItem("teamnumber");
            //check hash
            var myHashValue = voltmx.store.getItem("instanceAuthHash");
            voltmx.print("### frmLogin_Logon_SingleUser myHashValue: " + myHashValue);
            //check hashes
            if (myHashValue === null) {
                continueWithoutLogon = false;
            } else {
                continueWithoutLogon = true;
            }
        }
    }
    //call service
    if (Global.vars.gPassword != null && Global.vars.gUsername != null) {
        if (continueWithoutLogon === true) {
            voltmx.print("### frmLogin_Logon_SingleUser logon service skipped because token was still ok");
            frmLogin_afterInstanceSelect();
        } else {
            voltmx.print("### frmLogin_Logon_SingleUser call logon service");
            service_LogonService(Global.vars.gUsername, Global.vars.gPassword, Global.vars.gInstanceId, frmLogin_calluserSingleUserCallback);
        }
    } else {
        frmLogin.show();
    }
}

function frmLogin_calluserSingleUserCallback(result) {
    voltmx.print("### frmLogin_calluserSingleUserCallback result: " + JSON.stringify(result));
    if (result.opstatus === 0 && result.httpStatusCode == 200) {
        Global.vars.mfRetry = 0;
        var mytoken = result.sessionToken;
        if (mytoken != null) {
            Global.vars.gMyAuthToken = mytoken;
        } else {
            Global.vars.gMyAuthToken = null;
        }
        var couchToken = result.couchDbToken;
        voltmx.print("### frmLogin_calluserSingleUserCallback couchDbToken: " + couchToken);
        if (couchToken != null) {
            Global.vars.gCouchAuthToken = couchToken;
        } else {
            Global.vars.gCouchAuthToken = null;
        }
        Global.vars.tokenExpirationTime = result.tokenExpirationTime;
        voltmx.print("### frmLogin_calluserSingleUserCallback Global.vars.tokenExpirationTime: " + Global.vars.tokenExpirationTime);
        voltmx.store.setItem("tokenExpirationTime", Global.vars.tokenExpirationTime);
        //
        voltmx.print("#### frmLogin_calluserSingleUserCallback environment: " + Global.vars.environment);
        //voltmx.print("#### frmLogin_calluserSingleUserCallback gSyncPassword: " + Global.vars.gSyncPassword);//niet uit service
        //voltmx.print("#### frmLogin_calluserSingleUserCallback gSyncUsername: " + Global.vars.gSyncUsername);//niet uit service
        voltmx.print("#### frmLogin_calluserSingleUserCallback Global.vars.gInstanceId: " + Global.vars.gInstanceId);
        //voltmx.print("#### frmLogin_calluserSingleUserCallback Global.vars.gMyAuthToken: " + Global.vars.gMyAuthToken);
        //voltmx.print("### frmLogin_calluserSingleUserCallback Global.vars.gCouchAuthToken: " + Global.vars.gCouchAuthToken);
        if (Global.vars.environment === null || Global.vars.gSyncUsername === null || Global.vars.gSyncPassword === null || Global.vars.gInstanceId === null || Global.vars.gMyAuthToken === null) {
            voltmx.print("### frmLogin_calluserSingleUserCallback gRegisteredWithSyncserver 1");
            Global.vars.gRegisteredWithSyncserver = false;
            gInitialSync = true;
            voltmx.application.dismissLoadingScreen();
            voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0002") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
        } else {
            voltmx.print("### frmLogin_calluserSingleUserCallback Logon succes");
            Global.vars.LoggedIn = true;
            //sync server info
            SyncUtil_saveServerInfo();
            voltmx.application.dismissLoadingScreen();
            //set other user globals
            if (result.email !== undefined) {
                Global.vars.gEmail = result.email;
                Utility_storeSetItem("email", Global.vars.gEmail);
            }
            if (result.fullName !== undefined) {
                Global.vars.gFullName = result.fullName;
                Utility_storeSetItem("fullname", Global.vars.gFullName);
            } else if (result.given_name !== undefined && result.family_name !== undefined) {
                Global.vars.gFullName = result.given_name + " " + result.family_name;
                Utility_storeSetItem("fullname", Global.vars.gFullName);
            }
            SyncUtil.gInstance = Global.vars.gInstanceId;
            Global.vars.instanceAuthorizations = result.instanceAuth;
            voltmx.store.setItem("instanceAuthorizations", Global.vars.instanceAuthorizations);
            //save auth block as string hash
            var algo = "sha512";
            var inputstr = JSON.stringify(result.instanceAuth);
            var myHashValue = voltmx.crypto.createHash(algo, inputstr);
            voltmx.print("### frmLogin_calluserSingleUserCallback Logon succes myHashValue: " + myHashValue);
            voltmx.store.setItem("instanceAuthHash", myHashValue);
            frmLogin_afterInstanceSelect();
        }
    } else if (result.opstatus == 1014) {
        voltmx.application.dismissLoadingScreen();
        alert(voltmx.i18n.getLocalizedString("e_ser0001"));
    } else {
        voltmx.application.dismissLoadingScreen();
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0001") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
    }
}

function frmLogin_onClick_butCancel() {
    voltmx.print("#### onClick_frmLogin_butCancel ###");
    if (Global.vars.previousForm === null) {
        // cancel application
        Global_exitApplication();
    } else {
        back_to_Settings_Menu();
        //SyncUtil_resetDatabase();
    }
}

function frmLogin_cancelLoginHandler(response) {
    if (response === true) {
        Global_exitApplication();
    }
}

function frmLogin_onSelection_lbxHostlocation() {
    voltmx.print("#### frmLogin_onSelection_lbxHostlocation: " + frmLogin.hostlocation.lbxList.selectedKey);
    Global.vars.hostlocation = frmLogin.hostlocation.lbxList.selectedKey;
    if (frmLogin.hostlocation.lbxList.selectedKey !== "l_choose") {
        Global_setServerInfo();
    }
    frmLogin.hostlocation.lbxList.setFocus(false);
}
// function frmLogin_onSelection_lbxEnvironment(){
//   voltmx.print("#### frmLogin_onSelection_lbxEnvironment: " + frmLogin.environment.lbxList.selectedKey);
//   if(Global.vars.firstLogin == "no" && Global.vars.previousForm != null){ //als je vanuit het menu komt
//     if(frmLogin.environment.lbxList.selectedKey !== Global.vars.environment){
//       voltmx.print("### environment changed by user");
//       voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_changeEnvironment"),
//                     frmLogin_changeEnvironment_alertConfirmation,
//                     "confirmation",
//                     voltmx.i18n.getLocalizedString("bt_yes"),
//                     voltmx.i18n.getLocalizedString("bt_no"),
//                     "Info",
//                     null
//                    );
//     }
//   }else{
//     Global.vars.environment = frmLogin.environment.lbxList.selectedKey;
//     Global_setServerInfo();
//   }
// }
function frmLogin_onSelection_lbxEnvironment() {
    voltmx.print("#### frmLogin_onSelection_lbxEnvironment: " + frmLogin.environment.lbxList.selectedKey);
    var _gDomain = Utility_getDomainFromEmail(frmLogin.username.txtInputText.text);
    if (frmLogin.environment.lbxList.selectedKey == "production" || frmLogin.environment.lbxList.selectedKey == "acceptance") {
        Global.vars.environment = frmLogin.environment.lbxList.selectedKey;
        if (_gDomain === "TWYNS") {
            Global.vars.hostlocation = "l_choose";
            frmLogin.hostlocation.lbxList.selectedKey = Global.vars.hostlocation;
            frmLogin_hostlocation_setVisibility(true);
        } else if (_gDomain === "NSLOGIN" || _gDomain === "TRANSDEV") {
            Global.vars.environment = frmLogin.environment.lbxList.selectedKey;
            Global.vars.hostlocation = "eu-central-1.pt";
            frmLogin.hostlocation.lbxList.selectedKey = Global.vars.hostlocation;
            frmLogin_hostlocation_setVisibility(false);
        } else {
            Global.vars.environment = frmLogin.environment.lbxList.selectedKey;
            Global.vars.hostlocation = "eu-central-1.rl";
            frmLogin.hostlocation.lbxList.selectedKey = Global.vars.hostlocation;
            frmLogin_hostlocation_setVisibility(false);
        }
        Global_setServerInfo();
    } else {
        Global.vars.environment = frmLogin.environment.lbxList.selectedKey;
        Global.vars.hostlocation = "eu-central-1.rl";
        Global_setServerInfo();
        frmLogin_hostlocation_setVisibility(false);
    }
    var version = appConfig.appVersion;
    var appType = appConfig.appName.substring(0, appConfig.appName.indexOf(appConfig.appId));
    if (appType.length === 0) {
        appType = appConfig.appName;
    }
    var environment = "";
    for (var i in Global.vars.preferences.environments) {
        if (Global.vars.preferences.environments[i].name == Global.vars.environment) {
            environment = voltmx.i18n.getLocalizedString(Global.vars.preferences.environments[i].i18n);
        }
    } //set appropriate apptype
    if (environment !== "") {
        frmLogin.lblVersion.text = appType + " " + version + " " + environment;
    } else {
        frmLogin.lblVersion.text = appType + " " + version;
    }
    frmLogin.environment.lbxList.setFocus(false);
}

function frmLogin_clearDataStoreItems() {
    voltmx.print("#### frmLogin_clearDataStoreItems ###");
    //empty database
    SyncUtil_emptyDatabase();
    //remove stored values
    voltmx.store.removeItem("MultiUserDevice");
    Global.vars.multiUserDevice = null;
    frmLogin.multiUserDevice.lbxList.selectedKey = "No";
    voltmx.store.removeItem("instance");
    Global.vars.gInstanceId = null;
    Global.vars.gPassword = null;
    voltmx.store.removeItem("firstLogin");
    Global.vars.firstLogin = "yes";
    voltmx.store.removeItem("loggedOut");
    Global.vars.loggedOut = "yes";
    voltmx.store.removeItem("globalInit");
    Global.vars.encryptionInUse = true;
    voltmx.store.setItem("globalInit", Global.vars.encryptionInUse);
    voltmx.store.removeItem("instanceAuthHash");
    //remove pin
    Utility_keychain_remove("pinHash");
    Global.vars.pinHash = null;
    voltmx.store.removeItem("usePin");
    Global.vars.usePin = null;
    Global.vars.resetPin = false;
    Global.vars.createPinAfter = false;
    Global_resetApp();
    //empty instances
    Global.vars.instances = [];
    //clear text fields
    frmLogin.password.text = "";
    frmLogin.username.txtInputText.text = "";
    //go back to start
    frmStart.show();
}

function frmLogin_OfficerTextSearch() {
    if (frmLogin.username.txtInputText.text.length > 2 && frmLogin.username.txtInputText.text != Global.vars.gUsername && Global.vars.multiUserDevice == "Yes") {
        voltmx.print("#### frmLogin_OfficerTextSearch search..." + frmLogin.username.txtInputText.text);
        var multiusers = [];
        var searchtext = frmLogin.username.txtInputText.text.toLowerCase();
        for (var i in Global.vars.gMultiUsers) {
            var v = Global.vars.gMultiUsers[i];
            var user = v.userName.toLowerCase();
            if (user.indexOf(searchtext) != -1 && Number(v.teamCode) != 100) {
                multiusers.push(v);
            }
        }
        if (multiusers.length > 0) {
            voltmx.print("### frmLogin_OfficerTextSearch: " + JSON.stringify(multiusers));
            frmLogin_segUsers_setVisibility(true);
            frmLogin.segUsers.setData(multiusers);
        } else {
            frmLogin_segUsers_setVisibility(false);
        }
    } else {
        frmLogin_segUsers_setVisibility(false);
    }
}

function frmLogin_onclick_segUsers() {
    var selected = frmLogin.segUsers.selectedItems[0].userName;
    voltmx.print("#### frmLogin_onclick_segUsers selected: " + selected);
    frmLogin.username.txtInputText.text = selected;
    frmLogin_segUsers_setVisibility(false);
}

function frmLogin_btnRemoveUsername() {
    frmLogin.username.txtInputText.text = "";
    frmLogin_flcChoosEnvironment_setVisibility(false);
    frmLogin_environment_setVisibility(false);
    frmLogin_hostlocation_setVisibility(false);
    txtUserChanged = true;
    Global.vars.preferences.environments = [{
        name: "production",
        i18n: "l_production"
    }, {
        name: "acceptance",
        i18n: "l_acceptance"
    }];
    Global.vars.environment = "production";
    Global.vars.hostlocation = "eu-central-1.rl";
    var version = appConfig.appVersion;
    var appType = appConfig.appName.substring(0, appConfig.appName.indexOf(appConfig.appId));
    if (appType.length === 0) {
        appType = appConfig.appName;
    }
    var environment = "";
    for (var i in Global.vars.preferences.environments) {
        if (Global.vars.preferences.environments[i].name == Global.vars.environment) {
            environment = voltmx.i18n.getLocalizedString(Global.vars.preferences.environments[i].i18n);
        }
    } //set appropriate apptype
    if (environment !== "") {
        frmLogin.lblVersion.text = appType + " " + version + " " + environment;
    } else {
        frmLogin.lblVersion.text = appType + " " + version;
    }
    try {
        frmLogin.environment.lbxList.selectedKey = Global.vars.environment;
    } catch (err) {}
    try {
        frmLogin.hostlocation.lbxList.selectedKey = Global.vars.hostlocation;
    } catch (err) {}
    frmLogin_segUsers_setVisibility(false);
    frmLogin.username.txtInputText.setFocus(true);
}

function frmLogin_btnRemovePassword() {
    frmLogin.password.text = "";
    frmLogin.password.setFocusPassWord = true;
}

function frmLogin_MobileAppAzureADLogin() {
    //MicrosoftAzureADMobileFabric
    if (Global.vars.hostlocation !== "l_choose") {
        frmLogin.flcMainPage.setEnabled(false);
        Global.vars.afterDeeplink = false;
        Global.vars.useCognitoLogin = false;
        voltmx.print("### frmLogin_MobileAppAzureADLogin firstLogin: " + Global.vars.firstLogin);
        voltmx.print("### frmLogin_MobileAppAzureADLogin loggedOut: " + Global.vars.loggedOut);
        voltmx.print("### frmLogin_MobileAppAzureADLogin gDomain: " + Global.vars.gDomain);
        voltmx.print("### frmLogin_MobileAppAzureADLogin gIdentityProvider: " + Global.vars.gIdentityProvider);
        if (Global.vars.firstLogin === "yes" || Global.vars.loggedOut === "yes") {
            identityService_login(frmLogin_MobileAppAzureADLoginResponse);
        } else {
            identityService_refresh_login(Global.vars.gIdentityProvider, frmLogin_MobileAppAzureADLoginResponse);
        }
    } else {
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_chooseHost") + "", null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
    }
}

function frmLogin_NormalLogon() {
    //RotterdamCognito
    voltmx.print("### frmLidentityService_logoutogin_NormalLogon");
    Global.vars.selectedOAuthServiceProvider = "NormalLogon";
    Global.vars.useCognitoLogin = false;
    Global.vars.useMobileAppAzureAD = false;
    frmLogin_onClick_butNext();
}

function frmLogin_CognitoLogin(result) {
    //RotterdamCognito
    voltmx.print("### frmLogin_CognitoLogin result: " + JSON.stringify(result));
    voltmx.print("### frmLogin_CognitoLogin call login service");
    service_LogonAmazonCognito(frmLogin_credentialsUserCallback);
}
/**
 * @function
 *
 * @param result 
 */
function frmLogin_MobileAppAzureADLoginResponse(result) {
    voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("pleaseWait"), "center", true, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    //MobileAppAzure
    try {
        frmLogin.flcMainPage.setEnabled(false);
        var client = voltmx.sdk.getCurrentInstance();
        var auth_client = client.getIdentityService(Global.vars.gIdentityProvider);
        var isLoginPersisted = null;
        try {
            isLoginPersisted = auth_client.usePersistedLogin();
            voltmx.print("### frmLogin_MobileAppAzureADLoginResponse isLoginPersisted : " + isLoginPersisted);
        } catch (err) {
            voltmx.print("### frmLogin_MobileAppAzureADLoginResponse isLoginPersisted error : " + JSON.stringify(err));
        }
        if (isLoginPersisted === true && Global.vars.objectSyncSetupDone === false) {
            voltmx.print("### frmLogin_MobileAppAzureADLoginResponse objectSync_setup started");
            objectSync_setup();
        }
        Global.vars.microsoftAccessToken = null;
        frmLogin_extract_token(result);
        voltmx.print("### frmLogin_MobileAppAzureADLoginResponse Global.vars.gInstanceId: " + Global.vars.gInstanceId);
        frmLogin_check_login_session();
    } catch (exception) {
        voltmx.print("### frmLogin_MobileAppAzureADLoginResponse exception: " + exception);
        //alert("Exception" + exception.message);
        //     voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0004") + "",
        //                   null,
        //                   "error",
        //                   voltmx.i18n.getLocalizedString("bt_ok"),
        //                   null,
        //                   voltmx.i18n.getLocalizedString("l_error"),
        //                   null);
        frmLogin_MobileAppAzureADLogin();
        voltmx.application.dismissLoadingScreen();
    }
}
/**
 * @function
 *
 */
function frmLogin_check_login_session() {
    voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("pleaseWait"), "center", true, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    if (Global.vars.microsoftAccessToken != null) {
        if (Global.vars.gInstanceId != null) {
            voltmx.print("### frmLogin_MobileAppAzureADLoginResponse service_LogonTokenInstance");
            service_LogonTokenInstance(Global.vars.microsoftAccessToken, Global.vars.gInstanceId, frmLogin_credentialsUserCallback);
        } else {
            voltmx.print("### frmLogin_MobileAppAzureADLoginResponse service_LogonToken");
            service_LogonToken(Global.vars.microsoftAccessToken, frmLogin_credentialsUserCallback);
        }
    } else {
        voltmx.print("### frmLogin_MobileAppAzureADLoginResponse Global.vars.microsoftAccessToken: " + Global.vars.microsoftAccessToken + ". ###");
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0004") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
        voltmx.application.dismissLoadingScreen();
    }
}

function frmLogin_extract_token(response) {
    if (response != null && response.params != null && response.params.access_token != null) {
        Global.vars.microsoftAccessToken = response.params.access_token;
        if (frmLogin.username.isVisible === true) {
            var unique_name = frmLogin_getUniqueName(Global.vars.microsoftAccessToken);
            if (unique_name != null && unique_name != "" && voltmx.string.isValidEmail(unique_name)) {
                if (Global.vars.gUsername != null && Global.vars.gUsername !== unique_name && Global.vars.firstLogin == "yes" && (Global.vars.multiUserDevice == null || Global.vars.multiUserDevice === "No")) {
                    frmLogin.username.txtInputText.text = unique_name;
                    voltmx.print("### frmLogin_extract_token frmLogin.username.txtInputText.text: " + frmLogin.username.txtInputText.text);
                }
            }
        }
    }
}

function frmLogin_getUniqueName(token) {
    var payloadBase64 = token.split(".")[1]; // Extract the payload
    var decodedPayload = atob(payloadBase64); // Decode from Base64
    var payload = JSON.parse(decodedPayload); // Parse to JSON
    return payload.unique_name || ""; // Retrieve unique_name
}

function frmLogin_displayMicrosoftOAuthProfile(profile) {
    voltmx.print("### frmLogin_displayMicrosoftOAuthProfile profile: " + JSON.stringify(profile));
    Global.vars.profile = {
        full_name: profile.profile_attributes.display_name,
        u_id: profile.userid,
        p_p: profile.profile_attributes.picture,
        first_name: profile.firstname,
        last_name: profile.lastname,
        email: profile.email
    };
    frmLogin_afterLogOn();
    frmProfile.lblSubHeader.text = "Profiel";
}

function frmLogin_MicrosoftADALOAuthLogin() {
    //   var microsoft={"provider_name":"MicrosoftADAL"};
    //   Global.vars.selectedOAuthServiceProvider="MicrosoftADAL";
    //   Global.vars.afterDeeplink = false;
    //   microsoftAdal_login(frmLogin_displayMicrosoftADALOAuthProfile);
}

function frmLogin_displayMicrosoftADALOAuthProfile(profile) {
    voltmx.print("### frmLogin_displayMicrosoftADALOAuthProfile profile: " + JSON.stringify(profile));
    Global.vars.profile = {
        full_name: profile[0].userProfile.displayName,
        u_id: profile[0].userProfile.id,
        p_p: null, //"https://graph.microsoft.com/beta/me/photo/$value",
        first_name: profile[0].userProfile.givenName,
        last_name: profile[0].userProfile.surname,
        email: profile[0].userProfile.userPrincipalName
    };
    voltmx.print("### frmLogin_displayMicrosoftADALOAuthProfile Global.vars.profile: " + JSON.stringify(Global.vars.profile));
    voltmx.print("### frmLogin_displayMicrosoftADALOAuthProfile is token Expired: " + JSON.stringify(profile[0].isExpired));
    voltmx.print("### frmLogin_displayMicrosoftADALOAuthProfile token expiration Date: " + JSON.stringify(profile[0].expirationDate));
    voltmx.print("### frmLogin_displayMicrosoftADALOAuthProfile " + JSON.stringify(profile[0].userProfile.displayName));
    if (Global.vars.profile.email !== undefined && Global.vars.profile.email != null && Global.vars.profile.email !== "") {
        Global.vars.gUsername = Global.vars.profile.email.toLowerCase();
        Utility_storeSetItem("username", Global.vars.gUsername);
    }
    if (profile[0].isExpired === false) {
        //Global.vars.firstLogin = "no";
        Global.vars.microsoftAccessToken = profile[0].accessToken;
        voltmx.print("### frmLogin_displayMicrosoftADALOAuthProfile Global.vars.microsoftAccessToken: " + Global.vars.microsoftAccessToken);
        if (Global.vars.setServerInfoDone !== true) {
            voltmx.print("### frmLogin_onClick_butNext try to set server info");
            Global_setServerInfo();
            //             try{
            //               voltmx.timer.schedule("callLogin",frmLogin_waitBeforeCallingServiceLogin,2,false);
            //             }catch(err){}
        } else {
            service_LogonToken(Global.vars.microsoftAccessToken, frmLogin_credentialsUserCallback);
        }
    } else {
        alert(voltmx.i18n.getLocalizedString("l_tokenExpired"));
    }
}

function frmLogin_goToStart() {
    frmStart.show();
}

function frmLogin_normalLogin() {
    var microsoft = {
        provider_name: "MicrosoftOAuth"
    };
    Global.vars.selectedOAuthServiceProvider = "Microsoft";
    Global.vars.afterDeeplink = false;
    frmLogin_afterLogOn();
}
// function frmLogin_waitBeforeCallingServiceLogin(){
//   service_LogonToken(Global.vars.microsoftAccessToken, frmLogin_credentialsUserCallback);
// }
function frmLogin_setAuthorizedItemsMenu(menuitems) {
    voltmx.print("### frmLogin_setAuthorizedItemsMenu menuitems: " + JSON.stringify(menuitems));
    Global.vars.menudata = [];
    /// RKA temp RL-2960
    Global.vars.registerConceptEnabled = false;
    /// RKA temp
    for (var i in menuitems) {
        var v = menuitems[i];
        voltmx.print("### frmLogin_setAuthorizedItemsMenu menu item: " + v.identification);
        frmLogin_addMenuItem(voltmx.i18n.getLocalizedString(v.identification));
        if (v.identification === "l_registerconcept") {
            Global.vars.registerConceptEnabled = true;
        }
    }
}

function frmLogin_setAuthorizedModules(mobilemodules) {
    voltmx.print("### frmLogin_setAuthorizedModules mobilemodules: " + JSON.stringify(mobilemodules));
    Global.vars.directPayAuthorized = false;
    for (var i in mobilemodules) {
        var v = mobilemodules[i];
        voltmx.print("### frmLogin_setAuthorizedModules mobilemodule: " + v.identification);
        if (v.identification == "l_vehicle_owner") {
            Global.vars.vehicleOwnerEnabled = true;
        } else if (v.identification == "l_show_history") {
            Global.vars.historyEnabled = true;
        } else if (v.identification == "l_directpay") {
            Global.vars.directPayAuthorized = true;
            Global.vars.onStreetPayment = true;
        } else if (v.identification == "l_enableperson_onresume") {
            Global.vars.setPersonOnResume = Global.vars.enablePersonOnResume;
        }
    }
    voltmx.print("### frmLogin_setAuthorizedModules mobilemodule l_enableperson_onresume: " + Global.vars.setPersonOnResume);
}

function frmLogin_addMenuItem(item) {
    voltmx.print("### frmLogin_addMenuItem menu start");
    // temp
    if (item !== voltmx.i18n.getLocalizedString("l_person")) {
        var exists = false;
        var addMenuItem = null;
        voltmx.print("### frmLogin_addMenuItem menu item: " + item);
        if (item === voltmx.i18n.getLocalizedString("l_follow")) {
            voltmx.print("### frmLogin_addMenuItem menu follow: " + item);
            /// RKA temp RL-12452
            //addMenuItem = {imgSelector : "menuunselect.png", imgIcon : "menu_follow_select.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_followUp"), lblLineBottom : " "};
            frmLogin_addMenuItem(voltmx.i18n.getLocalizedString("l_overviewTask"));
            /// RKA temp
            Global.vars.startInFollow = true;
        } else if (item === voltmx.i18n.getLocalizedString("l_overviewTask")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_follow_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_overviewTask"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_trackDown")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_trace_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_trackDown"),
                lblLineBottom: " "
            };
            Global.vars.startInTrackDown = true;
        } else if (item === voltmx.i18n.getLocalizedString("l_register")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_register.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_register"),
                lblLineBottom: " "
            };
            Global.vars.startInRegister = true;
        } else if (item === voltmx.i18n.getLocalizedString("l_checkvehicle")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_trace_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_checkvehicle"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_checkcard")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_sensor_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_checkcard"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_checklocation")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_follow_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_checklocation"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_registerconcept")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_register.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_registerconcept"),
                lblLineBottom: " "
            };
            Global.vars.startInRegisterConcept = true;
        } else if (item === voltmx.i18n.getLocalizedString("l_vehicle")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_vehicle.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_vehicle"),
                lblLineBottom: " "
            };
            //   }else if(item === voltmx.i18n.getLocalizedString("l_person")){
            //    	addMenuItem = {imgSelector : "menuunselect.png", imgIcon : "menu_person.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_person"), lblLineBottom : " "};
        } else if (item === voltmx.i18n.getLocalizedString("l_openTasks")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_activecase_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_openTasks"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_activeCases")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_activecase_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_activeCases"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_statistics")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_activecase_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_statistics"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_history")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_history.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_history"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_cardcheck")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_activecase_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_cardcheck"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_concepts")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_concept.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_concepts"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_outbox")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_outbox.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_outbox"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_login")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_logon.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_login"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_prohibitions")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_logon.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_prohibitions"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_addLabel")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_label_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_addLabel"),
                lblLineBottom: " "
            };
            Global.vars.startInAddLabel = true;
        } else if (item === voltmx.i18n.getLocalizedString("l_labelCheck")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_check_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_labelCheck"),
                lblLineBottom: " "
            };
        } else if (item === voltmx.i18n.getLocalizedString("l_overviewLabel")) {
            addMenuItem = {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_info_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_overviewLabel"),
                lblLineBottom: " "
            };
        } else {
            voltmx.print("### frmLogin_addMenuItem menu item: " + item + " item not known");
        }
        if (addMenuItem != null) {
            for (var j in Global.vars.menudata) {
                var w = Global.vars.menudata[j];
                if (w != null && w.lblMenuItem !== undefined && w.lblMenuItem != null) {
                    if (voltmx.string.startsWith(w.lblMenuItem, addMenuItem.lblMenuItem) === true) {
                        exists = true;
                    }
                }
            }
            voltmx.print("### frmLogin_addMenuItem menu item exists: " + exists);
            if (!exists) {
                Global.vars.menudata.push(addMenuItem);
            }
        }
    }
    voltmx.print("### frmLogin_addMenuItem menu " + JSON.stringify(Global.vars.menudata));
    voltmx.print("### frmLogin_addMenuItem menu end");
}

function frmLogin_deleteMenuItem(item) {
    voltmx.print("### frmLogin_deleteMenuItem item: " + item);
    var deleteIndex = null;
    for (var j in Global.vars.menudata) {
        var w = Global.vars.menudata[j];
        voltmx.print("### frmLogin_deleteMenuItem menudata: " + JSON.stringify(w));
        if (w != null && w.lblMenuItem !== undefined && w.lblMenuItem != null && item !== undefined && item != null) {
            if (voltmx.string.startsWith(w.lblMenuItem, item) === true) {
                deleteIndex = j;
                break;
            }
        }
    }
    voltmx.print("### frmLogin_deleteMenuItem menu item deleteIndex: " + deleteIndex);
    if (deleteIndex != null) {
        Global.vars.menudata.splice(deleteIndex, 1);
    }
}
// Variables to track tap count and timer
let tapCount = 0;
let tapTimerId = null;
// Function to activate on the third tap
function frmLogin_activateFunction() {
    voltmx.print("### frmLogin_activateFunction");
    voltmx.ui.Alert({
        message: voltmx.i18n.getLocalizedString("l_environmentListActivated"),
        alertType: voltmx.ui.ALERT_TYPE_INFO,
        alertTitle: "Success",
        yesLabel: "OK"
    }, {});
    frmLogin_resetTapCount();
    frmLogin_flcChoosEnvironment_setVisibility(true);
    frmLogin_environment_setVisibility(true);
    var _gDomain = Utility_getDomainFromEmail(frmLogin.username.txtInputText.text);
    voltmx.print("### frmLogin_activateFunction frmLogin.environment.lbxList.selectedKey: " + frmLogin.environment.lbxList.selectedKey);
    if (frmLogin.environment.lbxList.selectedKey == "development" || frmLogin.environment.lbxList.selectedKey == "test") {
        frmLogin_hostlocation_setVisibility(false);
    } else {
        if (_gDomain === "TWYNS") {
            frmLogin_hostlocation_setVisibility(true);
        }
    }
}
// Function to reset tap count and cancel timer
function frmLogin_resetTapCount() {
    voltmx.print("### frmLogin_resetTapCount");
    tapCount = 0;
    if (tapTimerId) {
        voltmx.print("### frmLogin_resetTapCount cancel tapTimerId");
        voltmx.timer.cancel(tapTimerId); // Cancel the timer
        tapTimerId = null;
        //     frmLogin_flcChoosEnvironment_setVisibility(false);
        //     frmLogin_environment_setVisibility(false);
    }
}
// Function called on button tap
function frmLogin_onTripleTapButton() {
    voltmx.print("### frmLogin_onTripleTapButton");
    if (Global.vars.firstLogin == "yes" && Global.vars.loggedOut == "yes" && Global.vars.runningLogonProcess === false && frmLogin.instance.isVisible === false) {
        tapCount++;
        voltmx.print("### frmLogin_onTripleTapButton tapCount: " + tapCount);
        voltmx.print("### frmLogin_onTripleTapButton tapTimerId: " + tapTimerId);
        if (tapCount == 1) {
            frmLogin_onDone_txtUser();
        } else if (tapCount >= 3) {
            if (frmLogin.username.txtInputText.text != null && frmLogin.username.txtInputText.text !== "" && voltmx.string.isValidEmail(frmLogin.username.txtInputText.text)) {
                frmLogin_activateFunction();
            } else {
                voltmx.ui.Alert({
                    message: voltmx.i18n.getLocalizedString("l_enterUsernameFirst"),
                    alertType: voltmx.ui.ALERT_TYPE_INFO,
                    alertTitle: "Success",
                    yesLabel: "OK"
                }, {});
            }
        } else {
            // If a timer already exists, cancel it to reset the countdown
            if (tapTimerId) {
                voltmx.timer.cancel(tapTimerId);
            }
            // Start a new 1-second timer to reset tap count if no tap within 1 second
            tapTimerId = "tripleTapTimeout"; // Unique ID for the timer
            voltmx.timer.schedule(tapTimerId, frmLogin_resetTapCount, 2, false); // 1-second single-use timer
        }
    }
}

function frmLogin_SearchUserCallback(result) {
    voltmx.print("### frmLogin_SearchUserCallback: " + JSON.stringify(result));
    if (result.opstatus === 0 && result.httpStatusCode == 200) {
        var response = result.response;
        voltmx.print("### frmLogin_SearchUserCallback: " + JSON.stringify(response));
        if (response != null) {
            if (response.length >= 1) {
                var v = response[0];
                if (v.document_number != null) {
                    Global.vars.gOfficerDocumentNumber = v.document_number;
                } else {
                    Global.vars.gOfficerDocumentNumber = null;
                }
                CaseData_setUserInformationToCaseInfo();
                voltmx.print("### frmLogin_SearchUserCallback Global.vars.gOfficerDocumentNumber: " + Global.vars.gOfficerDocumentNumber);
            }
        }
    }
}

function frmLogin_SearchUserErrorcallback(error) {
    voltmx.print("### frmLogin_SearchUserErrorcallback: " + JSON.stringify(error));
    Global.vars.gOfficerDocumentNumber = null;
}