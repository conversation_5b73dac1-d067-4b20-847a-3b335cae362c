function frmPersonCities_flcSearchHolder_setVisibility(boolean) {
    voltmx.print("### frmPersonCities_flcSearchHolder_setVisibility");

    function flcSearchHolder_setVisibility() {
        voltmx.print("### frmPersonCities_flcSearchHolder_setVisibility flcSearchHolder_setVisibility: " + boolean);
        frmPersonCities.flcSearchHolder.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcSearchHolder_setVisibility, []);
}

function frmPersonCities_btnBack_setVisibility(boolean) {
    voltmx.print("### frmPersonCities_btnBack_setVisibility");

    function btnBack_setVisibility() {
        voltmx.print("### frmPersonCities_btnBack_setVisibility btnBack_setVisibility: " + boolean);
        frmPersonCities.btnBack.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnBack_setVisibility, []);
}

function frmPersonCities_init() {
    voltmx.print("#### frmPersonCities_init");
    //Utility_registerForIdleTimeout();
    frmPersonCities.onDeviceBack = Global_onDeviceBack;
    frmPersonCities.txtSearch.text = "";
    frmPersonCities.txtSearch.placeholder = frmPersonCities.txtSearch.placeholder + " (" + voltmx.i18n.getLocalizedString("l_3Characterinput") + ")";
    frmPersonCities.segSearch.widgetDataMap = {
        lbl1: "lbl1"
    };
}

function frmPersonCities_preShow() {
    Analytics_logScreenView("person-cities");
    voltmx.print("#### frmPersonCities_preShow Search text: " + frmPersonCities.txtSearch.text);
    frmPersonCities.segSearch.removeAll();
    frmPersonCities_flcSearchHolder_setVisibility(true);
    frmPersonCities_btnBack_setVisibility(false);
    frmPersonCities.btnSearch.text = voltmx.i18n.getLocalizedString("bt_cancel");
    frmPersonCities_getCities();
}

function frmPersonCities_toggleSearch() {
    if (frmPersonCities.btnSearch.text == voltmx.i18n.getLocalizedString("l_search")) {
        frmPersonCities_flcSearchHolder_setVisibility(true);
        frmPersonCities_btnBack_setVisibility(false);
        frmPersonCities.btnSearch.text = voltmx.i18n.getLocalizedString("bt_cancel");
    } else {
        frmPersonCities_flcSearchHolder_setVisibility(false);
        frmPersonCities_btnBack_setVisibility(true);
        frmPersonCities.btnSearch.text = voltmx.i18n.getLocalizedString("l_search");
        frmPersonCities.txtSearch.text = "";
        frmPersonCities_TextSearch();
    }
}

function frmPersonCities_clearSearchText() {
    frmPersonCities.txtSearch.text = "";
    frmPersonCities.txtSearch.setFocus(true);
    frmPersonCities_TextSearch();
}

function frmPersonCities_getCities() {
    if (Global.vars.previousForm === "frmPersonManualPerson") {
        frmPersonCities_getCitiesManualPerson();
    } else {
        frmPersonCities_getCitiesOthers();
    }
}

function frmPersonCities_getCitiesOthers() {
    voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_load"), "center", true, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    var getCities = {
        showCount: 25,
        startRec: 0,
        endRec: 25,
        orderBy: "ORDER BY Name ASC",
        whereClause: "Select * from mle_v_city_m WHERE (UPPER(name) like '%" + frmPersonCities.txtSearch.text.toUpperCase() + "%')"
    };
    voltmx.print("### frmPersonCities_getCities: get" + getCities.showCount + " Cities");
    var wcs = "";
    wcs = getCities.whereClause;
    wcs = wcs + " " + getCities.orderBy; // + " LIMIT " + getCities.showCount + " OFFSET " + getCities.startRec;
    voltmx.print("### frmPersonCities_getCities wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonCities_successCallback, frmPersonCities_errorCallback);
}

function frmPersonCities_getCitiesManualPerson() {
    voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_load"), "center", true, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    var getCities = {
        showCount: 25,
        startRec: 0,
        endRec: 25,
        orderBy: "ORDER BY Name ASC",
        whereClause: "Select * from mle_v_city_m WHERE (UPPER(name) like '%" + frmPersonCities.txtSearch.text.toUpperCase() + "%')"
    };
    voltmx.print("### frmPersonCities_getCities: get" + getCities.showCount + " Cities");
    var wcs = "";
    if (frmPersonManualPerson.txtYearOfBirth.text !== "") {
        voltmx.print("#### frmPersonManualPerson.calDateOfBirth: " + frmPersonManualPerson.calDateOfBirth.dateComponents);
        wcs = Utility_addTimelineToWhereClauseObjectSync(getCities.whereClause, frmPersonManualPerson.calDateOfBirth.dateComponents, false);
    } else {
        wcs = getCities.whereClause;
    }
    wcs = wcs + " " + getCities.orderBy; // + " LIMIT " + getCities.showCount + " OFFSET " + getCities.startRec;
    voltmx.print("### frmPersonCities_getCities wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonCities_successCallback, frmPersonCities_errorCallback);
}

function frmPersonCities_successCallback(result) {
    voltmx.print("### frmPersonCities_successCallback result: " + JSON.stringify(result));
    // only add records if there were new database records
    if (result.length > 0) {
        for (var i = 0;
            ((result) != null) && i < result.length; i++) {
            var v = result[i];
            result[i].lbl1 = result[i].name;
            result[i].orderSequence = Utility_getSequenceOrder(result[i].name, frmPersonCities.txtSearch.text);
        }
        // Sort
        result.sort((a, b) => a.orderSequence.localeCompare(b.orderSequence, undefined, {
            numeric: true,
            sensitivity: 'base'
        }));
        frmPersonCities.segSearch.setData(result);
        voltmx.application.dismissLoadingScreen();
    } else {
        var value = [{
            lbl1: voltmx.i18n.getLocalizedString("i_noCities")
        }];
        frmPersonCities.segSearch.setData(value);
        voltmx.application.dismissLoadingScreen();
    }
}

function frmPersonCities_errorCallback(error) {
    voltmx.print("### frmPersonCities_errorCallback error: " + JSON.stringify(error));
    voltmx.application.dismissLoadingScreen();
}

function frmPersonCities_onClick_segment() {
    try {
        var focusedCharacteristic = frmPersonCities.segSearch.selectedItems;
        if (focusedCharacteristic[0].lbl1 !== voltmx.i18n.getLocalizedString("i_noCities")) {
            voltmx.print("#### frmPersonCities_onClick_segment focuseditem: " + JSON.stringify(focusedCharacteristic));
            if (Global.vars.previousForm == "frmPersonManualPerson") {
                Global.vars.gCasePersons.birthPlaceNL = focusedCharacteristic[0].code;
                Global.vars.gCasePersons.birthPlaceNLDesc = focusedCharacteristic[0].lbl1; //municipality is put in to place
                frmPersonCities_getMunicipality(focusedCharacteristic[0].municipal_code);
            } else if (Global.vars.previousForm == "frmPersonManualAddress") {
                Global.vars.gCasePersons.addresses[0].cityCode = focusedCharacteristic[0].code;
                Global.vars.gCasePersons.addresses[0].city = focusedCharacteristic[0].lbl1;
                frmPersonManualAddress.lblPlaceOfResidency.text = focusedCharacteristic[0].lbl1;
                frmPersonCities_getMunicipality(focusedCharacteristic[0].municipal_code);
            } else if (Global.vars.previousForm == "frmPersonLegalEntityAddress") {
                Global.vars.gCaseLegalEntities.addresses[0].cityCode = focusedCharacteristic[0].code;
                Global.vars.gCaseLegalEntities.addresses[0].city = focusedCharacteristic[0].lbl1;
                frmPersonLegalEntityAddress.lblPlaceOfResidency.text = focusedCharacteristic[0].lbl1;
                frmPersonCities_getMunicipality(focusedCharacteristic[0].municipal_code);
            } else if (Global.vars.previousForm == "frmEnforcementObjectList") {
                frmEnforcementObjectList.lblCity.text = focusedCharacteristic[0].lbl1;
                frmEnforcementObjectList.show();
            } else if (Global.vars.previousForm == "frmCheckLocationObjectList") {
                frmCheckLocationObjectList.lblCity.text = focusedCharacteristic[0].lbl1;
                frmCheckLocationObjectList.show();
            } else if (Global.vars.previousForm == "frmEnforcementObject") {
                frmEnforcementObject.lblPlaceofEstablishment.text = focusedCharacteristic[0].lbl1;
                frmEnforcementObject.show();
            }
            frmPersonCities.txtSearch.text = "";
        }
    } catch (error) {
        voltmx.print("#### frmPersonCities_onClick_segment error: " + JSON.stringify(error));
        voltmx.print("#### frmPersonCities_onClick_segment error msg: " + JSON.stringify(error.message));
    }
}

function frmPersonCities_TextSearch() {
    if (frmPersonCities.txtSearch.text.length > 2 || frmPersonCities.txtSearch.text.length === 0) {
        frmPersonCities_getCities();
    }
}

function frmPersonCities_btnback() {
    frmPersonCities.txtSearch.text = "";
    if (Global.vars.previousForm == "frmPersonManualAddress") {
        frmPersonManualAddress.show();
    } else if (Global.vars.previousForm == "frmPersonManualPerson") {
        frmPersonManualPerson.show();
    } else if (Global.vars.previousForm == "frmEnforcementObjectList") {
        frmEnforcementObjectList.show();
    } else if (Global.vars.previousForm == "frmCheckLocationObjectList") {
        frmCheckLocationObjectList.show();
    } else if (Global.vars.previousForm == "frmEnforcementObject") {
        frmEnforcementObject.show();
    }
}

function frmPersonCities_getMunicipality(code) {
    var wcs = "select * from mle_v_municipality_m where code = '" + code + "'";
    voltmx.print("#### frmPersonCities_getMunicipality wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonCities_getMunicipalitysuccessCallback, frmPersonCities_getMunicipalityerrorCallback);
}

function frmPersonCities_getMunicipalitysuccessCallback(result) {
    voltmx.print("### frmPersonCities_getMunicipalitysuccessCallback result: " + JSON.stringify(result));
    if (result.length > 0) {
        Global.vars.gCasePersons.addresses[0].municipalCode = result[0].code;
        Global.vars.gCasePersons.addresses[0].municipality = result[0].name;
        voltmx.application.dismissLoadingScreen();
    }
    if (Global.vars.previousForm == "frmPersonManualPerson") {
        frmPersonManualPerson.show();
    } else if (Global.vars.previousForm == "frmPersonManualAddress") {
        frmPersonManualAddress.show();
    } else if (Global.vars.previousForm == "frmPersonLegalEntityAddress") {
        frmPersonLegalEntityAddress.show();
    }
}

function frmPersonCities_getMunicipalityerrorCallback(error) {
    voltmx.print("### frmPersonCities_getMunicipalityerrorCallback error: " + JSON.stringify(error));
    voltmx.application.dismissLoadingScreen();
}