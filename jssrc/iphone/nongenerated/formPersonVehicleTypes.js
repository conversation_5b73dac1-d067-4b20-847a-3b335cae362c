function frmPersonVehicleTypes_flcSearchHolder_setVisibility(boolean) {
    voltmx.print("### frmPersonVehicleTypes_flcSearchHolder_setVisibility");

    function flcSearchHolder_setVisibility() {
        voltmx.print("### frmPersonVehicleTypes_flcSearchHolder_setVisibility flcSearchHolder_setVisibility: " + boolean);
        frmPersonVehicleTypes.flcSearchHolder.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcSearchHolder_setVisibility, []);
}

function frmPersonVehicleTypes_btnBack_setVisibility(boolean) {
    voltmx.print("### frmPersonVehicleTypes_btnBack_setVisibility");

    function btnBack_setVisibility() {
        voltmx.print("### frmPersonVehicleTypes_btnBack_setVisibility btnBack_setVisibility: " + boolean);
        frmPersonVehicleTypes.btnBack.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnBack_setVisibility, []);
}

function frmPersonVehicleTypes_init() {
    voltmx.print("### frmPersonVehicleTypes_init");
    // Disable Back Button
    frmPersonVehicleTypes.onDeviceBack = Global_onDeviceBack;
    //Utility_registerForIdleTimeout();
    frmPersonVehicleTypes.txtSearch.text = "";
    frmPersonVehicleTypes.segSearch.widgetDataMap = {
        lbl1: "lbl1",
        imgRight: "imgRight",
    };
    frmPersonVehicleTypes.segSearch.setData([]);
    frmPersonVehicleTypes.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_vehicleTypeGroup");
    //var swipeSettings = {fingers:1,swipedistance:75,swipevelocity:75};
    //var swipeGesture =  frmPersonVehicleTypes.setGestureRecognizer(2,swipeSettings, frmPersonVehicleTypes_handleGesture);
}
//function  frmPersonVehicleTypes_handleGesture(myWidget, gestureInfo){
// 	voltmx.print("#### frmPersonVehicleTypes_handleGesture: " + gestureInfo.swipeDirection);
//	if (gestureInfo.swipeDirection == 2){
//		frmPersonVehicleTypes_back();
//	} 	
//}
function frmPersonVehicleTypes_onclick_segVehicleTypes() {
    voltmx.print("### frmPersonVehicleTypes_onclick_segSearch item: " + JSON.stringify(frmPersonVehicleTypes.segSearch.selectedItems));
    if (frmPersonVehicleTypes.segSearch.selectedItems[0].category == "vehicleType") {
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType = frmPersonVehicleTypes.segSearch.selectedItems[0].code;
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc = frmPersonVehicleTypes.segSearch.selectedItems[0].description;
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType = frmPersonVehicleTypes.segSearch.selectedItems[0].ident_type_vehicle;
        frmPerson.show();
    } else if (frmPersonVehicleTypes.segSearch.selectedItems[0].category == "vehicleTypeGroup") {
        voltmx.print("### frmPersonVehicleTypes_onclick_segSearch vehicleTypeGroup");
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup = frmPersonVehicleTypes.segSearch.selectedItems[0].code;
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc = frmPersonVehicleTypes.segSearch.selectedItems[0].description;
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupId = frmPersonVehicleTypes.segSearch.selectedItems[0].id;
        voltmx.print("### frmPersonVehicleTypes_onclick_segSearch vehicleTypeGroup caseType: " + CaseData.caseinfo.caseType);
        frmPersonVehicleTypes_getVehicleTypes(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup);
    }
}
//back
function frmPersonVehicleTypes_back() {
    voltmx.print("### frmPersonVehicleTypes_back");
    voltmx.print("### frmPersonVehicleTypes_back Global.vars.appMode" + Global.vars.appMode);
    voltmx.print("### frmPersonVehicleTypes_back Global.vars.previousForm" + Global.vars.previousForm);
    if (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") {
        frmPerson.show();
    } else {
        voltmx.print("### frmPersonVehicleTypes_back claimed doc vehicleTypeGroup: " + Global.vars.claimedDoc.case.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup);
        voltmx.print("### frmPersonVehicleTypes_back original vehicleTypeGroup: " + Global.vars.vehicleTypeGroupOriginal.vehicleTypeGroup);
        if (Global.vars.VehicleTypeModus == "vehicleType") {
            voltmx.print("### frmPersonVehicleTypes_back modus vehicleType");
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType = Global.vars.claimedDoc.case.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType;
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc = Global.vars.claimedDoc.case.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc;
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identtypevehicle = Global.vars.claimedDoc.case.vehicle[Global.vars.gCaseVehiclesIndex].identtypevehicle;
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identtype = Global.vars.claimedDoc.case.vehicle[Global.vars.gCaseVehiclesIndex].identtype;
            frmPersonVehicleTypes_getVehicleTypeGroups();
        } else if (Global.vars.VehicleTypeModus == "vehicleTypeGroup") {
            voltmx.print("### frmPersonVehicleTypes_back modus vehicleTypeGroup");
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup = Global.vars.claimedDoc.case.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup;
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc = Global.vars.claimedDoc.case.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc;
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupId = Global.vars.claimedDoc.case.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupId;
            if (Global.vars.vehicleTypeGroupOriginal.vehicleTypeGroup !== "") {
                voltmx.print("### frmPersonVehicleTypes_back modus vehicleTypeGroup original not empty");
                CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup = Global.vars.vehicleTypeGroupOriginal.vehicleTypeGroup;
                CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc = Global.vars.vehicleTypeGroupOriginal.vehicleTypeGroupDesc;
                CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupId = Global.vars.vehicleTypeGroupOriginal.vehicleTypeGroupId;
            }
            frmPerson.show();
        }
    }
}

function frmPersonVehicleTypes_preShow() {
    Analytics_logScreenView("person-vehicle-types");
    voltmx.print("### frmPersonVehicleTypes preShow");
    voltmx.print("### frmPersonVehicleTypes preShow Global.vars.VehicleTypeModus: " + Global.vars.VehicleTypeModus);
    voltmx.print("### frmPersonVehicleTypes preShow Global.vars.previousForm: " + Global.vars.previousForm);
    voltmx.print("### frmPersonVehicleTypes preShow original vehicleTypeGroup: " + Global.vars.vehicleTypeGroupOriginal.vehicleTypeGroup);
    frmPersonVehicleTypes_flcSearchHolder_setVisibility(false);
    frmPersonVehicleTypes.btnSearch.text = voltmx.i18n.getLocalizedString("l_search");
    if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup === null) {
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup = "";
    }
    voltmx.print("### frmPersonVehicleTypes preShow vehicleTypeGroup: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup);
    if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup === "" || Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") {
        Global.vars.VehicleTypeModus = "vehicleTypeGroup";
        frmPersonVehicleTypes.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_vehicleTypeGroup");
    } else {
        Global.vars.VehicleTypeModus = "vehicleType";
        frmPersonVehicleTypes.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_vehicletype");
    }
}

function frmPersonVehicleTypes_toggleSearch() {
    if (frmPersonVehicleTypes.btnSearch.text == voltmx.i18n.getLocalizedString("l_search")) {
        frmPersonVehicleTypes_flcSearchHolder_setVisibility(true);
        frmPersonVehicleTypes_btnBack_setVisibility(false);
        frmPersonVehicleTypes.btnSearch.text = voltmx.i18n.getLocalizedString("bt_cancel");
    } else {
        frmPersonVehicleTypes_flcSearchHolder_setVisibility(false);
        frmPersonVehicleTypes.btnSearch.text = voltmx.i18n.getLocalizedString("l_search");
        frmPersonVehicleTypes.txtSearch.text = "";
        frmPersonVehicleTypes_btnBack_setVisibility(true);
        frmPersonVehicleTypes_TextSearch();
    }
}

function frmPersonVehicleTypes_clearSearchText() {
    frmPersonVehicleTypes.txtSearch.text = "";
    frmPersonVehicleTypes.txtSearch.setFocus(true);
    frmPersonVehicleTypes_TextSearch();
}

function frmPersonVehicleTypes_postShow() {
    voltmx.print("#### frmPersonVehicleTypes_postShow search text: " + frmPersonVehicleTypes.txtSearch.text);
    if ((frmPersonVehicleTypes.txtSearch.text !== "") || (frmPersonVehicleTypes.txtSearch.text.length > 0)) {
        frmPersonVehicleTypes_TextSearch();
    } else {
        voltmx.print("### frmPersonVehicleTypes_postShow go to frmPersonVehicleTypes_getVehicleTypes");
        if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup === null) {
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup = "";
        }
        if (Global.vars.VehicleTypeModus == "vehicleType") {
            frmPersonVehicleTypes_getVehicleTypes(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup);
        } else if (Global.vars.VehicleTypeModus == "vehicleTypeGroup") {
            frmPersonVehicleTypes_getVehicleTypeGroups();
        }
    }
    voltmx.application.dismissLoadingScreen();
}

function frmPersonVehicleTypes_getVehicleTypeGroups() {
    voltmx.print("### frmPersonVehicleTypes_getVehicleTypeGroups");
    var lVehicleTypeGroupClause = "select * from mle_v_vehicle_type_group_m where code in ('R', 'RW') AND organisation = 'ITN'AND LOWER(description) like LOWER('%" + frmPersonVehicleTypes.txtSearch.text + "%')";
    lVehicleTypeGroupClause = Utility_addTimelineToWhereClauseObjectSync(lVehicleTypeGroupClause, CaseData.time.dateComponents);
    lVehicleTypeGroupClause = Utility_addLanguageToWhereClauseObjectSync(lVehicleTypeGroupClause);
    //     var options = {};
    //     options["whereConditionAsAString"] = lVehicleTypeGroupClause;
    voltmx.print("### frmPersonVehicleTypes_getVehicleTypeGroups wcs: " + JSON.stringify(lVehicleTypeGroupClause));
    //     Global.vars.ObjServiceVehicleObject.appTypeGroupObj.get(options, frmPersonVehicleTypes_getVehicleTypeGroupsSuccesCallback, frmPersonVehicleTypes_getVehicleTypeGroupsErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonVehicleTypes_getVehicleTypeGroupsSuccesCallback, frmPersonVehicleTypes_getVehicleTypeGroupsErrorCallback);
}

function frmPersonVehicleTypes_getVehicleTypeGroupsErrorCallback(error) {
    voltmx.print("### frmPersonVehicleTypes_getVehicleTypeGroupsErrorCallback: " + JSON.stringify(error));
}

function frmPersonVehicleTypes_getVehicleTypeGroupsSuccesCallback(result) {
    voltmx.print("### frmPersonVehicleTypes_getVehicleTypeGroupsSuccesCallback: " + JSON.stringify(result));
    for (var i in result) {
        var v = result[i];
        v.lbl1 = v.description;
        v.category = "vehicleTypeGroup";
    }
    frmPersonVehicleTypes.segSearch.setData(result);
    Global.vars.VehicleTypeModus = "vehicleTypeGroup";
    frmPersonVehicleTypes.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_vehicleTypeGroup");
}

function frmPersonVehicleTypes_getVehicleTypes(vehicleTypeGroupCode) {
    voltmx.print("### frmPersonVehicleTypes_getVehicleTypes: " + vehicleTypeGroupCode);
    var lVehicleTypeGroupClause = "Select id from mle_v_vehicle_type_group_m where code = '" + vehicleTypeGroupCode + "'";
    lVehicleTypeGroupClause = Utility_addTimelineToWhereClauseObjectSync(lVehicleTypeGroupClause);
    lVehicleTypeGroupClause = Utility_addLanguageToWhereClauseObjectSync(lVehicleTypeGroupClause);
    var lvehicleTypeClause = "Select * from mle_v_vehicle_type_m where vtp_id in (" + lVehicleTypeGroupClause + ") and lower(description) like lower('%" + frmPersonVehicleTypes.txtSearch.text + "%')";
    lvehicleTypeClause = Utility_addTimelineToWhereClauseObjectSync(lvehicleTypeClause, CaseData.time.dateComponents);
    lvehicleTypeClause = Utility_addLanguageToWhereClauseObjectSync(lvehicleTypeClause);
    voltmx.print("### frmPersonVehicleTypes_getVehicleTypes ocwcs: " + lvehicleTypeClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lvehicleTypeClause, frmPersonVehicleTypes_getVehicleTypesSuccesCallback, frmPersonVehicleTypes_getVehicleTypesErrorCallback);
}

function frmPersonVehicleTypes_getVehicleTypesErrorCallback(error) {
    voltmx.print("### frmPersonVehicleTypes_getVehicleTypesErrorCallback: " + JSON.stringify(error));
}

function frmPersonVehicleTypes_getVehicleTypesSuccesCallback(result) {
    voltmx.print("### frmPersonVehicleTypes_getVehicleTypesSuccesCallback: " + JSON.stringify(result));
    for (var i in result) {
        var v = result[i];
        v.lbl1 = v.description;
        v.category = "vehicleType";
    }
    frmPersonVehicleTypes.segSearch.setData(result);
    Global.vars.VehicleTypeModus = "vehicleType";
    frmPersonVehicleTypes.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_vehicletype");
}

function frmPersonVehicleTypes_getVehicleTypeDescription() {
    var wcs = "select * from mle_v_vehicle_type_m where code = '" + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType + "'";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### frmPersonVehicleTypes_getVehicleTypeDescription wcs: " + JSON.stringify(wcs));
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonVehicleTypes_getVehicleTypeDescriptionSuccesCallback, frmPersonVehicleTypes_getVehicleTypeDescriptionErrorCallback);
}

function frmPersonVehicleTypes_getVehicleTypeDescriptionErrorCallback(error) {
    voltmx.print("### frmPersonVehicleTypes_getVehicleTypeDescriptionSuccesCallback: " + JSON.stringify(error));
    frmPersonVehicleTypes.show();
}

function frmPersonVehicleTypes_getVehicleTypeDescriptionSuccesCallback(result) {
    voltmx.print("### frmHandleCharacteristic_getVehicleTypesSuccesCallback: " + JSON.stringify(result));
    if (result.length > 0) {
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc = result[0].description;
        frmPerson.show();
    }
}

function frmPersonVehicleTypes_TextSearch() {
    voltmx.print("#### frmPersonVehicleTypes_TextSearch Start search..." + frmPersonVehicleTypes.txtSearch.text);
    if (Global.vars.VehicleTypeModus == "vehicleType") {
        frmPersonVehicleTypes_getVehicleTypes(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup);
    } else if (Global.vars.VehicleTypeModus == "vehicleTypeGroup") {
        frmPersonVehicleTypes_getVehicleTypeGroups();
    }
}