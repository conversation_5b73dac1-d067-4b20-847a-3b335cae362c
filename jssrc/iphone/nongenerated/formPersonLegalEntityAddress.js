function frmPersonLegalEntityAddress_init() {
    voltmx.print("#### frmPersonLegalEntityAddress_init");
    //Utility_registerForIdleTimeout();
    frmPersonLegalEntityAddress.onDeviceBack = Global_onDeviceBack;
    frmPersonLegalEntityAddress_clearAddress();
}

function frmPersonLegalEntityAddress_preshow() {
    Analytics_logScreenView("person-legal-entity-address");
    voltmx.print("#### frmPersonLegalEntityAddress_preshow");
    if (Global.vars.gCaseLegalEntities.addresses[0].Zipcode !== "") {
        frmPersonLegalEntityAddress.txtZipCode.text = Global.vars.gCaseLegalEntities.addresses[0].Zipcode;
    }
    if (Global.vars.gCaseLegalEntities.addresses[0].StreetNumber !== "") {
        frmPersonLegalEntityAddress.txtStreetNumber.text = Global.vars.gCaseLegalEntities.addresses[0].StreetNumber;
    }
    if (Global.vars.gCaseLegalEntities.addresses[0].StreetNumAdditn !== "") {
        frmPersonLegalEntityAddress.txtHouseNumberAddition.text = Global.vars.gCaseLegalEntities.addresses[0].StreetNumAdditn;
    }
    if (Global.vars.gCaseLegalEntities.addresses[0].Street !== "") {
        frmPersonLegalEntityAddress.txtStreet.text = Global.vars.gCaseLegalEntities.addresses[0].Street;
    }
    if (Global.vars.gCaseLegalEntities.addresses[0].City !== "") {
        frmPersonLegalEntityAddress.lblPlaceOfResidency.text = Global.vars.gCaseLegalEntities.addresses[0].City;
    }
}

function frmPersonLegalEntityAddress_clearAddress() {
    voltmx.print("##### frmPerson_clearAddress ");
    frmPersonLegalEntityAddress.lblPlaceOfResidency.text = voltmx.i18n.getLocalizedString("l_city");
    frmPersonLegalEntityAddress.txtStreet.text = "";
    frmPersonLegalEntityAddress.txtZipCode.text = "";
    frmPersonLegalEntityAddress.txtStreetNumber.text = "";
    frmPersonLegalEntityAddress.txtHouseNumberAddition.text = "";
}

function frmPersonLegalEntityAddress_onDoneZipcode() {
    var validatedZipcode = Utility_checkZipcode(frmPersonLegalEntityAddress.txtZipCode.text);
    if (!validatedZipcode) {
        alert("Dit is geen geldige postcode");
    } else {
        frmPersonLegalEntityAddress.txtZipCode.text = validatedZipcode;
    }
}

function frmPersonLegalEntityAddress_searchStreet() {
    voltmx.print("### frmPersonLegalEntityAddress_searchStreet");
    if (frmPersonLegalEntityAddress.txtZipCode.text !== "" && frmPersonLegalEntityAddress.txtStreetNumber.text !== "") {
        alert("Roep zoek straat op basis van postcode service aan");
        //implement dutchzipcode service
        // service call
        //service_Dutchzipcode(frmPersonLegalEntityAddress.txtZipCode.text, frmPersonLegalEntityAddress.txtStreetNumber.text, frmPersonLegalEntityAddress_callbackfunctionDutchzipcode);
    } else {
        alert("Controleer of zowel Postcode als Huisnummer zijn ingevuld en probeer opnieuw");
    }
}

function frmPersonLegalEntityAddress_onclick_municipalityAddress() {
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    frmPersonMunicipalities.show();
}

function frmPersonLegalEntityAddress_onclickDone() {
    frmPersonLegalEntityAddress_SetGlobals();
}

function frmPersonLegalEntityAddress_SetGlobals() {
    Global.vars.gCaseLegalEntities.addresses[0].IndSecret = 0;
    Global.vars.gCaseLegalEntities.addresses[0].Source = "03"; // source 01 basisregister, use 03 for manual, use 02 for orther register
    Global.vars.gCaseLegalEntities.addresses[0].IndInResearch = 0;
    Global.vars.gCaseLegalEntities.addresses[0].Street = frmPersonLegalEntityAddress.txtStreet.text;
    Global.vars.gCaseLegalEntities.addresses[0].StreetNumber = frmPersonLegalEntityAddress.txtStreetNumber.text;
    Global.vars.gCaseLegalEntities.addresses[0].StreetNumAdditn = frmPersonLegalEntityAddress.txtHouseNumberAddition.text;
    Global.vars.gCaseLegalEntities.addresses[0].Zipcode = frmPersonLegalEntityAddress.txtZipCode.text.replace(/\s+/g, '');
    Global.vars.gCaseLegalEntities.addresses[0].City = frmPersonLegalEntityAddress.lblPlaceOfResidency.text;
    var validated = frmPersonLegalEntityAddress_validateSetValues(Global.vars.gCaseLegalEntities);
    if (validated.validated) {
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        frmPersonLegalEntityResult.show();
        frmPersonLegalEntityAddress_clearAddress();
    } else {
        alert(validated.errorMsg);
    }
}

function frmPersonLegalEntityAddress_validateSetValues(inputPerson) {
    voltmx.print("#### frmPersonLegalEntityAddress_validateSetValues ####");
    var lvalidated = true;
    var lmessage = "";
    var lResult = {
        entity: "legalentity",
        validated: lvalidated,
        errorCode: "",
        errorMsg: lmessage
    };
    if (inputPerson.addresses[0].StreetNumber === "" || inputPerson.addresses[0].StreetNumber === null) // && (inputPerson.PersonFilled == 0))
    {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = "(" + voltmx.i18n.getLocalizedString("l_streetnumber");
        } else {
            lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_streetnumber");
        }
    }
    if (inputPerson.addresses[0].Street === "" || inputPerson.addresses[0].Street === null) // && (inputPerson.PersonFilled == 0))
    {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = "(" + voltmx.i18n.getLocalizedString("l_street");
        } else {
            lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_street");
        }
    }
    if (inputPerson.addresses[0].CityCode === "" || inputPerson.addresses[0].CityCode === null) // && (inputPerson.PersonFilled == 0))
    {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = "(" + voltmx.i18n.getLocalizedString("l_placeofresidency");
        } else {
            lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_placeofresidency");
        }
    }
    voltmx.print("#### Validate.legalentity address: " + JSON.stringify(inputPerson.addresses[0]));
    if (lvalidated === false) {
        lmessage = voltmx.i18n.getLocalizedString("e_mand0002") + " " + lmessage.trim() + ")";
        lResult = {
            entity: "legalentity",
            validated: lvalidated,
            errorCode: "e_mand0002",
            errorMsg: lmessage
        }; //check errorcode
    }
    return lResult;
}

function frmPersonLegalEntityAddress_btnBack() {
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    frmPerson.show();
}