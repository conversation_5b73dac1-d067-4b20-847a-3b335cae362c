gHandleCharacteristicBreadCrumb = [];
gHandleCharacteristicPreviousCategory = [];

function frmHandleCharacteristic_flcSaveCase_setVisibility(boolean) {
    voltmx.print("### frmHandleCharacteristic_flcSaveCase_setVisibility");

    function btnEdit_setVisibility() {
        voltmx.print("### frmHandleCharacteristic_flcSaveCase_setVisibility btnEdit_setVisibility: " + boolean);
        frmHandleCharacteristic.flcSaveCase.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnEdit_setVisibility, []);
}

function frmHandleCharacteristic_init() {
    //Utility_registerForIdleTimeout();
    frmHandleCharacteristic.onDeviceBack = Global_onDeviceBack;
    //frmHandleCharacteristic.segSearch.widgetDataMap = {lbl1 : "name", imgRight: "imgRight", imgLeft : "image"};
    //frmHandleCharacteristic.segSearch.removeAll();
    Global.vars.handleCharacteristicType = "Handlingtype";
    //Global.vars.directOffenceSelect = false;
    voltmx.print("### frmHandleCharacteristic_init Global.vars.directOffenceSelect: " + Global.vars.directOffenceSelect);
    var swipeSettings = {
        fingers: 1,
        swipedistance: 75,
        swipevelocity: 75
    };
    var swipeGesture = frmHandleCharacteristic.setGestureRecognizer(2, swipeSettings, frmHandleCharacteristic_handleGesture);
}

function frmHandleCharacteristic_handleGesture(myWidget, gestureInfo) {
    voltmx.print("#### frmHandleCharacteristic_handleGesture: " + gestureInfo.swipeDirection);
    if (gestureInfo.swipeDirection == 2) {
        voltmx.print("### swipe direction 2");
        //frmHandleCharacteristic_getPrevious();
    }
}

function frmHandleCharacteristic_preshow() {
    Analytics_logScreenView("handle-characteristic");
    voltmx.print("#### frmHandleCharacteristic_preshow Global.vars.directOffenceSelect: " + Global.vars.directOffenceSelect);
    voltmx.print("#### frmHandleCharacteristic_preshow Global.vars.appMode: " + Global.vars.appMode);
    voltmx.print("#### frmHandleCharacteristic_preshow CaseType: " + CaseData.caseinfo.caseType);
    voltmx.print("#### frmHandleCharacteristic_preshow CaseData.caseinfo: " + JSON.stringify(CaseData.caseinfo));
    voltmx.print("#### frmHandleCharacteristic_preshow BreadCrumb :" + JSON.stringify(gHandleCharacteristicBreadCrumb));
    voltmx.print("#### frmHandleCharacteristic_preshow gHandleCharacteristicPreviousCategory :" + JSON.stringify(gHandleCharacteristicPreviousCategory));
    if (frmHandleCharacteristic.segSearch != null) {
        frmHandleCharacteristic.segSearch.widgetDataMap = {
            lbl1: "name",
            imgRight: "imgRight",
            imgLeft: "image",
            template: "template"
        };
        frmHandleCharacteristic.segSearch.removeAll();
    }
    frmHandleCharacteristic_getTaskType();
    frmHandleCharacteristic_flcSaveCase_setVisibility(false);
    if (Global.vars.previousForm === "frmHandleCharacteristic") {
        gHandleCharacteristicBreadCrumb.pop();
        gHandleCharacteristicPreviousCategory.pop();
    }
    if (gHandleCharacteristicBreadCrumb.length > 0) {
        var index = gHandleCharacteristicBreadCrumb.length;
        frmHandleCharacteristic.lblSubHeader.text = gHandleCharacteristicBreadCrumb[index - 1];
    }
}

function frmHandleCharacteristic_postshow() {
    voltmx.print("#### frmHandleCharacteristic_postshow");
    voltmx.application.dismissLoadingScreen();
}

function frmHandleCharacteristic_showForm() {
    Utility_clearOptions();
    //   Utility_resetToOriginalCaseType();
    frmHandleCharacteristic.show();
}

function frmHandleCharacteristic_getTaskType() {
    voltmx.print("#### frmHandleCharacteristic_getTaskType CaseData.processinfo.activeTaskType1 : " + CaseData.processinfo.activeTaskType);
    var lTaskTypeClause = "select * from mle_v_task_type_msv where identification = '" + CaseData.processinfo.activeTaskType + "'";
    voltmx.print("#### frmHandleCharacteristic_getTaskType CaseData.processinfo.activeTaskType 2: " + CaseData.processinfo.activeTaskType);
    voltmx.print("#### frmHandleCharacteristic_getTaskType lTaskTypeClause: " + lTaskTypeClause);
    lTaskTypeClause = Utility_addTimelineToWhereClauseObjectSync(lTaskTypeClause, CaseData.time.dateComponents);
    lTaskTypeClause = Utility_addLanguageToWhereClauseObjectSync(lTaskTypeClause);
    voltmx.print("### frmHandleCharacteristic_getTaskType ocwcs: " + lTaskTypeClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lTaskTypeClause, frmHandleCharacteristic_getTaskTypeSuccessCallback, frmHandleCharacteristic_getTaskTypeErrorCallback);
}

function frmHandleCharacteristic_getTaskTypeSuccessCallback(result) {
    voltmx.print("#### frmHandleCharacteristic_getTaskTypeSuccessCallback: " + JSON.stringify(result));
    if (result.length > 0) {
        Global.vars.taskTypeId = result[0].id;
        Global.vars.taskType = result[0].identification;
    }
    frmHandleCharacteristic_getTaskTypeOutcomeCategories();
}

function frmHandleCharacteristic_getTaskTypeErrorCallback(error) {
    voltmx.print("#### frmHandleCharacteristic_getTaskTypeErrorCallback: " + JSON.stringify(error));
}

function frmHandleCharacteristic_getTaskTypeOutcomeCategories() {
    voltmx.print("#### frmHandleCharacteristic_getTaskTypeOutcomeCategories");
    var lTaskTypeOutcomeCategorieClause = "select * from mle_v_task_type_outcome_category_msv where tte_id = '" + Global.vars.taskTypeId + "'";
    voltmx.print("#### frmHandleCharacteristic_getTaskTypeOutcomeCategories lOffenceCatClause: " + lTaskTypeOutcomeCategorieClause);
    lTaskTypeOutcomeCategorieClause = Utility_addTimelineToWhereClauseObjectSync(lTaskTypeOutcomeCategorieClause, CaseData.time.dateComponents);
    lTaskTypeOutcomeCategorieClause = Utility_addLanguageToWhereClauseObjectSync(lTaskTypeOutcomeCategorieClause);
    voltmx.print("### frmHandleCharacteristic_getTaskTypeOutcomeCategories ocwcs: " + lTaskTypeOutcomeCategorieClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lTaskTypeOutcomeCategorieClause, frmHandleCharacteristic_getTaskTypeOutcomeCategoriesSuccessCallback, frmHandleCharacteristic_getTaskTypeOutcomeCategoriesErrorCallback);
}

function frmHandleCharacteristic_getTaskTypeOutcomeCategoriesErrorCallback(error) {
    voltmx.print("#### frmHandleCharacteristic_getTaskTypeOutcomeCategoriesErrorCallback: " + JSON.stringify(error));
}

function frmHandleCharacteristic_getTaskTypeOutcomeCategoriesSuccessCallback(result) {
    voltmx.print("#### frmHandleCharacteristic_getTaskTypeOutcomeCategories: " + JSON.stringify(result));
    Global.vars.handlingTypes = [];
    if (result.length > 0) {
        //var lAddHeaderRecord = {lblSecHdr1:voltmx.i18n.getLocalizedString("l_wanted"), imgHeader:"empty.png", template:flcHeaderRed};
        for (var i in result) {
            var v = result[i];
            v.category = "OutcomeTypeCategory";
            v.name = v.ouy_description;
            v.imgRight = "arrowrightmini.png";
            v.order = "999";
            v.template = flcSegItemHandle;
            Global.vars.handlingTypes.push(v);
        }
    }
    voltmx.print("#### frmHandleCharacteristic_getTaskTypeOutcomeCategories Global.vars.handlingTypes: " + JSON.stringify(Global.vars.handlingTypes));
    if (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") {
        //if((CaseData.person[Global.vars.gCasePersonsIndex] !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].edited === true) || (CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].edited === true && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vehicle)||(CaseData.enforcementObject !== undefined && CaseData.enforcementObject.edited !== undefined && CaseData.enforcementObject.edited === true)){
        frmHandleCharacteristic_getCtcAuthorization();
        //}else{
        //  frmHandleCharacteristic_setCharacteristic();
        //} 
    } else {
        frmHandleCharacteristic_getCtcAuthorization();
    }
}

function frmHandleCharacteristic_getCtcAuthorization() {
    voltmx.print("#### frmHandleCharacteristic_getCtcAuthorization");
    var functions = "";
    for (var b in Global.vars.officerFunctions) {
        var officerFunction = Global.vars.officerFunctions[b];
        for (var j = 0; j < Global.vars.authorizedFunctions.length; j++) {
            var authorizedFunction = Global.vars.authorizedFunctions[j];
            if (authorizedFunction === officerFunction) {
                if (functions === "") {
                    functions = "'" + officerFunction + "'";
                } else {
                    functions = functions + ",'" + officerFunction + "'";
                }
                break;
            }
        }
    }
    //check employeefunctions
    voltmx.print("### frmHandleCharacteristic_getCtcAuthorization functions: " + functions);
    var lCtcAuthorizationClause = "select * from mle_v_ctc_authorization_m where function_code in (" + functions + ")";
    voltmx.print("#### frmHandleCharacteristic_getCtcAuthorization lCtcAuthorizationClause: " + lCtcAuthorizationClause);
    lCtcAuthorizationClause = Utility_addTimelineToWhereClauseObjectSync(lCtcAuthorizationClause, CaseData.time.dateComponents);
    voltmx.print("### frmHandleCharacteristic_getCtcAuthorization ocwcs: " + lCtcAuthorizationClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lCtcAuthorizationClause, frmHandleCharacteristic_getCtcAuthorizationSuccessCallback, frmHandleCharacteristic_getCtcAuthorizationErrorCallback);
}

function frmHandleCharacteristic_getCtcAuthorizationErrorCallback(error) {
    voltmx.print("#### frmHandleCharacteristic_getCtcAuthorizationErrorCallback: " + JSON.stringify(error));
}

function frmHandleCharacteristic_getCtcAuthorizationSuccessCallback(result) {
    voltmx.print("#### frmHandleCharacteristic_getCtcAuthorizationSuccessCallback: " + JSON.stringify(result));
    //ctcid
    var caseCategoryIds = "";
    for (var i in result) {
        var v = result[i];
        if (caseCategoryIds.indexOf("'" + v.ctc_id + "'") == -1) {
            caseCategoryIds = caseCategoryIds + "'" + v.ctc_id + "'" + ",";
        }
    }
    caseCategoryIds = caseCategoryIds.replace(/,\s*$/, "");
    voltmx.print("### frmHandleCharacteristic_getCtcAuthorizationSuccessCallback caseCategoryIds: " + caseCategoryIds);
    //   frmHandleCharacteristic_getCaseTypeCategries(caseCategoryIds);
    frmHandleCharacteristic_getCaseTypeCategriesObject(caseCategoryIds);
}

function frmHandleCharacteristic_getCaseTypeCategriesObject(caseCategoryIds) {
    voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesObject");
    var wcs = "select * from mle_v_case_type_category_m where id in (" + caseCategoryIds + ")";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    //   var options = {};
    //   options["whereConditionAsAString"] = wcs;
    voltmx.print("### frmHandleCharacteristic_getCaseTypeCategriesObject wcs: " + wcs);
    //   Global.vars.syncObjects.caseTypeCategoryObj.get(options, frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback, frmHandleCharacteristic_getCaseTypeCategriesErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback, frmHandleCharacteristic_getCaseTypeCategriesErrorCallback);
}

function frmHandleCharacteristic_getCaseTypeCategriesErrorCallback(error) {
    voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesErrorCallback: " + JSON.stringify(error));
}

function frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback(result) {
    voltmx.print("### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback result: " + JSON.stringify(result));
    voltmx.print("##### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback " + Global.vars.appMode);
    var amountTax = true;
    var skipParking = false;
    //
    //  CaseData.offence.amountTax = "1.50";
    //
    if (CaseData.offence.amountTax === undefined || CaseData.offence.amountTax == "0.0" || CaseData.offence.amountTax === "" || CaseData.offence.amountTax === null) {
        if (Global.vars.enableParkiusMode === true) {
            //	CaseData.offence.amountTax = "1.05";  
        } else {
            amountTax = false;
        }
    }
    try {
        if (CaseData.parking != null && (CaseData.parking.serviceResult.valid === true && (CaseData.parking.serviceResult.locationVerified === undefined || CaseData.parking.serviceResult.locationVerified === null || (CaseData.parking.serviceResult.locationVerified !== undefined && CaseData.parking.serviceResult.locationVerified === true))) || (CaseData.parking.serviceResult.valid === false && (CaseData.parking.serviceResult.serviceErrorsDetected === undefined || CaseData.parking.serviceResult.serviceErrorsDetected === null || (CaseData.parking.serviceResult.serviceErrorsDetected !== undefined && CaseData.parking.serviceResult.serviceErrorsDetected === true)))) {
            // skip Parking when:
            // valid parking result
            // invalid parking result ánd service error
            skipParking = true;
        }
    } catch (err) {
        skipParking = false;
    }
    // result code find = pv?
    // var parentId = result.id
    // if result.id_master_category = parentId
    // array.push {caseTypeCategory: result.code}
    for (var i in result) {
        var v = result[i];
        //var order = (v.ordercategory === null ? i : v.ordercategory)+"";
        var order = (v.order_category === null ? i : v.order_category) + "";
        voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback order: " + order);
        v.category = "CaseTypeCategory";
        v.name = v.description;
        v.imgRight = "arrowrightmini.png";
        v.order = order;
        v.indhasoffencetypes = Utility_stringToBoolean(v.ind_has_offence_types);
        v.template = flcSegItemHandle;
        voltmx.print("##### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback ind_display_in_app:  " + v.ind_display_in_app + " - " + v.description);
        if (Utility_stringToBoolean(v.ind_display_in_app) === true) {
            voltmx.print("##### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback v: " + JSON.stringify(v));
            if (((Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") || ((Global.vars.appMode == voltmx.i18n.getLocalizedString("l_trackDown") || Global.vars.appMode == voltmx.i18n.getLocalizedString("l_followUp")) && (amountTax === false || skipParking === true))) && v.code == 'f') {
                // do nothing: no fiscal when in register mode!
                voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback no fiscal in register mode");
            } else if (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_followUp") && CaseData.caseinfo.caseType == "CONTROL_L" && v.code == 'f') {
                // do nothing: no Fiscal when in follow with Legal
                voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback no Fiscal when in follow with Legal");
            } else if ((Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") && Utility_isEmptyEnforcementObject(CaseData.enforcementObject) === false && (v.enforcement_type == 'S' || v.enforcement_type == 'F')) {
                // do nothing: no fiscal or combibon when in register mode with enforcementObject
                voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback no fiscal or combibon when in register mode with filled enforcementObject");
            } else if ((Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") && ((CaseData.person[Global.vars.gCasePersonsIndex] === undefined || CaseData.person[Global.vars.gCasePersonsIndex].edited === false) && (CaseData.vehicle[Global.vars.gCaseVehiclesIndex] === undefined || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].edited === false || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType !== vehicleIdentType.vehicle)) && (v.enforcement_type == 'S' || v.enforcement_type == 'F')) {
                // do nothing: no fiscal or combibon when in register mode when there is no person or vehicle
                voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback no fiscal or combibon when in register mode when there is no person or vehicle");
            } else if ((Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") && Utility_isEmptyEnforcementObject(CaseData.enforcementObject) === true && (v.enforcement_type == 'B')) {
                // do nothing: no horeca controle in register mode with empty enforcementObject
                voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback no horeca controle in register mode with empty enforcementObject");
            } else if ((Global.vars.buildFor == "OV" || Global.vars.buildFor == "NS") && (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") && (CaseData.location.chosenFurtherIndicationKey !== undefined && CaseData.location.chosenFurtherIndicationKey != null && CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.others) && (v.enforcement_type == 'O')) {
                // do nothing: no horeca controle in register mode with empty enforcementObject
                voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback Locatie aanduiding is overig dus geen verboden laten zien");
            } else if (Global.vars.buildFor == "NS" && (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") && (CaseData.location.chosenFurtherIndicationKey !== undefined && CaseData.location.chosenFurtherIndicationKey != null && CaseData.location.chosenFurtherIndicationKey !== furtherIndicationType.others) && (CaseData.person[Global.vars.gCasePersonsIndex] === undefined || CaseData.person[Global.vars.gCasePersonsIndex].edited === false) && (v.enforcement_type == 'O')) {
                // do nothing: no horeca controle in register mode with empty enforcementObject
                voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback Geen persoon dus geen verboden laten zien");
            } else if (Global.vars.buildFor == "OV" && (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") && (CaseData.location.chosenFurtherIndicationKey !== undefined && CaseData.location.chosenFurtherIndicationKey != null && CaseData.location.chosenFurtherIndicationKey !== furtherIndicationType.others) && (v.enforcement_type == 'O')) {
                // do nothing: no horeca controle in register mode with empty enforcementObject
                voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback Geen persoon dus geen verboden laten zien");
                Global.vars.handlingTypes.push(v);
            } else {
                voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback others v: " + JSON.stringify(v));
                Global.vars.handlingTypes.push(v);
            }
        }
        //nog een keer door result loopen voor Bestuursdwang Rotterdam
        if (Utility_stringToBoolean(Global.vars.governance.governance) === true) {
            voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback Try to set governance");
            for (var k in result) {
                var x = result[k];
                if (x.enforcement_type == 'S') {
                    //duplicate combibon as Bestuursdwang/governance
                    var governanceCombibon = JSON.parse(JSON.stringify(x));
                    governanceCombibon.description = Global.vars.governance.description;
                    governanceCombibon.name = governanceCombibon.description;
                    var alreadyExists = false;
                    for (var l in Global.vars.handlingTypes) {
                        var y = Global.vars.handlingTypes[l];
                        if (y.name == Global.vars.governance.description) {
                            voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback governance already exists");
                            alreadyExists = true;
                            break;
                        }
                    }
                    if (alreadyExists === false) {
                        voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback added governance");
                        Global.vars.handlingTypes.push(governanceCombibon);
                    }
                    break;
                }
            }
        }
        //Einde Bestuursdwang
    }
    if (Utility_stringToBoolean(Global.vars.UVB.UVB) === true) {
        for (var j in Global.vars.handlingTypes) {
            var w = Global.vars.handlingTypes[j];
            if (w.enforcement_type == 'S' && CaseData.location.chosenFurtherIndicationKey != furtherIndicationType.others && CaseData.location.chosenFurtherIndicationKey != furtherIndicationType.alongTheTrack) {
                //duplicate combibon as UVB
                var uvbCombibon = JSON.parse(JSON.stringify(w));
                uvbCombibon.description = Global.vars.UVB.description;
                uvbCombibon.name = uvbCombibon.description;
                Global.vars.handlingTypes.push(uvbCombibon);
                break;
            }
        }
    }
    for (var k in Global.vars.handlingTypes) {
        var x = Global.vars.handlingTypes[k];
        voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback Global.vars.handlingTypes k: " + k);
        voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback Global.vars.handlingTypes k.template pre: " + x.template);
        if (CaseData.caseinfo.caseTypeCategoryDescription != null && CaseData.caseinfo.caseTypeCategoryDescription === x.description) {
            Global.vars.handlingTypes[k].template = flcSegItemHandleSelect;
        } else {
            Global.vars.handlingTypes[k].template = flcSegItemHandle;
        }
        voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback Global.vars.handlingTypes k.template post: " + x.template);
    }
    voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback Global.vars.handlingTypes: " + JSON.stringify(Global.vars.handlingTypes));
    if (Global.vars.handlingTypes.length > 0) {
        Global.vars.handlingTypes.sort((a, b) => a.order.localeCompare(b.order, undefined, {
            numeric: true,
            sensitivity: 'base'
        }));
    }
    voltmx.print("#### frmHandleCharacteristic_getCaseTypeCategriesSuccessCallback Global.vars.handlingTypes: " + JSON.stringify(Global.vars.handlingTypes));
    if (Global.vars.cameBackFromOffenceSelect === true) {
        Global.vars.cameBackFromOffenceSelect = false;
        frmHandleCharacteristic_setCharacteristic(true);
    } else {
        frmHandleCharacteristic_setCharacteristic();
    }
}

function frmHandleCharacteristic_setCharacteristic(from_getPrevious) {
    var _from_getPrevious = from_getPrevious === undefined ? false : from_getPrevious;
    voltmx.print("#### frmHandleCharacteristic_setCharacteristic _from_getPrevious: " + JSON.stringify(_from_getPrevious));
    voltmx.print("#### frmHandleCharacteristic_setCharacteristic Global.vars.handleCharacteristicType: " + JSON.stringify(Global.vars.handleCharacteristicType));
    voltmx.print("#### frmHandleCharacteristic_setCharacteristic Global.vars.handlingTypes: " + JSON.stringify(Global.vars.handlingTypes));
    voltmx.print("#### frmHandleCharacteristic_setCharacteristic CaseType: " + CaseData.caseinfo.caseType);
    voltmx.print("#### frmHandleCharacteristic_setCharacteristic caseTypeDescription: " + CaseData.caseinfo.caseTypeDescription);
    voltmx.print("#### frmHandleCharacteristic_setCharacteristic caseTypeCategory: " + CaseData.caseinfo.caseTypeCategory);
    voltmx.print("#### frmHandleCharacteristic_setCharacteristic caseTypeCategoryDescription: " + CaseData.caseinfo.caseTypeCategoryDescription);
    for (var kk in Global.vars.handlingTypes) {
        if (CaseData.caseinfo.caseTypeCategoryDescription != null && CaseData.caseinfo.caseTypeCategoryDescription === Global.vars.handlingTypes[kk].description) {
            Global.vars.handlingTypes[kk].template = flcSegItemHandleSelect;
        } else {
            Global.vars.handlingTypes[kk].template = flcSegItemHandle;
        }
    }
    // == "CaseTypeCategory"
    if (Global.vars.handleCharacteristicType == "Handlingtype") {
        gHandleCharacteristicBreadCrumb = [];
        gHandleCharacteristicPreviousCategory = [];
        Global.vars.handleCharacteristicsBack = true;
        voltmx.print("### frmHandleCharacteristic_setCharacteristic l_kindOfHandling 0");
        frmHandleCharacteristic.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_kindOfHandling");
        frmHandleCharacteristic.segSearch.setData(Global.vars.handlingTypes);
        //Hier zie je OutComeTypeCategories en CaseTypeCategories - dus moeten taakuitkomsten en zaaktypen geleegd worden + de categorien zelf
        //Utility_resetToOriginalCaseType();
        //leeg taakuitkomsten en het definitief maken van de zaak
        voltmx.print("#### frmHandleCharacteristic_setCharacteristic CaseData.processinfo 1a: " + JSON.stringify(CaseData.processinfo));
        for (var i in CaseData.processinfo.tasks) {
            var v = CaseData.processinfo.tasks[i];
            if (v.taskType == Global.vars.taskType) {
                v.taskOutcome = null;
                v.taskOutcomeId = null;
                v.taskOutcomeDescription = null;
                v.taskCompletedOn = null;
                //v.taskClaimedBy = null;
                //v.taskClaimedByName = null;
                CaseData.caseinfo.indComplete = false;
                CaseData.caseinfo.timeComplete = null;
                //Utility_cleanStatus(CaseData.statuscode);
                CaseData.statuscode = null;
                CaseData.processinfo.activeTaskType = Global.vars.taskType;
            }
            CaseData.processinfo.lastTaskProcessed = {};
        }
        voltmx.print("#### frmHandleCharacteristic_setCharacteristic CaseData.processinfo 1b: " + JSON.stringify(CaseData.processinfo));
        if (Global.vars.handlingTypes.length == 1) {
            var focusedCharacteristic = Global.vars.handlingTypes;
            voltmx.print("#### frmHandleCharacteristic_setCharacteristic Handlingtype only 1 choice");
            if ((focusedCharacteristic[0].category == "CaseTypeCategory")) {
                if (_from_getPrevious === false) {
                    voltmx.print("#### frmHandleCharacteristic_setCharacteristic Handlingtype only 1 choice CaseTypeCategory");
                    if ((voltmx.string.len(focusedCharacteristic[0].name) > 15)) {
                        frmHandleCharacteristic.lblSubHeader.text = focusedCharacteristic[0].name.substring(0, 15) + "...";
                    } else {
                        frmHandleCharacteristic.lblSubHeader.text = focusedCharacteristic[0].name;
                    }
                    gHandleCharacteristicBreadCrumb.push(focusedCharacteristic[0].name);
                    gHandleCharacteristicPreviousCategory.push(focusedCharacteristic[0].category);
                    voltmx.print("#### frmHandleCharacteristic_setCharacteristic BreadCrumb :" + JSON.stringify(gHandleCharacteristicBreadCrumb));
                    Global.vars.gOffenceSearchModus = Global.vars.previousOffenceSearchModus; //"search";
                    //set caseType
                    CaseData.caseinfo.caseTypeCategory = focusedCharacteristic[0].code;
                    CaseData.caseinfo.caseTypeCategoryId = focusedCharacteristic[0].id;
                    Global.vars.caseTypeCategoryDescription = focusedCharacteristic[0].description;
                    CaseData.caseinfo.caseTypeCategoryDescription = focusedCharacteristic[0].description;
                    Global.vars.indHasOffenceTypes = Utility_stringToBoolean(focusedCharacteristic[0].indhasoffencetypes);
                    frmHandleCharacteristic_getCaseType(focusedCharacteristic[0].id);
                } else {
                    voltmx.print("#### frmHandleCharacteristic_setCharacteristic Handlingtype only 1 + frmHandleCharacteristic_getPrevious");
                    Global.vars.directOffenceSelect = false;
                    Global.vars.handleCharacteristicsBack = true;
                    frmHandleCharacteristic_getPrevious();
                }
            }
        }
        //} else if(Global.vars.handleCharacteristicType == "CaseTypeCategory" && Global.vars.claimedDoc.case.caseinfo.caseType.indexOf("TRACK") > -1){
    } else if (Global.vars.handleCharacteristicType == "CaseTypeCategory") {
        gHandleCharacteristicBreadCrumb = [];
        gHandleCharacteristicPreviousCategory = [];
        Global.vars.handleCharacteristicsBack = true;
        voltmx.print("### frmHandleCharacteristic_setCharacteristic l_kindOfHandling 2");
        if (CaseData.caseinfo.caseTypeCategory !== "dvv" && CaseData.caseinfo.caseTypeCategory !== "drv") {
            voltmx.print("### frmHandleCharacteristic_setCharacteristic l_kindOfHandling 2a");
            frmHandleCharacteristic.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_kindOfHandling");
        }
        for (var k in Global.vars.handlingTypes) {
            if (CaseData.caseinfo.caseTypeCategoryDescription != null && CaseData.caseinfo.caseTypeCategoryDescription === Global.vars.handlingTypes[k].description) {
                Global.vars.handlingTypes[k].template = flcSegItemHandleSelect;
            } else {
                Global.vars.handlingTypes[k].template = flcSegItemHandle;
            }
        }
        frmHandleCharacteristic.segSearch.setData(Global.vars.handlingTypes);
        //Hier zie je OutComeTypeCategories en CaseTypeCategories - dus moeten taakuitkomsten en zaaktypen geleegd worden + de categorien zelf
        //Utility_resetToOriginalCaseType(); // uit gezet ivm verboden, ik begrijp ook niet waar dit voor is??
        //leeg taakuitkomsten en het definitief maken van de zaak
        for (var j in CaseData.processinfo.tasks) {
            var w = CaseData.processinfo.tasks[j];
            if (w.taskType == Global.vars.taskType) {
                w.taskOutcome = null;
                w.taskOutcomeId = null;
                w.taskOutcomeDescription = null;
                w.taskCompletedOn = null;
                //w.taskClaimedBy = null;
                //w.taskClaimedByName = null;
                CaseData.caseinfo.indComplete = false;
                CaseData.caseinfo.timeComplete = null;
                //Utility_cleanStatus(CaseData.statuscode);
                CaseData.statuscode = null;
                CaseData.processinfo.activeTaskType = Global.vars.taskType;
            }
            CaseData.processinfo.lastTaskProcessed = {};
        }
        voltmx.print("#### frmHandleCharacteristic_setCharacteristic CaseData.processinfo 2b: " + JSON.stringify(CaseData.processinfo));
    } else if (Global.vars.handleCharacteristicType == "OutcomeTypeCategory") {
        voltmx.print("### frmHandleCharacteristic_setCharacteristic Global.vars.handleCharacteristicType: " + Global.vars.handleCharacteristicType);
        Global.vars.handleCharacteristicsBack = false;
        //hier zijn nog geen uitkomsten gekozen dus leegmaken
        Global.vars.chosenTaskOutcome = "";
        for (var k in CaseData.processinfo.tasks) {
            var x = CaseData.processinfo.tasks[k];
            if (x.taskType == Global.vars.taskType) {
                x.taskOutcome = null;
                x.taskOutcomeId = null;
                x.taskOutcomeDescription = null;
                x.taskCompletedOn = null;
                CaseData.processinfo.activeTaskType = Global.vars.taskType;
            }
            CaseData.processinfo.lastTaskProcessed = {};
        }
        frmHandleCharacteristic.segSearch.setData(Global.vars.outcomeTypes);
        voltmx.print("### frmHandleCharacteristic_setCharacteristic l_kindOfHandling 1");
        if (frmHandleCharacteristic.lblSubHeader.text == voltmx.i18n.getLocalizedString("l_kindOfHandling")) {
            //Hier zie je OutComeTypeCategories en CaseTypeCategories - dus moeten taakuitkomsten en zaaktypen geleegd worden + de categorien zelf
            Global.vars.handleCharacteristicsBack = true;
            Utility_resetToOriginalCaseType();
            frmHandleCharacteristic.segSearch.setData(Global.vars.handlingTypes);
        }
        voltmx.print("#### frmHandleCharacteristic_setCharacteristic CaseData.processinfo 1a: " + JSON.stringify(CaseData.processinfo));
    } else if (Global.vars.handleCharacteristicType == "CaseType") {
        voltmx.print("#### frmHandleCharacteristic_setCharacteristic CaseType Global.vars.caseTypes: " + JSON.stringify(Global.vars.caseTypes));
        Global.vars.handleCharacteristicsBack = false;
        for (var l in Global.vars.caseTypes) {
            voltmx.print("#### frmHandleCharacteristic_setCharacteristic CaseType Global.vars.caseTypes identification: " + Global.vars.caseTypes[l].identification);
            if (CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType === Global.vars.caseTypes[l].identification) {
                Global.vars.caseTypes[l].template = flcSegItemHandleSelect;
            } else {
                Global.vars.caseTypes[l].template = flcSegItemHandle;
            }
        }
        frmHandleCharacteristic.segSearch.setData(Global.vars.caseTypes);
        //hier zijn nog geen zaaktypen gekozen dus leegmaken
        //     if(Global.vars.originalCaseInfo != null){
        //       CaseData.caseinfo.caseType = Global.vars.originalCaseInfo.caseType;
        //       CaseData.caseinfo.caseTypeId = Global.vars.originalCaseInfo.caseTypeId;
        //       if (Global.vars.originalCaseInfo.caseTypeDescription !== undefined){
        //         CaseData.caseinfo.caseTypeDescription = Global.vars.originalCaseInfo.caseTypeDescription;
        //       }
        //     }
    }
}

function frmHandleCharacteristic_onClick_segment() {
    var skipBreadCrum = false;
    var focusedCharacteristic = frmHandleCharacteristic.segSearch.selectedItems;
    Global.vars.caseTypeDescription = "";
    voltmx.print("#### frmHandleCharacteristic_onClick_segment Onclick: " + focusedCharacteristic[0].category);
    if (focusedCharacteristic[0].category != "OutcomeType") {
        Global.vars.handleCharacteristicType = focusedCharacteristic[0].category;
    }
    // Choose segement
    var caseCategoryId = "";
    voltmx.print("#### frmHandleCharacteristic_onClick_segment focuseditem: " + JSON.stringify(focusedCharacteristic[0]));
    voltmx.print("#### frmHandleCharacteristic_onClick_segment CaseData.offence: " + JSON.stringify(CaseData.offence));
    if (focusedCharacteristic[0].category == "CaseTypeCategory") {
        if (CaseData.caseinfo.caseTypeCategory != focusedCharacteristic[0].code) {
            //CaseData.offence.optionUsage = null;
        } else {
            if (CaseData.offence != null && CaseData.offence.offenceCode != null) {
                // offence code present or alresady selected do nothing
            } else {
                CaseData.offence.optionUsage = null;
            }
        }
    } else {
        CaseData.offence.optionUsage = null;
    }
    voltmx.print("#### frmHandleCharacteristic_onClick_segment CaseData.offence.optionUsage: " + CaseData.offence.optionUsage);
    if ((focusedCharacteristic[0].category == "CaseTypeCategory")) {
        voltmx.print("#### frmHandleCharacteristic_onClick_segment CaseTypeCategory");
        skipBreadCrum = false;
        if ((voltmx.string.len(focusedCharacteristic[0].name) > 15)) {
            frmHandleCharacteristic.lblSubHeader.text = focusedCharacteristic[0].name.substring(0, 15) + "...";
        } else {
            frmHandleCharacteristic.lblSubHeader.text = focusedCharacteristic[0].name;
        }
        gHandleCharacteristicBreadCrumb.push(focusedCharacteristic[0].name);
        gHandleCharacteristicPreviousCategory.push(focusedCharacteristic[0].category);
        voltmx.print("#### frmHandleCharacteristic_onClick_segment BreadCrumb :" + JSON.stringify(gHandleCharacteristicBreadCrumb));
        Global.vars.gOffenceSearchModus = Global.vars.previousOffenceSearchModus; //"search";
        //set caseType
        //
        CaseData.caseinfo.caseTypeCategory = focusedCharacteristic[0].code;
        CaseData.caseinfo.caseTypeCategoryId = focusedCharacteristic[0].id;
        Global.vars.caseTypeCategoryDescription = focusedCharacteristic[0].description;
        CaseData.caseinfo.caseTypeCategoryDescription = focusedCharacteristic[0].description;
        caseCategoryId = focusedCharacteristic[0].id;
        Global.vars.indHasOffenceTypes = Utility_stringToBoolean(focusedCharacteristic[0].indhasoffencetypes);
        if (Utility_stringToBoolean(focusedCharacteristic[0].indhasoffencetypes) === true) {
            skipBreadCrum = true;
        }
        voltmx.print("#### frmHandleCharacteristic_onClick_segment CaseData.caseinfo.caseTypeCategory :" + CaseData.caseinfo.caseTypeCategory);
        frmHandleCharacteristic_getCaseType(caseCategoryId);
    } else if (focusedCharacteristic[0].category == "CaseType") {
        voltmx.print("#### frmHandleCharacteristic_onClick_segment CaseType");
        var previousTitle = frmHandleCharacteristic.lblSubHeader.text;
        skipBreadCrum = false;
        if ((voltmx.string.len(focusedCharacteristic[0].name) > 15)) {
            frmHandleCharacteristic.lblSubHeader.text = focusedCharacteristic[0].name.substring(0, 15) + "...";
        } else {
            frmHandleCharacteristic.lblSubHeader.text = focusedCharacteristic[0].name;
        }
        if (CaseData.caseinfo.caseType != null && (focusedCharacteristic[0].identification.startsWith("DVV_") || focusedCharacteristic[0].identification.startsWith("DRV_"))) {
            gHandleCharacteristicBreadCrumb.push(previousTitle);
        } else {
            gHandleCharacteristicBreadCrumb.push(focusedCharacteristic[0].name);
        }
        gHandleCharacteristicPreviousCategory.push(focusedCharacteristic[0].category);
        voltmx.print("#### frmHandleCharacteristic_onClick_segment BreadCrumb :" + JSON.stringify(gHandleCharacteristicBreadCrumb));
        //Vul CaseType
        if (CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType !== focusedCharacteristic[0].identification) {
            voltmx.print("#### frmHandleCharacteristic_onClick_segment caseType ongelijk focused casetype");
            if (CaseData.questions !== undefined && CaseData.questions != null && CaseData.questions.length > 0) {
                voltmx.print("#### frmHandleCharacteristic_onClick_segment");
                CaseData.questions = [];
                Global.vars.questionTypes = [];
                Global.vars.questionTypesUsage = "";
                Global.vars.selectedQuestionTypes = [];
                Global.vars.questionTypeChooseEnabled = false;
                Global.vars.QuestionsSet = false;
            }
        }
        CaseData.caseinfo.caseType = focusedCharacteristic[0].identification;
        CaseData.caseinfo.caseTypeId = focusedCharacteristic[0].id;
        Global.vars.caseTypeDescription = focusedCharacteristic[0].description;
        CaseData.caseinfo.caseTypeDescription = focusedCharacteristic[0].description;
        CaseData.caseinfo.maxDuration = null;
        if (Global.vars.indHasOffenceTypes === false) {
            if (CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("DVV_") || CaseData.caseinfo.caseType.startsWith("DRV_"))) {
                //Scherm met stations en duur
                CaseData.caseinfo.maxDuration = focusedCharacteristic[0].max_duration;
                frmProhibitionDetails.show();
            } else if (CaseData.caseinfo.caseType.startsWith("CTE_") || CaseData.caseinfo.caseType.startsWith("MLD_")) {
                frmHandleCharacteristic_goToQuestions();
            } else if ((Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV") && Global.vars.showProhibitions === true) {
                //do something
                frmResume.show();
            }
        } else {
            frmHandleCharacteristic_getCteOffenctype(focusedCharacteristic[0].id);
        }
    } else if (focusedCharacteristic[0].category == "OutcomeTypeCategory") {
        voltmx.print("#### frmHandleCharacteristic_onClick_segment OutcomeTypeCategory");
        skipBreadCrum = false;
        if ((voltmx.string.len(focusedCharacteristic[0].name) > 15)) {
            frmHandleCharacteristic.lblSubHeader.text = focusedCharacteristic[0].name.substring(0, 15) + "...";
        } else {
            frmHandleCharacteristic.lblSubHeader.text = focusedCharacteristic[0].name;
        }
        gHandleCharacteristicBreadCrumb.push(focusedCharacteristic[0].name);
        gHandleCharacteristicPreviousCategory.push(focusedCharacteristic[0].category);
        voltmx.print("#### frmHandleCharacteristic_onClick_segment BreadCrumb :" + JSON.stringify(gHandleCharacteristicBreadCrumb));
        //now find OutComeTypes
        var ouyidentification = focusedCharacteristic[0].ouy_identification;
        var tteid = focusedCharacteristic[0].tte_id;
        frmHandleCharacteristic_getOutcomeTypes(ouyidentification, tteid);
    } else if (focusedCharacteristic[0].category == "OutcomeType") {
        voltmx.print("#### frmHandleCharacteristic_onClick_segment OutcomeType");
        voltmx.print("#### Set outcometype to task");
        var outcomeType = focusedCharacteristic[0].identification;
        var outcomeTypeId = focusedCharacteristic[0].id;
        var outcomeTypeDesc = focusedCharacteristic[0].description;
        var outcomeSteidsucceeding = focusedCharacteristic[0].ste_id_succeeding;
        frmHandleCharacteristic_setOutcomeTypeToTask(outcomeType, outcomeTypeId, outcomeTypeDesc, outcomeSteidsucceeding);
        skipBreadCrum = true;
    }
    // set breadcrumb
    if (skipBreadCrum === false) {
        voltmx.print("#### frmHandleCharacteristic_onClick_segment BreadCrumb 2:" + JSON.stringify(gHandleCharacteristicBreadCrumb));
    }
    voltmx.print("#### frmHandleCharacteristic_onClick_segment Global.vars.handleCharacteristicType: " + Global.vars.handleCharacteristicType);
    voltmx.print("#### frmHandleCharacteristic_onClick_segment CaseData.processinfo.activeTaskType: " + CaseData.processinfo.activeTaskType);
    voltmx.print("#### frmHandleCharacteristic_onClick_segment CaseData.caseinfo.caseType: " + CaseData.caseinfo.caseType);
    //voltmx.print("#### frmHandleCharacteristic_onClick_segment Global.vars.claimedDoc.case.caseinfo.caseType (track): " + Global.vars.claimedDoc.case.caseinfo.caseType);
    voltmx.print("#### frmHandleCharacteristic_onClick_segment Global.vars.gOffenceSearchModus: " + Global.vars.gOffenceSearchModus);
}

function frmHandleCharacteristic_getCaseType(caseCategoryId) {
    voltmx.print("#### frmHandleCharacteristic_getCaseType caseCategoryId: " + caseCategoryId);
    // function frmHandleCharacteristic_getCaseType_getTaskTypeSuccessCallback(result){
    //   voltmx.print("#### frmHandleCharacteristic_getTaskTypeSuccessCallback: " + JSON.stringify(result));
    //   if(result.length > 0){
    //     Global.vars.taskTypeProcess = result[0].process;
    var lCaseTypeClause = "select * from mle_v_case_type_m where ctc_id = '" + caseCategoryId + "'";
    lCaseTypeClause = Utility_addTimelineToWhereClauseObjectSync(lCaseTypeClause, CaseData.time.dateComponents);
    lCaseTypeClause = Utility_addLanguageToWhereClauseObjectSync(lCaseTypeClause);
    voltmx.print("### frmHandleCharacteristic_getCaseType ocwcs: " + lCaseTypeClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lCaseTypeClause, frmHandleCharacteristic_getCaseTypeSuccessCallback, frmHandleCharacteristic_getCaseTypeErrorCallback);
    //   }
    // }
    // Utility_getTaskType(CaseData.processinfo.activeTaskType, frmHandleCharacteristic_getCaseType_getTaskTypeSuccessCallback);
}

function frmHandleCharacteristic_getCaseTypeErrorCallback(error) {
    voltmx.print("#### frmHandleCharacteristic_getCaseTypeErrorCallback: " + JSON.stringify(error));
}

function frmHandleCharacteristic_getCaseTypeSuccessCallback(result) {
    voltmx.print("#### frmHandleCharacteristic_getCaseTypeSuccessCallback: " + JSON.stringify(result));
    voltmx.print("#### frmHandleCharacteristic_getCaseTypeSuccessCallback Global.vars.indHasOffenceTypes: " + Global.vars.indHasOffenceTypes);
    if ((result.length == 1 && result[0].identification !== undefined) && (CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType !== result[0].identification)) {
        voltmx.print("#### frmHandleCharacteristic_getCaseTypeSuccessCallback caseType ongelijk result casetype");
        if (CaseData.questions !== undefined && CaseData.questions != null && CaseData.questions.length > 0) {
            voltmx.print("#### frmHandleCharacteristic_getCaseTypeSuccessCallback clear questions");
            CaseData.questions = [];
            Global.vars.questionTypes = [];
            Global.vars.questionTypesUsage = "";
            Global.vars.selectedQuestionTypes = [];
            Global.vars.questionTypeChooseEnabled = false;
            Global.vars.QuestionsSet = false;
        }
    }
    if (result.length == 1 && Global.vars.indHasOffenceTypes === true) {
        //Vul CaseType
        CaseData.caseinfo.caseType = result[0].identification;
        CaseData.caseinfo.caseTypeId = result[0].id;
        CaseData.caseinfo.caseTypeDescription = result[0].description;
        frmHandleCharacteristic_getCteOffenctype(result[0].id);
    } else if (result.length == 1 && (result[0].identification.startsWith("CTE_") || result[0].identification.startsWith("MLD_"))) {
        //Vul CaseType
        CaseData.caseinfo.caseType = result[0].identification;
        CaseData.caseinfo.caseTypeId = result[0].id;
        CaseData.caseinfo.caseTypeDescription = result[0].description;
        Global.vars.caseTypeDescription = result[0].description;
        Utility_preserveOffenceKeepStatement();
        frmHandleCharacteristic_goToQuestions();
    } else if (result.length > 1 && Global.vars.indHasOffenceTypes === false) {
        //de waarden moeten op het scherm komen om te kiezen
        Global.vars.caseTypes = [];
        for (var i in result) {
            var v = result[i];
            v.category = "CaseType";
            v.name = v.description;
            v.imgRight = "arrowrightmini.png";
            if (CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType === v.identification) {
                v.template = flcSegItemHandleSelect;
            } else {
                v.template = flcSegItemHandle;
            }
            Global.vars.caseTypes.push(v);
        }
        //sort
        if (Global.vars.caseTypes.length > 0) {
            Global.vars.caseTypes.sort((a, b) => a.description.localeCompare(b.description, undefined, {
                numeric: true,
                sensitivity: 'base'
            }));
        }
        voltmx.print("#### frmHandleCharacteristic_getCaseTypeSuccessCallback set Casetypes Global.vars.caseTypes: " + JSON.stringify(Global.vars.caseTypes));
        //handling types vullen met casetypes???
        //Global.vars.handlingTypes = Global.vars.caseTypes;
        Global.vars.handleCharacteristicType = "CaseType";
        frmHandleCharacteristic_setCharacteristic();
    } else if (result.length > 1 && Global.vars.indHasOffenceTypes === true) {
        var caseTypeIds = "";
        for (var j in result) {
            var w = result[j];
            if (caseTypeIds.indexOf(w.id) == -1) {
                caseTypeIds = caseTypeIds + "'" + w.id + "'" + ",";
            }
        }
        caseTypeIds = caseTypeIds.replace(/,\s*$/, "");
        Global.vars.caseTypeIds = caseTypeIds;
        frmHandleCharacteristic_getCteOffenctype(caseTypeIds);
    } else {
        alert(voltmx.i18n.getLocalizedString("l_noCaseTypesFound"));
        Global.vars.handleCharacteristicType = "Handlingtype";
        frmHandleCharacteristic_setCharacteristic();
    }
}

function frmHandleCharacteristic_getCteOffenctype(caseTypeIds) {
    voltmx.print("#### frmHandleCharacteristic_getCteOffenctype");
    var lCteOffenceTypeClause = "select * from mle_v_cte_offence_type_m where cte_id in (" + caseTypeIds + ")";
    voltmx.print("#### frmHandleCharacteristic_getCteOffenctype lCteOffenceTypeClause: " + lCteOffenceTypeClause);
    lCteOffenceTypeClause = Utility_addTimelineToWhereClauseObjectSync(lCteOffenceTypeClause, CaseData.time.dateComponents);
    voltmx.print("### frmHandleCharacteristic_getCteOffenctype ocwcs: " + lCteOffenceTypeClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lCteOffenceTypeClause, frmHandleCharacteristic_getCteOffenctypeSuccessCallback, frmHandleCharacteristic_getCteOffenctypeErrorCallback);
}

function frmHandleCharacteristic_getCteOffenctypeErrorCallback(error) {
    voltmx.print("#### frmHandleCharacteristic_getCtcOffenceTypeErrorCallback: " + JSON.stringify(error));
}

function frmHandleCharacteristic_getCteOffenctypeSuccessCallback(result) {
    voltmx.print("#### frmHandleCharacteristic_getCtcOffenceTypeSuccessCallback: " + JSON.stringify(result));
    var otecodes = "";
    for (var i in result) {
        var v = result[i];
        if (otecodes.indexOf("'" + v.ote_code + "'") == -1) {
            otecodes = otecodes + "'" + v.ote_code + "'" + ",";
        }
    }
    otecodes = otecodes.replace(/,\s*$/, "");
    Global.vars.offenceTypeCodes = otecodes;
    frmHandleCharacteristic_getOffenceTypes(otecodes);
}

function frmHandleCharacteristic_getOffenceTypes(otecodes) {
    voltmx.print("#### frmHandleCharacteristic_getOffenceTypes");
    var lOffenceTypeClause = "select * from mle_v_offence_type_m where code in (" + otecodes + ")";
    voltmx.print("#### frmHandleCharacteristic_getOffenceTypes lOffenceTypeClause: " + lOffenceTypeClause);
    lOffenceTypeClause = Utility_addTimelineToWhereClauseObjectSync(lOffenceTypeClause, CaseData.time.dateComponents);
    voltmx.print("### frmHandleCharacteristic_getOffenceTypes ocwcs: " + lOffenceTypeClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lOffenceTypeClause, frmHandleCharacteristic_getOffenceTypesSuccessCallback, frmHandleCharacteristic_getOffenceTypesErrorCallback);
}

function frmHandleCharacteristic_getOffenceTypesErrorCallback(error) {
    voltmx.print("#### frmHandleCharacteristic_getCtcOffenceTypeErrorCallback: " + JSON.stringify(error));
}

function frmHandleCharacteristic_getOffenceTypesSuccessCallback(result) {
    voltmx.print("#### frmHandleCharacteristic_getOffenceTypesSuccessCallback: " + JSON.stringify(result));
    var toplevels = "";
    for (var i in result) {
        var v = result[i];
        if (toplevels.indexOf("'" + v.top_level_theme + "'") == -1) {
            toplevels = toplevels + "'" + v.top_level_theme + "'" + ",";
        }
    }
    toplevels = toplevels.replace(/,\s*$/, "");
    Global.vars.codeMasterThemes = toplevels;
    voltmx.print("### frmHandleCharacteristic_getOffenceTypesSuccessCallback Global.vars.indHasOffenceTypes: " + Global.vars.indHasOffenceTypes);
    voltmx.print("### frmHandleCharacteristic_getOffenceTypesSuccessCallback CaseData.caseinfo.caseType: " + CaseData.caseinfo.caseType);
    if (CaseData.caseinfo.caseType == "TICKET_F") {
        Utility_resetPerson();
        if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType !== "") {
            voltmx.print("### frmHandleCharacteristic_getOffenceTypesSuccessCallback vehicleType: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType);
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType = vehicleIdentType.vehicle;
        }
    }
    if (Global.vars.indHasOffenceTypes === true) {
        if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType !== "" && CaseData.caseinfo.caseType === "TICKET_F") {
            voltmx.print("### frmHandleCharacteristic_getOffenceTypesSuccessCallback vehicleType: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType);
            frmHandleCharacteristic_getVehicleTypeDescription();
        } else {
            Global.vars.previousForm = "frmHandleCharacteristic";
            Utility_restorePreservedOffence();
            if ((CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType !== "") || (CaseData.person[Global.vars.gCasePersonsIndex] !== undefined) || (frmHandleCharacteristic.lblSubHeader.text === Global.vars.governance.description && Utility_stringToBoolean(Global.vars.governance.governance) === true) && Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") {
                Global.vars.directOffenceSelect = true;
                voltmx.print("### frmHandleCharacteristic_getOffenceTypesSuccessCallback Global.vars.directOffenceSelect: " + Global.vars.directOffenceSelect);
                voltmx.print("### frmHandleCharacteristic_getOffenceTypesSuccessCallback frmOffenceSelect");
                frmOffenceSelect.show();
            } else {
                voltmx.print("### frmHandleCharacteristic_getOffenceTypesSuccessCallback Global.vars.previousForm: " + Global.vars.previousForm);
                voltmx.print("### frmHandleCharacteristic_getOffenceTypesSuccessCallback frmSelectVehicleType");
                frmSelectVehicleType.show();
            }
        }
    }
}

function frmHandleCharacteristic_getVehicleTypeDescription() {
    var wcs = "select * from mle_v_vehicle_type_m where code = '" + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType + "'";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    //     var options = {};
    //     options["whereConditionAsAString"] = wcs;
    voltmx.print("### frmHandleCharacteristic_getVehicleTypeDescription wcs: " + JSON.stringify(wcs));
    //     Global.vars.ObjServiceVehicleObject.appTypeObj.get(options, frmHandleCharacteristic_getVehicleTypesSuccesCallback, frmHandleCharacteristic_getVehicleTypesErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmHandleCharacteristic_getVehicleTypesSuccesCallback, frmHandleCharacteristic_getVehicleTypesErrorCallback);
}

function frmHandleCharacteristic_getVehicleTypesErrorCallback(error) {
    voltmx.print("### frmHandleCharacteristic_getVehicleTypesErrorCallback: " + JSON.stringify(error));
    Global.vars.previousForm = "frmHandleCharacteristic";
    frmSelectVehicleType.show();
}

function frmHandleCharacteristic_getVehicleTypesSuccesCallback(result) {
    voltmx.print("### frmHandleCharacteristic_getVehicleTypesSuccesCallback: " + JSON.stringify(result));
    voltmx.print("### frmHandleCharacteristic_getVehicleTypesSuccesCallback Global.vars.directOffenceSelect 1: " + Global.vars.directOffenceSelect);
    Global.vars.previousForm = "frmHandleCharacteristic";
    if (result.length > 0) {
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc = result[0].description;
        Global.vars.directOffenceSelect = true;
        frmOffenceSelect.show();
    } else {
        Global.vars.directOffenceSelect = false;
        frmSelectVehicleType.show();
    }
    voltmx.print("### frmHandleCharacteristic_getVehicleTypesSuccesCallback Global.vars.directOffenceSelect 1: " + Global.vars.directOffenceSelect);
}

function frmHandleCharacteristic_getPrevious() {
    voltmx.print("### frmHandleCharacteristic_getPrevious Global.vars.previousForm: " + Global.vars.previousForm);
    voltmx.print("### frmHandleCharacteristic_getPrevious Global.vars.appMode: " + Global.vars.appMode);
    gHandleCharacteristicBreadCrumb.pop();
    voltmx.print("#### frmHandleCharacteristic_getPrevious previous headertext: " + gHandleCharacteristicBreadCrumb);
    gHandleCharacteristicPreviousCategory.pop();
    voltmx.print("#### frmHandleCharacteristic_getPrevious previous category: " + gHandleCharacteristicPreviousCategory);
    var lHeaderText = gHandleCharacteristicBreadCrumb[gHandleCharacteristicBreadCrumb.length - 1] + "";
    voltmx.print("#### frmHandleCharacteristic_getPrevious headertext: " + lHeaderText);
    voltmx.print("#### frmHandleCharacteristic_getPrevious Global.vars.handleCharacteristicsBack: " + Global.vars.handleCharacteristicsBack);
    if (Global.vars.handleCharacteristicsBack === true) {
        if (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_trackDown")) {
            Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
            frmTrackDown.show();
        } else if (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") {
            Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
            frmRegister.show();
        } else if (Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_registerconcept")) {
            Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
            frmRegisterConcept.show();
        } else {
            Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
            frmHandle.show();
        }
        Global.vars.handleCharacteristicsBack = false;
    } else {
        if (lHeaderText === undefined || lHeaderText === "undefined" || lHeaderText === "nil" || lHeaderText.length === 0) {
            Global.vars.handleCharacteristicType = "Handlingtype";
            Global.vars.handleCharacteristicsBack = true;
        } else {
            Global.vars.handleCharacteristicType = gHandleCharacteristicPreviousCategory[gHandleCharacteristicPreviousCategory.length - 1] + "";
            Global.vars.handleCharacteristicsBack = false;
        }
        if ((voltmx.string.len(lHeaderText) > 18)) {
            frmHandleCharacteristic.lblSubHeader.text = lHeaderText.substring(0, 18) + "...";
        } else if ((voltmx.string.len(lHeaderText) < 19)) {
            frmHandleCharacteristic.lblSubHeader.text = lHeaderText;
        }
        frmHandleCharacteristic_setCharacteristic(true);
    }
}

function frmHandleCharacteristic_getOutcomeTypes(ouyidentification, tteid) {
    Global.vars.outcomeTypes = [];
    voltmx.print("#### frmHandleCharacteristic_getOutcomeTypes");
    var lOutcomeTypeClause = "select * from mle_v_outcome_type_m where ouy_identification = '" + ouyidentification + "' and tte_id = '" + tteid + "'";
    voltmx.print("#### frmHandleCharacteristic_getOutcomeTypes lOutcomeTypeClause: " + lOutcomeTypeClause);
    lOutcomeTypeClause = Utility_addTimelineToWhereClauseObjectSync(lOutcomeTypeClause); //, CaseData.time.dateComponents);
    lOutcomeTypeClause = Utility_addLanguageToWhereClauseObjectSync(lOutcomeTypeClause);
    voltmx.print("### frmHandleCharacteristic_getOutcomeTypes ocwcs: " + lOutcomeTypeClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lOutcomeTypeClause, frmHandleCharacteristic_getOutcomeTypesSuccessCallback, frmHandleCharacteristic_getOutcomeTypesErrorCallback);
}

function frmHandleCharacteristic_getOutcomeTypesErrorCallback(error) {
    voltmx.print("#### frmHandleCharacteristic_getOutcomeTypesErrorCallback: " + JSON.stringify(error));
}

function frmHandleCharacteristic_getOutcomeTypesSuccessCallback(result) {
    voltmx.print("#### frmHandleCharacteristic_getOutcomeTypesSuccessCallback: " + JSON.stringify(result));
    if (result.length == 1) {
        var outcomeType = result[0].identification;
        var outcomeTypeId = result[0].id;
        var outcomeTypeDesc = result[0].description;
        var outcomeSteidsucceeding = null;
        if (result[0].ste_id_succeeding !== undefined) {
            outcomeSteidsucceeding = result[0].ste_id_succeeding;
        }
        Global.vars.handleCharacteristicType = "OutcomeTypeCategory";
        gHandleCharacteristicBreadCrumb.pop();
        voltmx.print("#### frmHandleCharacteristic_getOutcomeTypesSuccessCallback BreadCrumb  after pop:" + JSON.stringify(gHandleCharacteristicBreadCrumb));
        voltmx.print("### frmHandleCharacteristic_getOutcomeTypesSuccessCallback l_kindOfHandling 3");
        frmHandleCharacteristic.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_kindOfHandling");
        frmHandleCharacteristic_setOutcomeTypeToTask(outcomeType, outcomeTypeId, outcomeTypeDesc, outcomeSteidsucceeding);
    } else {
        for (var i in result) {
            var v = result[i];
            v.category = "OutcomeType";
            v.name = v.description;
            v.imgRight = "arrowrightmini.png";
            v.template = flcSegItemHandle;
            Global.vars.outcomeTypes.push(v);
        }
        Global.vars.outcomeTypes.sort(function(a, b) {
            if (a.name < b.name) return -1;
            if (a.name > b.name) return 1;
            return 0;
        });
        frmHandleCharacteristic_setCharacteristic();
    }
}

function frmHandleCharacteristic_setOutcomeTypeToTask(outcomeType, outcomeTypeId, outcomeTypeDesc, outcomeSteidsucceeding) {
    voltmx.print("#### frmHandleCharacteristic_setOutcomeTypeToTask outcomeType: " + JSON.stringify(outcomeType));
    voltmx.print("#### frmHandleCharacteristic_setOutcomeTypeToTask outcomeTypeDesc: " + JSON.stringify(outcomeTypeDesc));
    voltmx.print("#### frmHandleCharacteristic_setOutcomeTypeToTask outcomeSteidsucceeding: " + JSON.stringify(outcomeSteidsucceeding));
    Global.vars.processinfo_lastTaskProcessed = {};
    for (var i in CaseData.processinfo.tasks) {
        var v = CaseData.processinfo.tasks[i];
        if (v.taskType == CaseData.processinfo.activeTaskType && (v.taskCompletedOn === "" || v.taskCompletedOn === null)) {
            v.taskOutcome = outcomeType;
            v.taskOutcomeId = outcomeTypeId;
            v.taskOutcomeDescription = outcomeTypeDesc;
            v.taskType = Global.vars.taskType;
            v.taskTypeId = Global.vars.taskTypeId;
            v.taskCompletedOn = Utility_getUTCJavascriptDate(null);
            v.taskClaimedBy = Global.vars.gUsername;
            v.taskClaimedByName = CaseData.caseinfo.officerName;
            //CaseData.processinfo.activeTaskType = "";
            voltmx.print("### frmHandleCharacteristic_setOutcomeTypeToTask Global.vars.claimedDoc.case.caseinfo" + JSON.stringify(Global.vars.claimedDoc.case.caseinfo));
            if (Global.vars.appMode === voltmx.i18n.getLocalizedString("l_trackDown")) {
                Utility_resetPerson();
            }
            Utility_resetToOriginalCaseType();
            if (outcomeSteidsucceeding != null && outcomeSteidsucceeding !== undefined) {
                //CaseData.statuscode = outcomeSteidsucceeding;
                //Utility_setStatus(outcomeSteidsucceeding);
            }
            Global.vars.chosenTaskOutcome = outcomeTypeDesc;
            Global.vars.processinfo_lastTaskProcessed = v;
            Global.vars.serviceTaskValues = {
                taskOutcome: v.taskOutcome,
                taskOutcomeDescription: v.taskOutcomeDescription,
                taskType: v.taskType
            };
            voltmx.print("### frmHandleCharacteristic_setOutcomeTypeToTask Global.vars.serviceTaskValues: " + JSON.stringify(Global.vars.serviceTaskValues));
        }
    }
    voltmx.print("#### frmHandleCharacteristic_setOutcomeTypeToTask Global.vars.chosenTaskOutcome: " + Global.vars.chosenTaskOutcome);
    voltmx.print("#### frmHandleCharacteristic_setOutcomeTypeToTask CaseData: " + JSON.stringify(CaseData));
    //setDevice GPS
    Utility_setDeviceLocationToCase(frmHandleCharacteristic_setDeviceLocationToCaseCallback);
}

function frmHandleCharacteristic_setDeviceLocationToCaseCallback() {
    voltmx.print("### Global.vars.appMode: " + Global.vars.appMode);
    voltmx.print("### frmHandleCharacteristic_setDeviceLocationToCaseCallback");
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    if (CaseData.caseinfo.caseType == "CONTROL_K" || CaseData.caseinfo.caseType == "CONTROL_S") {
        frmClamp.show();
    } else {
        frmResume.show();
    }
}

function frmHandleCharacteristic_showHandleCasePopup() {
    //setGaussianBlur(frmHandleCharacteristic.flcMainPage, frmHandleCharacteristic.imgBlured);
    frmHandleCharacteristic.flcMainPage.setEnabled(false);
    var choices = [{
        lblItem: voltmx.i18n.getLocalizedString("l_save_data"),
        caseData: CaseData
    }, {
        lblItem: voltmx.i18n.getLocalizedString("l_saveAndExit"),
        caseData: CaseData
    }];
    frmHandleCharacteristic.segCaseHandle.setData(choices);
    frmHandleCharacteristic.flcHandleCasePopup.bottom = "0%";
    //move popup options
    var scanOptionsAnimation = voltmx.ui.createAnimation({
        "100": {
            "bottom": 0 + '%',
            "stepConfig": {
                "timingFunction": voltmx.anim.EASE_IN_OUT
            }
        }
    });
    var scanOptionsSetting = {
        "delay": 0,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.2
    };
    frmHandleCharacteristic.flcCases.animate(scanOptionsAnimation, scanOptionsSetting);
}

function frmHandleCharacteristic_cancelHandleCasePopup() {
    frmHandleCharacteristic.flcMainPage.setEnabled(true);
    //move popup options
    var scanOptionsAnimation = voltmx.ui.createAnimation({
        "100": {
            "bottom": -39 + '%',
            "stepConfig": {
                "timingFunction": voltmx.anim.EASE_IN_OUT
            }
        }
    });
    var scanOptionsSetting = {
        "delay": 0,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.4
    };
    frmHandleCharacteristic.flcCases.animate(scanOptionsAnimation, scanOptionsSetting);
    frmHandleCharacteristic.flcHandleCasePopup.bottom = "-100%";
}

function frmHandleCharacteristic_onclickSegCaseHandle() {
    voltmx.print("### frmHandleCharacteristic_onclickSegCaseTypes selecteditem: " + JSON.stringify(frmHandleCharacteristic.segCaseHandle.selectedItems));
    var selectedCase = frmHandleCharacteristic.segCaseHandle.selectedItems[0];
    if (selectedCase != null && selectedCase !== "") {
        var name = null;
        if (selectedCase.lblItem == voltmx.i18n.getLocalizedString("l_save_data")) {
            voltmx.print("### frmHandleCharacteristic_onclickSegCaseHandle l_save_data clicked");

            function updateCaseData(result) {
                voltmx.print("#### frmSelectVehicleType_onclickSegCaseTypes updateCaseData result: " + JSON.stringify(result));
                if (result !== undefined && result.response[0].externalCaseId !== undefined && result.response[0].externalCaseId != null && result.response[0].externalCaseId !== "" && result.response[0].caseId !== undefined && result.response[0].caseId != null && result.response[0].caseId !== "") {
                    var externalCaseID = result.response[0].externalCaseId;
                    var couchID = result.response[0].caseId;
                    CaseData.caseinfo.externalId = externalCaseID;
                    CaseData.caseinfo.id = couchID;
                } else {
                    voltmx.print("#### frmSelectVehicleType_onclickSegCaseTypes updateCaseData case is saved offline on device");
                }
            }
            Utility_saveUploadCaseData(CaseData, updateCaseData, "frmHandleCharacteristic");
            frmHandleCharacteristic_cancelHandleCasePopup();
        } else if (selectedCase.lblItem == voltmx.i18n.getLocalizedString("l_saveAndExit")) {
            voltmx.print("### frmHandleCharacteristic_onclickSegCaseHandle l_save_data and exit clicked");
            Utility_saveUploadCaseData(CaseData, frmHandleCharacteristic_saveCaseCallback, "frmHandleCharacteristic");
        }
    }
}

function frmHandleCharacteristic_saveCaseCallback() {
    frmHandleCharacteristic_cancelHandleCasePopup();
    Global.vars.directOffenceSelect = false;
    if (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_trackDown")) {
        frmTrackDown_resetApp(false);
        voltmx.application.dismissLoadingScreen();
        frmTrackDown.show();
    } else if (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") {
        frmRegister_resetApp();
        voltmx.application.dismissLoadingScreen();
        frmRegister.show();
    } else if (Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_registerconcept")) {
        frmRegisterConcept_resetApp();
        voltmx.application.dismissLoadingScreen();
        frmRegisterConcept.show();
    }
}

function frmHandleCharacteristic_goToQuestions() {
    voltmx.print("### frmHandleCharacteristic_goToQuestions");
    Global.vars.previousForm = "frmHandleCharacteristic";
    frmQuestionTypes_getCaseTypeOptions();
    //frmQuestionTypes.show();
}