function frmPreferences_init() {
    voltmx.print("### frmPreferences init");
    //Utility_registerForIdleTimeout();
    frmPreferences.onDeviceBack = Global_onDeviceBack;
    // set swipe gesture handler
    var swipeSettings = {
        fingers: 1,
        swipedistance: 75,
        swipevelocity: 75
    };
    var swipeGesture = frmPreferences.setGestureRecognizer(2, swipeSettings, frmPreferences_handleGesture);
}

function frmPreferences_handleGesture(myWidget, gestureInfo) {
    voltmx.print("#### frmPreferences_handleGesture: " + gestureInfo.swipeDirection);
    if (gestureInfo.swipeDirection == 2) {
        voltmx.print("### swipe direction 2");
        //back_to_Settings_Menu();
    }
}

function frmPreferences_preshow() {
    Analytics_logScreenView("preferences");
    voltmx.print("### frmPreferences preshow");
    Global.vars.crntTheme = voltmx.theme.getCurrentTheme();
    voltmx.print("### frmPreferences preshow current theme is:" + Global.vars.crntTheme + " And the type is " + typeof(Global.vars.crntTheme));
    try {
        frmPreferences.lbxTheme.selectedKey = Global.vars.crntTheme;
    } catch (err) {
        voltmx.print("### frmPreferences_preshow lbxTheme selectedkey not set: " + JSON.stringify(err));
    }
    try {
        frmPreferences.lbxTravelMode.selectedKey = Global.vars.travelMode;
    } catch (err) {
        voltmx.print("### frmPreferences_preshow lbxTheme selectedkey not set: " + JSON.stringify(err));
    }
    var WatchGPSTimeSeconds = Number(Global.vars.watchMinimumTime) / 1000;
    frmPreferences.txtWatchGPSTimeout.text = WatchGPSTimeSeconds.toString();
}

function frmPreferences_postshow() {
    voltmx.print("### frmPreferences postshow");
}

function frmPreferences_SetTheme() {
    voltmx.print("#### frmPreferences_SetTheme choice: " + frmPreferences.lbxTheme.selectedKey);
    Global.vars.selectedTheme = frmPreferences.lbxTheme.selectedKey;
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "android")) {
        if (frmPreferences.lbxTheme.selectedKey == "REDORA") {
            //Redora
            voltmx.application.setApplicationProperties({
                statusBarColor: "C70027"
            }); //Red
        } else if (frmPreferences.lbxTheme.selectedKey == "Egis") {
            //Egis
            voltmx.application.setApplicationProperties({
                statusBarColor: "9ec536"
            }); //Green 
        } else if (frmPreferences.lbxTheme.selectedKey == "Arvoo") {
            //Arvoo
            voltmx.application.setApplicationProperties({
                statusBarColor: "f57f22"
            }); //Orange
        }
    }
    voltmx.theme.setCurrentTheme(Global.vars.selectedTheme, frmStart_setCurrentThemeSucces, frmStart_setCurrentThemeError);
}

function frmPreferences_setCurrentThemeSucces(result) {
    voltmx.print("### frmPreferences_setCurrentThemeSucces: " + JSON.stringify(result));
    frmPreferences.forceLayout();
}

function frmPreferences_setCurrentThemeError(error) {
    voltmx.print("### frmPreferences_setCurrentThemeSucces: " + JSON.stringify(error));
}

function frmPreferences_SetTravelMode() {
    voltmx.print("#### frmPreferences_SetTravelMode choice: " + frmPreferences.lbxTravelMode.selectedKey);
    Global.vars.travelMode = frmPreferences.lbxTravelMode.selectedKey;
    voltmx.store.setItem("travelMode", Global.vars.travelMode);
}

function frmPreferences_SetWatchGPSTimeout() {
    var WatchGPSTimeMilliSeconds = Number(frmPreferences.txtWatchGPSTimeout.text) * 1000;
    Global.vars.watchMinimumTime = WatchGPSTimeMilliSeconds;
}