var MyMRZAlert = null;
var flcDocumentCountryIsVisible = true;

function frmPerson_contentOffset() {
    frmPerson.contentSize = {
        height: "100%",
        width: "100%"
    };
    frmPerson.contentOffset = {
        "x": "0px",
        "y": "0px"
    };
}

function frmPerson_flcFooterScan_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcFooterScan_setVisibility");

    function flcFooterScan_setVisibility() {
        voltmx.print("### frmPerson_flcFooterScan_setVisibility flcFooterScan_setVisibility: " + boolean);
        frmPerson.flcFooterScan.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcFooterScan_setVisibility, []);
}

function frmPerson_flcScanInfo_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcScanInfo_setVisibility");

    function flcScanInfo_setVisibility() {
        voltmx.print("### frmPerson_flcScanInfo_setVisibility flcScanInfo_setVisibility: " + boolean);
        frmPerson.flcScanInfo.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcScanInfo_setVisibility, []);
}

function frmPerson_flcFooterMain_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcFooterMain_setVisibility");

    function flcFooterMain_setVisibility() {
        voltmx.print("### frmPerson_flcFooterMain_setVisibility flcFooterMain_setVisibility: " + boolean);
        frmPerson.flcFooterMain.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcFooterMain_setVisibility, []);
}

function frmPerson_flcToggleInput_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcToggleInput_setVisibility");

    function flcToggleInput_setVisibility() {
        voltmx.print("### frmPerson_flcToggleInput_setVisibility flcToggleInput_setVisibility: " + boolean);
        frmPerson.flcToggleInput.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcToggleInput_setVisibility, []);
}

function frmPerson_flcDocumentCountry_setVisibility(boolean) {
    flcDocumentCountryIsVisible = boolean;
    voltmx.print("### frmPerson_flcDocumentCountry_setVisibility");

    function flcDocumentCountry_setVisibility() {
        voltmx.print("### frmPerson_flcDocumentCountry_setVisibility flcDocumentCountry_setVisibility: " + boolean);
        frmPerson.flcDocumentCountry.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcDocumentCountry_setVisibility, []);
}

function frmPerson_flcDocumentNumber_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcDocumentNumber_setVisibility");

    function flcDocumentNumber_setVisibility() {
        voltmx.print("### frmPerson_flcDocumentNumber_setVisibility flcDocumentNumber_setVisibility: " + boolean);
        frmPerson.flcDocumentNumber.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcDocumentNumber_setVisibility, []);
}

function frmPerson_flcDocumentTypeAdditional_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcDocumentTypeAdditional_setVisibility");

    function flcDocumentTypeAdditional_setVisibility() {
        voltmx.print("### frmPerson_flcDocumentTypeAdditional_setVisibility flcDocumentTypeAdditional_setVisibility: " + boolean);
        frmPerson.flcDocumentTypeAdditional.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcDocumentTypeAdditional_setVisibility, []);
}

function frmPerson_documentdescription_setVisibility(boolean) {
    voltmx.print("### frmPerson_documentdescription_setVisibility");

    function documentdescription_setVisibility() {
        voltmx.print("### frmPerson_documentdescription_setVisibility documentdescription_setVisibility: " + boolean);
        frmPerson.documentdescription.setVisibility(boolean);
    }
    voltmx.runOnMainThread(documentdescription_setVisibility, []);
}

function frmPerson_flcLayoutPerson_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcLayoutPerson_setVisibility");

    function flcLayoutPerson_setVisibility() {
        voltmx.print("### frmPerson_flcLayoutPerson_setVisibility flcLayoutPerson_setVisibility: " + boolean);
        frmPerson.flcLayoutPerson.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcLayoutPerson_setVisibility, []);
}

function frmPerson_flcCharacteristics_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcCharacteristics_setVisibility");

    function flcCharacteristics_setVisibility() {
        voltmx.print("### frmPerson_flcCharacteristics_setVisibility flcCharacteristics_setVisibility: " + boolean);
        frmPerson.flcCharacteristics.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcCharacteristics_setVisibility, []);
}

function frmPerson_settext_setVisibility(boolean) {
    voltmx.print("### frmPerson_settext_setVisibility");

    function settext_setVisibility() {
        voltmx.print("### frmPerson_settext_setVisibility settext_setVisibility: " + boolean);
        frmPerson.settext.setVisibility(boolean);
    }
    voltmx.runOnMainThread(settext_setVisibility, []);
}

function frmPerson_flcNFCchipSymbol_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcNFCchipSymbol_setVisibility");

    function flcNFCchipSymbol_setVisibility() {
        voltmx.print("### frmPerson_flcNFCchipSymbol_setVisibility flcNFCchipSymbol_setVisibility: " + boolean);
        frmPerson.flcNFCchipSymbol.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcNFCchipSymbol_setVisibility, []);
}

function frmPerson_lblOutPutMZRDateOfBirth_setVisibility(boolean) {
    voltmx.print("### frmPerson_lblOutPutMZRDateOfBirth_setVisibility");

    function lblOutPutMZRDateOfBirth_setVisibility() {
        voltmx.print("### frmPerson_lblOutPutMZRDateOfBirth_setVisibility lblOutPutMZRDateOfBirth_setVisibility: " + boolean);
        frmPerson.lblOutPutMZRDateOfBirth.setVisibility(boolean);
    }
    voltmx.runOnMainThread(lblOutPutMZRDateOfBirth_setVisibility, []);
}

function frmPerson_lblOutPutMZRDateofExpiry_setVisibility(boolean) {
    voltmx.print("### frmPerson_lblOutPutMZRDateofExpiry_setVisibility");

    function lblOutPutMZRDateofExpiry_setVisibility() {
        voltmx.print("### frmPerson_lblOutPutMZRDateofExpiry_setVisibility lblOutPutMZRDateofExpiry_setVisibility: " + boolean);
        frmPerson.lblOutPutMZRDateofExpiry.setVisibility(boolean);
    }
    voltmx.runOnMainThread(lblOutPutMZRDateofExpiry_setVisibility, []);
}

function frmPerson_flcNoChip_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcNoChip_setVisibility");

    function flcNoChip_setVisibility() {
        voltmx.print("### frmPerson_flcNoChip_setVisibility flcNoChip_setVisibility: " + boolean);
        frmPerson.flcNoChip.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcNoChip_setVisibility, []);
}

function frmPerson_flcReadNFC_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcReadNFC_setVisibility");

    function flcReadNFC_setVisibility() {
        voltmx.print("### frmPerson_flcReadNFC_setVisibility flcReadNFC_setVisibility: " + boolean);
        frmPerson.flcReadNFC.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcReadNFC_setVisibility, []);
}

function frmPerson_flcCountryOfOrigin_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcCountryOfOrigin_setVisibility");

    function flcCountryOfOrigin_setVisibility() {
        voltmx.print("### frmPerson_flcCountryOfOrigin_setVisibility flcCountryOfOrigin_setVisibility: " + boolean);
        frmPerson.flcCountryOfOrigin.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcCountryOfOrigin_setVisibility, []);
}

function frmPerson_flcMunicipalityOfBirth_setVisibility(boolean) {
    voltmx.print("### frmPerson_flcMunicipalityOfBirth_setVisibility");

    function flcMunicipalityOfBirth_setVisibility() {
        voltmx.print("### frmPerson_flcMunicipalityOfBirth_setVisibility flcMunicipalityOfBirth_setVisibility: " + boolean);
        frmPerson.flcMunicipalityOfBirth.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcMunicipalityOfBirth_setVisibility, []);
}

function frmPerson_init() {
    voltmx.print("#### frmPerson_init");
    //Utility_registerForIdleTimeout();
    frmPerson.onDeviceBack = Global_onDeviceBack;
    frmPerson.lbxDocumentTypeAdditional.centerX = 52 + "%";
    frmPerson.lbxDocumentType.centerX = 52 + "%";
    frmPerson.lbxDocumentTypeAdditional.expandListItemToParentWidth = true;
    frmPerson.lbxDocumentType.expandListItemToParentWidth = true;
    if (Global.vars.enableReadId === false) {
        frmPerson_flcToggleInput_setVisibility(false);
        frmPerson.flcLayoutPerson.top = 56 + "dp";
        frmPerson_flcLayoutPerson_setVisibility(true);
        frmPerson.flcLayoutPerson.bottom = "44dp";
        frmPerson_flcScanInfo_setVisibility(false);
        frmPerson_flcFooterScan_setVisibility(false);
    }
    Utility_getGenderTypes(frmPerson_getGenderTypes);
    Utility_getAddressTypes(frmPerson_getAddressTypes);
    // Register scanner button pressed callback
    if (Global.vars.showButtonCheckdocument === false) {
        frmPerson_flcFooterMain_setVisibility(false);
    }
    frmPerson.onKeyboardDidHide = keyboardCallbacksDidHide;
}

function keyboardCallbacksDidHide(wdgRef, keyboardData) {
    voltmx.print("#### frmPerson keyboardCallbacksDidHide");
    // handle the event here
    // wdgRef returns the current form.
    // keyboardData returns keyboard related data provided by iOS platform
    frmPerson_contentOffset();
}

function frmPerson_getGenderTypes(result) {
    if (result.length > 0) {
        Global.vars.gPersonGenderResult = result;
        voltmx.print("### frmPerson_getGenderTypes genderdata1: " + JSON.stringify(Global.vars.gPersonGenderResult));
    }
}

function frmPerson_getAddressTypes(result) {
    if (result.length > 0) {
        Global.vars.gPersonAdressType = result;
        voltmx.print("### frmPerson_getAddressTypes: " + JSON.stringify(Global.vars.gPersonAdressType));
    }
}

function frmPerson_preshow() {
    Analytics_logScreenView("person");
    if (Global.vars.externalDocumentScan === true) {
        Global.vars.externalDocumentScan = false;
        return;
    }
    voltmx.print("#### frmPerson_preshow");
    Global.vars.continueToFormAfterDataReset = "";
    if (Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_registerconcept")) {
        if (Global.vars.previousForm == "frmRegisterResume" || Global.vars.previousForm == "frmRegisterConcept") {
            Global.vars.cameToPersonFromForm = Global.vars.previousForm;
        }
    } else {
        Global.vars.cameToPersonFromForm = "";
    }
    if (Global.vars.enableReadId === true) {
        if ((Global.vars.previousForm == "frmResume" || Global.vars.previousForm == "frmRegister" || Global.vars.previousForm == "frmRegisterConcept" || Global.vars.previousForm == "frmRegisterResume") && Global.vars.gCasePersons.idenDocType === null) {
            voltmx.print("#### frmPerson_preshow frmPerson_onclick_btnPersonReadID");
            frmPerson_onclick_btnPersonReadID();
        } else {
            voltmx.print("#### frmPerson_preshow frmPerson_disableNFCButton");
            frmPerson_disableNFCButton();
        }
    }
    voltmx.print("#### frmPerson_preshow");
    if (Global.vars.documentTypes.length > 0) {
        frmPerson.lbxDocumentType.masterDataMap = [Global.vars.documentTypes, "key", "value"];
        frmPerson_fillDocumentTypeAdditional();
    } else {
        Utility_getPersonDocIdentificationTypes(frmPerson_getPersonDocIdentificationTypes);
    }
    voltmx.print("#### frmPerson_preshow Global.vars.previousForm: " + Global.vars.previousForm);
    if (Global.vars.previousForm == "frmPersonResult" && Global.vars.setEditPerson === true) {
        frmPerson.txtDocumentNumber.text = Global.vars.gCasePersons.documentNumber;
        frmPerson_onclick_btnPersonManual();
        frmPerson_flcToggleInput_setVisibility(false);
        frmPerson.flcLayoutPerson.top = "53dp";
        Global.vars.setEditPerson = false;
    } else {
        if (Global.vars.enableReadId === true && Global.vars.externalDocumentScan === false) {
            frmPerson_flcToggleInput_setVisibility(true);
            frmPerson.flcLayoutPerson.top = "102dp";
            frmPerson_clearReadIDGlobals();
        }
        frmPerson_clear_labels();
        frmPerson_clearFields();
        frmPerson_setFields();
        frmPerson_fillFields();
        frmPerson_togglePersonCharacteristics();
    }
    Utility_getGenderTypes(frmPerson_getGenderTypes);
    Utility_getAddressTypes(frmPerson_getAddressTypes);
    voltmx.print("### frmPerson_preshow Global.vars.gCasePersons.idenDocType: " + Global.vars.gCasePersons.idenDocType);
    voltmx.print("### frmPerson_preshow Global.vars.gCasePersons.idenDocTypeDesc: " + Global.vars.gCasePersons.idenDocTypeDesc);
}

function frmPerson_setFields() {
    voltmx.print("#### frmPerson_setFields persons object Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    if (Global.vars.gCasePersons.idenDocType === undefined) {
        Global.vars.gCasePersons.idenDocType = null;
    }
    voltmx.print("#### frmPerson_setFields persons object idenDocType: " + Global.vars.gCasePersons.idenDocType);
    var docOthers = Global.vars.gCasePersons.idenDocType === null ? false : Global.vars.gCasePersons.idenDocType.toString().startsWith("99");
    var docMandatory = false;
    var index = Global.vars.additionalDocumentTypes.map(function(e) {
        return e.numbervalue;
    }).indexOf(Global.vars.gCasePersons.idenDocType);
    if (index > -1) {
        docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
    }
    voltmx.print("### frmPerson_setFields docMandatory: " + docMandatory);
    voltmx.print("### frmPerson_setFields docOthers: " + docOthers);
    if (docOthers === false && Global.vars.gCasePersons.idenDocType != 20 && Global.vars.gCasePersons.idenDocType !== 0) {
        voltmx.print("### frmPerson_setFields IF");
        frmPerson_flcDocumentCountry_setVisibility(true);
        frmPerson.btnDocumentCountry.onClick = null;
        frmPerson.imgDocumentCountryRight.src = "empty.png";
        frmPerson_flcDocumentNumber_setVisibility(true);
        frmPerson_flcDocumentTypeAdditional_setVisibility(false);
        frmPerson_fillDocumentTypeAdditional();
        frmPerson_documentdescription_setVisibility(false);
        frmPerson_getCountryIdentification();
    } else if (docMandatory === true) {
        voltmx.print("### frmPerson_setFields ELSE IF");
        frmPerson_flcDocumentCountry_setVisibility(true);
        frmPerson.btnDocumentCountry.onClick = frmPerson_onclick_btnDocumentCountry;
        frmPerson.imgDocumentCountryRight.src = "arrowrightmini.png";
        frmPerson_flcDocumentNumber_setVisibility(true);
        frmPerson_flcDocumentTypeAdditional_setVisibility(true);
        frmPerson_documentdescription_setVisibility(true);
        frmPerson_getCountryIdentification();
    } else {
        voltmx.print("### frmPerson_setFields ELSE");
        frmPerson.btnDocumentCountry.onClick = frmPerson_onclick_btnDocumentCountry;
        frmPerson.imgDocumentCountryRight.src = "arrowrightmini.png";
        frmPerson_flcDocumentCountry_setVisibility(false);
        frmPerson_flcDocumentNumber_setVisibility(false);
        frmPerson_flcDocumentTypeAdditional_setVisibility(true);
        frmPerson_documentdescription_setVisibility(true);
    }
}

function frmPerson_clearDocumentNumber() {
    frmPerson.txtDocumentNumber.text = "";
    if (Global.vars.validatePersonDocument === true) {
        frmPerson.txtDocumentNumber.setFocus(true);
    }
    Global.vars.gCasePersons.documentNumber = null;
    frmPerson_togglePersonCharacteristics();
}

function frmPerson_fillFields() {
    voltmx.print("#### frmPerson_fillFields");
    //voltmx.print("### frmPerson_fillFields Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    voltmx.print("### frmPerson_fillFields CaseData.person: " + JSON.stringify(CaseData.person));
    voltmx.print("### frmPerson_fillFields Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    voltmx.print("#### frmPerson_fillFields validatePersonDocument: " + Global.vars.validatePersonDocument);
    voltmx.print("#### frmPerson_fillFields documentNumber: " + Global.vars.gCasePersons.documentNumber);
    voltmx.print("#### frmPerson_fillFields idenDocType: " + Global.vars.gCasePersons.idenDocType);
    voltmx.print("#### frmPerson_fillFields countryIdenDocDesc: " + Global.vars.gCasePersons.countryIdenDocDesc);
    if (Global.vars.gCasePersons.documentNumber !== undefined && Global.vars.gCasePersons.documentNumber != null && Global.vars.gCasePersons.documentNumber !== "") {
        voltmx.print("### frmPerson_fillFields document number is filled");
        if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType.toString().startsWith("99")) {
            frmPerson.lbxDocumentType.selectedKey = "99";
            frmPerson.lbxDocumentTypeAdditional.selectedKey = Global.vars.gCasePersons.idenDocType.toString();
            //frmPerson_onselect_lbxDocumentType();
        } else {
            frmPerson_deleteDocumentDescription();
            if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null) {
                frmPerson.lbxDocumentType.selectedKey = Global.vars.gCasePersons.idenDocType.toString();
            }
        }
        frmPerson.txtDocumentNumber.text = Global.vars.gCasePersons.documentNumber;
        voltmx.print("### frmPerson_fillFields Global.vars.gCasePersons.countryIdenDocDesc: " + Global.vars.gCasePersons.countryIdenDocDesc);
        frmPerson.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
        frmPerson.lblDocumentCountry.skin = lblFieldInfo;
        if (Global.vars.gCasePersons.countryIdenDocDesc === "" && Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && (Global.vars.gCasePersons.idenDocType.toString().startsWith("99") === false)) {
            frmPerson_getCountryIdentification();
        }
    } else {
        if (Global.vars.gCasePersons.countryIdenDocDesc !== undefined && Global.vars.gCasePersons.countryIdenDocDesc != null && Global.vars.gCasePersons.countryIdenDocDesc !== "") {
            frmPerson.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
            frmPerson.lblDocumentCountry.skin = lblFieldInfo;
        } else {
            frmPerson.lblDocumentCountry.skin = lblFieldNotFilled;
        }
        if (Global.vars.gCasePersons.idenDocType === undefined || Global.vars.gCasePersons.idenDocType === null || Global.vars.gCasePersons.idenDocType === "") {
            voltmx.print("#### frmPerson_fillFields 1");
            if (Global.vars.validatePersonDocument === false) {
                voltmx.print("#### frmPerson_fillFields 2a");
                voltmx.print("#### frmPerson_fillFields 2a l_noDocument " + voltmx.i18n.getLocalizedString("l_noDocument"));
                frmPerson.lbxDocumentType.selectedKey = "99";
                Global.vars.gCasePersons.idenDocType = 9900;
                frmPerson.documentdescription.lblText.text = voltmx.i18n.getLocalizedString("l_noDocument");
                voltmx.print("#### frmPerson_fillFields 2a frmPerson.documentdescription.lblText.text " + frmPerson.documentdescription.lblText.text);
                frmPerson_onselect_lbxDocumentType();
                //if(frmPerson.documentdescription.lblText.text != null && frmPerson.documentdescription.lblText.text !== "" && frmPerson.documentdescription.lblText.text !== "Beschrijving document(en)"){
                //add record to CaseData.text
                var loctextindex = null;
                for (var p = 0;
                    ((CaseData.text) != null) && p < CaseData.text.length; p++) {
                    var v = CaseData.text[p];
                    if ((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))) { //beschrijving documenten
                        voltmx.print("#### frmPerson_fillFields 2a: " + JSON.stringify(v) + " index: " + p);
                        loctextindex = p;
                        break;
                    }
                }
                var laddrecord = CaseData_setNewtext();
                laddrecord.inserted = true;
                laddrecord.edited = true;
                laddrecord.type = 3; //beschrijving documenten
                laddrecord.value = "Beschrijving document(en): " + voltmx.i18n.getLocalizedString("l_noDocument");
                voltmx.print("#### frmPerson_fillFields 2a laddrecord " + JSON.stringify(laddrecord));
                if (loctextindex === null) {
                    CaseData.text.splice(0, 0, laddrecord);
                } else {
                    CaseData.text.splice(loctextindex, 1, laddrecord);
                }
                voltmx.print("#### frmPerson_fillFields 2a documentdescription.lblText CaseData.text after: " + JSON.stringify(CaseData.text));
                // }
            } else {
                voltmx.print("#### frmPerson_fillFields 2b");
                frmPerson.lbxDocumentType.selectedKey = null;
            }
        } else if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType.toString().startsWith("99")) {
            voltmx.print("#### frmPerson_fillFields 3");
            frmPerson.lbxDocumentType.selectedKey = "99";
            if (Global.vars.gCasePersons.idenDocType == 99) {
                frmPerson.lbxDocumentTypeAdditional.selectedKey = "9900";
            } else {
                frmPerson.lbxDocumentTypeAdditional.selectedKey = Global.vars.gCasePersons.idenDocType.toString();
            }
            var docMandatory = false;
            var index = Global.vars.additionalDocumentTypes.map(function(e) {
                return e.numbervalue;
            }).indexOf(Global.vars.gCasePersons.idenDocType);
            if (index > -1) {
                docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
            }
            voltmx.print("#### frmPerson_fillFields docMandatory:" + docMandatory);
            frmPerson_flcDocumentCountry_setVisibility(docMandatory);
            frmPerson_flcDocumentNumber_setVisibility(docMandatory);
            frmPerson_flcDocumentTypeAdditional_setVisibility(true);
            frmPerson_documentdescription_setVisibility(true);
            frmPerson_flcFooterMain_setVisibility(false);
            if (docMandatory === false) {
                Global.vars.gCasePersons.countryIdenDoc = null;
                Global.vars.gCasePersons.countryIdenDocDesc = null;
                frmPerson.lblDocumentCountry.text = voltmx.i18n.getLocalizedString("l_country");
                frmPerson.lblDocumentCountry.skin = lblFieldNotFilled;
                Global.vars.gCasePersons.documentNumber = "";
                frmPerson.txtDocumentNumber.text = "";
            }
        }
    }
    voltmx.print("#### frmPerson_fillFields: Finding document description: " + frmPerson.documentdescription.isVisible);
    voltmx.print("#### frmPerson_fillFields: Finding document description: " + JSON.stringify(CaseData.text));
    if (frmPerson.documentdescription.isVisible === true) {
        var documentDescription = "";
        for (var q = 0;
            ((CaseData.text) != null) && q < CaseData.text.length; q++) {
            var w = CaseData.text[q];
            if ((w.type == 3 && voltmx.string.startsWith(w.value, "Beschrijving document(en): "))) { //beschrijving documenten
                voltmx.print("#### frmPerson_fillFields: Finding document description: " + JSON.stringify(w) + " index: " + q);
                documentDescription = w.value.replace("Beschrijving document(en): ", "");
                break;
            }
        }
        voltmx.print("#### frmPerson_fillFields: documentDescription: " + documentDescription);
        if (Global.vars.validatePersonDocument === false && documentDescription === voltmx.i18n.getLocalizedString("l_noDocument")) {
            voltmx.print("#### frmPerson_fillFields A");
            frmPerson.documentdescription.lblText.text = documentDescription;
            frmPerson.documentdescription.lblText.skin = lblFieldNotFilled;
        } else if (documentDescription === "") {
            voltmx.print("#### frmPerson_fillFields B");
            frmPerson.documentdescription.lblText.text = "Beschrijving document(en)";
            frmPerson.documentdescription.lblText.skin = lblFieldNotFilled;
        } else {
            frmPerson.documentdescription.lblText.text = documentDescription;
            frmPerson.documentdescription.lblText.skin = lblFieldInfo;
            voltmx.print("#### frmPerson_fillFields C");
        }
    }
    //   	voltmx.print("#### fill legal entity text with globals: " + JSON.stringify(Global.vars.gCaseLegalEntities));
    // 	if(Global.vars.gCaseLegalEntities.LegalEntityName !== undefined){
    //   		frmPerson.txtLegalEntityName.text = Global.vars.gCaseLegalEntities.LegalEntityName;
    // 		frmPerson.txtLegalEntityCocNumber.text = Global.vars.gCaseLegalEntities.LegalEntityCocNumber;
    //     }else{
    //       	frmPerson.txtLegalEntityName.text = "";
    // 		frmPerson.txtLegalEntityCocNumber.text = "";
    //     }
    if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex] === undefined || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType === null) {
        voltmx.print("### frmRegister_clearLicenseplate set person as vhicleType");
        frmPerson_setPersonVehicleType();
    }
    voltmx.print("### frmPerson_fillFields end");
}

function frmPerson_onEndEditCoCNumber() {
    var validCoCNumber = frmPerson_checkCoCNumber();
    if (validCoCNumber) {
        Global.vars.gCaseLegalEntities.LegalEntityCocNumber = frmPerson.txtLegalEntityCocNumber.text;
    }
}

function frmPerson_onclick_btnPersonManual() {
    Global.vars.personInputMethod = Global.vars.personInputMethod === "" ? "Manual" : Global.vars.personInputMethod;
    frmPerson.btnPersonManual.skin = btnGreyToggleFocus;
    frmPerson.btnPersonReadID.skin = btnGreyToggle;
    frmPerson_flcLayoutPerson_setVisibility(true);
    frmPerson_flcScanInfo_setVisibility(false);
    if (Global.vars.showButtonCheckdocument === false) {
        frmPerson_flcFooterMain_setVisibility(false);
        frmPerson.flcLayoutPerson.bottom = "0dp";
    } else {
        if (Global.vars.checkdocument !== "") {
            frmPerson_flcFooterMain_setVisibility(false);
            frmPerson.flcLayoutPerson.bottom = "44dp";
        } else {
            frmPerson_flcFooterMain_setVisibility(false);
            frmPerson.flcLayoutPerson.bottom = "0dp";
        }
    }
    if (CaseData.person === null || CaseData.person.length === 0) {
        Global.vars.gCasePersonsIndex = 0;
        Global.vars.gCasePersons = CaseData_setNewperson(Global.vars.gCasePersonsIndex);
    }
    frmPerson_flcFooterScan_setVisibility(false);
    //gooi scandata leeg
    if (Global.vars.previousForm == "frmPersonResult" && Global.vars.setEditPerson === true) {} else {
        frmPerson_clearReadIDGlobals();
        frmPerson_clearDocumentNumber();
    }
    frmPerson_clear_labels();
    frmPerson_clearFields();
    Global.vars.continueToFormAfterDataReset = "";
    frmPerson_setFields();
    voltmx.print("### frmPerson_onclick_btnPersonManual Global.vars.gCasePersons.idenDocType: " + Global.vars.gCasePersons.idenDocType);
    if (Global.vars.gCasePersons.idenDocType === null) {
        voltmx.print("### frmPerson_onclick_btnPersonManual IF");
        if (Global.vars.documentTypes.length > 0) {
            voltmx.print("### frmPerson_onclick_btnPersonManual Global.vars.documentTypes: " + JSON.stringify(Global.vars.documentTypes));
            try {
                voltmx.print("### frmPerson_onclick_btnPersonManual IF Global.vars.documentTypes.length > 0");
                frmPerson.lbxDocumentType.masterDataMap = [Global.vars.documentTypes, "key", "value"];
                voltmx.print("### frmPerson_onclick_btnPersonManual IF Global.vars.documentTypes[0].key: " + Global.vars.documentTypes[0].key);
                frmPerson.lbxDocumentType.selectedKey = Global.vars.documentTypes[0].key + "";
                frmPerson_fillDocumentTypeAdditional();
            } catch (e) {
                voltmx.print("### frmPerson_onclick_btnPersonManual error: " + JSON.stringify(e));
                Utility_getPersonDocIdentificationTypes(frmPerson_getPersonDocIdentificationTypes);
            }
        } else {
            voltmx.print("### frmPerson_onclick_btnPersonManual ELSE Global.vars.documentTypes ");
            Utility_getPersonDocIdentificationTypes(frmPerson_getPersonDocIdentificationTypes);
        }
    } else {
        voltmx.print("### frmPerson_onclick_btnPersonManual ELSE");
        frmPerson_fillFields();
    }
    frmPerson_togglePersonCharacteristics();
    Global.vars.documentCountryPersonManual = false;
}

function frmPerson_onclick_btnPersonReadID() {
    voltmx.print("### frmPerson_onclick_btnPersonReadID");
    frmPerson.btnPersonManual.skin = btnGreyToggle;
    frmPerson.btnPersonReadID.skin = btnGreyToggleFocus;
    frmPerson_flcLayoutPerson_setVisibility(false);
    frmPerson_flcScanInfo_setVisibility(true);
    frmPerson_flcFooterMain_setVisibility(false);
    frmPerson_flcFooterScan_setVisibility(true);
    //gooi scandata leeg
    frmPerson_clearReadIDGlobals();
    frmPerson_clear_labels();
    frmPerson_clearFields();
    frmPerson_disableNFCButton();
    Global.vars.gCasePersonsIndex = 0;
    Global.vars.gCasePersons = CaseData_setNewperson(Global.vars.gCasePersonsIndex);
    frmPerson_deleteDocumentDescription();
}

function frmPerson_checkCoCNumber() {
    var regex = /^[0-9]{8}([0-9]{3}H?)?$/;
    var verify = regex.test(frmPerson.txtLegalEntityCocNumber.text);
    if (verify === false) {
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_legalEntityCocNr") + ": " + voltmx.i18n.getLocalizedString("i_isan0002"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
    }
    return verify;
}

function frmPerson_onclickNext() {
    frmPerson_LegalEntity_SetGlobals();
}

function frmPerson_LegalEntity_SetGlobals() {
    var legalEntityFieldsVerified = false;
    var validCoCNumber = frmPerson_checkCoCNumber();
    if (validCoCNumber) {
        Global.vars.gCaseLegalEntities.LegalEntityCocNumber = frmPerson.txtLegalEntityCocNumber.text;
        legalEntityFieldsVerified = true;
    }
    if (frmPerson.txtLegalEntityName.text != null && frmPerson.txtLegalEntityName.text !== "") {
        Global.vars.gCaseLegalEntities.LegalEntityName = frmPerson.txtLegalEntityName.text;
        legalEntityFieldsVerified = true;
    } else {
        alert("Vul de naam van de rechtspersoon in");
        legalEntityFieldsVerified = false;
    }
    if (legalEntityFieldsVerified) {
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        frmPersonLegalEntityAddress.show();
    }
}

function frmPerson_onselect_lbxDocumentType() {
    try {
        voltmx.timer.cancel("selectDoc");
    } catch (err) {}
    voltmx.print("### frmPerson_onselect_lbxDocumentType frmPerson.lbxDocumentType.selectedKeyValue: " + frmPerson.lbxDocumentType.selectedKeyValue);
    if (frmPerson.lbxDocumentType.selectedKeyValue != null && Number(frmPerson.lbxDocumentType.selectedKeyValue[0]) !== -1) {
        if (frmPerson.lbxDocumentType.selectedKeyValue != null) {
            if (Global.vars.gCasePersons.idenDocType !== Number(frmPerson.lbxDocumentType.selectedKeyValue[0]) && Global.vars.gCasePersons.idenDocType != null) {
                Global.vars.gCasePersons.documentNumber = "";
                frmPerson.txtDocumentNumber.text = "";
            }
            Global.vars.gCasePersons.idenDocTypeDesc = frmPerson.lbxDocumentType.selectedKeyValue[1];
            Global.vars.gCasePersons.idenDocType = Number(frmPerson.lbxDocumentType.selectedKeyValue[0]);
        }
        voltmx.print("### frmPerson_onselect_lbxDocumentType Global.vars.gCasePersons.idenDocType: " + Global.vars.gCasePersons.idenDocType);
        if (Global.vars.gCasePersons.idenDocType !== 99 && Global.vars.gCasePersons.idenDocType != 20 && Global.vars.gCasePersons.idenDocType !== 0) {
            Global.vars.gCasePersons.countryIdenDoc = 6030;
            Global.vars.gCasePersons.countryIdenDocDesc = "Nederland";
            frmPerson.lblDocumentCountry.text = "Nederland";
            frmPerson.lblDocumentCountry.skin = lblFieldInfo;
            frmPerson_fillDocumentTypeAdditional();
            frmPerson_deleteDocumentDescription();
            frmPerson_flcDocumentCountry_setVisibility(true);
            frmPerson.lblDocumentCountry.skin = lblFieldInfo;
            frmPerson_flcDocumentNumber_setVisibility(true);
            frmPerson_flcDocumentTypeAdditional_setVisibility(false);
            frmPerson_documentdescription_setVisibility(false);
            frmPerson_flcFooterMain_setVisibility(false);
            frmPerson.imgDocumentCountryRight.src = "empty.png";
            frmPerson.btnDocumentCountry.onClick = null;
        } else {
            Global.vars.gCasePersons.countryIdenDoc = null;
            Global.vars.gCasePersons.countryIdenDocDesc = null;
            frmPerson.lblDocumentCountry.text = voltmx.i18n.getLocalizedString("l_country");
            frmPerson.lblDocumentCountry.skin = lblFieldNotFilled;
            Global.vars.gCasePersons.documentNumber = "";
            frmPerson.txtDocumentNumber.text = "";
            frmPerson.imgDocumentCountryRight.src = "arrowrightmini.png";
            frmPerson.btnDocumentCountry.onClick = frmPerson_onclick_btnDocumentCountry;
            frmPerson_flcDocumentCountry_setVisibility(false);
            frmPerson_flcDocumentNumber_setVisibility(false);
            frmPerson_flcDocumentTypeAdditional_setVisibility(true);
            frmPerson_documentdescription_setVisibility(true);
            frmPerson_flcFooterMain_setVisibility(false);
        }
        if (frmPerson.lbxDocumentType.selectedKeyValue != null) {
            Utility_getPersonDocIdentificationTypeById(Number(frmPerson.lbxDocumentType.selectedKeyValue[0]), frmPerson_getPersonDocIdentificationTypeById);
        }
        frmPerson_togglePersonCharacteristics();
    } else if (frmPerson.lbxDocumentType.selectedKeyValue != null && Number(frmPerson.lbxDocumentType.selectedKeyValue[0]) === -1) {
        Global.vars.gCasePersons.countryIdenDoc = null;
        Global.vars.gCasePersons.countryIdenDocDesc = null;
        frmPerson.lblDocumentCountry.text = voltmx.i18n.getLocalizedString("l_country");
        Global.vars.gCasePersons.documentNumber = "";
        frmPerson.txtDocumentNumber.text = "";
        frmPerson_fillDocumentTypeAdditional();
        frmPerson_deleteDocumentDescription();
        frmPerson_flcDocumentCountry_setVisibility(true);
        frmPerson.lblDocumentCountry.skin = lblFieldNotFilled;
        frmPerson_flcDocumentNumber_setVisibility(true);
        frmPerson_flcDocumentTypeAdditional_setVisibility(false);
        frmPerson_documentdescription_setVisibility(false);
        frmPerson_flcFooterMain_setVisibility(false);
        frmPerson.imgDocumentCountryRight.src = "empty.png";
        frmPerson.btnDocumentCountry.onClick = null;
    }
    frmPerson.lbxDocumentType.setFocus(false);
}

function frmPerson_onselect_lbxDocumentTypeAdditional() {
    try {
        voltmx.timer.cancel("selectDoc");
    } catch (err) {}
    Global.vars.gCasePersons.idenDocTypeDesc = frmPerson.lbxDocumentTypeAdditional.selectedKeyValue[1];
    Global.vars.gCasePersons.idenDocType = Number(frmPerson.lbxDocumentTypeAdditional.selectedKeyValue[0]);
    var docMandatory = false;
    var index = Global.vars.additionalDocumentTypes.map(function(e) {
        return e.numbervalue;
    }).indexOf(Global.vars.gCasePersons.idenDocType);
    if (index > -1) {
        docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
    }
    voltmx.print("### frmPerson_onselect_lbxDocumentTypeAdditional docMandatory: " + docMandatory);
    if (docMandatory === true) {
        voltmx.print("### frmPerson_onselect_lbxDocumentTypeAdditional IF");
        frmPerson_flcDocumentCountry_setVisibility(true);
        frmPerson_flcDocumentNumber_setVisibility(true);
        frmPerson_flcDocumentTypeAdditional_setVisibility(true);
        frmPerson_documentdescription_setVisibility(true);
        if (frmPerson.documentdescription.lblText.text === voltmx.i18n.getLocalizedString("l_noDocument")) {
            frmPerson_deleteDocumentDescription();
        }
    } else {
        voltmx.print("### frmPerson_onselect_lbxDocumentTypeAdditional ELSE");
        frmPerson_flcDocumentCountry_setVisibility(false);
        frmPerson_flcDocumentNumber_setVisibility(false);
        frmPerson_flcDocumentTypeAdditional_setVisibility(true);
        frmPerson_documentdescription_setVisibility(true);
        Global.vars.gCasePersons.countryIdenDoc = null;
        Global.vars.gCasePersons.countryIdenDocDesc = null;
        frmPerson.lblDocumentCountry.text = voltmx.i18n.getLocalizedString("l_country");
        frmPerson.lblDocumentCountry.skin = lblFieldNotFilled;
        Global.vars.gCasePersons.documentNumber = "";
        frmPerson.txtDocumentNumber.text = "";
    }
    frmPerson_togglePersonCharacteristics();
    frmPerson.lbxDocumentTypeAdditional.setFocus(false);
    voltmx.print("### frmPerson_onselect_lbxDocumentTypeAdditional Global.vars.gCasePersons.idenDocType: " + Global.vars.gCasePersons.idenDocType);
    voltmx.print("### frmPerson_onselect_lbxDocumentTypeAdditional Global.vars.gCasePersons.idenDocTypeDesc: " + Global.vars.gCasePersons.idenDocTypeDesc);
}

function frmPerson_getPersonDocIdentificationTypeById(result) {
    voltmx.print("### frmPerson_getPersonDocIdentificationTypeById ###");
    voltmx.print("### frmPerson_getPersonDocIdentificationTypeById PersonDocIdentificationTypeIdDescription: " + JSON.stringify(result));
    Global.vars.checkdocument = "";
    if (result.length > 0) {
        var v = result[0];
        var sub1 = "_travel_";
        var sub2 = "_driving_";
        var sub3 = "_alien_";
        if (v.code.indexOf(sub1) !== -1) {
            Global.vars.checkdocument = "TRAVEL_DOCUMENT";
        } else if (v.code.indexOf(sub2) !== -1) {
            Global.vars.checkdocument = "DRIVING_LICENSE";
        } else if (v.code.indexOf(sub3) !== -1) {
            Global.vars.checkdocument = "ALIEN_DOCUMENT";
        }
    }
    voltmx.print("### frmPerson_getPersonDocIdentificationTypeById Global.vars.checkdocument: " + Global.vars.checkdocument);
    if (Global.vars.showButtonCheckdocument === false) {
        frmPerson_flcFooterMain_setVisibility(false);
        frmPerson.flcLayoutPerson.bottom = "0dp";
    } else {
        if (Global.vars.checkdocument !== "") {
            frmPerson_flcFooterMain_setVisibility(false);
            frmPerson.flcLayoutPerson.bottom = "44dp";
        } else {
            frmPerson_flcFooterMain_setVisibility(false);
            frmPerson.flcLayoutPerson.bottom = "0dp";
        }
    }
}

function frmPerson_getPersonDocIdentificationTypes(result) {
    voltmx.print("### frmPerson_getPersonDocIdentificationTypes ###");
    voltmx.print("### frmPerson_getPersonDocIdentificationTypes PersonDocIdentificationTypeIdDescription: " + JSON.stringify(result));
    var documentTypeData = [];
    var selectedkey = null;
    Global.vars.checkdocument = "";
    documentTypeData.push({
        key: -1,
        value: voltmx.i18n.getLocalizedString("l_choose")
    });
    for (var j in result) {
        var v = result[j];
        documentTypeData.push({
            key: v.number_value.toString(),
            value: v.descripton
        });
        //     if(v.descripton == "rijbewijs"){
        //       selectedkey = v.numbervalue.toString();
        //       var sub1 = "_travel_";
        //       var sub2 = "_driving_";
        //       var sub3 = "_alien_";
        //       if(v.code.indexOf(sub1) !== -1){
        //         Global.vars.checkdocument = "TRAVEL_DOCUMENT";
        //       }else if(v.code.indexOf(sub2) !== -1){
        //         Global.vars.checkdocument = "DRIVING_LICENSE";
        //       }else if(v.code.indexOf(sub3) !== -1){
        //         Global.vars.checkdocument = "ALIEN_DOCUMENT";
        //       }
        //     }
    }
    Global.vars.documentTypes = documentTypeData;
    voltmx.print("### frmPerson_getPersonDocIdentificationTypes Global.vars.checkdocument: " + Global.vars.checkdocument);
    voltmx.print("### frmPerson_getPersonDocIdentificationTypes documentTypeData: " + JSON.stringify(documentTypeData));
    frmPerson.lbxDocumentType.masterDataMap = [documentTypeData, "key", "value"];
    voltmx.print("### frmPerson_getPersonDocIdentificationTypes frmPerson.lbxDocumentType.masterDataMap: " + JSON.stringify(frmPerson.lbxDocumentType.masterDataMap));
    if (selectedkey != null) {
        voltmx.print("### frmPerson_getPersonDocIdentificationTypes set selectedkey: " + selectedkey);
        frmPerson.lbxDocumentType.selectedKey = selectedkey;
        try {
            voltmx.timer.schedule("selectDoc", frmPerson_onselect_lbxDocumentType, 0.5, false);
        } catch (err) {}
        if (Global.vars.showButtonCheckdocument === false) {
            frmPerson_flcFooterMain_setVisibility(false);
            frmPerson.flcLayoutPerson.bottom = "0dp";
        } else {
            if (Global.vars.checkdocument !== "") {
                frmPerson_flcFooterMain_setVisibility(false);
                frmPerson.flcLayoutPerson.bottom = "44dp";
            } else {
                frmPerson_flcFooterMain_setVisibility(false);
                frmPerson.flcLayoutPerson.bottom = "0dp";
            }
        }
    }
    //fill documenttypeAdditional listbox
    frmPerson_fillDocumentTypeAdditional();
}

function frmPerson_fillDocumentTypeAdditional() {
    var documentTypeDataAdditional = [];
    var selectedkeyAdditional = null;
    for (var k in Global.vars.additionalDocumentTypes) {
        var x = Global.vars.additionalDocumentTypes[k];
        documentTypeDataAdditional.push({
            key: x.numbervalue.toString(),
            value: x.description
        });
        if (x.description == "Overig") {
            selectedkeyAdditional = x.numbervalue.toString();
        }
    }
    voltmx.print("### frmPerson_fillDocumentTypeAdditional documentTypeDataAdditional: " + JSON.stringify(documentTypeDataAdditional));
    frmPerson.lbxDocumentTypeAdditional.masterDataMap = [documentTypeDataAdditional, "key", "value"];
    frmPerson.lbxDocumentTypeAdditional.selectedKey = selectedkeyAdditional;
}

function frmPerson_getCountryIdentification() {
    voltmx.print("##### frmPerson_getCountryIdentification ");
    voltmx.print("### frmPerson_getCountryIdentification Current Country: " + Global.vars.gCasePersons.countryIdenDocDesc);
    voltmx.print("### frmPerson_getCountryIdentification Current CountryCode: " + Global.vars.gCasePersons.countryIdenDoc);
    //   	if(Global.vars.gCasePersons.countryIdenDoc === undefined || Global.vars.gCasePersons.countryIdenDoc === null || Global.vars.gCasePersons.countryIdenDoc === ""){
    //      	voltmx.print("##### frmPerson_getCountryIdentification set nederland");
    //       	Global.vars.gCasePersons.countryIdenDocDesc = "Nederland";
    //         frmPerson.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
    //         Global.vars.gCasePersons.countryIdenDoc = 6030; 
    //     }
    //
    function getCountryIdentificationSuccessCallback(resultcountryid) {
        voltmx.print("##### frmPerson_getCountryIdentification getCountryIdentificationSuccessCallback ");
        if ((resultcountryid.length !== 0)) {
            voltmx.print("### frmPerson_getCountryIdentification resultcountryid: " + JSON.stringify(resultcountryid));
            Global.vars.gCasePersons.countryIdenDocDesc = resultcountryid[0].description;
            frmPerson.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
            frmPerson.lblDocumentCountry.skin = lblFieldInfo;
            voltmx.print("#### frmPerson_getCountryIdentification coutry succes and set ");
        } else {
            voltmx.print("#### frmPerson_getCountryIdentification no results found for Country Info");
        }
        voltmx.print("##### frmPerson_getCountryIdentification getCountryIdentificationSuccessCallback ");
    }

    function getCountryIdentificationErrorCallback(error) {
        voltmx.print("##### frmPerson_getCountryIdentification getCountryIdentificationErrorCallback ");
        voltmx.print("### frmPerson_getCountryIdentification Country Info error: " + error);
    }
    if (Global.vars.gCasePersons.countryIdenDocDesc !== "" && Global.vars.gCasePersons.countryIdenDocDesc != null && Global.vars.gCasePersons.countryIdenDocDesc !== undefined) {
        frmPerson.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
        frmPerson.lblDocumentCountry.skin = lblFieldInfo;
    } else {
        var lCountrywhereClause = "select * from mle_v_country_m where code = '" + Global.vars.gCasePersons.countryIdenDoc + "'";
        lCountrywhereClause = Utility_addLanguageToWhereClauseObjectSync(lCountrywhereClause);
        voltmx.print("### Country clause: " + lCountrywhereClause);
        if (Global.vars.gCasePersons.countryIdenDoc !== undefined || Global.vars.gCasePersons.countryIdenDoc != null || Global.vars.gCasePersons.countryIdenDoc !== "") {
            KNYMobileFabric.OfflineObjects.executeSelectQuery(lCountrywhereClause, getCountryIdentificationSuccessCallback, getCountryIdentificationErrorCallback);
        }
    }
    voltmx.print("##### frmPerson_getCountryIdentification end");
}

function frmPerson_onclick_btnDocumentCountry() {
    Global.vars.personCountryType = "documentcountry";
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    frmPersonCountries.show();
}

function frmPerson_onclick_btnButSNN() {
    if (Global.vars.validatePersonDocument === false || frmPerson_validateFieldsFilled()) {
        frmPerson_setGlobalsPersonDocument();
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        if (Global.vars.gCasePersons.personFilled === true) {
            Global.vars.continueToFormAfterDataReset = "frmPersonSSN";
            frmPerson_showAlertRemoveData();
        } else {
            frmPerson_silentDocumentCheck();
            frmPersonSSN.show();
        }
    }
}

function frmPerson_validateFieldsFilled() {
    var fieldsFilled = false;
    var kindofdocument = frmPerson_validateKindOfDocument();
    var docnumber = frmPerson_validateDocumentNumber();
    var docdescriptionfilled = frmPerson_validateOtherDocumentDescription();
    var documentcountry = frmPerson_validatePersonDocumentCountry();
    if (kindofdocument && docnumber && docdescriptionfilled) {
        fieldsFilled = true;
    } else {
        var message = "Vul:";
        if (kindofdocument) {
            message = message + " Soort document";
        }
        if (docnumber) {
            if (message == "Vul") {
                message = message + " Documentnummer";
            } else {
                message = message + ", Documentnummer";
            }
        }
        if (docdescriptionfilled) {
            if (message == "Vul") {
                message = message + " Documentbeschrijving";
            } else {
                message = message + ", Documentbeschrijving";
            }
        }
    }
    return fieldsFilled;
}

function frmPerson_onclick_btnButManual() {
    if (frmPerson_validateFieldsFilled()) {
        frmPerson_setGlobalsPersonDocument();
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        if (Global.vars.gCasePersons.personFilled === true) {
            Global.vars.continueToFormAfterDataReset = "frmPersonManualPerson";
            frmPerson_showAlertRemoveData();
        } else {
            frmPerson_silentDocumentCheck();
            frmPersonManualPerson.show();
        }
    }
}

function frmPerson_onclick_btnButAlternative() {
    if (frmPerson_validateFieldsFilled()) {
        frmPerson_setGlobalsPersonDocument();
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        if (Global.vars.gCasePersons.personFilled === true) {
            Global.vars.continueToFormAfterDataReset = "frmPersonAlternativeSearch";
            frmPerson_showAlertRemoveData();
        } else {
            frmPerson_silentDocumentCheck();
            frmPersonAlternativeSearch.show();
        }
    }
}

function frmPerson_validatePersonDocumentCountry() {
    voltmx.print("### frmPerson_validatePersonDocumentCountry");
    voltmx.print("### frmPerson_validatePersonDocumentCountry text: " + frmPerson.lblDocumentCountry.text);
    var validated = false;
    if (flcDocumentCountryIsVisible === false) {
        validated = true;
    } else if (frmPerson.lblDocumentCountry.text !== "" && frmPerson.lblDocumentCountry.text != null && frmPerson.lblDocumentCountry.text !== voltmx.i18n.getLocalizedString("l_country")) {
        validated = true;
    }
    if (validated === true) {
        frmPerson.flcDocumentCountry.skin = flcFieldEdge;
        frmPerson.lblDocumentCountry.skin = lblFieldInfo;
    } else {
        frmPerson.flcDocumentCountry.skin = flcFieldEdgeRed;
        frmPerson.lblDocumentCountry.skin = lblFieldNotFilled;
    }
    return validated;
}

function frmPerson_validateOtherDocumentDescription() {
    var validated = false;
    var docMandatory = false;
    var index = Global.vars.additionalDocumentTypes.map(function(e) {
        return e.numbervalue;
    }).indexOf(Global.vars.gCasePersons.idenDocType);
    if (index > -1) {
        docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
    }
    if (frmPerson.documentdescription.isVisible === false || docMandatory === true) {
        validated = true;
    } else if (frmPerson.documentdescription.lblText.text !== "" && frmPerson.documentdescription.lblText.text != null && frmPerson.documentdescription.lblText.text !== "Beschrijving document(en)") {
        validated = true;
    }
    if (validated === true) {
        frmPerson.documentdescription.skin = flcFieldEdge;
    } else {
        frmPerson.documentdescription.skin = flcFieldEdgeRed;
    }
    return validated;
}

function frmPerson_showAlertRemoveData() {
    voltmx.ui.Alert("Wilt u de aanwezige persoonsgegevens gebruiken?", //i18n
        frmPersonResult_confirm_RemoveData, "confirmation", voltmx.i18n.getLocalizedString("bt_yes"), voltmx.i18n.getLocalizedString("bt_no"), "Info", null);
}

function frmPersonResult_confirm_RemoveData(response) {
    if (response) {
        frmPerson_continueToForm();
    } else {
        frmPerson_ClearPersonData();
    }
}

function frmPerson_ClearPersonData() {
    // empty globals
    //preserve nationality
    //var nationalitycode = Global.vars.gCasePersons.nationality;
    //var inddutch = Global.vars.gCasePersons.indicationDutch;
    //var nationalitydesc = Global.vars.gCasePersons.nationalityDesc;
    //preserve document info
    var doctypedesc = Global.vars.gCasePersons.idenDocTypeDesc;
    var doctype = Global.vars.gCasePersons.idenDocType;
    var docnumber = Global.vars.gCasePersons.documentNumber;
    var docCountry = Global.vars.gCasePersons.countryIdenDoc;
    var docCountryDesc = Global.vars.gCasePersons.countryIdenDocDesc;
    //
    Global.vars.gCasePersons = {};
    Global.vars.personInputMethod = "";
    Global.vars.gCasePersons = CaseData_setNewperson();
    // set preserved nationality
    //Global.vars.gCasePersons.nationality = nationalitycode;
    //Global.vars.gCasePersons.indicationDutch = inddutch;
    //Global.vars.gCasePersons.nationalityDesc = nationalitydesc;
    // end set preserved nationality
    //set preserved document info
    Global.vars.gCasePersons.idenDocTypeDesc = doctypedesc;
    Global.vars.gCasePersons.idenDocType = doctype;
    Global.vars.gCasePersons.documentNumber = docnumber;
    Global.vars.gCasePersons.countryIdenDoc = docCountry;
    Global.vars.gCasePersons.countryIdenDocDesc = docCountryDesc;
    frmPerson_continueToForm();
}

function frmPerson_continueToForm() {
    frmPerson_silentDocumentCheck();
    if (Global.vars.continueToFormAfterDataReset == "frmPersonSSN") {
        frmPersonSSN.show();
    } else if (Global.vars.continueToFormAfterDataReset == "frmPersonManualPerson") {
        frmPersonManualPerson.show();
    } else if (Global.vars.continueToFormAfterDataReset == "frmPersonAlternativeSearch") {
        frmPersonAlternativeSearch.show();
    }
}

function frmPerson_setGlobalsPersonDocument() {
    voltmx.print("### frmPerson_setGlobalsPersonDocument");
    Global.vars.gCasePersons.documentNumber = frmPerson.txtDocumentNumber.text;
    if (frmPerson.flcDocumentTypeAdditional.isVisible === true && frmPerson.lbxDocumentTypeAdditional.selectedKey !== undefined && frmPerson.lbxDocumentTypeAdditional.selectedKey != null) {
        Global.vars.gCasePersons.idenDocType = Number(frmPerson.lbxDocumentTypeAdditional.selectedKey);
        Global.vars.gCasePersons.idenDocTypeDesc = frmPerson.lbxDocumentTypeAdditional.selectedKeyValue[1];
    } else {
        if (frmPerson.lbxDocumentType.selectedKey !== undefined && frmPerson.lbxDocumentType.selectedKey != null) {
            Global.vars.gCasePersons.idenDocType = Number(frmPerson.lbxDocumentType.selectedKey);
            Global.vars.gCasePersons.idenDocTypeDesc = frmPerson.lbxDocumentType.selectedKeyValue[1];
        }
    }
    var docMandatory = false;
    var index = Global.vars.additionalDocumentTypes.map(function(e) {
        return e.numbervalue;
    }).indexOf(Global.vars.gCasePersons.idenDocType);
    if (index > -1) {
        docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
    }
    if (docMandatory === true || (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType.toString().startsWith("99") === false)) {
        Global.vars.gCasePersons.countryIdenDocDesc = frmPerson.lblDocumentCountry.text;
    }
}

function frmPerson_onclick_btnBack() {
    voltmx.print("### frmPerson_onclick_btnBack");
    Global.vars.readIDScanned = false;
    if (CaseData.person[Global.vars.gCasePersonsIndex] === undefined || CaseData.person[Global.vars.gCasePersonsIndex].edited === false) {
        if (CaseData.offence !== undefined && CaseData.offence != null) {
            CaseData.offence.person = false;
        }
    }
    if (Global.vars.openedFromResume === true) {
        Global.vars.openedFromResume = false;
        Global.vars.previousForm = Global.vars.openedFromResumePreviousForm;
        frmResume.show();
    } else {
        if (Global.vars.previousForm == "frmResume") {
            frmResume.show();
        } else if (Global.vars.previousForm == "frmRegister") {
            frmRegister.show();
        } else if (Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_registerconcept") && Global.vars.cameToPersonFromForm !== "") {
            if (Global.vars.cameToPersonFromForm == "frmRegisterConcept") {
                frmRegisterConcept.show();
            } else if (Global.vars.cameToPersonFromForm == "frmRegisterResume") {
                frmRegisterResume.show();
            }
        } else if (Global.vars.previousForm == "frmPersonResult") {
            frmPersonResult.show();
        } else if (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") {
            frmRegister.show();
        }
    }
}

function frmPerson_scannerButtonResultCallback(scanResult) {
    //TESTDATA
    scanResult = null;
    scanResult = {
        value: "A345B678"
    };
    //
    voltmx.print("##### frmPerson_scannerButtonResultCallback ");
    frmPerson.txtDocumentNumber.text = scanResult.value;
    voltmx.print("##### frmPerson_scannerButtonResultCallback ");
}

function frmPerson_onEndEditDocumentNumber() {
    Global.vars.gCasePersons.documentNumber = frmPerson.txtDocumentNumber.text;
    frmPerson_togglePersonCharacteristics();
}

function frmPerson_onTextChangeDocumentNumber() {
    //frmLocation.txtDetailHouseletter.text = frmLocation.txtDetailHouseletter.text;
    var _text = frmPerson.txtDocumentNumber.text;
    voltmx.print("### frmPerson_onTextChangeDocumentNumber start text: " + _text);
    if (_text.length > 0) {
        _text = _text.replace(/[^a-zA-Z0-9]/gi, '');
    }
    voltmx.print("### frmPerson_onTextChangeDocumentNumber text: " + _text);
    frmPerson.txtDocumentNumber.text = _text;
}

function frmPerson_togglePersonCharacteristics() {
    var isDocValidated = frmPerson_validateDocumentNumber();
    var isDescriptionValidated = frmPerson_validateOtherDocumentDescription();
    var isKindOfDocValidated = frmPerson_validateKindOfDocument();
    var isDocumentCountryValidated = frmPerson_validatePersonDocumentCountry();
    voltmx.print("### frmPerson_togglePersonCharacteristics isDocValidated: " + isDocValidated);
    voltmx.print("### frmPerson_togglePersonCharacteristics isDescriptionValidated: " + isDescriptionValidated);
    voltmx.print("### frmPerson_togglePersonCharacteristics isKindOfDocValidated: " + isKindOfDocValidated);
    voltmx.print("### frmPerson_togglePersonCharacteristics isDocumentCountryValidated: " + isDocumentCountryValidated);
    if (isDocValidated === true && isDescriptionValidated === true && isKindOfDocValidated === true && isDocumentCountryValidated === true) {
        frmPerson_flcCharacteristics_setVisibility(true);
    } else {
        frmPerson_flcCharacteristics_setVisibility(false);
    }
    //  frmPerson_contentOffset();
}

function frmPerson_validateDocumentNumber() {
    voltmx.print("### frmPerson_validateDocumentNumber Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    voltmx.print("### frmPerson_validateDocumentNumber txtDocumentNumber: " + frmPerson.txtDocumentNumber.text);
    var validate = false;
    var docMandatory = false;
    var index = Global.vars.additionalDocumentTypes.map(function(e) {
        return e.numbervalue;
    }).indexOf(Global.vars.gCasePersons.idenDocType);
    if (index > -1) {
        docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
    }
    voltmx.print("#### frmPerson_validateDocumentNumber docMandatory: " + docMandatory);
    voltmx.print("#### frmPerson_validateDocumentNumber Global.vars.gCasePersons.idenDocType: " + Global.vars.gCasePersons.idenDocType);
    if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null) {
        if (docMandatory === true && (frmPerson.txtDocumentNumber.text !== "" && frmPerson.txtDocumentNumber.text != null)) {
            validate = true;
        } else if (Global.vars.gCasePersons.idenDocType.toString().startsWith("99") === true && docMandatory === false) {
            validate = true;
        } else if (Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType !== 99 && Global.vars.gCasePersons.idenDocType != 20 && Global.vars.gCasePersons.idenDocType !== 0 && (frmPerson.txtDocumentNumber.text !== "" && frmPerson.txtDocumentNumber.text != null)) {
            validate = true;
        } else if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && (Global.vars.gCasePersons.idenDocType === 99 || Global.vars.gCasePersons.idenDocType == 20 || Global.vars.gCasePersons.idenDocType === 0)) {
            validate = true;
        }
    } else if (frmPerson.txtDocumentNumber.text !== undefined && frmPerson.txtDocumentNumber.text != null && frmPerson.txtDocumentNumber.text !== "") {
        validate = true;
    }
    if (validate === true) {
        frmPerson.flcDocumentNumber.skin = flcFieldEdge;
    } else {
        frmPerson.flcDocumentNumber.skin = flcFieldEdgeRed;
    }
    return validate;
}

function frmPerson_validateKindOfDocument() {
    voltmx.print("### frmPerson_validateKindOfDocument Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    var validate = false;
    if (Global.vars.gCasePersons.idenDocType != null) {
        validate = true;
    }
    if (Global.vars.gCasePersons.idenDocType === undefined || Global.vars.gCasePersons.idenDocType === null || Global.vars.gCasePersons.idenDocType === "") {
        frmPerson.lbxDocumentType.selectedKey = null;
    }
    if (validate === true) {
        frmPerson.flcDocumentType.skin = flcFieldEdge;
    } else {
        frmPerson.flcDocumentType.skin = flcFieldEdgeRed;
    }
    return validate;
}

function frmPerson_checkIfValidDocument() {
    voltmx.print("##### frmPerson_checkIfValidDocument");
    if (frmPerson.txtDocumentNumber.text != null && frmPerson.txtDocumentNumber.text !== "") {
        service_GetPersonInfoValidateDocument(frmPerson.txtDocumentNumber.text, frmPerson_checkIfValidDocumentcallback);
    }
}

function frmPerson_checkIfValidDocumentcallback(result) {
    voltmx.print("##### frmPerson_checkIfValidDocumentcallback: " + JSON.stringify(result));
    Global.vars.indDcocumentChecked = true;
    voltmx.application.dismissLoadingScreen();
    if (result.valid === "false") {
        Global.vars.indDcocumentValidated = false;
        alert(result.resultDescription);
    } else if (result.valid === "true") {
        Global.vars.indDcocumentValidated = true;
        alert(result.resultDescription);
    }
}

function frmPerson_showSetText() {
    voltmx.print("### frmPerson_showSetText");
    try {
        //deactivate footer and mainpage
        frmPerson.settext.textarea.TextAreaText.text = frmPerson.documentdescription.lblText.text;
        frmPerson.settext.textarea.TextAreaText.maxTextLength = 200;
        if (frmPerson.documentdescription.lblText.text == "Beschrijving document(en)") {
            frmPerson.settext.textarea.TextAreaText.text = "";
        }
        frmPerson.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmPerson_settext_setVisibility(true);
        frmPerson_showSetText_preAnim();
        frmPerson_showSetText_animationStart();
    } catch (e) {
        voltmx.print("### frmPerson_showSetText error: " + JSON.stringify(e));
    }
}

function frmPerson_showSetText_preAnim() {
    try {
        voltmx.print("### frmPerson_showSetText_preAnim");
        var trans1 = voltmx.ui.makeAffineTransform();
        trans1.scale(0.1, 0.1);
        var trans2 = voltmx.ui.makeAffineTransform();
        trans2.translate(0, 10);
        //frmPerson.settext.flcDetail.transform = trans1;
        //frmPerson.settext.imgPopupLogo1.transform = trans1;
        //frmPerson.settext.flcTextDetails.transform = trans1;
    } catch (e) {
        voltmx.print("### frmPerson_showSetText_preAnim error: " + JSON.stringify(e));
    }
}

function frmPerson_showSetText_arrangeWidgets() {
    try {
        voltmx.print("### frmPerson_showSetText_arrangeWidgets");
        //popup fields
        frmPerson.settext.imgPopupLogo1.isVisible = false;
        frmPerson.settext.flcDetail.isVisible = false;
        frmPerson.settext.flcTextDetails.isVisible = false;
        frmPerson.settext.lbl1.isVisible = false;
        frmPerson.settext.flcFooterSetText.isVisible = false;
        frmPerson_settext_setVisibility(false);
        frmPerson.settext.flcFooterSetText.setEnabled(false);
        frmPerson.settext.forceLayout();
    } catch (e) {
        voltmx.print("### frmPerson_showSetText_preAnim error: " + JSON.stringify(e));
    }
}

function frmPerson_showSetText_animationStart(eventobject) {
    try {
        voltmx.print("### frmPerson_showSetText_animationStart");
        frmPerson_settext_setVisibility(true);
        frmPerson.settext.flcDetail.isVisible = true;
        var trans100 = voltmx.ui.makeAffineTransform();
        trans100.scale(1, 1);
        frmPerson.settext.flcDetail.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans100,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.25
        }, {
            "animationEnd": voltmx.runOnMainThread(frmPerson_showSetText_animLogo)
        });
    } catch (e) {
        voltmx.print("### frmPerson_showSetText_animationStart error: " + JSON.stringify(e));
    }
}

function frmPerson_showSetText_animLogo() {
    try {
        voltmx.print("### frmPerson_showSetText_animLogo");
        frmPerson_showSetText_animOtherWidgets(frmPerson.settext.flcTextDetails);
        frmPerson_showSetText_animOtherWidgets(frmPerson.settext.lbl1);
        frmPerson.settext.imgPopupLogo1.isVisible = true;
        frmPerson.forceLayout();
    } catch (e) {
        voltmx.print("### frmPerson_showSetText_animLogo error: " + JSON.stringify(e));
    }
}

function frmPerson_showSetText_animOtherWidgets(widget) {
    try {
        voltmx.print("### frmPerson_showSetText_animOtherWidgets");
        var trans1 = voltmx.ui.makeAffineTransform();
        trans1.translate(1, 1);
        //trans1.translate(1, -10);
        widget.isVisible = true;
        widget.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans1,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.25
        }, {
            "animationEnd": function() {}
        });
        frmPerson.settext.flcTextDetails.isVisible = true;
        frmPerson.settext.lbl1.isVisible = true;
        frmPerson.settext.flcFooterSetText.isVisible = true;
        frmPerson.settext.flcFooterSetText.setEnabled(true);
        frmPerson.forceLayout();
    } catch (e) {
        voltmx.print("### frmPerson_showSetText_animOtherWidgets error: " + JSON.stringify(e));
    }
}

function frmPerson_showSetText_animLogoBack() {
    try {
        voltmx.print("### frmPerson_showSetText_animLogoBack");
        var trans = voltmx.ui.makeAffineTransform();
        trans.scale(1, 1);
        frmPerson.settext.imgPopupLogo1.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.15
        }, {
            "animationEnd": function() {}
        });
        frmPerson.forceLayout();
    } catch (e) {
        voltmx.print("### frmPerson_showSetText_animLogoBack error: " + JSON.stringify(e));
    }
}

function frmPerson_hideSetText() {
    //activate footer and mainpage
    frmPerson.flcMainPage.setEnabled(true);
    voltmx.print("### flcMainPage enabled");
    //  frmPerson_contentOffset();
    frmPerson_settext_setVisibility(false);
    frmPerson_togglePersonCharacteristics();
}

function frmPerson_setTextDone() {
    voltmx.print("#### frmPerson_setTextDone CaseData text before: " + JSON.stringify(CaseData.text));
    if (frmPerson.settext.textarea.TextAreaText.text != null && frmPerson.settext.textarea.TextAreaText.text !== "") {
        frmPerson.documentdescription.lblText.text = frmPerson.settext.textarea.TextAreaText.text;
        frmPerson.documentdescription.lblText.skin = lblFieldInfo;
    } else {
        frmPerson.documentdescription.lblText.text = "Beschrijving document(en)";
        frmPerson.documentdescription.lblText.skin = lblFieldNotFilled;
    }
    voltmx.print("#### frmPerson_setTextDone Text: " + frmPerson.documentdescription.lblText.text);
    if (frmPerson.documentdescription.lblText.text != null && frmPerson.documentdescription.lblText.text !== "" && frmPerson.documentdescription.lblText.text !== "Beschrijving document(en)") {
        // add record to CaseData.text
        var loctextindex = null;
        for (var p = 0;
            ((CaseData.text) != null) && p < CaseData.text.length; p++) {
            var v = CaseData.text[p];
            if ((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))) { //beschrijving documenten
                voltmx.print("#### frmPerson_setTextDone: Finding officer present: " + v + " index: " + p);
                loctextindex = p;
                break;
            }
        }
        var laddrecord = CaseData_setNewtext();
        laddrecord.inserted = true;
        laddrecord.edited = true;
        laddrecord.type = 3; //beschrijving documenten
        laddrecord.value = "Beschrijving document(en): " + frmPerson.documentdescription.lblText.text;
        if (loctextindex === null) {
            CaseData.text.splice(0, 0, laddrecord);
        } else {
            CaseData.text.splice(loctextindex, 1, laddrecord);
        }
        voltmx.print("#### frmPerson_setTextDone documentdescription.lblText CaseData.text after: " + JSON.stringify(CaseData.text));
    }
    frmPerson_hideSetText();
}

function frmPerson_clearTextAreaText() {
    frmPerson.settext.textarea.TextAreaText.text = "";
    frmPerson.settext.textarea.TextAreaText.setFocus(true);
}

function frmPerson_deleteDocumentDescription() {
    voltmx.print("#### frmPerson_deleteDocumentDescription CaseData text before: " + JSON.stringify(CaseData.text));
    frmPerson.settext.textarea.TextAreaText.text = "";
    frmPerson.documentdescription.lblText.text = "Beschrijving document(en)";
    frmPerson.documentdescription.lblText.skin = lblFieldNotFilled;
    for (var p = 0;
        ((CaseData.text) != null) && p < CaseData.text.length; p++) {
        var v = CaseData.text[p];
        if ((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))) { //beschrijving documenten
            CaseData.text.splice(p, 1);
        }
    }
    voltmx.print("#### frmPerson_deleteDocumentDescription documentdescription.lblText CaseData.text after: " + JSON.stringify(CaseData.text));
    frmPerson_togglePersonCharacteristics();
}

function frmPerson_onclick_butPersonVehicleTypes() {
    frmPersonVehicleTypes.show();
}

function frmPerson_setPersonVehicleType() {
    Utility_setPersonAsVehicleType();
    frmPerson.lblPersonVehicleType.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc;
}

function frmPerson_silentDocumentCheck() {
    voltmx.print("#### frmPerson_silentDocumentCheck Global.vars.checkdocument: " + Global.vars.checkdocument);
    if (Global.vars.checkdocument !== "") {
        if (frmPerson.txtDocumentNumber.text != null && frmPerson.txtDocumentNumber.text !== "") {
            Global.vars.documentTypeCheckable = Global.vars.checkdocument;
            Global.vars.gCasePersons.documentTypeCheckable = Global.vars.documentTypeCheckable;
            service_GetPersonInfoValidateDocumentSilent(frmPerson.txtDocumentNumber.text, frmPerson_checkIfValidDocumentcallbackSilent, frmPerson_checkIfValidDocumenterrorcallbackSilent);
        }
    }
}

function frmPerson_checkIfValidDocumentcallbackSilent(result) {
    voltmx.print("### frmPerson_checkIfValidDocumentcallbackSilent: " + JSON.stringify(result));
    Global.vars.indDcocumentChecked = true;
    Global.vars.gCasePersons.documentNumberChecked = Global.vars.indDcocumentChecked;
    Global.vars.gCasePersons.documentNumberValid = result.valid;
    voltmx.application.dismissLoadingScreen();
    if (result.valid === "false") {
        Global.vars.indDcocumentValidated = false;
    } else if (result.valid === "true") {
        Global.vars.indDcocumentValidated = true;
    }
}

function frmPerson_checkIfValidDocumenterrorcallbackSilent(error) {
    voltmx.print("##### frmPerson_checkIfValidDocumenterrorcallbackSilent: " + JSON.stringify(error));
    voltmx.application.dismissLoadingScreen();
}
//READ ID code
function frmPerson_clearReadIDGlobals() {
    voltmx.print("### frmPerson_clearReadIDGlobals");
    Global.vars.readIDScanned = false;
    Global.vars.readID = {
        MRZ_documentNumber: "",
        MRZ_dateOfBirth: "",
        MRZ_dateOfExpiry: "",
        MRZ_response: null,
        fullname: "",
        birthdate: "",
        birthdateDesc: "",
        birthdateComponents: [],
        yearOfBirth: "",
        documentNumber: "",
        ssn: "",
        surname: "",
        middlename: "",
        givenNames: "",
        idenDocType: "",
        idenDocTypeDesc: "",
        countryIdenDoc: null,
        countryIdenDocDesc: "",
        gender: "",
        genderDesc: "",
        indicationDutch: "",
        nationality: "",
        nationalityDesc: "",
        countryOfBirthDesc: "",
        birthplace: "",
        originalNFCOutPut: {},
        nfcVerificationStatusDS: "",
        nfcVerificationStatusHT: "",
        nfcVerificationStatusCS: "",
        nfcVerificationStatusCSReason: "",
        nfcVerificationStatusAA: "",
        nfcVerificationStatusEACCA: "",
        indMRZ: false, //boolean
        indNFC: false, //boolean
        indManual: false //boolean
    };
    Global.vars.readIDTestSet = null;
}

function frmPerson_clearFields() {
    voltmx.print("#### frmPerson_clearFields");
    frmPerson.lblDocumentType.text = "";
    frmPerson.lblDocumentCountry.text = voltmx.i18n.getLocalizedString("l_country");
    frmPerson.lblDocumentCountry.skin = lblFieldNotFilled;
    frmPerson.txtDocumentNumber.text = "";
    frmPerson.lblDocumentNumber.text = "";
    frmPerson.txtDocumentNumber.text = "";
    frmPerson.lblSSN.text = "";
    frmPerson.lblFullName.text = "";
    frmPerson.lblInitails.text = "";
    frmPerson.lblDateOfBirth.text = "";
    frmPerson.lblPersonNationality.text = "";
    frmPerson.lblCountryOfOrigin.text = "";
    frmPerson.lblMunicipalityOfBirth.text = "";
    frmPerson.lblGender.text = "";
    frmPerson.flcProgressBar.flcProgress.width = 0 + '%';
}

function frmPerson_mrz_scan_android() {
    voltmx.print("### frmPerson_mrz_scan_android");
    var data = {
        action: "mrzScan",
    };
    const packageName = Utility_appPackageName();
    voltmx.print("#### frmPerson_mrz_scan_android packageName: " + packageName);
    var deepLinkUrl = "twynsTest://document.scan/" + "?" + kony.net.urlEncode(JSON.stringify(data));
    if (packageName.toLowerCase().includes("twynsgen")) {
        deepLinkUrl = deepLinkUrl.replace('twynsTest://', "twyns://");
        const isScannerInstalled = Utility_isPackageInstalled("com.twyns.documentscanner");
        voltmx.print("#### frmPerson_mrz_scan_android isScannerInstalled: " + isScannerInstalled);
        if (!isScannerInstalled) {
            alert(voltmx.i18n.getLocalizedString("scanner_app_not_installed"));
        }
    } else {
        const isScannerInstalled = Utility_isPackageInstalled("com.twyns.documentscanner.test");
        voltmx.print("#### frmPerson_mrz_scan_android isScannerInstalled: " + isScannerInstalled);
        if (!isScannerInstalled) {
            alert(voltmx.i18n.getLocalizedString("scanner_app_not_installed"));
        }
    }
    voltmx.print("#### frmPerson_mrz_scan_android deepLinkUrl: " + deepLinkUrl);
    voltmx.application.openURL(deepLinkUrl);
}

function frmPerson_read_nfc_android(isDriverlicense, documentNumber, documentCode, issuingCountryCode, dateOfBirth, dateOfExpiry) {
    var data = {
        "action": "readNFC",
        "isDriverlicense": isDriverlicense,
        "documentNumber": documentNumber,
        "dateOfBirth": dateOfBirth,
        "dateOfExpiry": dateOfExpiry,
        "country": issuingCountryCode
    };
    const packageName = Utility_appPackageName();
    voltmx.print("#### frmPerson_read_nfc_android packageName: " + packageName);
    var deepLinkUrl = "twynsTest://document.scan/" + "?" + kony.net.urlEncode(JSON.stringify(data));
    if (packageName.toLowerCase().includes("twynsgen")) {
        deepLinkUrl = deepLinkUrl.replace('twynsTest://', "twyns://");
    }
    voltmx.print("#### frmPerson_read_nfc_android deepLinkUrl: " + deepLinkUrl);
    voltmx.print("### frmPerson_read_nfc_android data: " + JSON.stringify(data));
    voltmx.application.openURL(deepLinkUrl);
}

function frmPerson_onclick_btnMRZ() {
    voltmx.print("### frmPerson_onclick_btnMZR");
    frmPerson_clearReadIDGlobals();
    frmPerson_clear_labels();
    frmPerson_clearFields();
    frmPerson_deleteDocumentDescription();
    //clear casedata person
    CaseData.person[Global.vars.gCasePersonsIndex] = CaseData_setNewperson();
    if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber === null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vehicle) {
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex] = CaseData_setNewvehicle();
    }
    Global.vars.gCasePersons = JSON.parse(JSON.stringify(CaseData.person[Global.vars.gCasePersonsIndex]));
    MyMRZAlert = null;
    Analytics_logEvent("read_id", "document_scan_launch");
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        //Creates an object of class 'ReadIDiOS'
        var ReadIDiOSObject = new REDLINE.ReadIDiOS();
        //Invokes method 'ReadIDSwiftMRZ' on the object
        ReadIDiOSObject.ReadIDSwiftMRZ(
            /**Function*/
            frmPerson_MRZ_resultcallback);
    } else {
        frmPerson_mrz_scan_android();
        //     var StartReadIDCallObject = new REDLINE.showMRZview(
        // 		/**Function*/ frmPerson_MRZ_resultcallback);
    }
}

function frmPerson_clear_labels() {
    voltmx.print("### frmPerson_clear_labels");
    frmPerson.lblProgressNFC.text = "";
    frmPerson_flcNFCchipSymbol_setVisibility(false);
    frmPerson.lblOutPutMZRDcoument.text = "MRZ gegevens";
    frmPerson.lblOutPutMZRDateOfBirth.text = "";
    frmPerson.lblOutPutMZRDateofExpiry.text = "";
    frmPerson_lblOutPutMZRDateOfBirth_setVisibility(false);
    frmPerson_lblOutPutMZRDateofExpiry_setVisibility(false);
    frmPerson_clearFields();
}

function frmPerson_clear_labelsNFC() {
    voltmx.print("### frmPerson_clear_labelsNFC");
    frmPerson.lblProgressNFC.text = "";
    frmPerson_flcNFCchipSymbol_setVisibility(false);
}

function frmPerson_MRZ_resultcallback(MRZResponse) {
    Global.vars.readIDScanned = true;
    Global.vars.readID.indMRZ = true;
    Global.vars.personInputMethod = "ReadId";
    frmPerson.lblOutPutMZRDateOfBirth.text = "";
    frmPerson.lblOutPutMZRDateofExpiry.text = "";
    voltmx.print("### frmPerson_MRZ_resultcallback MRZResponse: " + JSON.stringify(MRZResponse));
    var mrzcontent = MRZResponse.replace("#MRZDATA#", "");
    var parsedContent;
    try {
        parsedContent = JSON.parse(mrzcontent)[0];
    } catch {
        voltmx.application.dismissLoadingScreen();
        alert(voltmx.i18n.getLocalizedString("l_photoProcessingFailed"));
        return;
    }
    if (parsedContent == null) {
        voltmx.application.dismissLoadingScreen();
        alert(voltmx.i18n.getLocalizedString("l_photoProcessingFailed"));
        return;
    }
    Global.vars.readID.MRZ_response = parsedContent;
    Global.vars.readID.issuingCountryCode = parsedContent.issuingCountryCode === null ? "" : parsedContent.issuingCountryCode;
    voltmx.print("### Global.vars.readID.MRZ_response:" + JSON.stringify(Global.vars.readID.MRZ_response));
    if (parsedContent.ocrResult == "P<UTOMONTGOMERY<WILLIAMS<<LEONARD<JAMES<ALEX\nREADID1235UTO8210128M2808082<<<<<<<<<<<<<<<4") {
        voltmx.print("frmPerson_MRZ_resultcallback want to run testset?");
        frmPerson_showHandleTestReadIDPopup();
    } else {
        voltmx.print("### frmPerson_MRZ_resultcallback MRZResponse: " + JSON.stringify(parsedContent));
        Analytics_logEvent("read_id", "document_scanned");
        if (parsedContent != null) {
            var birthDate = null;
            var expiryDate = null;
            if (parsedContent.dateOfBirthString !== undefined && parsedContent.dateOfBirthString != null && parsedContent.dateOfBirthString !== "") {
                birthDate = parsedContent.dateOfBirthString.match(/.{1,2}/g);
                birthDate[1] = birthDate[1].replace("<<", "01");
                birthDate[1] = birthDate[1].replace("<", "0");
                birthDate[2] = birthDate[2].replace("<<", "01");
                birthDate[2] = birthDate[2].replace("<", "0");
                frmPerson.lblOutPutMZRDateOfBirth.text = "Geboortedatum: " + birthDate[2] + "-" + birthDate[1] + "-" + birthDate[0];
                Global.vars.readID.MRZ_dateOfBirth = parsedContent.dateOfBirthString;
                frmPerson_lblOutPutMZRDateOfBirth_setVisibility(true);
                voltmx.print("### frmPerson_MRZ_resultcallback Global.vars.readID.MRZ_dateOfBirth: " + Global.vars.readID.MRZ_dateOfBirth);
            } else {
                frmPerson_lblOutPutMZRDateOfBirth_setVisibility(false);
            }
            if (parsedContent.dateOfExpiryString !== undefined && parsedContent.dateOfExpiryString != null && parsedContent.dateOfExpiryString !== "") {
                Global.vars.readID.MRZ_dateOfExpiry = parsedContent.dateOfExpiryString;
                voltmx.print("### frmPerson_MRZ_resultcallback Global.vars.readID.MRZ_dateOfExpiry: " + Global.vars.readID.MRZ_dateOfExpiry);
                expiryDate = parsedContent.dateOfExpiryString.match(/.{1,2}/g);
                frmPerson.lblOutPutMZRDateofExpiry.text = "Verloopdatum: " + expiryDate[2] + "-" + expiryDate[1] + "-" + expiryDate[0];
                frmPerson_lblOutPutMZRDateofExpiry_setVisibility(true);
            } else {
                frmPerson_lblOutPutMZRDateofExpiry_setVisibility(false);
            }
            Global.vars.readID.MRZ_documentNumber = parsedContent.documentNumber === undefined ? "" : parsedContent.documentNumber.replace(/</g, "");
            if (parsedContent.documentNumber != null && parsedContent.ocrResult != null && parsedContent.documentNumber == parsedContent.ocrResult && parsedContent.ocrResult.length > 0) {
                parsedContent.documentNumber = parsedContent.ocrResult.substr(6, 10);
                parsedContent.issuingCountryCode = parsedContent.ocrResult.substr(2, 3);
                Global.vars.readID.issuingCountryCode = parsedContent.issuingCountryCode === null ? "" : parsedContent.issuingCountryCode;
                if (parsedContent.issuingCountryCode == "D<<") {
                    parsedContent.issuingCountryCode = "DEU";
                }
            }
            frmPerson.lblOutPutMZRDcoument.text = "Documentnr: " + parsedContent.documentNumber.replace(/</g, "");
            //fill fields with MRZ values
            if (parsedContent.firstName !== undefined && parsedContent.firstName !== "" && parsedContent.lastName !== undefined && parsedContent.lastName !== "") {
                Global.vars.readID.fullname = Utility_CapatilizeNames((parsedContent.firstName + " " + parsedContent.lastName));
                Global.vars.readID.givenNames = Utility_CapatilizeNames(parsedContent.firstName);
                Global.vars.readID.surname = Utility_CapatilizeNames(parsedContent.lastName);
            } else if ((parsedContent.firstName === undefined || parsedContent.firstName === "") && parsedContent.lastName !== undefined && parsedContent.lastName !== "") {
                Global.vars.readID.fullname = Utility_CapatilizeNames(parsedContent.lastName);
                Global.vars.readID.givenNames = "";
                Global.vars.readID.surname = Utility_CapatilizeNames(parsedContent.lastName);
            } else if (parsedContent.firstName !== undefined && parsedContent.firstName !== "" && (parsedContent.lastName === undefined || parsedContent.lastName === "")) {
                Global.vars.readID.fullname = Utility_CapatilizeNames(parsedContent.firstName);
                Global.vars.readID.givenNames = Utility_CapatilizeNames(parsedContent.firstName);
            }
            var birthyear = null;
            if (birthDate != null) {
                if (Number(birthDate[0]) > 30) {
                    birthyear = "19" + birthDate[0];
                } else {
                    birthyear = "20" + birthDate[0];
                }
                voltmx.print("### frmPerson_MRZ_resultcallback birthDate[0]: " + birthDate[0]);
            }
            voltmx.print("### frmPerson_MRZ_resultcallback birthyear: " + birthyear);
            if (birthyear != null) {
                birthDate[1] = birthDate[1].replace("<<", "01");
                birthDate[1] = birthDate[1].replace("<", "0");
                birthDate[2] = birthDate[2].replace("<<", "01");
                birthDate[2] = birthDate[2].replace("<", "0");
                var dateinterperted = birthyear + "-" + birthDate[1] + "-" + birthDate[2];
                voltmx.print("### frmPerson_MRZ_resultcallback dateinterperted: " + dateinterperted);
                dateinterperted = new Date(dateinterperted);
                voltmx.print("### frmPerson_MRZ_resultcallback dateinterperted javascript date: " + dateinterperted);
                Global.vars.readID.birthdate = dateinterperted; //751124
                Global.vars.readID.birthdateDesc = Utility_getLocaleShortDateString(dateinterperted); //751124
                Global.vars.readID.birthdateComponents = [dateinterperted.getDate(), (dateinterperted.getMonth() + 1), dateinterperted.getFullYear(), 0, 0, 0];
                Global.vars.readID.yearOfBirth = dateinterperted.getFullYear(); //751124
            }
            Global.vars.readID.documentNumber = parsedContent.documentNumber === undefined ? "" : parsedContent.documentNumber.replace(/</g, "");
            voltmx.print("### frmPerson_MRZ_resultcallback issuing country: " + parsedContent.issuingCountryCode);
            if (parsedContent.documentCode !== undefined) {
                Global.vars.readID.idenDocType = parsedContent.documentCode.match(/[a-zA-Z]+/g);
                if (parsedContent.issuingCountryCode !== undefined && parsedContent.issuingCountryCode == "NLD") {
                    if (Global.vars.readID.idenDocType == "P") {
                        Global.vars.readID.idenDocTypeDesc = "paspoort";
                    } else if (Global.vars.readID.idenDocType == "I") {
                        Global.vars.readID.idenDocTypeDesc = "identiteitskaart";
                    } else if (Global.vars.readID.idenDocType == "IR") {
                        Global.vars.readID.idenDocTypeDesc = "verblijfsdocument (documenttype I t/m IV en EU/EER)";
                        Global.vars.readID.ssn = "";
                    } else if (Global.vars.readID.idenDocType == "IT") {
                        Global.vars.readID.idenDocTypeDesc = "verblijfsdocument (documenttype I t/m IV en EU/EER)";
                        Global.vars.readID.ssn = "";
                    } else if (Global.vars.readID.idenDocType == "IW") {
                        Global.vars.readID.idenDocTypeDesc = "W-document";
                        Global.vars.readID.ssn = "";
                    } else if ((Global.vars.readID.idenDocType === undefined || Global.vars.readID.idenDocType === "") && (Global.vars.readID.MRZ_dateOfBirth === null || Global.vars.readID.MRZ_dateOfBirth === "")) {
                        Global.vars.readID.idenDocTypeDesc = "rijbewijs";
                    } else if (Global.vars.readID.idenDocType !== undefined && (Global.vars.readID.MRZ_response.kindOfData != null && Global.vars.readID.MRZ_response.kindOfData == "Europees Rijbewijs")) {
                        Global.vars.readID.idenDocTypeDesc = "rijbewijs";
                    } else {
                        Global.vars.readID.idenDocTypeDesc = Global.vars.readID.idenDocType;
                    }
                    voltmx.print("### frmPerson_resultcallbackNFC Global.vars.readID.idenDocTypeDesc: " + Global.vars.readID.idenDocTypeDesc);
                    frmPerson_setDocumentType(Global.vars.readID.idenDocTypeDesc);
                } else {
                    var documentCode = "";
                    if ((Global.vars.readID.idenDocType === undefined || Global.vars.readID.idenDocType === "") && (Global.vars.readID.MRZ_dateOfBirth === null || Global.vars.readID.MRZ_dateOfBirth === "")) {
                        documentCode = "RBW";
                    } else if (Global.vars.readID.idenDocType !== undefined && (Global.vars.readID.MRZ_response.kindOfData != null && Global.vars.readID.MRZ_response.kindOfData == "Europees Rijbewijs")) {
                        documentCode = "RBW";
                    } else {
                        documentCode = Global.vars.readID.idenDocType;
                    }
                    frmPerson_setDocumentTypeOther(documentCode);
                }
            }
            voltmx.print("### frmPerson_MRZ_resultcallback Global.vars.readID.idenDocTypeDesc: " + Global.vars.readID.idenDocTypeDesc);
            //opzoeken in country Country where iso31661alpha3 = 'NLD';
            if (parsedContent.issuingCountryCode !== undefined) {
                Utility_findCountryByIso31661alpha3(parsedContent.issuingCountryCode, CaseData.time.dateComponents, frmPerson_findCountryIdenDocByIso31661alpha3Callback);
            }
            if (parsedContent.gender == null) {
                parsedContent.gender = "UNKNOWN";
            }
            // Do not parse anything else then existing gender domain values
            Global.vars.readID.genderDesc = genderMap[parsedContent.gender] || genderMap["UNKNOWN"];
            //
            for (var i in Global.vars.gPersonGenderResult) {
                var v = Global.vars.gPersonGenderResult[i];
                if (v.descripton == Global.vars.readID.genderDesc) {
                    Global.vars.readID.gender = v.number_value;
                }
            }
            //Nationality mist de iso3166alpha3 code!!!!!!!!!
            Global.vars.readID.nationality = parsedContent.nationality === undefined ? null : parsedContent.nationality; //check
            Global.vars.readID.nationalityDesc = parsedContent.nationality === undefined ? null : parsedContent.nationality;
            //Moet dus uitgezocht worden!!!!!!!!!!
            if (parsedContent.nationality == "D<<") {
                parsedContent.nationality = "DEU";
            }
            if (parsedContent.nationality !== undefined && parsedContent.nationality == "NLD") { //uitzoeken!!!!
                var surname = Global.vars.readID.surname.match(/\S*$/)[0];
                Global.vars.readID.middlename = Global.vars.readID.surname.replace(surname, ""); //alles voor de laatste spatie
                Global.vars.readID.surname = Utility_CapatilizeNames(surname);
                Global.vars.readID.indicationDutch = true;
                Global.vars.checkedDutch = "done";
                Global.vars.readID.nationality = 1;
                Global.vars.readID.nationalityDesc = "Nederlandse";
            } else {
                Global.vars.readID.indicationDutch = false;
                Global.vars.readID.middlename = "";
                Global.vars.readID.surname = parsedContent.lastName === undefined ? "" : Utility_CapatilizeNames(parsedContent.lastName);
                if (parsedContent.nationality !== undefined) {
                    voltmx.print("frmPerson_MRZ_resultcallback find nationality through country");
                    Utility_findCountryByIso31661alpha3(parsedContent.nationality, CaseData.time.dateComponents, frmPerson_findNationalityByIso31661alpha3Callback);
                } else {
                    Global.vars.readID.nationality = 0;
                    Global.vars.readID.nationalityDesc = "Onbekend";
                }
            }
            //frmPerson_setDocumentType(Global.vars.readID.idenDocTypeDesc);
            voltmx.print("### frmPerson_MRZ_resultcallback ReadID globals: " + JSON.stringify(Global.vars.readID));
            if (parsedContent.isCisCompositeCheckDigitCorrect === true) {
                voltmx.print("### frmPerson_MRZ_resultcallback Check digit is ok");
                frmPerson_enableNFCButton();
                if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
                    //ios
                    frmPerson_onclick_btnNFCID();
                } else {
                    voltmx.print("### frmPerson_MRZ_resultcallback android scanNFCTimer");
                    try {
                        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === false) {
                            voltmx.print("### frmPerson_onclick_btnNFCID android cancel scanNFCTimer");
                            voltmx.timer.cancel("scanNFCTimer");
                            voltmx.timer.schedule("scanNFCTimer", frmPerson_onclick_btnNFCID, 1, false);
                        }
                    } catch (e) {
                        frmPerson_onclick_btnNFCID();
                    }
                }
            } else if ((parsedContent.isCisCompositeCheckDigitCorrect === undefined || parsedContent.isCisCompositeCheckDigitCorrect === null) && (parsedContent.documentNumberCheckDigit !== undefined && parsedContent.documentNumberCheckDigit != null)) {
                voltmx.print("### frmPerson_MRZ_resultcallback Check the Check digit");
                var checkdigit = frmPerson_checkdigitsCalculator(parsedContent.documentNumber.replace(/</g, ""));
                if (Number(checkdigit) == Number(parsedContent.documentNumberCheckDigit)) {
                    voltmx.print("### frmPerson_MRZ_resultcallback Checked check digit is ok");
                    frmPerson_enableNFCButton();
                    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
                        //ios
                        frmPerson_onclick_btnNFCID();
                    } else {
                        voltmx.print("### frmPerson_MRZ_resultcallback android scanNFCTimer");
                        try {
                            if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === false) {
                                voltmx.print("### frmPerson_onclick_btnNFCID android cancel scanNFCTimer");
                                voltmx.timer.cancel("scanNFCTimer");
                                voltmx.timer.schedule("scanNFCTimer", frmPerson_onclick_btnNFCID, 1, false);
                            }
                        } catch (e) {
                            frmPerson_onclick_btnNFCID();
                        }
                    }
                } else {
                    voltmx.print("### frmPerson_MRZ_resultcallback Checked check digit is NOT ok");
                    if (MyMRZAlert === null) {
                        MyMRZAlert = alert("Gescande code waarschijnlijk niet goed herkend, probeer het nog eens of probeer toch de nfc te lezen.");
                    }
                    frmPerson_enableNFCButton();
                }
            } else {
                voltmx.print("### frmPerson_MRZ_resultcallback NO check");
                if (MyMRZAlert === null) {
                    MyMRZAlert = alert("Gescande code waarschijnlijk niet goed herkend, probeer het nog eens of probeer toch de nfc te lezen.");
                }
                frmPerson_enableNFCButton();
            }
        }
    }
}

function frmPerson_MRZRunTestSet() {
    voltmx.print("### Global.vars.readID.MRZ_response: " + Global.vars.readID.MRZ_response);
    var parsedContent = Global.vars.readID.MRZ_response;
    if (Global.vars.readIDTestSet != null) {
        parsedContent = Global.vars.readIDTestSet.MRZ_response;
        Global.vars.readID = Global.vars.readIDTestSet;
        voltmx.print("### Global.vars.readIDTestSet.MRZ_response: " + Global.vars.readIDTestSet.MRZ_response);
    }
    voltmx.print("### frmPerson_MRZRunTestSet MRZResponse: " + JSON.stringify(parsedContent));
    if (parsedContent != null) {
        var birthDate = null;
        var expiryDate = null;
        if (parsedContent.dateOfBirthString !== undefined && parsedContent.dateOfBirthString != null && parsedContent.dateOfBirthString !== "") {
            birthDate = parsedContent.dateOfBirthString.match(/.{1,2}/g);
            birthDate[1] = birthDate[1].replace("<<", "01");
            birthDate[1] = birthDate[1].replace("<", "0");
            birthDate[2] = birthDate[2].replace("<<", "01");
            birthDate[2] = birthDate[2].replace("<", "0");
            frmPerson.lblOutPutMZRDateOfBirth.text = "Geboortedatum: " + birthDate[2] + "-" + birthDate[1] + "-" + birthDate[0];
            Global.vars.readID.MRZ_dateOfBirth = parsedContent.dateOfBirthString;
            frmPerson_lblOutPutMZRDateOfBirth_setVisibility(true);
        } else {
            frmPerson_lblOutPutMZRDateOfBirth_setVisibility(false);
        }
        if (parsedContent.dateOfExpiryString !== undefined && parsedContent.dateOfExpiryString != null && parsedContent.dateOfExpiryString !== "") {
            expiryDate = parsedContent.dateOfExpiryString.match(/.{1,2}/g);
            frmPerson.lblOutPutMZRDateofExpiry.text = "Verloopdatum: " + expiryDate[2] + "-" + expiryDate[1] + "-" + expiryDate[0];
            Global.vars.readID.MRZ_dateOfExpiry = parsedContent.dateOfExpiryString;
            voltmx.print("### frmPerson_MRZRunTestSet Global.vars.readID.MRZ_dateOfExpiry: " + Global.vars.readID.MRZ_dateOfExpiry);
            frmPerson_lblOutPutMZRDateofExpiry_setVisibility(true);
        } else {
            frmPerson_lblOutPutMZRDateofExpiry_setVisibility(false);
        }
        frmPerson.lblOutPutMZRDcoument.text = "Documentnr: " + parsedContent.documentNumber.replace(/</g, "");
        Global.vars.readID.MRZ_documentNumber = parsedContent.documentNumber === undefined ? "" : parsedContent.documentNumber.replace(/</g, "");
        //fill fields with MRZ values
        if (parsedContent.firstName !== undefined && parsedContent.lastName !== undefined) {
            Global.vars.readID.fullname = Utility_CapatilizeNames((parsedContent.firstName + " " + parsedContent.lastName));
            Global.vars.readID.givenNames = Utility_CapatilizeNames(parsedContent.firstName);
            Global.vars.readID.surname = Utility_CapatilizeNames(parsedContent.lastName);
        } else if (parsedContent.firstName === undefined && parsedContent.lastName !== undefined) {
            Global.vars.readID.fullname = Utility_CapatilizeNames(parsedContent.lastName);
            Global.vars.readID.givenNames = "";
            Global.vars.readID.surname = Utility_CapatilizeNames(parsedContent.lastName);
        } else if (parsedContent.firstName !== undefined && parsedContent.lastName === undefined) {
            Global.vars.readID.fullname = Utility_CapatilizeNames(parsedContent.firstName);
            Global.vars.readID.givenNames = Utility_CapatilizeNames(parsedContent.firstName);
        }
        var birthyear = null;
        if (birthDate != null) {
            if (Number(birthDate[0]) > 30) {
                birthyear = "19" + birthDate[0];
            } else {
                birthyear = "20" + birthDate[0];
            }
            voltmx.print("### frmPerson_MRZRunTestSet birthDate[0]: " + birthDate[0]);
        }
        voltmx.print("### frmPerson_MRZRunTestSet birthyear: " + birthyear);
        if (birthyear != null) {
            birthDate[1] = birthDate[1].replace("<<", "01");
            birthDate[1] = birthDate[1].replace("<", "0");
            birthDate[2] = birthDate[2].replace("<<", "01");
            birthDate[2] = birthDate[2].replace("<", "0");
            var dateinterperted = birthyear + "-" + birthDate[1] + "-" + birthDate[2];
            voltmx.print("### frmPerson_MRZRunTestSet dateinterperted: " + dateinterperted);
            dateinterperted = new Date(dateinterperted);
            Global.vars.readID.birthdate = dateinterperted; //751124
            Global.vars.readID.birthdateDesc = Utility_getLocaleShortDateString(dateinterperted); //751124
            Global.vars.readID.birthdateComponents = [dateinterperted.getDate(), (dateinterperted.getMonth() + 1), dateinterperted.getFullYear(), 0, 0, 0];
            Global.vars.readID.yearOfBirth = dateinterperted.getFullYear(); //751124
        }
        Global.vars.readID.documentNumber = parsedContent.documentNumber === undefined ? "" : parsedContent.documentNumber.replace(/</g, "");
        voltmx.print("### frmPerson_MRZRunTestSet issuing country: " + parsedContent.issuingCountryCode);
        if (parsedContent.documentCode !== undefined) {
            Global.vars.readID.idenDocType = parsedContent.documentCode.match(/[a-zA-Z]+/g);
            if (parsedContent.issuingCountryCode !== undefined && parsedContent.issuingCountryCode == "NLD") {
                if (Global.vars.readID.idenDocType == "P") {
                    Global.vars.readID.idenDocTypeDesc = "paspoort";
                } else if (Global.vars.readID.idenDocType == "I") {
                    Global.vars.readID.idenDocTypeDesc = "identiteitskaart";
                } else if (Global.vars.readID.idenDocType == "IR") {
                    Global.vars.readID.idenDocTypeDesc = "verblijfsdocument (documenttype I t/m IV en EU/EER)";
                    Global.vars.readID.ssn = "";
                } else if (Global.vars.readID.idenDocType == "IT") {
                    Global.vars.readID.idenDocTypeDesc = "verblijfsdocument (documenttype I t/m IV en EU/EER)";
                    Global.vars.readID.ssn = "";
                } else if (Global.vars.readID.idenDocType == "IW") {
                    Global.vars.readID.idenDocTypeDesc = "W-document";
                    Global.vars.readID.ssn = "";
                } else if ((Global.vars.readID.idenDocType === undefined || Global.vars.readID.idenDocType === "") && (Global.vars.readID.MRZ_dateOfBirth === null || Global.vars.readID.MRZ_dateOfBirth === "")) {
                    Global.vars.readID.idenDocTypeDesc = "rijbewijs";
                } else if (Global.vars.readID.idenDocType !== undefined && (Global.vars.readID.MRZ_response.kindOfData != null && Global.vars.readID.MRZ_response.kindOfData == "Europees Rijbewijs")) {
                    Global.vars.readID.idenDocTypeDesc = "rijbewijs";
                } else {
                    Global.vars.readID.idenDocTypeDesc = Global.vars.readID.idenDocType;
                }
                voltmx.print("### frmPerson_MRZRunTestSet Global.vars.readID.idenDocTypeDesc: " + Global.vars.readID.idenDocTypeDesc);
                frmPerson_setDocumentType(Global.vars.readID.idenDocTypeDesc);
            } else {
                var documentCode = "";
                if ((Global.vars.readID.idenDocType === undefined || Global.vars.readID.idenDocType === "") && (Global.vars.readID.MRZ_dateOfBirth === null || Global.vars.readID.MRZ_dateOfBirth === "")) {
                    documentCode = "RBW";
                } else if (Global.vars.readID.idenDocType !== undefined && (Global.vars.readID.MRZ_response.kindOfData != null && Global.vars.readID.MRZ_response.kindOfData == "Europees Rijbewijs")) {
                    documentCode = "RBW";
                } else {
                    documentCode = Global.vars.readID.idenDocType;
                }
                frmPerson_setDocumentTypeOther(documentCode);
            }
        }
        voltmx.print("### frmPerson_MRZRunTestSet Global.vars.readID.idenDocTypeDesc: " + Global.vars.readID.idenDocTypeDesc);
        //opzoeken in country Country where iso31661alpha3 = 'NLD';
        if (parsedContent.issuingCountryCode !== undefined) {
            if (parsedContent.issuingCountryCode == "D<<" || parsedContent.issuingCountryCode == "UTO") {
                parsedContent.issuingCountryCode = "DEU";
            }
            Utility_findCountryByIso31661alpha3(parsedContent.issuingCountryCode, CaseData.time.dateComponents, frmPerson_findCountryIdenDocByIso31661alpha3Callback);
        }
        if (parsedContent.gender == null) {
            parsedContent.gender = "UNKNOWN";
        }
        // Do not parse anything else then existing gender domain values
        Global.vars.readID.genderDesc = genderMap[parsedContent.gender] || genderMap["UNKNOWN"];
        //
        for (var i in Global.vars.gPersonGenderResult) {
            var v = Global.vars.gPersonGenderResult[i];
            if (v.descripton == Global.vars.readID.genderDesc) {
                Global.vars.readID.gender = v.number_value;
            }
        }
        //Nationality mist de iso3166alpha3 code!!!!!!!!!
        Global.vars.readID.nationality = parsedContent.nationality === undefined ? null : parsedContent.nationality; //check
        Global.vars.readID.nationalityDesc = parsedContent.nationality === undefined ? null : parsedContent.nationality;
        //Moet dus uitgezocht worden!!!!!!!!!!
        if (parsedContent.nationality == "D<<") {
            parsedContent.nationality = "DEU";
        }
        if (parsedContent.nationality !== undefined && parsedContent.nationality == "NLD") { //uitzoeken!!!!
            var surname = Global.vars.readID.surname.match(/\S*$/)[0];
            Global.vars.readID.middlename = Global.vars.readID.surname.replace(surname, ""); //alles voor de laatste spatie
            Global.vars.readID.surname = Utility_CapatilizeNames(surname);
            Global.vars.readID.indicationDutch = true;
            Global.vars.checkedDutch = "done";
            Global.vars.readID.nationality = 1;
            Global.vars.readID.nationalityDesc = "Nederlandse";
        } else {
            Global.vars.readID.indicationDutch = false;
            Global.vars.readID.middlename = "";
            Global.vars.readID.surname = parsedContent.lastName === undefined ? "" : Utility_CapatilizeNames(parsedContent.lastName);
            if (parsedContent.nationality !== undefined) {
                voltmx.print("frmPerson_MRZ_resultcallback find nationality through country");
                Utility_findCountryByIso31661alpha3(parsedContent.nationality, CaseData.time.dateComponents, frmPerson_findNationalityByIso31661alpha3Callback);
            } else {
                Global.vars.readID.nationality = 0;
                Global.vars.readID.nationalityDesc = "Onbekend";
            }
        }
        //frmPerson_setDocumentType(Global.vars.readID.idenDocTypeDesc);
        voltmx.print("### frmPerson_MRZRunTestSet ReadID globals: " + JSON.stringify(Global.vars.readID));
        if (parsedContent.documentNumber == "B100459900" || parsedContent.documentNumber == "241453110") {
            voltmx.print("### frmPerson_MRZRunTestSet no nfc data available in Belgium testset");
            frmPerson_flcNoChip_setVisibility(true);
        } else if (parsedContent.isCisCompositeCheckDigitCorrect === true) {
            voltmx.print("### frmPerson_MRZRunTestSet Check digit is ok");
            if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
                //ios
                frmPerson_onclick_btnNFCID();
            } else {
                frmPerson_onclick_btnNFCID();
            }
            frmPerson_enableNFCButton();
        } else if ((parsedContent.isCisCompositeCheckDigitCorrect === undefined || parsedContent.isCisCompositeCheckDigitCorrect === null) && (parsedContent.documentNumberCheckDigit !== undefined && parsedContent.documentNumberCheckDigit != null)) {
            voltmx.print("### frmPerson_MRZRunTestSet Check the Check digit");
            var checkdigit = frmPerson_checkdigitsCalculator(parsedContent.documentNumber.replace(/</g, ""));
            if (Number(checkdigit) == Number(parsedContent.documentNumberCheckDigit)) {
                voltmx.print("### frmPerson_MRZRunTestSet Checked check digit is ok");
                if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
                    //ios
                    frmPerson_onclick_btnNFCID();
                } else {
                    frmPerson_onclick_btnNFCID();
                }
                frmPerson_enableNFCButton();
            } else {
                alert("Gescande code waarschijnlijk niet goed herkend, probeer het nog eens of probeer toch de nfc te lezen.");
                frmPerson_enableNFCButton();
            }
        } else {
            alert("Gescande code waarschijnlijk niet goed herkend, probeer het nog eens of probeer toch de nfc te lezen.");
            frmPerson_enableNFCButton();
        }
    }
}

function frmPerson_findCountryIdenDocByIso31661alpha3Callback(result) {
    voltmx.print("### frmPerson_findCountryIdenDocByIso31661alpha3Callback: " + JSON.stringify(result));
    if (result.length > 0) {
        var country = result[0];
        Global.vars.readID.countryIdenDocDesc = country.description;
        Global.vars.readID.countryIdenDoc = country.code;
    } else {
        Global.vars.readID.countryIdenDocDesc = null;
        Global.vars.readID.countryIdenDoc = null;
    }
}

function frmPerson_findNationalityByIso31661alpha3Callback(result) {
    voltmx.print("### frmPerson_findNationalityByIso31661alpha3Callback country result: " + JSON.stringify(result));
    if (result.length > 0) {
        var country = result[0];
        Global.vars.readID.nationality = country.nny_code;
        Utility_findNationalityByCode(country.nny_code, CaseData.time.dateComponents, frmPerson_findNationalityByCountryCodeCallback);
    } else {
        Global.vars.readID.nationality = 0;
        Global.vars.readID.nationalityDesc = "Onbekend";
    }
}

function frmPerson_findNationalityByCountryCodeCallback(result) {
    voltmx.print("### frmPerson_findNationalityByCountryCodeCallback nationality result: " + JSON.stringify(result));
    if (result.length > 0) {
        var nationality = result[0];
        Global.vars.readID.nationalityDesc = nationality.description;
    } else {
        Global.vars.readID.nationality = 0;
        Global.vars.readID.nationalityDesc = "Onbekend";
    }
}

function frmPerson_enableNFCButton() {
    frmPerson_flcReadNFC_setVisibility(true);
    frmPerson_flcNoChip_setVisibility(true);
    frmPerson.flcScanMRZ.width = "48%";
    frmPerson.flcScanMRZ.centerX = "25%";
}

function frmPerson_disableNFCButton() {
    frmPerson_flcReadNFC_setVisibility(false);
    frmPerson_flcNoChip_setVisibility(false);
    frmPerson.flcScanMRZ.width = "96%";
    frmPerson.flcScanMRZ.centerX = "50%";
}

function frmPerson_onclick_btnNFCID() {
    try {
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === false) {
            voltmx.print("### frmPerson_onclick_btnNFCID android cancel scanNFCTimer");
            voltmx.timer.cancel("scanNFCTimer");
        }
    } catch (e) {}
    voltmx.print("### frmPerson_onclick_btnNFCID documentNumber: " + Global.vars.readID.MRZ_documentNumber);
    voltmx.print("### frmPerson_onclick_btnNFCID dateOfBirth: " + Global.vars.readID.MRZ_dateOfBirth);
    voltmx.print("### frmPerson_onclick_btnNFCID dateOfExpiry: " + Global.vars.readID.MRZ_dateOfExpiry);
    if (Global.vars.readID.MRZ_documentNumber != null && Global.vars.readID.MRZ_documentNumber !== "") {
        var driverlicense = false;
        frmPerson_clear_labelsNFC();
        if (Global.vars.readID.MRZ_dateOfBirth != null && Global.vars.readID.MRZ_dateOfBirth !== "" && Global.vars.readID.MRZ_dateOfExpiry != null && Global.vars.readID.MRZ_dateOfExpiry !== "") {
            driverlicense = false;
        } else {
            driverlicense = true;
        }
        frmPerson.flcProgress.width = 5 + '%';
        frmPerson.lblProgressNFC.text = "Houd het document tegen de onderkant van de telefoon als deze een chip bevat.";
        frmPerson_flcNFCchipSymbol_setVisibility(true);
        frmPerson_clearFields();
        //TESTSET injection NFC input
        if (Global.vars.readIDTestSet != null) {
            frmPerson_resultcallbackNFC("#ALL#");
        }
        //END OF TESTSET
        else {
            if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
                //ios
                //Creates an object of class 'ReadIDiOS'
                voltmx.print("### frmPerson_onclick_btnNFCID start NFC iOS");
                var ReadIDiOSObject = new REDLINE.ReadIDiOS();
                //Invokes method 'ReadIDSwiftNFC' on the object
                ReadIDiOSObject.ReadIDSwiftNFC(
                    /**Function*/
                    frmPerson_resultcallbackNFC,
                    /**Boolean*/
                    driverlicense,
                    /**String*/
                    Global.vars.readID.MRZ_documentNumber,
                    /**String*/
                    Global.vars.readID.MRZ_dateOfBirth,
                    /**String*/
                    Global.vars.readID.MRZ_dateOfExpiry);
            } else {
                voltmx.print("### frmPerson_onclick_btnNFCID start NFC Android");
                frmPerson_read_nfc_android(driverlicense, Global.vars.readID.MRZ_documentNumber, Global.vars.readID.MRZ_documentCode, Global.vars.readID.issuingCountryCode, Global.vars.readID.MRZ_dateOfBirth, Global.vars.readID.MRZ_dateOfExpiry);
                //             var StartReadIDCallObject = new REDLINE.startNFC(
                //               driverlicense,//driverlicense ja of nee
                //                   Global.vars.readID.MRZ_documentNumber,
                //                   Global.vars.readID.MRZ_dateOfBirth,
                //                   Global.vars.readID.MRZ_dateOfExpiry,
                //                   /**Function*/ frmPerson_resultcallbackNFC);
            }
        }
    } else {
        alert("Scan eerst de code met de Scan MRZ knop");
        frmPerson_disableNFCButton();
    }
}
progress = 6.7;

function frmPerson_resultcallbackNFC(response1) {
    voltmx.print("### frmPerson_resultcallbackNFC: " + JSON.stringify(response1));
    if (response1.startsWith("#Progress#")) {
        var nfcText = "";
        if (response1 == "#Progress#Tag Found") {
            nfcText = "Tag gevonden";
        } else if (response1 == "#Progress#Start reading") {
            nfcText = "Lezen gestart";
        } else if (response1 == "#Progress#Start access") {
            nfcText = "Beveiliging document checken";
        } else if (response1 == "#Progress#Finished access, start data read") {
            nfcText = "Lezen persoonsinformatie";
        } else if (response1 == "#Progress#Document read done") {
            nfcText = "Document gelezen";
        } else if (response1 == "#Progress#Data processing failed") {
            nfcText = "Document lezen mislukt, probeer het opnieuw";
        }
        if (nfcText !== "") {
            frmPerson.lblProgressNFC.text = nfcText;
            frmPerson_flcNFCchipSymbol_setVisibility(false);
        }
        progress = progress + 6.7;
        if (progress > 100) {
            progress = 100;
        }
        frmPerson.flcProgressBar.flcProgress.width = progress + '%';
    } else if (response1.startsWith("#NODATA#")) {
        voltmx.application.dismissLoadingScreen();
        alert("Geen data kunnen uitlezen controleer het documentnummer: " + Global.vars.readID.documentNumber + ". Klopt het documentnummer begin dan opnieuw met de NFC actie, anders begin opnieuw met scan.");
    } else if (response1.startsWith("#ALL#")) {
        var allcontent = response1.replace("#ALL#", "");
        Global.vars.readID.indNFC = true;
        progress = 6.7;
        var parsedContent = {};
        //TESTSET injection NFC input
        if (Global.vars.readIDTestSet != null) {
            parsedContent = Global.vars.readIDTestSet.originalNFCOutPut;
        }
        //END OF TESTSET
        else {
            parsedContent = JSON.parse(allcontent);
            parsedContent = parsedContent[0];
        }
        Global.vars.readID.originalNFCOutPut = parsedContent;
        Global.vars.readID.nfcVerificationStatusAA = parsedContent.nfcVerificationStatusAA;
        Global.vars.readID.nfcVerificationStatusCS = parsedContent.nfcVerificationStatusCS;
        Global.vars.readID.nfcVerificationStatusCSReason = parsedContent.nfcVerificationStatusCSReason;
        Global.vars.readID.nfcVerificationStatusDS = parsedContent.nfcVerificationStatusDS;
        Global.vars.readID.nfcVerificationStatusEACCA = parsedContent.nfcVerificationStatusEACCA;
        Global.vars.readID.nfcVerificationStatusHT = parsedContent.nfcVerificationStatusHT;
        voltmx.print("### frmPerson_resultcallbackNFC parsedContent: " + JSON.stringify(parsedContent));
        voltmx.print("### frmPerson_resultcallbackNFC name: " + parsedContent.name);
        var documentTampered = false;
        //nfc verification checks
        //     if(parsedContent.nfcVerificationStatusDS !== "" || parsedContent.nfcVerificationStatusHT !== ""){
        //       if(parsedContent.nfcVerificationStatusDS == "PRESENT_FAILED" || parsedContent.nfcVerificationStatusHT == "PRESENT_FAILED"){
        //         alert("Er is iets niet in orde met dit document");
        //         documentTampered = true;
        //       }
        //     }
        //     if(parsedContent.nfcVerificationStatusAA !== "" || parsedContent.nfcVerificationStatusEACCA !== ""){
        //       if(parsedContent.nfcVerificationStatusAA == "PRESENT_FAILED" && parsedContent.nfcVerificationStatusEACCA == "PRESENT_FAILED"){
        //         alert("Er is iets niet in orde met dit document");
        //         documentTampered = true;
        //       }
        //       else if(parsedContent.nfcVerificationStatusAA == "PRESENT_FAILED" && parsedContent.nfcVerificationStatusEACCA == "NOT_PRESENT"){
        //         voltmx.print("### 1 present en die slaagt niet, dan nog een keer proberen (max 3)");
        //         alert("Security check niet geslaagd, probeer het nog eens. Als u na meerdere keren proberen nog deze melding krijgt dan is er mogelijk iets mis met het document");
        //       	documentTampered = true;
        //       }else if(parsedContent.nfcVerificationStatusAA == "NOT_PRESENT" && parsedContent.nfcVerificationStatusEACCA == "PRESENT_FAILED"){
        //         voltmx.print("### 1 present en die slaagt niet, dan nog een keer proberen (max 3)");
        //         alert("Security check niet geslaagd, probeer het nog eens. Als u na meerdere keren proberen nog deze melding krijgt dan is er mogelijk iets mis met het document");
        //       	documentTampered = true;
        //       }
        //     }
        //     if(parsedContent.nfcVerificationStatusCS === "PRESENT_FAILED" && parsedContent.nfcVerificationStatusCSReason === "CERTIFICATE_EXPIRED"){
        //       alert("Dit document is verlopen");
        //       documentTampered = true;
        //     }
        if (documentTampered === false) {
            voltmx.print("### frmPerson_resultcallbackNFC document not tampered");
            Global.vars.readID.fullname = Utility_CapatilizeNames(parsedContent.name);
            Global.vars.readID.givenNames = Utility_CapatilizeNames(parsedContent.secondaryIdentifier);
            Global.vars.readID.surname = Utility_CapatilizeNames(parsedContent.primaryIdentifier);
            //birthyear + "-" + birthDate[1] + "-" + birthDate[2];
            var interpretedDate = "";
            try {
                if (parsedContent.interpretedDateOfBirth != null) {
                    interpretedDate = parsedContent.interpretedDateOfBirth.replace(/(\d{2}).(\d{2}).(\d{4})/, "$3-$2-$1");
                    voltmx.print("### frmPerson_resultcallbackNFC interpretedDate: " + interpretedDate);
                    var date = new Date(interpretedDate);
                    Global.vars.readID.birthdate = date; //751124
                    Global.vars.readID.birthdateDesc = Utility_getLocaleShortDateString(date); //751124
                    Global.vars.readID.birthdateComponents = [date.getDate(), (date.getMonth() + 1), date.getFullYear(), 0, 0, 0];
                    Global.vars.readID.yearOfBirth = date.getFullYear(); //751124
                }
            } catch (e) {
                voltmx.print("### frmPerson_resultcallbackNFC error interpretedDate: " + parsedContent.interpretedDateOfBirth);
                Global.vars.readID.birthdate = "";
                Global.vars.readID.birthdateDesc = "";
                Global.vars.readID.birthdateComponents = [];
                Global.vars.readID.yearOfBirth = "";
            }
            voltmx.print("### frmPerson_resultcallbackNFC Global.vars.readID.yearOfBirth: " + Global.vars.readID.yearOfBirth);
            // this is a excepted way of checking for NaN 
            if (Global.vars.readID.yearOfBirth !== Global.vars.readID.yearOfBirth) {
                voltmx.print("### frmPerson_resultcallbackNFC Global.vars.readID.yearOfBirth NaN: " + Global.vars.readID.yearOfBirth);
                Global.vars.readID.birthdate = "";
                Global.vars.readID.birthdateDesc = "";
                Global.vars.readID.birthdateComponents = [];
                Global.vars.readID.yearOfBirth = "";
                voltmx.print("### frmPerson_resultcallbackNFC Global.vars.readID.yearOfBirth: " + Global.vars.readID.yearOfBirth);
            }
            if (parsedContent.documentNumber !== undefined) {
                Global.vars.readID.documentNumber = parsedContent.documentNumber.replace(/</g, "");
            }
            if (parsedContent.documentNumber !== "READID123") {
                Global.vars.readID.ssn = parsedContent.personalNumber;
            } else {
                Global.vars.readID.ssn = "";
            }
            Global.vars.readID.idenDocType = parsedContent.DocumentCode; //als nederland dan P paspoort en I ID kaart anders kiezen
            if (Global.vars.readID.countryIdenDoc === null) {
                if (parsedContent.interpretedIssuingCountry == "Netherlands") {
                    Global.vars.readID.countryIdenDocDesc = "Nederland";
                    Global.vars.readID.countryIdenDoc = 6030;
                    voltmx.print("### frmPerson_resultcallbackNFC issuing country Netherlands");
                } else if (parsedContent.issuingCountry !== undefined) {
                    if (parsedContent.issuingCountry == "D<<" || parsedContent.issuingCountry == "UTO") {
                        parsedContent.issuingCountry = "DEU";
                    }
                    Utility_findCountryByIso31661alpha3(parsedContent.issuingCountry, CaseData.time.dateComponents, frmPerson_findCountryIdenDocByIso31661alpha3Callback);
                }
            }
            if (parsedContent.Nationality == "D<<") {
                parsedContent.Nationality = "DEU";
            }
            voltmx.print("### frmPerson_resultcallbackNFC issuing country: " + parsedContent.issuingCountry);
            if (parsedContent.issuingCountry !== undefined && parsedContent.issuingCountry == "NLD") {
                if (parsedContent.DocumentCode == "P") {
                    Global.vars.readID.idenDocTypeDesc = "paspoort";
                } else if (parsedContent.DocumentCode == "I") {
                    Global.vars.readID.idenDocTypeDesc = "identiteitskaart";
                } else if (parsedContent.DocumentCode == "IR") {
                    Global.vars.readID.idenDocTypeDesc = "verblijfsdocument (documenttype I t/m IV en EU/EER)";
                    Global.vars.readID.ssn = "";
                } else if (parsedContent.DocumentCode == "IT") {
                    Global.vars.readID.idenDocTypeDesc = "verblijfsdocument (documenttype I t/m IV en EU/EER)";
                    Global.vars.readID.ssn = "";
                } else if (parsedContent.DocumentCode == "IW") {
                    Global.vars.readID.idenDocTypeDesc = "W-document";
                    Global.vars.readID.ssn = "";
                } else if ((parsedContent.DocumentCode === undefined || parsedContent.DocumentCode === "") && (Global.vars.readID.MRZ_dateOfBirth === null || Global.vars.readID.MRZ_dateOfBirth === "")) {
                    Global.vars.readID.idenDocTypeDesc = "rijbewijs";
                } else if (Global.vars.readID.idenDocType !== undefined && Global.vars.readID.MRZ_response != null && (Global.vars.readID.MRZ_response.kindOfData != null && Global.vars.readID.MRZ_response.kindOfData == "Europees Rijbewijs")) {
                    Global.vars.readID.idenDocTypeDesc = "rijbewijs";
                } else {
                    Global.vars.readID.idenDocTypeDesc = parsedContent.documentCode;
                    Global.vars.readID.nationalityDesc = parsedContent.Nationality;
                }
                voltmx.print("### frmPerson_resultcallbackNFC Global.vars.readID.idenDocTypeDesc: " + Global.vars.readID.idenDocTypeDesc);
                frmPerson_setDocumentType(Global.vars.readID.idenDocTypeDesc);
            } else {
                var documentCode = "";
                if ((parsedContent.DocumentCode === undefined || parsedContent.DocumentCode === "") && (Global.vars.readID.MRZ_dateOfBirth === null || Global.vars.readID.MRZ_dateOfBirth === "")) {
                    documentCode = "RBW";
                } else if (Global.vars.readID.idenDocType !== undefined && Global.vars.readID.MRZ_response != null && (Global.vars.readID.MRZ_response.kindOfData != null && Global.vars.readID.MRZ_response.kindOfData == "Europees Rijbewijs")) {
                    documentCode = "RBW";
                } else {
                    documentCode = parsedContent.DocumentCode;
                }
                frmPerson_setDocumentTypeOther(documentCode);
            }
            voltmx.print("### frmPerson_resultcallbackNFC parsedContent.Nationality:" + parsedContent.Nationality);
            voltmx.print("### frmPerson_resultcallbackNFC Global.vars.readID.nationality:" + Global.vars.readID.nationality);
            if (parsedContent.Nationality == "Dutch" && (Global.vars.readID.nationality === null || Global.vars.readID.nationality === "" || Global.vars.readID.nationality === 1 || Global.vars.readID.nationality === "NLD")) {
                voltmx.print("### frmPerson_resultcallbackNFC nationality dutch");
                Global.vars.readID.surname = parsedContent.primaryIdentifier.match(/\S*$/)[0];
                if (parsedContent.primaryIdentifier !== undefined) {
                    Global.vars.readID.middlename = Utility_CapatilizeNames(parsedContent.primaryIdentifier.replace(Global.vars.readID.surname, "")); //alles voor de laatste spatie 
                }
                Global.vars.readID.surname = Utility_CapatilizeNames(Global.vars.readID.surname);
                Global.vars.readID.indicationDutch = true;
                Global.vars.checkedDutch = "done";
                Global.vars.readID.nationality = 1;
                Global.vars.readID.nationalityDesc = "Nederlandse";
            } else if (parsedContent.Nationality !== "Dutch" && (Global.vars.readID.nationality === null || Global.vars.readID.nationality === "")) {
                voltmx.print("### frmPerson_resultcallbackNFC nationality empty");
                Global.vars.readID.indicationDutch = false;
                voltmx.print("### frmPerson_resultcallbackNFC Global.vars.readID.idenDocTypeDesc: " + Global.vars.readID.idenDocTypeDesc);
                //frmPerson_setDocumentType(Global.vars.readID.idenDocTypeDesc);
            } else {
                voltmx.print("### frmPerson_resultcallbackNFC nationality not dutch");
                if (parsedContent.Nationality !== undefined && Global.vars.readID.nationality === null && Global.vars.readID.nationality !== "") {
                    voltmx.print("frmPerson_resultcallbackNFC find nationality through country");
                    Utility_findCountryByIso31661alpha3(parsedContent.Nationality, CaseData.time.dateComponents, frmPerson_findNationalityByIso31661alpha3Callback);
                }
                //als niet nederlands zelf kiezen
                Global.vars.readID.middlename = "";
                Global.vars.readID.surname = Utility_CapatilizeNames(parsedContent.primaryIdentifier);
            }
            if (parsedContent.Gender == null) {
                parsedContent.Gender = "UNKNOWN";
            }
            // Do not parse anything else then existing gender domain values
            Global.vars.readID.genderDesc = genderMap[parsedContent.Gender] || genderMap["UNKNOWN"];
            //
            for (var i in Global.vars.gPersonGenderResult) {
                var v = Global.vars.gPersonGenderResult[i];
                if (v.descripton == Global.vars.readID.genderDesc) {
                    Global.vars.readID.gender = v.number_value;
                }
            }
            Global.vars.readID.countryOfBirthDesc = "";
            Global.vars.readID.birthplace = "";
            if (parsedContent.CountryOfBirth !== undefined && parsedContent.CountryOfBirth != null && parsedContent.CountryOfBirth !== "") {
                Global.vars.readID.countryOfBirthDesc = parsedContent.CountryOfBirth;
            }
            if (parsedContent.PlaceOfBirth !== undefined && parsedContent.PlaceOfBirth != null && parsedContent.PlaceOfBirth !== "" && parsedContent.PlaceOfBirth !== "[]") {
                Global.vars.readID.birthplace = parsedContent.PlaceOfBirth;
                //check if place exists in city
                if (Global.vars.readID.indicationDutch === false) {
                    Global.vars.checkedDutch = "waiting";
                    Utility_checkIfCityExists(Global.vars.readID.birthplace, frmPerson_checkIfCityExists_succescallback);
                } else {
                    frmPerson_SetData_and_Continue();
                }
            } else {
                frmPerson_SetData_and_Continue();
            }
            voltmx.print("### frmPerson_resultcallbackNFC readidresponse1: " + JSON.stringify(parsedContent));
            voltmx.print("### frmPerson_resultcallbackNFC Global.vars.readID: " + JSON.stringify(Global.vars.readID));
            //TEST//
            //Global.vars.readID = {"MRZ_documentNumber":"D1NLD1523244394042L452XM82M366","MRZ_dateOfBirth":null,"MRZ_dateOfExpiry":null,"fullname":"van Veldhuizen e/v Duijf Jeroen","birthdate":"1973-09-18T23:00:00.000Z","birthdateDesc":"19-9-1973","birthdateComponents":[19,9,1973,0,0,0],"yearOfBirth":1973,"documentNumber":"5232443940","ssn":"184640969","surname":"van Veldhuizen e/v Duijf","middlename":"","givenNames":"Jeroen","idenDocType":2,"idenDocTypeDesc":"rijbewijs","countryIdenDoc":6030,"countryIdenDocDesc":"Nederland","gender":"","indicationDutch":false,"countryOfBirthDesc":"","birthplace":"Alkmaar","originalNFCOutPut":{"name":"van Veldhuizen e/v Duijf Jeroen","dateOfBirth":"19730919","dateOfExpiry":"20250127","dateOfIssue":"20150127","documentNumber":"5232443940","interpretedDateOfBirth":"19.09.1973","interpretedIssuingCountry":"Netherlands","interpretedDateOfExpiry":"27.01.2025","issuingCountry":"NLD","personalNumber":"184640969","primaryIdentifier":"van Veldhuizen e/v Duijf","secondaryIdentifier":"Jeroen","fullDateOfBirth":"Wed Sep 19 00:00:00 GMT+01:00 1973","CountryOfBirth":"","PlaceOfBirth":"Alkmaar","IssuingAuthority":"Gemeente Zaanstad","DateOfIssue":"27.01.2015","PlaceOfBirthAlt":"Alkmaar","DrivingCategories":[{"Category":"B"},{"Category":"B"}],"LDS version: ":"1.0"}};
            //END TEST//
        }
    } else {
        voltmx.print("### frmPerson_resultcallbackNFC readidresponse1: " + JSON.stringify(response1));
    }
}

function frmPerson_onclickNoChip() {
    voltmx.print("### frmPerson_onclickNoChip frmPerson_SetData_and_Continue");
    frmPerson_SetData_and_Continue();
}

function frmPerson_showHandleTestReadIDPopup() {
    //setGaussianBlur(frmResume.flcMainPage, frmResume.imgBlured);
    frmPerson.flcMainPage.setEnabled(false);
    frmPerson.flcHandleTestReadIDPopup.bottom = "0%";
    //move popup options
    var scanOptionsAnimation = voltmx.ui.createAnimation({
        "100": {
            "bottom": 0 + '%',
            "stepConfig": {
                "timingFunction": voltmx.anim.EASE_IN_OUT
            }
        }
    });
    var scanOptionsSetting = {
        "delay": 0,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.2
    };
    frmResume.flcCases.animate(scanOptionsAnimation, scanOptionsSetting);
}

function frmPerson_cancelHandleTestReadIDPopup() {
    frmPerson.flcMainPage.setEnabled(true);
    //move popup options
    frmPerson.flcHandleTestReadIDPopup.bottom = "-150%";
}

function frmPerson_onclick_segReadIDTests() {
    voltmx.print("### frmPerson_onclick_segReadIDTests selecteditem: " + JSON.stringify(frmPerson.segReadIDTests.selectedItems));
    var selectedCase = frmPerson.segReadIDTests.selectedItems[0];
    if (selectedCase != null && selectedCase !== "") {
        frmPerson_SetDocumentTestSet(selectedCase.lblItem);
    }
}

function frmPerson_SetDocumentTestSet(whichTestSet) {
    voltmx.print("### frmPerson_SetDocumentTestSet selecteditem: " + whichTestSet);
    var TestSet = {};
    //var whichTestSet = "";
    if (whichTestSet == "SpecimenDutchPassport") {
        TestSet = {
            "MRZ_documentNumber": "SPECI2014",
            "MRZ_dateOfBirth": "650310",
            "MRZ_dateOfExpiry": "240309",
            "MRZ_response": {
                "ocrResult": "P<NLDDE<BRUIJN<<WILLEKE<LISELOTTE<<<<<<<<<<<\nSPECI20142NLD6503101F2403096*********<<<<<84",
                "documentCode": "P<",
                "firstName": "WILLEKE LISELOTTE",
                "gender": "F",
                "lastName": "DE BRUIJN",
                "documentNumber": "SPECI2014",
                "dateOfBirthString": "650310",
                "dateOfExpiryString": "240309",
                "documentNumberCheckDigit": "2",
                "compositeCheckDigit": "4",
                "isCisCompositeCheckDigitCorrect": true,
                "issuingCountryCode": "NLD",
                "nationality": "NLD",
                "name": "DE<BRUIJN<<WILLEKE<LISELOTTE<<<<<<<<<<<",
                "optionalData": "*********<<<<<",
                "kindOfData": "Paspoort"
            },
            "fullname": "De Bruijn, Willeke Liselotte",
            "birthdate": "1965-03-10T00:00:00.000Z",
            "birthdateDesc": "10-3-1965",
            "birthdateComponents": [10, 3, 1965, 0, 0, 0],
            "yearOfBirth": 1965,
            "documentNumber": "SPECI2014",
            "ssn": "*********",
            "surname": "De Bruijn",
            "middlename": "",
            "givenNames": "Willeke Liselotte",
            "idenDocTypeDesc": "paspoort",
            "countryIdenDoc": 6030,
            "countryIdenDocDesc": "Nederland",
            "gender": 2,
            "genderDesc": "vrouwelijk",
            "indicationDutch": true,
            "nationality": 6030,
            "nationalityDesc": "Nederlandse",
            "countryOfBirthDesc": "",
            "birthplace": "[]",
            "originalNFCOutPut": {
                "nfcVerificationStatusDS": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusHT": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusCS": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusAA": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusEACCA": "PRESENT_NOT_CHECKED",
                "name": "DE BRUIJN, WILLEKE LISELOTTE",
                "dateOfBirth": "650310",
                "dateOfExpiry": "240309",
                "documentNumber": "SPECI2014",
                "interpretedDateOfBirth": "10.03.1965",
                "interpretedIssuingCountry": "Netherlands",
                "interpretedDateOfExpiry": "09.03.2024",
                "issuingCountry": "NLD",
                "personalNumber": "999993331",
                "primaryIdentifier": "DE BRUIJN",
                "secondaryIdentifier": "WILLEKE LISELOTTE",
                "fullDateOfBirth": "",
                "Nationality": "Dutch",
                "Nationality3code": "NLD",
                "Gender": "FEMALE",
                "LDSVersion": "1.7",
                "DocumentCode": "P",
                "OtherNames": "[]",
                "PermanentAddress": "[]",
                "UnicodeVersion": "4.0.0",
                "PlaceOfBirth": "[]"
            },
            "nfcVerificationStatusDS": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusHT": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusCS": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusAA": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusEACCA": "PRESENT_NOT_CHECKED"
        };
    } else if (whichTestSet == "SpecimenNewSealandPassport") {
        TestSet = {
            "MRZ_documentNumber": "LK000455<",
            "MRZ_dateOfBirth": "901001",
            "MRZ_dateOfExpiry": "251214",
            "MRZ_response": {
                "ocrResult": "P<NZLWATA<<AROHA<MERE<TERESA<<<<<<<<<<<<<<<<\nLK000455<1NZL9010015F2512141<<<<<<<<<<<<<<06",
                "documentCode": "P<",
                "firstName": "AROHA MERE TERESA",
                "gender": "F",
                "lastName": "WATA",
                "documentNumber": "LK000455<",
                "dateOfBirthString": "901001",
                "dateOfExpiryString": "251214",
                "documentNumberCheckDigit": "1",
                "compositeCheckDigit": "6",
                "isCisCompositeCheckDigitCorrect": true,
                "issuingCountryCode": "NZL",
                "nationality": "NZL",
                "name": "WATA<<AROHA<MERE<TERESA<<<<<<<<<<<<<<<<",
                "optionalData": "<<<<<<<<<<<<<<",
                "kindOfData": "Paspoort"
            },
            "fullname": "Wata, Aroha Mere Teresa",
            "birthdate": "1990-10-01T00:00:00.000Z",
            "birthdateDesc": "1-10-1990",
            "birthdateComponents": [1, 10, 1990, 0, 0, 0],
            "yearOfBirth": 1990,
            "documentNumber": "LK000455",
            "ssn": "",
            "surname": "Wata",
            "middlename": "",
            "givenNames": "Aroha Mere Teresa",
            "idenDocTypeDesc": "paspoort",
            "countryIdenDoc": 5013,
            "countryIdenDocDesc": "Nieuw-Zeeland",
            "gender": 2,
            "genderDesc": "vrouwelijk",
            "indicationDutch": false,
            "nationality": null,
            "nationalityDesc": null,
            "countryOfBirthDesc": "",
            "birthplace": "[]",
            "originalNFCOutPut": {
                "nfcVerificationStatusDS": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusHT": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusCS": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusAA": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusEACCA": "NOT_PRESENT",
                "name": "WATA, AROHA MERE TERESA",
                "dateOfBirth": "901001",
                "dateOfExpiry": "251214",
                "documentNumber": "LK000455",
                "interpretedDateOfBirth": "01.10.1990",
                "interpretedIssuingCountry": "New Zealand",
                "interpretedDateOfExpiry": "14.12.2025",
                "issuingCountry": "NZL",
                "personalNumber": "",
                "primaryIdentifier": "WATA",
                "secondaryIdentifier": "AROHA MERE TERESA",
                "fullDateOfBirth": "",
                "Nationality": "",
                "Nationality3code": "NZL",
                "Gender": "FEMALE",
                "LDSVersion": "1.7",
                "DocumentCode": "P",
                "OtherNames": "[]",
                "PermanentAddress": "[]",
                "UnicodeVersion": "4.0.0",
                "PlaceOfBirth": "[]"
            },
            "nfcVerificationStatusDS": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusHT": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusCS": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusAA": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusEACCA": "NOT_PRESENT"
        };
    } else if (whichTestSet == "OldGermanPassport") {
        TestSet = {
            "MRZ_documentNumber": "C7CT9KRNK",
            "MRZ_dateOfBirth": "830509",
            "MRZ_dateOfExpiry": "220217",
            "MRZ_response": {
                "ocrResult": "P<D<<KOENEMANN<<DIETMAR<HARALD<<<<<<<<<<<<<<\nC7CT9KRNK5D<<8305099M2202174<<<<<<<<<<<<<<<2",
                "documentCode": "P<",
                "firstName": "DIETMAR HARALD",
                "gender": "M",
                "lastName": "KOENEMANN",
                "documentNumber": "C7CT9KRNK",
                "dateOfBirthString": "830509",
                "dateOfExpiryString": "220217",
                "documentNumberCheckDigit": "5",
                "compositeCheckDigit": "2",
                "isCisCompositeCheckDigitCorrect": true,
                "issuingCountryCode": "D<<",
                "nationality": "DEU",
                "name": "KOENEMANN<<DIETMAR<HARALD<<<<<<<<<<<<<<",
                "optionalData": "<<<<<<<<<<<<<<",
                "kindOfData": "Paspoort"
            },
            "fullname": "Koenemann, Dietmar Harald",
            "birthdate": "1983-05-09T00:00:00.000Z",
            "birthdateDesc": "9-5-1983",
            "birthdateComponents": [9, 5, 1983, 0, 0, 0],
            "yearOfBirth": 1983,
            "documentNumber": "C7CT9KRNK",
            "ssn": "",
            "surname": "Koenemann",
            "middlename": "",
            "givenNames": "Dietmar Harald",
            "idenDocTypeDesc": "paspoort",
            "countryIdenDoc": null,
            "countryIdenDocDesc": null,
            "gender": 1,
            "genderDesc": "mannelijk",
            "indicationDutch": false,
            "nationality": null,
            "nationalityDesc": null,
            "countryOfBirthDesc": "",
            "birthplace": "[]",
            "originalNFCOutPut": {
                "nfcVerificationStatusDS": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusHT": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusCS": "PRESENT_NOT_CHECKED",
                "nfcVerificationStatusAA": "NOT_PRESENT",
                "nfcVerificationStatusEACCA": "PRESENT_NOT_CHECKED",
                "name": "KOENEMANN, DIETMAR HARALD",
                "dateOfBirth": "830509",
                "dateOfExpiry": "220217",
                "documentNumber": "C7CT9KRNK",
                "interpretedDateOfBirth": "09.05.1983",
                "interpretedIssuingCountry": "Germany",
                "interpretedDateOfExpiry": "17.02.2022",
                "issuingCountry": "D<<",
                "personalNumber": "",
                "primaryIdentifier": "KOENEMANN",
                "secondaryIdentifier": "DIETMAR HARALD",
                "fullDateOfBirth": "",
                "Nationality": "German",
                "Nationality3code": "D<<",
                "Gender": "MALE",
                "LDSVersion": "1.7",
                "DocumentCode": "P",
                "OtherNames": "[]",
                "PermanentAddress": "[]",
                "UnicodeVersion": "4.0.0",
                "PlaceOfBirth": "[]"
            },
            "nfcVerificationStatusDS": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusHT": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusCS": "PRESENT_NOT_CHECKED",
            "nfcVerificationStatusAA": "NOT_PRESENT",
            "nfcVerificationStatusEACCA": "PRESENT_NOT_CHECKED"
        };
    } else if (whichTestSet == "ExpiredIDCard") {
        TestSet = {
            "MRZ_documentNumber": "52045084<",
            "MRZ_dateOfBirth": "940401",
            "MRZ_dateOfExpiry": "130401",
            "MRZ_response": {
                "ocrResult": "ITNLD52045084<2VNR0100000023<<\n9404014F1304015CHE<I<32<<<<<<2\nBELSER<<JULIETTE<HENRIKE<SOPHI",
                "documentCode": "IT",
                "firstName": "JULIETTE HENRIKE SOPHI",
                "gender": "F",
                "lastName": "BELSER",
                "documentNumber": "52045084<",
                "dateOfBirthString": "940401",
                "dateOfExpiryString": "130401",
                "documentNumberCheckDigit": "2",
                "compositeCheckDigit": "2",
                "isCisCompositeCheckDigitCorrect": true,
                "issuingCountryCode": "NLD",
                "nationality": "CHE",
                "name": "BELSER<<JULIETTE<HENRIKE<SOPHI",
                "optionalData": "VNR0100000023<<",
                "kindOfData": "ID kaart"
            },
            "fullname": "Belser, Juliette Henrike Sophie",
            "birthdate": "1994-04-01T00:00:00.000Z",
            "birthdateDesc": "1-4-1994",
            "birthdateComponents": [1, 4, 1994, 0, 0, 0],
            "yearOfBirth": 1994,
            "documentNumber": "52045084",
            "ssn": "VNR0100000023",
            "surname": "Belser",
            "middlename": "",
            "givenNames": "Juliette Henrike Sophie",
            "idenDocTypeDesc": "verblijfsdocument (documenttype I t/m IV en EU/EER)",
            "countryIdenDoc": 6030,
            "countryIdenDocDesc": "Nederland",
            "gender": 2,
            "genderDesc": "vrouwelijk",
            "indicationDutch": false,
            "nationality": null,
            "nationalityDesc": null,
            "countryOfBirthDesc": "",
            "birthplace": "[]",
            "originalNFCOutPut": {
                "nfcVerificationStatusDS": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusHT": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusCSReason": "CERTIFICATE_EXPIRED",
                "nfcVerificationStatusCS": "PRESENT_FAILED",
                "nfcVerificationStatusAA": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusEACCA": "PRESENT_SUCCEEDED",
                "name": "BELSER, JULIETTE HENRIKE SOPHIE",
                "dateOfBirth": "940401",
                "dateOfExpiry": "130401",
                "documentNumber": "52045084",
                "interpretedDateOfBirth": "01.04.1994",
                "interpretedIssuingCountry": "Netherlands",
                "interpretedDateOfExpiry": "01.04.2013",
                "issuingCountry": "NLD",
                "personalNumber": "VNR0100000023",
                "primaryIdentifier": "BELSER",
                "secondaryIdentifier": "JULIETTE HENRIKE SOPHIE",
                "fullDateOfBirth": "",
                "Nationality": "Swiss",
                "Nationality3code": "CHE",
                "Gender": "FEMALE",
                "LDSVersion": "1.7",
                "DocumentCode": "IT",
                "OtherNames": "[]",
                "PermanentAddress": "[]",
                "UnicodeVersion": "4.0.0",
                "PlaceOfBirth": "[]"
            },
            "nfcVerificationStatusDS": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusHT": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusCS": "PRESENT_FAILED",
            "nfcVerificationStatusCSReason": "COULD_NOT_BUILD_CHAIN_FAILURE",
            "nfcVerificationStatusAA": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusEACCA": "PRESENT_SUCCEEDED"
        };
    } else if (whichTestSet == "NordicPassport") {
        TestSet = {
            "MRZ_documentNumber": "00000000<",
            "MRZ_dateOfBirth": "750531",
            "MRZ_dateOfExpiry": "241101",
            "MRZ_response": {
                "ocrResult": "PVNORSPECIMEN<<LAERKE<OEYVOR<AASE<<<<<<<<<<<\n00000000<0NOR7505319F24110154197505311234586",
                "documentCode": "PV",
                "firstName": "LAERKE OEYVOR AASE",
                "gender": "F",
                "lastName": "SPECIMEN",
                "documentNumber": "00000000<",
                "dateOfBirthString": "750531",
                "dateOfExpiryString": "241101",
                "documentNumberCheckDigit": "0",
                "compositeCheckDigit": "6",
                "isCisCompositeCheckDigitCorrect": true,
                "issuingCountryCode": "NOR",
                "nationality": "NOR",
                "name": "SPECIMEN<<LAERKE<OEYVOR<AASE<<<<<<<<<<<",
                "optionalData": "41975053112345",
                "kindOfData": "Paspoort"
            },
            "fullname": "Specimen, Laerke Oeyvor Aase",
            "birthdate": "1975-05-31T00:00:00.000Z",
            "birthdateDesc": "31-5-1975",
            "birthdateComponents": [31, 5, 1975, 0, 0, 0],
            "yearOfBirth": 1975,
            "documentNumber": "00000000",
            "ssn": "41975053112345",
            "surname": "Specimen",
            "middlename": "",
            "givenNames": "Laerke Oeyvor Aase",
            "idenDocTypeDesc": "overig",
            "countryIdenDoc": 6027,
            "countryIdenDocDesc": "Noorwegen",
            "gender": 2,
            "genderDesc": "vrouwelijk",
            "indicationDutch": false,
            "nationality": null,
            "nationalityDesc": null,
            "countryOfBirthDesc": "",
            "birthplace": "[]",
            "originalNFCOutPut": {
                "nfcVerificationStatusDS": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusHT": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusCSReason": "SIGNATURE_CHECKED",
                "nfcVerificationStatusCS": "PRESENT_FAILED",
                "nfcVerificationStatusAA": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusEACCA": "PRESENT_SUCCEEDED",
                "name": "SPECIMEN, LAERKE OEYVOR AASE",
                "dateOfBirth": "750531",
                "dateOfExpiry": "241101",
                "documentNumber": "00000000",
                "interpretedDateOfBirth": "31.05.1975",
                "interpretedIssuingCountry": "Norway",
                "interpretedDateOfExpiry": "01.11.2024",
                "issuingCountry": "NOR",
                "personalNumber": "41975053112345",
                "primaryIdentifier": "SPECIMEN",
                "secondaryIdentifier": "LAERKE OEYVOR AASE",
                "fullDateOfBirth": "",
                "Nationality": "Norwegian",
                "Nationality3code": "NOR",
                "Gender": "FEMALE",
                "LDSVersion": "1.7",
                "DocumentCode": "PV",
                "OtherNames": "[]",
                "PermanentAddress": "[]",
                "UnicodeVersion": "4.0.0",
                "PlaceOfBirth": "[]"
            },
            "nfcVerificationStatusDS": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusHT": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusCS": "PRESENT_FAILED",
            "nfcVerificationStatusAA": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusEACCA": "PRESENT_SUCCEEDED"
        };
    } else if (whichTestSet == "NordicPassport 2") {
        TestSet = {
            "MRZ_documentNumber": "00000000<",
            "MRZ_dateOfBirth": "750531",
            "MRZ_dateOfExpiry": "241101",
            "MRZ_response": {
                "ocrResult": "PVNORJOHANSEN<<LAERKE<OEYVOR<AASE<<<<<<<<<<<\n00002222<0NOR7505319F24110154197505311234586",
                "documentCode": "PV",
                "firstName": "LAERKE OEYVOR AASE",
                "gender": "F",
                "lastName": "JOHANSEN",
                "documentNumber": "00002222<",
                "dateOfBirthString": "750531",
                "dateOfExpiryString": "241101",
                "documentNumberCheckDigit": "0",
                "compositeCheckDigit": "6",
                "isCisCompositeCheckDigitCorrect": true,
                "issuingCountryCode": "NOR",
                "nationality": "NOR",
                "name": "JOHANSEN<<LAERKE<OEYVOR<AASE<<<<<<<<<<<",
                "optionalData": "41975053112345",
                "kindOfData": "Paspoort"
            },
            "fullname": "Johansen, Laerke Oeyvor Aase",
            "birthdate": "1975-05-31T00:00:00.000Z",
            "birthdateDesc": "31-5-1975",
            "birthdateComponents": [31, 5, 1975, 0, 0, 0],
            "yearOfBirth": 1975,
            "documentNumber": "00002222",
            "ssn": "41975053112345",
            "surname": "Johansen",
            "middlename": "",
            "givenNames": "Laerke Oeyvor Aase",
            "idenDocTypeDesc": "overig",
            "countryIdenDoc": 6027,
            "countryIdenDocDesc": "Noorwegen",
            "gender": 2,
            "genderDesc": "vrouwelijk",
            "indicationDutch": false,
            "nationality": null,
            "nationalityDesc": null,
            "countryOfBirthDesc": "",
            "birthplace": "[]",
            "originalNFCOutPut": {
                "nfcVerificationStatusDS": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusHT": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusCSReason": "SIGNATURE_CHECKED",
                "nfcVerificationStatusCS": "PRESENT_FAILED",
                "nfcVerificationStatusAA": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusEACCA": "PRESENT_SUCCEEDED",
                "name": "SPECIMEN, LAERKE OEYVOR AASE",
                "dateOfBirth": "750531",
                "dateOfExpiry": "241101",
                "documentNumber": "00000000",
                "interpretedIssuingCountry": "Norway",
                "interpretedDateOfExpiry": "01.11.2024",
                "issuingCountry": "NOR",
                "personalNumber": "41975053112345",
                "primaryIdentifier": "SPECIMEN",
                "secondaryIdentifier": "LAERKE OEYVOR AASE",
                "fullDateOfBirth": "",
                "Nationality": "Norwegian",
                "Nationality3code": "NOR",
                "Gender": "FEMALE",
                "LDSVersion": "1.7",
                "DocumentCode": "PV",
                "OtherNames": "[]",
                "PermanentAddress": "[]",
                "UnicodeVersion": "4.0.0",
                "PlaceOfBirth": "[]"
            },
            "nfcVerificationStatusDS": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusHT": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusCS": "PRESENT_FAILED",
            "nfcVerificationStatusAA": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusEACCA": "PRESENT_SUCCEEDED"
        };
    } else if (whichTestSet == "NordicPassport 3") {
        TestSet = {
            "MRZ_documentNumber": "00000000<",
            "MRZ_dateOfBirth": "750531",
            "MRZ_dateOfExpiry": "241101",
            "MRZ_response": {
                "ocrResult": "PVNORJOHANSEN<<LAERKE<OEYVOR<AASE<<<<<<<<<<<\n00002222<0NOR7505319F24110154197505311234586",
                "documentCode": "PV",
                "firstName": "LAERKE OEYVOR AASE",
                "gender": "F",
                "lastName": "JOHANSEN",
                "documentNumber": "00002222<",
                "dateOfBirthString": "750531",
                "dateOfExpiryString": "241101",
                "documentNumberCheckDigit": "0",
                "compositeCheckDigit": "6",
                "isCisCompositeCheckDigitCorrect": true,
                "issuingCountryCode": "NOR",
                "nationality": "NOR",
                "name": "JOHANSEN<<LAERKE<OEYVOR<AASE<<<<<<<<<<<",
                "optionalData": "41975053112345",
                "kindOfData": "Paspoort"
            },
            "fullname": "Johansen, Laerke Oeyvor Aase",
            "birthdate": "1975-05-31T00:00:00.000Z",
            "birthdateDesc": "31-5-1975",
            "birthdateComponents": [31, 5, 1975, 0, 0, 0],
            "yearOfBirth": 1975,
            "documentNumber": "00002222",
            "ssn": "41975053112345",
            "surname": "Johansen",
            "middlename": "",
            "givenNames": "Laerke Oeyvor Aase",
            "idenDocTypeDesc": "overig",
            "countryIdenDoc": 6027,
            "countryIdenDocDesc": "Noorwegen",
            "gender": 2,
            "genderDesc": "vrouwelijk",
            "indicationDutch": false,
            "nationality": null,
            "nationalityDesc": null,
            "countryOfBirthDesc": "",
            "birthplace": "[]",
            "originalNFCOutPut": {
                "nfcVerificationStatusDS": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusHT": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusCSReason": "SIGNATURE_CHECKED",
                "nfcVerificationStatusCS": "PRESENT_FAILED",
                "nfcVerificationStatusAA": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusEACCA": "PRESENT_SUCCEEDED",
                "name": "SPECIMEN, LAERKE OEYVOR AASE",
                "dateOfBirth": "750531",
                "dateOfExpiry": "241101",
                "documentNumber": "00000000",
                "interpretedDateOfBirth": "",
                "interpretedIssuingCountry": "Norway",
                "interpretedDateOfExpiry": "01.11.2024",
                "issuingCountry": "NOR",
                "personalNumber": "41975053112345",
                "primaryIdentifier": "SPECIMEN",
                "secondaryIdentifier": "LAERKE OEYVOR AASE",
                "fullDateOfBirth": "",
                "Nationality": "Norwegian",
                "Nationality3code": "NOR",
                "Gender": "FEMALE",
                "LDSVersion": "1.7",
                "DocumentCode": "PV",
                "OtherNames": "[]",
                "PermanentAddress": "[]",
                "UnicodeVersion": "4.0.0",
                "PlaceOfBirth": "[]"
            },
            "nfcVerificationStatusDS": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusHT": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusCS": "PRESENT_FAILED",
            "nfcVerificationStatusAA": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusEACCA": "PRESENT_SUCCEEDED"
        };
    } else if (whichTestSet == "BelgiumIDMRZonly") {
        TestSet = {
            "MRZ_documentNumber": "B100459900",
            "MRZ_dateOfBirth": "820122",
            "MRZ_dateOfExpiry": "060131",
            "MRZ_response": {
                "ocrResult": "IDBELB10045990<07<<<<<<<<<<<<<\n8201227F0601315FRA820122084272\nFLORES<<GEMA<FREDERIC<J<<<<<<<",
                "documentCode": "ID",
                "firstName": "GEMA FREDERIC J",
                "gender": "F",
                "lastName": "FLORES",
                "documentNumber": "B100459900",
                "dateOfBirthString": "820122",
                "dateOfExpiryString": "060131",
                "documentNumberCheckDigit": "7",
                "compositeCheckDigit": "2",
                "isCisCompositeCheckDigitCorrect": true,
                "issuingCountryCode": "BEL",
                "nationality": "FRA",
                "name": "FLORES<<GEMA<FREDERIC<J<<<<<<<",
                "optionalData": "07<<<<<<<<<<<<<",
                "kindOfData": "ID kaart"
            },
            "fullname": "Gema Frederic J Flores",
            "birthdate": "1982-01-22T00:00:00.000Z",
            "birthdateDesc": "22-1-1982",
            "birthdateComponents": [22, 1, 1982, 0, 0, 0],
            "yearOfBirth": 1982,
            "documentNumber": "B100459900",
            "ssn": "",
            "surname": "Flores",
            "middlename": "",
            "givenNames": "Gema Frederic J",
            "idenDocType": 99,
            "idenDocTypeDesc": "overig",
            "countryIdenDoc": null,
            "countryIdenDocDesc": "",
            "gender": 2,
            "genderDesc": "vrouwelijk",
            "indicationDutch": false,
            "nationality": null,
            "nationalityDesc": null,
            "countryOfBirthDesc": "",
            "birthplace": "",
            "originalNFCOutPut": {},
            "nfcVerificationStatusDS": "",
            "nfcVerificationStatusHT": "",
            "nfcVerificationStatusCS": "",
            "nfcVerificationStatusCSReason": "",
            "nfcVerificationStatusAA": "",
            "nfcVerificationStatusEACCA": ""
        };
    } else if (whichTestSet == "ItalianID") {
        TestSet = {
            "MRZ_documentNumber": "*********",
            "MRZ_dateOfBirth": "660111",
            "MRZ_dateOfExpiry": "290111",
            "MRZ_response": {
                "ocrResult": "C<ITA*********4<<<<<<<<<<<<<<<\n6601111F2901112ITA<<<<<<<<<<<8\nQUATTRO<<DATACARD<<<<<<<<<<<<<",
                "documentCode": "C<",
                "firstName": "DATACARD",
                "gender": "F",
                "lastName": "QUATTRO",
                "documentNumber": "*********",
                "dateOfBirthString": "660111",
                "dateOfExpiryString": "290111",
                "documentNumberCheckDigit": "4",
                "compositeCheckDigit": "8",
                "isCisCompositeCheckDigitCorrect": true,
                "issuingCountryCode": "ITA",
                "nationality": "ITA",
                "name": "QUATTRO<<DATACARD<<<<<<<<<<<<<",
                "optionalData": "<<<<<<<<<<<<<<<",
                "kindOfData": "ID kaart"
            },
            "fullname": "Quattro, Datacard",
            "birthdate": "1966-01-11T00:00:00.000Z",
            "birthdateDesc": "11-1-1966",
            "birthdateComponents": [11, 1, 1966, 0, 0, 0],
            "yearOfBirth": 1966,
            "documentNumber": "*********",
            "ssn": "",
            "surname": "Quattro",
            "middlename": "",
            "givenNames": "Datacard",
            "idenDocTypeDesc": "overig",
            "countryIdenDoc": 7044,
            "countryIdenDocDesc": "Italië",
            "gender": 2,
            "genderDesc": "vrouwelijk",
            "indicationDutch": false,
            "nationality": null,
            "nationalityDesc": null,
            "countryOfBirthDesc": "",
            "birthplace": "[PINO SULLA SPONDA DEL LAGO MAGGIORE, VA]",
            "originalNFCOutPut": {
                "nfcVerificationStatusDS": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusHT": "PRESENT_SUCCEEDED",
                "nfcVerificationStatusCSReason": "COULD_NOT_BUILD_CHAIN_FAILURE",
                "nfcVerificationStatusCS": "PRESENT_FAILED",
                "nfcVerificationStatusAA": "NOT_PRESENT",
                "nfcVerificationStatusEACCA": "PRESENT_SUCCEEDED",
                "name": "QUATTRO, DATACARD",
                "dateOfBirth": "660111",
                "dateOfExpiry": "290111",
                "documentNumber": "*********",
                "interpretedDateOfBirth": "11.01.1966",
                "interpretedIssuingCountry": "Italy",
                "interpretedDateOfExpiry": "11.01.2029",
                "issuingCountry": "ITA",
                "personalNumber": "419750531",
                "primaryIdentifier": "QUATTRO",
                "secondaryIdentifier": "DATACARD",
                "fullDateOfBirth": "Tue Jan 11 00:00:00 GMT+01:00 1966",
                "Nationality": "Italian",
                "Nationality3code": "ITA",
                "Gender": "FEMALE",
                "LDSVersion": "1.7",
                "DocumentCode": "C",
                "OtherNames": "[]",
                "PermanentAddress": "[VIA SALARIA 712, ROMA, RM]",
                "UnicodeVersion": "4.0.0",
                "PlaceOfBirth": "[PINO SULLA SPONDA DEL LAGO MAGGIORE, VA]"
            },
            "nfcVerificationStatusDS": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusHT": "PRESENT_SUCCEEDED",
            "nfcVerificationStatusCS": "PRESENT_FAILED",
            "nfcVerificationStatusCSReason": "COULD_NOT_BUILD_CHAIN_FAILURE",
            "nfcVerificationStatusAA": "NOT_PRESENT",
            "nfcVerificationStatusEACCA": "PRESENT_SUCCEEDED"
        };
    } else if (whichTestSet == "DutchID_MRZOnly<<Date") {
        TestSet = {
            "MRZ_documentNumber": "241453110",
            "MRZ_dateOfBirth": "82<<<<",
            "MRZ_dateOfExpiry": "211010",
            "MRZ_response": {
                "ocrResult": "IRNLD24145311<9VNR0605007857<<\n82<<<<2M2310107SOM<IV<38<<<<<7\nANNABA<<MOHAMED<MUSE<<<<<<<<<<",
                "documentCode": "IR",
                "firstName": "MOHAMED MUSE",
                "gender": "M",
                "lastName": "ANNABA",
                "documentNumber": "241453110",
                "dateOfBirthString": "82<<<<",
                "dateOfExpiryString": "211010",
                "documentNumberCheckDigit": "9",
                "compositeCheckDigit": "7",
                "isCisCompositeCheckDigitCorrect": true,
                "issuingCountryCode": "NLD",
                "nationality": "SOM",
                "name": "ANNABA<<MOHAMED<MUSE<<<<<<<<<<",
                "optionalData": "VNR0605007857<<",
                "kindOfData": "ID kaart"
            },
            "fullname": "Mohamed Muse Annaba",
            "birthdate": "1982-01-01T00:00:00.000Z",
            "birthdateDesc": "1-1-1982",
            "birthdateComponents": [1, 1, 1982, 0, 0, 0],
            "yearOfBirth": 1982,
            "documentNumber": "241453110",
            "ssn": "",
            "surname": "Annaba",
            "middlename": "",
            "givenNames": "Mohamed Muse",
            "idenDocType": 8,
            "idenDocTypeDesc": "verblijfsdocument (documenttype I t/m IV en EU/EER)",
            "countryIdenDoc": 6030,
            "countryIdenDocDesc": "Nederland",
            "gender": 1,
            "genderDesc": "mannelijk",
            "indicationDutch": false,
            "nationality": "SOM",
            "nationalityDesc": "SOM",
            "countryOfBirthDesc": "",
            "birthplace": "",
            "originalNFCOutPut": {},
            "nfcVerificationStatusDS": "",
            "nfcVerificationStatusHT": "",
            "nfcVerificationStatusCS": "",
            "nfcVerificationStatusCSReason": "",
            "nfcVerificationStatusAA": "",
            "nfcVerificationStatusEACCA": ""
        };
    } else {
        whichTestSet = "";
    }
    if (whichTestSet !== "") {
        //alert("TESTSET in gebruik: " + whichTestSet);
        Global.vars.readIDTestSet = TestSet;
    } else {
        Global.vars.readIDTestSet = null;
    }
    frmPerson_MRZRunTestSet();
    frmPerson_cancelHandleTestReadIDPopup();
}

function frmPerson_SetData_and_Continue() {
    try {
        voltmx.print("### frmPerson_SetData_and_Continue ");
        voltmx.application.getCurrentForm().remove(frmOverlayImage.flcBackImage);
        voltmx.print("### flexContainer1 removed from form");
    } catch (e) {}
    frmPerson_setGlobalsToFields();
    //check ssn
    voltmx.print("### frmPerson_SetData_and_Continue Global.vars.readID.idenDocType: " + Global.vars.readID.idenDocType);
    voltmx.print("### frmPerson_SetData_and_Continue Global.vars.readID.ssn: " + Global.vars.readID.ssn);
    if ((Global.vars.readID.idenDocType !== undefined && Global.vars.readID.idenDocType != null && Global.vars.readID.idenDocType.toString().startsWith("99") === false) && (Global.vars.readID.ssn !== undefined && Global.vars.readID.ssn.length > 7 && Global.vars.readID.ssn.length < 10) && (Global.vars.readID.ssn != "00000000" && Global.vars.readID.ssn != "000000000" && Global.vars.readID.ssn != "99999999" && Global.vars.readID.ssn != "999999999")) {
        voltmx.print("### frmPerson_SetData_and_Continue with ssn");
        if (Global.vars.readID.ssn.length < 9) {
            Global.vars.readID.ssn = "0" + Global.vars.readID.ssn;
            voltmx.print("### frmPerson_SetData_and_Continue concatenated number: " + Global.vars.readID.ssn);
        }
        voltmx.print("### frmPerson_SetData_and_Continue Global.vars.readID.ssn check: " + Global.vars.readID.ssn);
        try {
            voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loading") + "...", "center", false, true, {
                enablemenukey: true,
                enablebackkey: true
            });
            voltmx.timer.schedule("checkSSN", frmPerson_checkSSN, 0.5, false);
        } catch (e) {}
        //set wich checks where used
        Global.vars.gCasePersons.indMRZ = Global.vars.readID.indMRZ;
        Global.vars.gCasePersons.indNFC = Global.vars.readID.indNFC;
        //frmPerson_checkSSN();
    } else {
        //set person globals
        voltmx.print("### frmPerson_SetData_and_Continue set person globals");
        Global.vars.gCasePersons.nationality = Global.vars.readID.nationality;
        Global.vars.gCasePersons.indicationDutch = Global.vars.readID.indicationDutch;
        Global.vars.gCasePersons.nationalityDesc = Global.vars.readID.nationalityDesc;
        //document info
        voltmx.print("### frmPerson_SetData_and_Continue Global.vars.readID.idenDocType: " + Global.vars.readID.idenDocType);
        Global.vars.gCasePersons.idenDocTypeDesc = Global.vars.readID.idenDocTypeDesc;
        Global.vars.gCasePersons.idenDocType = Global.vars.readID.idenDocType;
        var docMandatory = false;
        var index = Global.vars.additionalDocumentTypes.map(function(e) {
            return e.numbervalue;
        }).indexOf(Global.vars.gCasePersons.idenDocType);
        if (index > -1) {
            docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
        }
        if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType.toString().startsWith("99") === true && docMandatory === false) {
            frmPerson.documentdescription.lblText.text = Global.vars.readID.documentNumber;
            frmPerson.documentdescription.lblText.skin = lblFieldInfo;
            if (frmPerson.documentdescription.lblText.text != null && frmPerson.documentdescription.lblText.text !== "" && frmPerson.documentdescription.lblText.text !== "Beschrijving document(en)") {
                // add record to CaseData.text
                var loctextindex = null;
                for (var p = 0;
                    ((CaseData.text) != null) && p < CaseData.text.length; p++) {
                    var v = CaseData.text[p];
                    if ((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))) { //beschrijving documenten
                        voltmx.print("#### frmPerson_SetData_and_Continue: Finding officer present: " + v + " index: " + p);
                        loctextindex = p;
                        break;
                    }
                }
                var laddrecord = CaseData_setNewtext();
                laddrecord.inserted = true;
                laddrecord.edited = true;
                laddrecord.type = 3; //beschrijving documenten
                laddrecord.value = "Beschrijving document(en): " + frmPerson.documentdescription.lblText.text;
                if (loctextindex === null) {
                    CaseData.text.splice(0, 0, laddrecord);
                } else {
                    CaseData.text.splice(loctextindex, 1, laddrecord);
                }
                voltmx.print("#### frmPerson_SetData_and_Continue documentdescription.lblText CaseData.text after: " + JSON.stringify(CaseData.text));
            }
            if (docMandatory === false) {
                Global.vars.gCasePersons.countryIdenDoc = null;
                Global.vars.gCasePersons.countryIdenDocDesc = null;
            }
        } else {
            Global.vars.gCasePersons.documentNumber = Global.vars.readID.documentNumber;
            Global.vars.gCasePersons.countryIdenDoc = Global.vars.readID.countryIdenDoc;
            Global.vars.gCasePersons.countryIdenDocDesc = Global.vars.readID.countryIdenDocDesc;
        }
        //other person globals
        Global.vars.gCasePersons.birthdate = Global.vars.readID.birthdate;
        Global.vars.gCasePersons.birthdateComponents = Global.vars.readID.birthdateComponents;
        Global.vars.gCasePersons.birthdateDesc = Global.vars.readID.birthdateDesc;
        if (Global.vars.readID.yearOfBirth != null && Global.vars.readID.yearOfBirth != "") {
            Global.vars.gCasePersons.yearOfBirth = Number(Global.vars.readID.yearOfBirth);
        }
        if (Global.vars.readID.birthdate != null && Global.vars.readID.birthdate !== "") {
            Global.vars.gCasePersons.birthDateSet = true;
        }
        Global.vars.gCasePersons.surname = Global.vars.readID.surname;
        Global.vars.gCasePersons.middlename = Global.vars.readID.middlename;
        Global.vars.gCasePersons.givenNames = Global.vars.readID.givenNames;
        Global.vars.gCasePersons.gender = Global.vars.readID.gender;
        Global.vars.gCasePersons.genderDesc = Global.vars.readID.genderDesc;
        //
        Global.vars.gCasePersons.countryOfBirthDesc = Global.vars.readID.countryOfBirthDesc;
        //READ ID statuses
        Global.vars.gCasePersons.readIDnfcVerificationStatusAA = Global.vars.readID.nfcVerificationStatusAA;
        Global.vars.gCasePersons.readIDnfcVerificationStatusCS = Global.vars.readID.nfcVerificationStatusCS;
        Global.vars.gCasePersons.readIDnfcVerificationStatusCSReason = Global.vars.readID.nfcVerificationStatusCSReason;
        Global.vars.gCasePersons.readIDnfcVerificationStatusDS = Global.vars.readID.nfcVerificationStatusDS;
        Global.vars.gCasePersons.readIDnfcVerificationStatusEACCA = Global.vars.readID.nfcVerificationStatusEACCA;
        Global.vars.gCasePersons.readIDnfcVerificationStatusHT = Global.vars.readID.nfcVerificationStatusHT;
        if (Global.vars.readID.birthplace !== undefined && Global.vars.readID.birthplace != "[]") {
            voltmx.print("### frmPerson_SetData_and_Continue set person globals fill bithplace");
            Global.vars.gCasePersons.birthplace = Global.vars.readID.birthplace.replace(/[\])}[{(]/g, '');
        }
        voltmx.print("### frmPerson_SetData_and_Continue no valid ssn or foreign issuing country. Probeer alternatief zoeken");
        frmPersonAlternativeSearch.show();
        voltmx.timer.schedule("alternativeSearch", frmPerson_alternativeSearch, 0.5, false);
    }
}

function frmPerson_alternativeSearch() {
    voltmx.print("### frmPerson_alternativeSearch");
    try {
        voltmx.timer.cancel("alternativeSearch");
    } catch (e) {}
    voltmx.print("### frmPerson_alternativeSearch birthdate: " + Global.vars.readID.birthdate);
    var birthDate = "";
    if (Global.vars.gCasePersons.birthdateComponents != null && Global.vars.gCasePersons.birthdateComponents.length > 0) {
        if (Global.vars.readID.indNFC === false) {
            if (Global.vars.readID.birthdate !== undefined && Global.vars.readID.birthdate != null && Global.vars.readID.birthdate !== "") {
                birthDate = Global.vars.readID.birthdateComponents[2].toString().lpad("0", 4) + "-" + Global.vars.readID.birthdateComponents[1].toString().lpad("0", 2) + "-" + Global.vars.readID.birthdateComponents[0].toString().lpad("0", 2);
            }
        } else {
            birthDate = Global.vars.gCasePersons.birthdateComponents[2].toString().lpad("0", 4) + "-" + Global.vars.gCasePersons.birthdateComponents[1].toString().lpad("0", 2) + "-" + Global.vars.gCasePersons.birthdateComponents[0].toString().lpad("0", 2);
        }
    }
    var inputparams = {
        genderCode: Number(Global.vars.readID.gender),
        birthDate: birthDate,
        givenNames: Global.vars.gCasePersons.givenNames,
        prefixes: Global.vars.gCasePersons.middlename,
        surname: Global.vars.gCasePersons.surname,
    };
    //CallSocialSecurityNr_service();
    Global.vars.previousForm = "frmPersonAlternativeSearch";
    voltmx.print("### frmPerson_alternativeSearch birthDate: // " + birthDate + " //");
    if (birthDate !== "") {
        voltmx.print("### frmPerson_alternativeSearch service_GetPersonInfoOther: " + JSON.stringify(inputparams));
        service_GetPersonInfoOther(inputparams, frmPersonResult_callbackfunctionBSN);
    }
}

function frmPerson_goToManualPerson() {
    voltmx.print("### frmPerson_goToManualPerson go to frmPersonManualPerson");
    try {
        voltmx.timer.cancel("frmPerson_goToManualPerson");
    } catch (e) {}
    voltmx.application.dismissLoadingScreen();
    //set nationality globals again
    Global.vars.gCasePersons.nationality = Global.vars.readID.nationality;
    Global.vars.gCasePersons.nationalityDesc = Global.vars.readID.nationalityDesc;
    Global.vars.previousForm = "frmPerson";
    frmPersonManualPerson.show();
    alert("Uit te lezen gegevens overgenomen, controleer de gegevens en vul aan.");
}

function frmPerson_checkIfCityExists_succescallback(result) {
    voltmx.print("### Utility_checkIfCityExists succes: " + JSON.stringify(result));
    if (result.length > 0) {
        Global.vars.readID.indicationDutch = true;
        Global.vars.readID.nationality = 1;
        Global.vars.readID.nationalityDesc = "Nederlandse";
    } else {
        Global.vars.readID.indicationDutch = false;
    }
    Global.vars.checkedDutch = "done";
    frmPerson_SetData_and_Continue();
}

function frmPerson_checkSSN() {
    try {
        voltmx.timer.cancel("checkSSN");
    } catch (e) {}
    var sseleven = Utility_ElevenCheck(Global.vars.readID.ssn);
    voltmx.print("#### frmPerson_checkSSN: " + sseleven);
    if (sseleven) {
        if (Global.vars.checkedDutch == "done") {
            frmPerson_wantToSearchSSN(true);
        }
    } else {
        alert("Gevonden nummer is geen BSN, voer de gegevens op een andere manier op.");
    }
}

function frmPerson_wantToSearchSSN(response) {
    if (response) {
        //set person globals
        voltmx.print("#### frmPerson_wantToSearchSSN");
        Global.vars.gCasePersons.nationality = Global.vars.readID.nationality;
        Global.vars.gCasePersons.indicationDutch = Global.vars.readID.indicationDutch;
        Global.vars.gCasePersons.nationalityDesc = Global.vars.readID.nationalityDesc;
        //document info
        Global.vars.gCasePersons.idenDocTypeDesc = Global.vars.readID.idenDocTypeDesc;
        Global.vars.gCasePersons.idenDocType = Global.vars.readID.idenDocType;
        Global.vars.gCasePersons.documentNumber = Global.vars.readID.documentNumber;
        Global.vars.gCasePersons.countryIdenDoc = Global.vars.readID.countryIdenDoc;
        Global.vars.gCasePersons.countryIdenDocDesc = Global.vars.readID.countryIdenDocDesc;
        if (Global.vars.gCasePersons.indicationDutch === true) {
            Global.vars.previousForm = "frmPerson";
            if (Global.vars.environment == "production" && Global.vars.gInstanceId !== "RL0003") {
                service_GetPersonInfoSSN(Global.vars.readID.ssn, frmPersonResult_callbackfunctionBSN);
            } else {
                //TESTDATA for test BSN
                voltmx.print("#### frmPerson_wantToSearchSSN Global.vars.readID.idenDocTypeDesc: " + Global.vars.readID.idenDocTypeDesc);
                if (Global.vars.readID.idenDocTypeDesc == "paspoort") {
                    service_GetPersonInfoSSN("999990019", frmPersonResult_callbackfunctionBSN); //testdata
                } else if (Global.vars.readID.idenDocTypeDesc == "identiteitskaart") {
                    service_GetPersonInfoSSN("999993331", frmPersonResult_callbackfunctionBSN); //testdata
                } else if (Global.vars.readID.idenDocTypeDesc == "verblijfsdocument (documenttype I t/m IV en EU/EER)") {
                    service_GetPersonInfoSSN("000009830", frmPersonResult_callbackfunctionBSN); //testdata
                } else if (Global.vars.readID.idenDocTypeDesc == "rijbewijs") {
                    service_GetPersonInfoSSN("999990160", frmPersonResult_callbackfunctionBSN); //testdata
                }
            }
        } else {
            voltmx.application.dismissLoadingScreen();
            Global.vars.previousForm = "frmPerson";
            Global.vars.gCasePersons.ssn = Global.vars.readID.ssn;
            frmPersonSSN.show();
            alert("Kies de juiste nationaliteit.");
        }
    }
}

function frmPerson_setDocumentType(description) {
    voltmx.print("### frmPerson_setDocumentType documenttypes: " + JSON.stringify(Global.vars.documentTypes));
    Global.vars.readID.idenDocType = null;
    for (var k in Global.vars.documentTypes) {
        var x = Global.vars.documentTypes[k];
        if (x.value == description) {
            Global.vars.readID.idenDocType = Number(x.key);
            if (Global.vars.readID.idenDocType === 99) {
                Global.vars.readID.idenDocType = 9900;
            }
            break;
        }
    }
    if (Global.vars.readID.idenDocType === null) {
        Global.vars.readID.idenDocType = 9900;
        Global.vars.readID.idenDocTypeDesc = "overig";
    }
    voltmx.print("### frmPerson_setDocumentType Global.vars.readID.idenDocType: " + Global.vars.readID.idenDocType);
    voltmx.print("### frmPerson_setDocumentType Global.vars.readID.idenDocTypeDesc: " + Global.vars.readID.idenDocTypeDesc);
}

function frmPerson_setDocumentTypeOther(documentCode) {
    voltmx.print("### frmPerson_setDocumentTypeOther documenttypes: " + JSON.stringify(Global.vars.additionalDocumentTypes));
    voltmx.print("### frmPerson_setDocumentTypeOther documentCode: " + documentCode);
    Global.vars.readID.idenDocType = 9900;
    Global.vars.readID.idenDocTypeDesc = "overig";
    for (var k in Global.vars.additionalDocumentTypes) {
        var x = Global.vars.additionalDocumentTypes[k];
        if (x.document_code == documentCode && x.document_code != null) {
            Global.vars.readID.idenDocType = Number(x.numbervalue);
            Global.vars.readID.idenDocTypeDesc = x.description;
            break;
        }
    }
    voltmx.print("### frmPerson_setDocumentTypeOther Global.vars.readID.idenDocType: " + Global.vars.readID.idenDocType);
    voltmx.print("### frmPerson_setDocumentTypeOther Global.vars.readID.idenDocTypeDesc: " + Global.vars.readID.idenDocTypeDesc);
}

function frmPerson_setGlobalsToFields() {
    voltmx.print("### frmPerson_setGlobalsToFields");
    //Document info
    frmPerson.lblDocumentType.text = Global.vars.readID.idenDocTypeDesc;
    frmPerson.lblDocumentCountry.text = Global.vars.readID.countryIdenDocDesc;
    frmPerson.lblDocumentCountry.skin = lblFieldInfo;
    frmPerson.lblDocumentNumber.text = Global.vars.readID.documentNumber;
    frmPerson.lblDocumentNumber.skin = lblFieldInfo;
    frmPerson_flcDocumentCountry_setVisibility(true);
    frmPerson_flcDocumentNumber_setVisibility(true);
    //Person info
    frmPerson.lblSSN.text = Global.vars.readID.ssn;
    var fullname = "";
    var givenNames = "";
    if (Global.vars.readID.givenNames != null && Global.vars.readID.givenNames !== "") {
        givenNames = Global.vars.readID.givenNames.trim();
    }
    if (Global.vars.readID.middlename != null && Global.vars.readID.middlename.length > 0) {
        fullname = givenNames === "" ? (Global.vars.readID.middlename + " " + Global.vars.readID.surname) : ((givenNames + " " + Global.vars.readID.middlename) + " " + Global.vars.readID.surname);
    } else {
        fullname = givenNames === "" ? Global.vars.readID.surname : (givenNames + " " + Global.vars.readID.surname);
    }
    frmPerson.lblFullName.text = fullname;
    Global.vars.readID.fullName = fullname;
    voltmx.print("### frmPerson_setGlobalsToFields fullname: " + fullname);
    frmPerson.lblDateOfBirth.text = Global.vars.readID.birthdateDesc;
    frmPerson.lblPersonNationality.text = Global.vars.readID.nationalityDesc;
    if (Global.vars.readID.countryOfBirthDesc !== undefined && Global.vars.readID.countryOfBirthDesc != null && Global.vars.readID.countryOfBirthDesc !== "") {
        frmPerson.lblCountryOfOrigin.text = Global.vars.readID.countryOfBirthDesc;
        frmPerson_flcCountryOfOrigin_setVisibility(true);
    } else {
        frmPerson_flcCountryOfOrigin_setVisibility(false);
    }
    if (Global.vars.readID.birthplace !== undefined && Global.vars.readID.birthplace != null) {
        frmPerson.lblMunicipalityOfBirth.text = Global.vars.readID.birthplace;
        frmPerson_flcMunicipalityOfBirth_setVisibility(true);
    } else {
        frmPerson_flcMunicipalityOfBirth_setVisibility(false);
    }
    frmPerson.lblGender.text = Global.vars.readID.genderDesc;
}
myIndex = 0;

function frmPerson_checkdigitsCalculator(alphannumeric) {
    voltmx.print("### frmPerson_checkdigitsCalculator alphannumeric: " + alphannumeric);
    alphannumeric = alphannumeric.toUpperCase();
    var values = [];
    for (var i = 0; i < alphannumeric.length; i++) {
        var char = alphannumeric.charAt(i);
        voltmx.print("### frmPerson_checkdigitsCalculator char: " + char);
        if (char == "<") {
            values.push(0);
        } else if (char.match(/[A-Z]/i)) {
            var charToNum = char.charCodeAt(0) - 55;
            values.push(charToNum);
        } else if (char.match(/[0-9]/i)) {
            values.push(Number(char));
        } else {
            alert("Onverwacht teken gelezen begin opnieuw");
        }
    }
    voltmx.print("### frmPerson_checkdigitsCalculator values: " + values);
    var total = 0;
    for (var j = 0; j < values.length; j++) {
        var add = values[j];
        add = Number(add);
        voltmx.print("### frmPerson_checkdigitsCalculator add: " + add);
        var weight = frmPerson_getWeight();
        weight = Number(weight);
        voltmx.print("### frmPerson_checkdigitsCalculator weight: " + weight);
        total = total + (add * weight);
    }
    voltmx.print("### frmPerson_checkdigitsCalculator total: " + total);
    var remainder = total % 10;
    voltmx.print("### frmPerson_checkdigitsCalculator remainder: " + remainder);
    return remainder;
}

function frmPerson_getWeight() {
    var weight = [7, 3, 1];
    var addweight = weight[myIndex];
    myIndex = (myIndex + 1) % (weight.length);
    return addweight;
}