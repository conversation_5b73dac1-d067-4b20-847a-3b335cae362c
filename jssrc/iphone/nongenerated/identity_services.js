function identityService_profile(provider_name, callback) {
    var user_store_client = konySDK.getIdentityService(provider_name);
    user_store_client.getProfile(false, function(profile) {
        callback(profile);
    }, function(error) {
        voltmx.application.dismissLoadingScreen();
        alert("Error occured while fetching the profile.");
        voltmx.application.dismissLoadingScreen();
    });
}

function identityService_login(callback) {
    voltmx.print("### identityService_login Global.vars.gIdentityProvider: " + Global.vars.gIdentityProvider);
    try {
        var identityServiceName = Global.vars.gIdentityProvider;
        var client = voltmx.sdk.getCurrentInstance();
        Global.vars.auth_client = client.getIdentityService(identityServiceName);
        var isLoginPersisted = Global.vars.auth_client.usePersistedLogin();
        voltmx.print("### identityService_login Global.vars.auth_client: " + JSON.stringify(Global.vars.auth_client));
        voltmx.print("### identityService_login isLoginPersisted: " + isLoginPersisted);
    } catch (exception) {
        voltmx.print("### identityService_login exception: " + exception);
        //alert("Exception" + exception.message);
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0004") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
        voltmx.application.dismissLoadingScreen();
        return;
    }
    var options = {};
    var loginOptions = {};
    loginOptions.customQueryParamsForOAuth = {
        login_hint: Global.vars.gEntraUsername
    };
    loginOptions.isSSOEnabled = false;
    loginOptions.continueOnRefreshError = false;
    loginOptions.persistLoginResponse = true;
    loginOptions.enable_refresh_login = true;
    options.loginOptions = loginOptions;
    try {
        voltmx.print("### identityService_login try and login options: " + JSON.stringify(options));
        Utility_dismissLoadingScreen();
        Global.vars.auth_client.login(options, function(response) {
            voltmx.print("### identityService_login login succes response: " + JSON.stringify(response));
            voltmx.print("### identityService_login login succes response: " + JSON.stringify(response.provider_token));
            //get backend token
            var forceFromServer = false;
            Global.vars.auth_client.getBackendToken(forceFromServer, {
                "refresh": "true"
            }, function(response2) {
                voltmx.print("### identityService_login login succes Backend token" + JSON.stringify(response2));
                Global.vars.loggedOut = "no";
                voltmx.store.setItem("loggedOut", Global.vars.loggedOut);
                callback(response2);
            }, function(error1) {
                voltmx.print("### identityService_login login succes Failed to get backend token : " + JSON.stringify(error1));
            });
        }, function(error2) {
            voltmx.print("### identityService_login login failed error2: " + JSON.stringify(error2));
            voltmx.print("### identityService_login login failed retry Global.vars.gIdentityProvider: " + Global.vars.gIdentityProvider);
            identityService_login(callback);
            voltmx.application.dismissLoadingScreen();
        });
    } catch (err) {
        voltmx.print("### identityService_login login failed catch: " + JSON.stringify(err));
        voltmx.application.dismissLoadingScreen();
    }
}

function identityService_refresh_login(provider_name, callback) {
    voltmx.print('### identityService_refresh_login start');
    voltmx.print('### identityService_refresh_login provider_name: ' + provider_name);
    voltmx.print('### identityService_refresh_login setInfo: ' + JSON.stringify(Global.vars.setServerInfoDone));
    try {
        var identityServiceName = provider_name;
        var client = voltmx.sdk.getCurrentInstance();
        Global.vars.auth_client = client.getIdentityService(identityServiceName);
        var options = {};
        options["retain_backend_refresh_token"] = true;
        Global.vars.auth_client.refreshLogin(function(response) {
            voltmx.print('### identityService_refresh_login success: ' + JSON.stringify(response));
            var isLoginPersisted = Global.vars.auth_client.usePersistedLogin();
            voltmx.print("### identityService_refresh_login isLoginPersisted: " + isLoginPersisted);
            if (isLoginPersisted === true) {
                var forceFromServer = false;
                Global.vars.auth_client.getBackendToken(forceFromServer, {
                    "refresh": "true"
                }, function(response2) {
                    voltmx.print("### identityService_refresh_login login succes Backend token" + JSON.stringify(response2));
                    Global.vars.loggedOut = "no";
                    voltmx.store.setItem("loggedOut", Global.vars.loggedOut);
                    callback(response2);
                }, function(error1) {
                    voltmx.print("### identityService_refresh_login login succes Failed to get backend token : " + JSON.stringify(error1));
                });
            } else {
                identityService_login(callback);
            }
        }, function(err) {
            voltmx.print('### identityService_refresh_login failed:' + JSON.stringify(err));
            identityService_login(callback);
        }, options);
    } catch (exception) {
        voltmx.print("### identityService_refresh_login Exception: " + JSON.stringify(exception));
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0004") + JSON.stringify(exception), null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
    }
}
//getprofile and set username
function identityService_getProfileUsername(provider_name, callback) {
    voltmx.print("### identityService_getProfileUsername");
    var identityServiceName = provider_name;
    var client = voltmx.sdk.getCurrentInstance();
    var identityObject = client.getIdentityService(identityServiceName);
    var forceFromServer = true;
    identityObject.getUserAttributes(forceFromServer, function(profile) {
        voltmx.print("### identityService_getProfileUsername profile: " + JSON.stringify(profile));
        if (callback !== null) {
            callback(profile);
        }
    }, function(error) {
        voltmx.print("### identityService_getProfileUsername error: " + JSON.stringify(error));
        //alert("Error occured while fetching the profile.");
        voltmx.application.dismissLoadingScreen();
    });
}
// logout call
function identityService_logout(provider_name, callback, errorCallback) {
    voltmx.print("### identityService_logout provider_name: " + provider_name);
    try {
        voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loggingOut"), "center", true, true, {
            enablemenukey: true,
            enablebackkey: true
        });
        var client = voltmx.sdk.getCurrentInstance();
        var auth_client = client.getIdentityService(provider_name);
        var isLoginPersisted = auth_client.usePersistedLogin();
        voltmx.print("### identityService_logout isLoginPersisted : " + isLoginPersisted);
        var options = {};
        auth_client.logout(function(response) {
            identity_removeCookies();
            isLoginPersisted = auth_client.usePersistedLogin();
            voltmx.print("### identityService_logout response isLoginPersisted : " + isLoginPersisted);
            if (callback != null) {
                Global.vars.loggedOut = "yes";
                voltmx.store.setItem("loggedOut", Global.vars.loggedOut);
                callback(response);
            } else {
                voltmx.application.dismissLoadingScreen();
            }
            voltmx.print("### identityService_logout Success in voltmx foundry logout");
        }, function(error) {
            if (errorCallback !== null) {
                errorCallback(error);
            } else {
                voltmx.application.dismissLoadingScreen();
            }
            voltmx.print("### identityService_logout Error in voltmx foundry logout : " + JSON.stringify(error));
        }, options);
        // auth_client.logout(function(response){voltmx.print("### logout Success in mobilefabric logout");} , 
        //                    function(error) {voltmx.print("### logout Error in mobilefabric logout : " + JSON.stringify(error));} , options);
        //}
    } catch (exception) {
        alert("Exception" + exception.message);
    }
}

function identity_removeCookies() {
    voltmx.print("### identity_removeCookies");
    voltmx.print("### identityService_logout identity_removeCookies");
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        voltmx.net.clearCookies("https://login.microsoftonline.com");
        voltmx.net.removeAllCachedResponses();
    } else {
        voltmx.net.removeAllCookies();
    }
}