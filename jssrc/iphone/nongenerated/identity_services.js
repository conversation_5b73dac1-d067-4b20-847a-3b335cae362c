function identityService_profile(provider_name, callback) {
    var user_store_client = konySDK.getIdentityService(provider_name);
    user_store_client.getProfile(false, function(profile) {
        callback(profile);
    }, function(error) {
        voltmx.application.dismissLoadingScreen();
        alert("Error occured while fetching the profile.");
        voltmx.application.dismissLoadingScreen();
    });
}

function identityService_login(callback) {
    voltmx.print("### identityService_login Global.vars.gIdentityProvider: " + Global.vars.gIdentityProvider);
    try {
        var identityServiceName = Global.vars.gIdentityProvider;
        var client = voltmx.sdk.getCurrentInstance();
        Global.vars.auth_client = client.getIdentityService(identityServiceName);
        var isLoginPersisted = Global.vars.auth_client.usePersistedLogin();
        voltmx.print("### identityService_login Global.vars.auth_client: " + JSON.stringify(Global.vars.auth_client));
        voltmx.print("### identityService_login isLoginPersisted: " + isLoginPersisted);
    } catch (exception) {
        voltmx.print("### identityService_login exception: " + exception);
        //alert("Exception" + exception.message);
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0004") + "", null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
        voltmx.application.dismissLoadingScreen();
        return;
    }
    var options = {};
    var loginOptions = {};
    loginOptions.customQueryParamsForOAuth = {
        login_hint: Global.vars.gEntraUsername
    };
    loginOptions.isSSOEnabled = false;
    loginOptions.continueOnRefreshError = false;
    loginOptions.persistLoginResponse = true;
    loginOptions.enable_refresh_login = true;
    options.loginOptions = loginOptions;
    try {
        voltmx.print("### identityService_login try and login options: " + JSON.stringify(options));
        Utility_dismissLoadingScreen();
        Global.vars.auth_client.login(options, function(response) {
            voltmx.print("### identityService_login login succes response: " + JSON.stringify(response));
            voltmx.print("### identityService_login login succes response: " + JSON.stringify(response.provider_token));
            //get backend token
            var forceFromServer = false;
            Global.vars.auth_client.getBackendToken(forceFromServer, {
                refresh: "true"
            }, function(response2) {
                voltmx.print("### identityService_login login succes Backend token: " + JSON.stringify(response2));
                Global.vars.loggedOut = "no";
                voltmx.store.setItem("loggedOut", Global.vars.loggedOut);
                // Get user profile
                identityService_getUserProfile(response2.params.access_token);
                callback(response2);
            }, function(error1) {
                voltmx.print("### identityService_login login succes Failed to get backend token : " + JSON.stringify(error1));
            });
        }, function(error2) {
            voltmx.print("### identityService_login login failed error2: " + JSON.stringify(error2));
            voltmx.print("### identityService_login login failed retry Global.vars.gIdentityProvider: " + Global.vars.gIdentityProvider);
            identityService_login(callback);
            voltmx.application.dismissLoadingScreen();
        });
    } catch (err) {
        voltmx.print("### identityService_login login failed catch: " + JSON.stringify(err));
        voltmx.application.dismissLoadingScreen();
    }
}

function identityService_getUserProfile(accessToken) {
    voltmx.print("### identityService_getUserProfile starting with token: " + (accessToken ? "present" : "missing"));
    if (!accessToken) {
        voltmx.print("### identityService_getUserProfile ERROR: No access token provided");
        return;
    }
    var httpRequest = new voltmx.net.HttpRequest();
    // Success callback
    httpRequest.onReadyStateChange = function() {
        if (httpRequest.readyState === 4) {
            // Request completed
            voltmx.print("### identityService_getUserProfile Response Code: " + httpRequest.status);
            voltmx.print("### identityService_getUserProfile Response: " + httpRequest.responseText);
            if (httpRequest.status === 200) {
                try {
                    var profile = JSON.parse(httpRequest.responseText);
                    identityService_processProfileData(profile);
                } catch (parseError) {
                    voltmx.print("### identityService_getUserProfile ERROR parsing response: " + parseError);
                }
            } else {
                var errorMsg = "Failed to fetch profile. Status: " + httpRequest.status;
                voltmx.print("### identityService_getUserProfile ERROR: " + errorMsg);
            }
        }
    };
    // Set up the request
    httpRequest.open("GET", "https://graph.microsoft.com/v1.0/me");
    httpRequest.setRequestHeader("Authorization", "Bearer " + accessToken);
    httpRequest.setRequestHeader("Content-Type", "application/json");
    // Send the request (asynchronous)
    httpRequest.send();
}

function identityService_processProfileData(profile) {
    if (profile && profile.userPrincipalName) {
        voltmx.print("### identityService_processProfileData profile: " + JSON.stringify(profile));
        const userPrincipalName = profile.userPrincipalName.includes("twynsexternalid") ? profile.userPrincipalName.split("#")[0] : null;
        if (userPrincipalName) {
            Global.vars.gEntraUsername = userPrincipalName.replace("_", "@");
            Utility_storeSetItem("entrausername", Global.vars.gEntraUsername);
            voltmx.print("### identityService_processProfileData gEntraUsername: " + Global.vars.gEntraUsername);
        }
        const mail = profile.mail ? profile.mail.toLowerCase() : null;
        if (mail) {
            Global.vars.gUsername = mail;
            Utility_storeSetItem("username", Global.vars.gUsername);
            voltmx.print("### identityService_processProfileData gUsername: " + Global.vars.gUsername);
        }
    } else {
        voltmx.print("### identityService_processProfileData Failed to get profile: " + error);
    }
}
/**
 * Test function to verify getUserProfile works
 * Call this after successful login to test the profile retrieval
 */
function identityService_testGetUserProfile() {
    voltmx.print("### Testing getUserProfile function");
    // Check if we have a stored access token
    var storedProfile = voltmx.store.getItem("userProfile");
    if (storedProfile) {
        voltmx.print("### Stored profile found: " + storedProfile);
        return JSON.parse(storedProfile);
    } else {
        voltmx.print("### No stored profile found. Make sure login was successful.");
        return null;
    }
}

function identityService_refresh_login(provider_name, callback) {
    voltmx.print("### identityService_refresh_login start");
    voltmx.print("### identityService_refresh_login provider_name: " + provider_name);
    voltmx.print("### identityService_refresh_login setInfo: " + JSON.stringify(Global.vars.setServerInfoDone));
    try {
        var identityServiceName = provider_name;
        var client = voltmx.sdk.getCurrentInstance();
        Global.vars.auth_client = client.getIdentityService(identityServiceName);
        var options = {};
        options["retain_backend_refresh_token"] = true;
        Global.vars.auth_client.refreshLogin(function(response) {
            voltmx.print("### identityService_refresh_login success: " + JSON.stringify(response));
            var isLoginPersisted = Global.vars.auth_client.usePersistedLogin();
            voltmx.print("### identityService_refresh_login isLoginPersisted: " + isLoginPersisted);
            if (isLoginPersisted === true) {
                var forceFromServer = false;
                Global.vars.auth_client.getBackendToken(forceFromServer, {
                    refresh: "true"
                }, function(response2) {
                    voltmx.print("### identityService_refresh_login login succes Backend token: " + JSON.stringify(response2));
                    Global.vars.loggedOut = "no";
                    voltmx.store.setItem("loggedOut", Global.vars.loggedOut);
                    // Get user profile with callback
                    identityService_getUserProfile(response2.params.access_token);
                    callback(response2);
                }, function(error1) {
                    voltmx.print("### identityService_refresh_login login succes Failed to get backend token : " + JSON.stringify(error1));
                });
            } else {
                identityService_login(callback);
            }
        }, function(err) {
            voltmx.print("### identityService_refresh_login failed:" + JSON.stringify(err));
            identityService_login(callback);
        }, options);
    } catch (exception) {
        voltmx.print("### identityService_refresh_login Exception: " + JSON.stringify(exception));
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_log0004") + JSON.stringify(exception), null, "error", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_error"), null);
    }
}
//getprofile and set username
function identityService_getProfileUsername(provider_name, callback) {
    voltmx.print("### identityService_getProfileUsername");
    var identityServiceName = provider_name;
    var client = voltmx.sdk.getCurrentInstance();
    var identityObject = client.getIdentityService(identityServiceName);
    var forceFromServer = true;
    identityObject.getUserAttributes(forceFromServer, function(profile) {
        voltmx.print("### identityService_getProfileUsername profile: " + JSON.stringify(profile));
        if (callback !== null) {
            callback(profile);
        }
    }, function(error) {
        voltmx.print("### identityService_getProfileUsername error: " + JSON.stringify(error));
        //alert("Error occured while fetching the profile.");
        voltmx.application.dismissLoadingScreen();
    });
}
// logout call
function identityService_logout(provider_name, callback, errorCallback) {
    voltmx.print("### identityService_logout provider_name: " + provider_name);
    try {
        voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loggingOut"), "center", true, true, {
            enablemenukey: true,
            enablebackkey: true
        });
        var client = voltmx.sdk.getCurrentInstance();
        var auth_client = client.getIdentityService(provider_name);
        var isLoginPersisted = auth_client.usePersistedLogin();
        voltmx.print("### identityService_logout isLoginPersisted : " + isLoginPersisted);
        var options = {};
        auth_client.logout(function(response) {
            identity_removeCookies();
            isLoginPersisted = auth_client.usePersistedLogin();
            voltmx.print("### identityService_logout response isLoginPersisted : " + isLoginPersisted);
            if (callback != null) {
                Global.vars.loggedOut = "yes";
                voltmx.store.setItem("loggedOut", Global.vars.loggedOut);
                callback(response);
            } else {
                voltmx.application.dismissLoadingScreen();
            }
            voltmx.print("### identityService_logout Success in voltmx foundry logout");
        }, function(error) {
            if (errorCallback !== null) {
                errorCallback(error);
            } else {
                voltmx.application.dismissLoadingScreen();
            }
            voltmx.print("### identityService_logout Error in voltmx foundry logout : " + JSON.stringify(error));
        }, options);
        // auth_client.logout(function(response){voltmx.print("### logout Success in mobilefabric logout");} ,
        //                    function(error) {voltmx.print("### logout Error in mobilefabric logout : " + JSON.stringify(error));} , options);
        //}
    } catch (exception) {
        alert("Exception" + exception.message);
    }
}

function identity_removeCookies() {
    voltmx.print("### identity_removeCookies");
    voltmx.print("### identityService_logout identity_removeCookies");
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        voltmx.net.clearCookies("https://login.microsoftonline.com");
        voltmx.net.removeAllCachedResponses();
    } else {
        voltmx.net.removeAllCookies();
    }
}