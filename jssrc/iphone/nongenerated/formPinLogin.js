function frmPinLogin_flcButtonLeft_setVisibility(boolean) {
    voltmx.print("### frmPinLogin_flcButtonLeft_setVisibility");

    function flcButtonLeft_setVisibility() {
        voltmx.print("### frmPinLogin_flcButtonLeft_setVisibility flcButtonLeft_setVisibility: " + boolean);
        frmPinLogin.flcButtonLeft.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcButtonLeft_setVisibility, []);
}

function frmPinLogin_flcCreatePinFields_setVisibility(boolean) {
    voltmx.print("### frmPinLogin_flcCreatePinFields_setVisibility");

    function flcCreatePinFields_setVisibility() {
        voltmx.print("### frmPinLogin_flcCreatePinFields_setVisibility flcCreatePinFields_setVisibility: " + boolean);
        frmPinLogin.flcCreatePinFields.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcCreatePinFields_setVisibility, []);
}

function frmPinLogin_flcLoginFields_setVisibility(boolean) {
    voltmx.print("### frmPinLogin_flcLoginFields_setVisibility");

    function flcLoginFields_setVisibility() {
        voltmx.print("### frmPinLogin_flcLoginFields_setVisibility flcLoginFields_setVisibility: " + boolean);
        frmPinLogin.flcLoginFields.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcLoginFields_setVisibility, []);
}

function frmPinLogin_flcroundfinger_setVisibility(boolean) {
    voltmx.print("### frmPinLogin_flcroundfinger_setVisibility");

    function flcroundfinger_setVisibility() {
        voltmx.print("### frmPinLogin_flcroundfinger_setVisibility flcroundfinger_setVisibility: " + boolean);
        frmPinLogin.flcroundfinger.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcroundfinger_setVisibility, []);
}

function frmPinLogin_init() {
    voltmx.print("### path 3");
    frmPinLogin_isAuthUsingTouchSupported();
    frmPinLogin.onDeviceBack = Global_onDeviceBack;
}

function frmPinLogin_preshow() {
    Analytics_logScreenView("pin-login");
    voltmx.print("### path 4");
    voltmx.print("### frmPinLogin_preshow");
    voltmx.print("### frmPinLogin_preshow Global.vars.resetPin: " + Global.vars.resetPin);
    voltmx.print("### frmPinLogin_preshow Global.vars.createPinAfter: " + Global.vars.createPinAfter);
    voltmx.print("### frmPinLogin_preshow Global.vars.supportForFingerprint: " + Global.vars.supportForFingerprint);
    voltmx.print("### frmPinLogin_preshow Global.vars.firstLogin: " + Global.vars.firstLogin);
    voltmx.print("### frmPinLogin_preshow pinHash pre: <" + Global.vars.pinHash + ">");
    if (Global.vars.firstLogin === "no" && Global.vars.pinHash == null) {
        Global.vars.pinHash = Utility_storeGetItem("pinHash");
        voltmx.print("### frmPinLogin_preshow pinHash post: <" + Global.vars.pinHash + ">");
    }
    Global.vars.pinNumbers = "";
    Global.vars.createPinNumbers = "";
    Global.vars.verifyPinNumbers = "";
    frmPinLogin_flcButtonLeft_setVisibility(false);
    if (Global.vars.backFromMenuSettings === true) {
        Global.vars.backFromMenuSettings = false;
        frmPinLogin_flcButtonLeft_setVisibility(true);
    }
    if (Global.vars.resetPin === true) {
        voltmx.print("### frmPinLogin_preshow resetPin");
        frmPinLogin_flcCreatePinFields_setVisibility(false);
        frmPinLogin_flcLoginFields_setVisibility(true);
        frmPinLogin_flcroundfinger_setVisibility(false);
        frmPinLogin.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_checkPin");
        frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_Pin");
        //frmPinLogin_flcButtonLeft_setVisibility(true);
    } else if (Global.vars.createPinAfter === true) {
        voltmx.print("### frmPinLogin_preshow createPinAfter");
        frmPinLogin_flcCreatePinFields_setVisibility(true);
        frmPinLogin_flcLoginFields_setVisibility(false);
        frmPinLogin_flcroundfinger_setVisibility(false);
        frmPinLogin.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_createPin");
        frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_enterPin");
    } else if (Global.vars.createPinAfter === true && Global.vars.supportForFingerprint === false) {
        voltmx.print("### frmPinLogin_preshow createPinAfter && supportForFingerprint");
        frmPinLogin_flcCreatePinFields_setVisibility(true);
        frmPinLogin_flcLoginFields_setVisibility(false);
        frmPinLogin_flcroundfinger_setVisibility(false);
        frmPinLogin.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_createPin");
        frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_enterPin");
    } else if (Global.vars.supportForFingerprint === true && (Global.vars.firstLogin === "yes" || Global.vars.pinHash === undefined || Global.vars.pinHash === null)) {
        voltmx.print("### frmPinLogin_preshow supportForFingerprint && firstLogin");
        Global.vars.createPinInThisSession = true;
        frmPinLogin_flcCreatePinFields_setVisibility(true);
        frmPinLogin_flcLoginFields_setVisibility(false);
        frmPinLogin_flcroundfinger_setVisibility(false);
        frmPinLogin.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_createPin");
        frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_enterPin");
    } else if (Global.vars.supportForFingerprint === false && (Global.vars.firstLogin === "yes" || Global.vars.pinHash === undefined || Global.vars.pinHash === null)) {
        voltmx.print("### frmPinLogin_preshow supportForFingerprint false && firstLogin");
        Global.vars.createPinInThisSession = true;
        frmPinLogin_flcCreatePinFields_setVisibility(true);
        frmPinLogin_flcLoginFields_setVisibility(false);
        frmPinLogin_flcroundfinger_setVisibility(false);
        frmPinLogin.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_createPin");
        frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_enterPin");
    } else if (Global.vars.supportForFingerprint === true && Global.vars.firstLogin === "no") {
        voltmx.print("### frmPinLogin_preshow supportForFingerprint true && firstLogin no");
        frmPinLogin_flcCreatePinFields_setVisibility(false);
        frmPinLogin_flcLoginFields_setVisibility(true);
        frmPinLogin_flcroundfinger_setVisibility(true);
        frmPinLogin.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_login");
        frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_Pin");
    } else {
        voltmx.print("### frmPinLogin_preshow else");
        frmPinLogin_flcCreatePinFields_setVisibility(false);
        frmPinLogin_flcLoginFields_setVisibility(true);
        frmPinLogin_flcroundfinger_setVisibility(false);
        frmPinLogin.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_login");
        frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_Pin");
    }
    frmPinLogin.imgPinStar1.src = "empty.png";
    frmPinLogin.imgPinStar2.src = "empty.png";
    frmPinLogin.imgPinStar3.src = "empty.png";
    frmPinLogin.imgPinStar4.src = "empty.png";
    frmPinLogin.imgPinStar5.src = "empty.png";
    //create pin fields
    frmPinLogin.imgPinVerify1entry1.src = "empty.png";
    frmPinLogin.imgPinVerify1entry2.src = "empty.png";
    frmPinLogin.imgPinVerify1entry3.src = "empty.png";
    frmPinLogin.imgPinVerify1entry4.src = "empty.png";
    frmPinLogin.imgPinVerify1entry5.src = "empty.png";
    frmPinLogin.imgPinVerify2entry1.src = "empty.png";
    frmPinLogin.imgPinVerify2entry2.src = "empty.png";
    frmPinLogin.imgPinVerify2entry3.src = "empty.png";
    frmPinLogin.imgPinVerify2entry4.src = "empty.png";
    frmPinLogin.imgPinVerify2entry5.src = "empty.png";
}

function frmPinLogin_resetPin() {
    frmPinLogin_flcCreatePinFields_setVisibility(true);
    frmPinLogin_flcLoginFields_setVisibility(false);
    frmPinLogin_flcroundfinger_setVisibility(false);
    frmPinLogin.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_createPin");
    frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_enterPin");
    //create pin fields
    frmPinLogin.imgPinVerify1entry1.src = "empty.png";
    frmPinLogin.imgPinVerify1entry2.src = "empty.png";
    frmPinLogin.imgPinVerify1entry3.src = "empty.png";
    frmPinLogin.imgPinVerify1entry4.src = "empty.png";
    frmPinLogin.imgPinVerify1entry5.src = "empty.png";
    frmPinLogin.imgPinVerify2entry1.src = "empty.png";
    frmPinLogin.imgPinVerify2entry2.src = "empty.png";
    frmPinLogin.imgPinVerify2entry3.src = "empty.png";
    frmPinLogin.imgPinVerify2entry4.src = "empty.png";
    frmPinLogin.imgPinVerify2entry5.src = "empty.png";
    //empty pinhash
    Global.vars.pinHash = null;
    //empty pin globals
    Global.vars.pinNumbers = "";
    Global.vars.createPinNumbers = "";
    Global.vars.verifyPinNumbers = "";
}

function frmPinLogin_postShow() {
    voltmx.print("### frmPinLogin postshow");
    if (Global.vars.needToUsePin === true) {
        //frmPinLogin_flcButtonLeft_setVisibility(true);
        Global.vars.usePin = "yes";
        voltmx.store.setItem("usePin", Global.vars.usePin);
    } else if (Global.vars.usePin === null) {
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_wantToUsePin"), frmPinLogin_confirm_setPin, "confirmation", voltmx.i18n.getLocalizedString("bt_yes"), voltmx.i18n.getLocalizedString("bt_no"), "Info", null);
    }
}

function frmPinLogin_confirm_setPin(response) {
    if (response) {
        //frmPinLogin_flcButtonLeft_setVisibility(true);
        Global.vars.usePin = "yes";
    } else {
        Global.vars.usePin = "no";
        frmSynchronisation_skipSync(); //frmSynchronization.show();
    }
    voltmx.store.setItem("usePin", Global.vars.usePin);
}

function frmPinLogin_inputPin(button) {
    var pin = "";
    var createpin = false;
    var verifypin = false;
    if (frmPinLogin.lblPageHeader.text == voltmx.i18n.getLocalizedString("l_enterPin")) {
        createpin = true;
    } else if (frmPinLogin.lblPageHeader.text == voltmx.i18n.getLocalizedString("l_verifyPin")) {
        verifypin = true;
    }
    if (button == frmPinLogin.btn1.id) {
        pin = pin + "1";
    } else if (button == frmPinLogin.btn2.id) {
        pin = pin + "2";
    } else if (button == frmPinLogin.btn3.id) {
        pin = pin + "3";
    } else if (button == frmPinLogin.btn4.id) {
        pin = pin + "4";
    } else if (button == frmPinLogin.btn5.id) {
        pin = pin + "5";
    } else if (button == frmPinLogin.btn6.id) {
        pin = pin + "6";
    } else if (button == frmPinLogin.btn7.id) {
        pin = pin + "7";
    } else if (button == frmPinLogin.btn8.id) {
        pin = pin + "8";
    } else if (button == frmPinLogin.btn9.id) {
        pin = pin + "9";
    } else if (button == frmPinLogin.btn0.id) {
        pin = pin + "0";
    }
    if (createpin === false && verifypin === false) {
        Global.vars.pinNumbers = Global.vars.pinNumbers + pin;
        frmPinLogin_setAsteriks();
    } else if (createpin === true && verifypin === false) {
        Global.vars.createPinNumbers = Global.vars.createPinNumbers + pin;
        frmPinLogin_setAsteriksCreatePin();
    } else if (createpin === false && verifypin === true) {
        Global.vars.verifyPinNumbers = Global.vars.verifyPinNumbers + pin;
        frmPinLogin_setAsteriksVerifyPin();
    }
}

function frmPinLogin_backspace() {
    if (Global.vars.pinNumbers.length > 0) {
        Global.vars.pinNumbers = Global.vars.pinNumbers.slice(0, -1);
        frmPinLogin_setAsteriks();
    } else if (Global.vars.createPinNumbers.length > 0 && Global.vars.verifyPinNumbers.length === 0) {
        frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_enterPin");
        Global.vars.createPinNumbers = Global.vars.createPinNumbers.slice(0, -1);
        frmPinLogin_setAsteriksCreatePin();
    } else if (Global.vars.createPinNumbers.length === 5 && Global.vars.verifyPinNumbers.length > 0) {
        Global.vars.verifyPinNumbers = Global.vars.verifyPinNumbers.slice(0, -1);
        frmPinLogin_setAsteriksVerifyPin();
    }
}

function frmPinLogin_setAsteriks() {
    if (Global.vars.pinNumbers.length === 0) {
        frmPinLogin.imgPinStar1.src = "empty.png";
        frmPinLogin.imgPinStar2.src = "empty.png";
        frmPinLogin.imgPinStar3.src = "empty.png";
        frmPinLogin.imgPinStar4.src = "empty.png";
        frmPinLogin.imgPinStar5.src = "empty.png";
    } else if (Global.vars.pinNumbers.length === 1) {
        frmPinLogin.imgPinStar1.src = "asteriks.png";
        frmPinLogin.imgPinStar2.src = "empty.png";
        frmPinLogin.imgPinStar3.src = "empty.png";
        frmPinLogin.imgPinStar4.src = "empty.png";
        frmPinLogin.imgPinStar5.src = "empty.png";
    } else if (Global.vars.pinNumbers.length === 2) {
        frmPinLogin.imgPinStar1.src = "asteriks.png";
        frmPinLogin.imgPinStar2.src = "asteriks.png";
        frmPinLogin.imgPinStar3.src = "empty.png";
        frmPinLogin.imgPinStar4.src = "empty.png";
        frmPinLogin.imgPinStar5.src = "empty.png";
    } else if (Global.vars.pinNumbers.length === 3) {
        frmPinLogin.imgPinStar1.src = "asteriks.png";
        frmPinLogin.imgPinStar2.src = "asteriks.png";
        frmPinLogin.imgPinStar3.src = "asteriks.png";
        frmPinLogin.imgPinStar4.src = "empty.png";
        frmPinLogin.imgPinStar5.src = "empty.png";
    } else if (Global.vars.pinNumbers.length === 4) {
        frmPinLogin.imgPinStar1.src = "asteriks.png";
        frmPinLogin.imgPinStar2.src = "asteriks.png";
        frmPinLogin.imgPinStar3.src = "asteriks.png";
        frmPinLogin.imgPinStar4.src = "asteriks.png";
        frmPinLogin.imgPinStar5.src = "empty.png";
    } else if (Global.vars.pinNumbers.length === 5) {
        frmPinLogin.imgPinStar1.src = "asteriks.png";
        frmPinLogin.imgPinStar2.src = "asteriks.png";
        frmPinLogin.imgPinStar3.src = "asteriks.png";
        frmPinLogin.imgPinStar4.src = "asteriks.png";
        frmPinLogin.imgPinStar5.src = "asteriks.png";
        //now try and use pincode
        frmPinLogin_FinishedPinEntry(Global.vars.pinNumbers);
    } else if (Global.vars.pinNumbers.length > 5) {
        frmPinLogin.imgPinStar1.src = "asteriks.png";
        frmPinLogin.imgPinStar2.src = "empty.png";
        frmPinLogin.imgPinStar3.src = "empty.png";
        frmPinLogin.imgPinStar4.src = "empty.png";
        frmPinLogin.imgPinStar5.src = "empty.png";
        var startnewNumber = Global.vars.pinNumbers.slice(-1);
        Global.vars.pinNumbers = startnewNumber;
    }
}

function frmPinLogin_setAsteriksCreatePin() {
    if (Global.vars.createPinNumbers.length === 0) {
        frmPinLogin.imgPinVerify1entry1.src = "empty.png";
        frmPinLogin.imgPinVerify1entry2.src = "empty.png";
        frmPinLogin.imgPinVerify1entry3.src = "empty.png";
        frmPinLogin.imgPinVerify1entry4.src = "empty.png";
        frmPinLogin.imgPinVerify1entry5.src = "empty.png";
    } else if (Global.vars.createPinNumbers.length === 1) {
        frmPinLogin.imgPinVerify1entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry2.src = "empty.png";
        frmPinLogin.imgPinVerify1entry3.src = "empty.png";
        frmPinLogin.imgPinVerify1entry4.src = "empty.png";
        frmPinLogin.imgPinVerify1entry5.src = "empty.png";
    } else if (Global.vars.createPinNumbers.length === 2) {
        frmPinLogin.imgPinVerify1entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry2.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry3.src = "empty.png";
        frmPinLogin.imgPinVerify1entry4.src = "empty.png";
        frmPinLogin.imgPinVerify1entry5.src = "empty.png";
    } else if (Global.vars.createPinNumbers.length === 3) {
        frmPinLogin.imgPinVerify1entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry2.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry3.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry4.src = "empty.png";
        frmPinLogin.imgPinVerify1entry5.src = "empty.png";
    } else if (Global.vars.createPinNumbers.length === 4) {
        frmPinLogin.imgPinVerify1entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry2.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry3.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry4.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry5.src = "empty.png";
    } else if (Global.vars.createPinNumbers.length === 5) {
        frmPinLogin.imgPinVerify1entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry2.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry3.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry4.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry5.src = "asteriks.png";
        //now verify the pincode
        frmPinLogin_FinishedPinEntry(Global.vars.createPinNumbers); //
    } else if (Global.vars.createPinNumbers.length > 5) {
        frmPinLogin.imgPinVerify1entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify1entry2.src = "empty.png";
        frmPinLogin.imgPinVerify1entry3.src = "empty.png";
        frmPinLogin.imgPinVerify1entry4.src = "empty.png";
        frmPinLogin.imgPinVerify1entry5.src = "empty.png";
        var startnewNumber = Global.vars.createPinNumbers.slice(-1);
        Global.vars.createPinNumbers = startnewNumber;
    }
}

function frmPinLogin_setAsteriksVerifyPin() {
    if (Global.vars.verifyPinNumbers.length === 0) {
        frmPinLogin.imgPinVerify2entry1.src = "empty.png";
        frmPinLogin.imgPinVerify2entry2.src = "empty.png";
        frmPinLogin.imgPinVerify2entry3.src = "empty.png";
        frmPinLogin.imgPinVerify2entry4.src = "empty.png";
        frmPinLogin.imgPinVerify2entry5.src = "empty.png";
    } else if (Global.vars.verifyPinNumbers.length === 1) {
        frmPinLogin.imgPinVerify2entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry2.src = "empty.png";
        frmPinLogin.imgPinVerify2entry3.src = "empty.png";
        frmPinLogin.imgPinVerify2entry4.src = "empty.png";
        frmPinLogin.imgPinVerify2entry5.src = "empty.png";
    } else if (Global.vars.verifyPinNumbers.length === 2) {
        frmPinLogin.imgPinVerify2entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry2.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry3.src = "empty.png";
        frmPinLogin.imgPinVerify2entry4.src = "empty.png";
        frmPinLogin.imgPinVerify2entry5.src = "empty.png";
    } else if (Global.vars.verifyPinNumbers.length === 3) {
        frmPinLogin.imgPinVerify2entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry2.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry3.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry4.src = "empty.png";
        frmPinLogin.imgPinVerify2entry5.src = "empty.png";
    } else if (Global.vars.verifyPinNumbers.length === 4) {
        frmPinLogin.imgPinVerify2entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry2.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry3.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry4.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry5.src = "empty.png";
    } else if (Global.vars.verifyPinNumbers.length === 5) {
        frmPinLogin.imgPinVerify2entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry2.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry3.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry4.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry5.src = "asteriks.png";
        //now verify the pincode
        frmPinLogin_FinishedPinEntry(Global.vars.verifyPinNumbers);
    } else if (Global.vars.verifyPinNumbers.length > 5) {
        frmPinLogin.imgPinVerify2entry1.src = "asteriks.png";
        frmPinLogin.imgPinVerify2entry2.src = "empty.png";
        frmPinLogin.imgPinVerify2entry3.src = "empty.png";
        frmPinLogin.imgPinVerify2entry4.src = "empty.png";
        frmPinLogin.imgPinVerify2entry5.src = "empty.png";
        var startnewNumber = Global.vars.verifyPinNumbers.slice(-1);
        Global.vars.verifyPinNumbers = startnewNumber;
    }
}

function frmPinLogin_isAuthUsingTouchSupported() {
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
        var biometricType = voltmx.localAuthentication.getBiometryType();
        voltmx.print("### frmPinLogin_isAuthUsingTouchSupported biometricType: " + JSON.stringify(biometricType));
        var promptMessage = "Sign in with ";
        switch (voltmx.localAuthentication.getBiometryType()) {
            case constants.BIOMETRY_TYPE_NONE: //0
                // Handle the case if the device doesn't support any biometryType
                voltmx.print("### frmPinLogin_isAuthUsingTouchSupported none");
                break;
            case constants.BIOMETRY_TYPE_TOUCHID: //1
                voltmx.print("### frmPinLogin_isAuthUsingTouchSupported TouchID");
                promptMessage += "TouchID";
                frmPinLogin.imgIdenticate.src = "tid.png";
                frmPinLogin.flcroundfinger.skin = flcFingerprint;
                break;
            case constants.BIOMETRY_TYPE_FACEID:
                voltmx.print("### frmPinLogin_isAuthUsingTouchSupported FaceID");
                promptMessage += "FaceID";
                frmPinLogin.imgIdenticate.src = "fid.png";
                frmPinLogin.flcroundfinger.skin = flcFaceId;
                break;
            case constants.BIOMETRY_TYPE_UNDEFINED:
                voltmx.print("### frmPinLogin_isAuthUsingTouchSupported Undefined");
                // Handle the case if the device is not a iOS11 device (or) above
                break;
        }
    }
    var status = voltmx.localAuthentication.getStatusForAuthenticationMode(constants.LOCAL_AUTHENTICATION_MODE_TOUCH_ID);
    voltmx.print("### frmPinLogin_isAuthUsingTouchSupported status: " + JSON.stringify(status));
    if (status == 5000) {
        Global.vars.supportForFingerprint = true;
    } else {
        Global.vars.supportForFingerprint = false;
    }
    if (Global.vars.supportForFingerprint === true && Global.vars.pinHash != null && Global.vars.UseTouchID == "yes") {
        voltmx.print("### path 5");
        frmPinLogin_LogonTouchID();
    }
}

function frmPinLogin_onSelectionCheckBoxGroupTouchID() {
    voltmx.print("### frmPinLogin_onSelectionCheckBoxGroupTouchID selectedkeys: " + JSON.stringify(frmPinLogin.CheckBoxGroupTouchID.selectedKeys));
    if (frmPinLogin.CheckBoxGroupTouchID.selectedKeys != null && frmPinLogin.CheckBoxGroupTouchID.selectedKeys !== undefined && frmPinLogin.CheckBoxGroupTouchID.selectedKeys[0] == "Touch") {
        voltmx.print("### Check touch id");
        //if(Global.vars.firstLogin == "yes"){
        frmPinLogin_firstAuthUsingTouchID();
        //}
    }
}

function frmPinLogin_firstAuthUsingTouchID() {
    voltmx.print("### path 8");
    var config = {
        "promptMessage": voltmx.i18n.getLocalizedString("l_verifyTouchId")
    };
    voltmx.localAuthentication.authenticate(constants.LOCAL_AUTHENTICATION_MODE_TOUCH_ID, frmPinLogin_Verify_statusCB, config);
}

function frmPinLogin_Verify_statusCB(status, message) {
    voltmx.print("### path 9");
    voltmx.print("### status: " + status);
    voltmx.print("### message: " + JSON.stringify(message));
    if (status == 5000) {
        alert(voltmx.i18n.getLocalizedString("l_touchIdSucces"));
        Global.vars.UseTouchID = "yes";
        voltmx.store.setItem("UseTouchID", Global.vars.UseTouchID);
    } else if (status == 5001) {
        alert(voltmx.i18n.getLocalizedString("l_touchIdFail"));
    } else if (status == 5002 || status == 5003) {
        voltmx.print("### TOUCH ID cancelled by user");
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_touchIdWillNotUse"), frmPinLogin_alertConfirmation, "confirmation", voltmx.i18n.getLocalizedString("bt_yes"), voltmx.i18n.getLocalizedString("bt_no"), "Info", null);
    } else if (status == 5004) {
        voltmx.print("### TOUCH ID cancelled by system");
    } else if (status == 5005) {
        alert(voltmx.i18n.getLocalizedString("l_doNotHaveLogOnCode"));
    } else if (status == 5007) {
        alert(voltmx.i18n.getLocalizedString("l_doNotHaveFingerprints"));
    } else {
        alert(voltmx.i18n.getLocalizedString("l_touchIdFail"));
    }
}

function frmPinLogin_LogonTouchID() {
    voltmx.print("### frmPinLogin_LogonTouchID");
    voltmx.print("### frmPinLogin_LogonTouchID Global.vars.previousForm: " + Global.vars.previousForm);
    Global.vars.previousForm = null;
    var config = {
        "promptMessage": voltmx.i18n.getLocalizedString("l_verifyTouchId")
    };
    voltmx.print("### path 6");
    voltmx.localAuthentication.authenticate(constants.LOCAL_AUTHENTICATION_MODE_TOUCH_ID, frmPinLogin_Logon_statusCB, config);
    Global.vars.UseTouchID = "yes";
    voltmx.store.setItem("UseTouchID", Global.vars.UseTouchID);
}

function frmPinLogin_Logon_statusCB(status, message) {
    voltmx.print("### path 7");
    voltmx.print("### status: " + status);
    voltmx.print("### message: " + JSON.stringify(message));
    if (voltmx.application.getCurrentForm().id == "frmPinLogin") {
        if (status == 5000) {
            frmPinLogin_resetPinRetry();
            frmSynchronisation_skipSync(); //frmSynchronization.show();
        } else if (status == 5001) {
            alert(voltmx.i18n.getLocalizedString("l_touchIdFail"));
        } else if (status == 5002 || status == 5003) {
            voltmx.print("### TOUCH ID cancelled by user");
        } else if (status == 5004) {
            voltmx.print("### TOUCH ID cancelled by system");
        } else if (status == 5005) {
            alert(voltmx.i18n.getLocalizedString("l_doNotHaveLogOnCode"));
        } else if (status == 5007) {
            alert(voltmx.i18n.getLocalizedString("l_doNotHaveFingerprints"));
        } else {
            alert(voltmx.i18n.getLocalizedString("l_touchIdFail"));
        }
    }
}

function frmPinLogin_LoginPin(pin) {
    if (frmPinLogin.lblSubHeader.text == voltmx.i18n.getLocalizedString("l_createPin")) {
        frmPinLogin_createPin(pin);
    } else if (frmPinLogin.lblSubHeader.text == voltmx.i18n.getLocalizedString("l_login")) {
        frmPinLogin_checkPin(pin);
    } else if (frmPinLogin.lblSubHeader.text == voltmx.i18n.getLocalizedString("l_checkPin")) {
        frmPinLogin_checkPin(pin);
    }
}

function frmPinLogin_createPin(pincode) {
    var pin = pincode;
    var pinSameNumber = /^([0-9])\1*$/.test(pincode);
    //voltmx.print("### frmPinLogin_createPin pinSameNumber: " + pinSameNumber);
    var pinNotSequentNumbers = false;
    var s = pincode;
    var numbers = "**********";
    //If reverse sequence is also needed to be checked
    var numbersRev = "**********";
    //Returns false, if the number is in sequence
    if (numbers.indexOf(String(pincode)) === -1 && numbersRev.indexOf(String(pincode)) === -1) {
        pinNotSequentNumbers = true;
    }
    //voltmx.print("### frmPinLogin_createPin pinNotSequentNumbers: " + pinNotSequentNumbers);
    if (pin.length < 5) {
        alert(voltmx.i18n.getLocalizedString("l_give5NumberPin"));
    } else if (pinSameNumber === true || pinNotSequentNumbers === false) {
        alert(voltmx.i18n.getLocalizedString("l_pinNotUsable"));
    } else {
        //hash the pin and save
        var salt = Utility_decompress("4oGO4pyh4rKp");
        Global.vars.pinHash = frmPinLogin_hashPin(pin, salt);
        if (Global.vars.pinHash.length > 0) {
            Utility_storeSetItem("pinHash", Global.vars.pinHash);
            Global.vars.usePin = "yes";
            voltmx.store.setItem("usePin", Global.vars.usePin);
            if ((frmPinLogin.CheckBoxGroupTouchID.selectedKeys === null || frmPinLogin.CheckBoxGroupTouchID.selectedKeys === undefined) && frmPinLogin.flcUseTouchID.isVisible === true) {
                voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_wantToUseTouchId"), frmPinLogin_alertConfirmation, "confirmation", voltmx.i18n.getLocalizedString("bt_yes"), voltmx.i18n.getLocalizedString("bt_no"), "Info", null);
            } else {
                if (Global.vars.resetPin === true || Global.vars.createPinAfter === true) {
                    Global.vars.resetPin = false;
                    Global.vars.createPinAfter = false;
                    back_to_Settings_Menu();
                } else {
                    Global_setMenuSettings();
                    frmSynchronisation_skipSync(); //frmSynchronization.show();
                }
            }
        }
    }
}

function frmPinLogin_alertConfirmation(response) {
    if (response) {
        Global.vars.UseTouchID = "yes";
        voltmx.store.setItem("UseTouchID", Global.vars.UseTouchID);
        frmPinLogin_firstAuthUsingTouchID();
    } else {
        voltmx.print("### User does not want Touch ID");
        Global.vars.UseTouchID = "no";
        voltmx.store.setItem("UseTouchID", Global.vars.UseTouchID);
        frmSynchronisation_skipSync(); //frmSynchronization.show();
    }
}

function frmPinLogin_hashPin(pin, salt) {
    try {
        var algo = "sha512";
        var inputstr = pin + salt;
        if (pin === "" || pin === null) {
            alert(voltmx.i18n.getLocalizedString("l_noPin"));
        } else {
            var myHashValue = voltmx.crypto.createHash(algo, inputstr);
            voltmx.print("### myHashValue: " + myHashValue);
            if (myHashValue.length > 0) {
                return myHashValue;
            }
        }
    } catch (err) {
        alert(err.message);
    }
}

function frmPinLogin_checkPin(pincode) {
    voltmx.print("### frmPinLogin_checkPin");
    var pin = pincode;
    if (pin.length < 5) {
        alert(voltmx.i18n.getLocalizedString("l_give5NumberPin"));
    } else {
        //hash the pin and save
        var salt = Utility_decompress("4oGO4pyh4rKp");
        currentHash = frmPinLogin_hashPin(pin, salt);
        if (currentHash.length > 0) {
            if (Global.vars.pinHash === null) {
                if (Global.vars.resetPin === false) { //TEST!!
                    Global.vars.pinHash = Utility_storeGetItem("pinHash");
                }
            }
            if (Global.vars.pinHash === currentHash) {
                voltmx.print("### frmPinLogin_checkPin pin verified");
                frmPinLogin_resetPinRetry();
                if (frmPinLogin.lblSubHeader.text == voltmx.i18n.getLocalizedString("l_checkPin")) {
                    frmPinLogin_resetPin();
                } else {
                    if (Global.vars.previousForm != null) {
                        back_to_Settings_Menu();
                    } else {
                        frmSynchronisation_skipSync(); //frmSynchronization.show();
                    }
                }
            } else {
                Global.vars.pinRetries = Number(Global.vars.pinRetries) + 1;
                voltmx.store.setItem("pinRetries", Global.vars.pinRetries);
                if (Number(Global.vars.pinRetries) > 4) {
                    voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_resetAfter5Tries"), frmPinLogin_resetApplication, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
                } else {
                    alert(voltmx.i18n.getLocalizedString("l_pinNotCorrect") + " " + voltmx.i18n.getLocalizedString("l_attempt") + ": " + Global.vars.pinRetries + "/5");
                }
            }
        }
    }
}

function frmPinLogin_resetApplication(response) {
    if (response) {
        frmPinLogin_resetPinRetry();
        frmInfo_clearUserAndDataStoreItems();
        Global_logoutIdentity();
    }
}

function frmPinLogin_resetPinRetry() {
    Global.vars.pinRetries = 0;
    voltmx.store.setItem("pinRetries", Global.vars.pinRetries);
}

function frmPinLogin_FinishedPinEntry(pin) {
    var pintotal = pin;
    if (pintotal.length < 5) {
        alert(voltmx.i18n.getLocalizedString("l_enter5DigitPin"));
    } else {
        if (frmPinLogin.lblPageHeader.text == voltmx.i18n.getLocalizedString("l_enterPin")) {
            var pinSameNumber = /^([0-9])\1*$/.test(pintotal);
            //voltmx.print("### frmPinLogin_FinishedPinEntry pinSameNumber: " + pinSameNumber);
            var pinNotSequentNumbers = false;
            var numbers = "**********";
            //If reverse sequence is also needed to be checked
            var numbersRev = "**********";
            //Returns false, if the number is in sequence
            if (numbers.indexOf(String(pin)) === -1 && numbersRev.indexOf(String(pin)) === -1) {
                pinNotSequentNumbers = true;
            }
            //voltmx.print("### frmPinLogin_FinishedPinEntry pinNotSequentNumbers: " + pinNotSequentNumbers);
            if (pinSameNumber === true || pinNotSequentNumbers === false) {
                alert(voltmx.i18n.getLocalizedString("l_pinNotUsable"));
                //remove pinnumbers and astrix
                Global.vars.createPinNumbers = "";
                Global.vars.verifyPinNumbers = "";
                frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_enterPin");
                frmPinLogin.imgPinVerify1entry1.src = "empty.png";
                frmPinLogin.imgPinVerify1entry2.src = "empty.png";
                frmPinLogin.imgPinVerify1entry3.src = "empty.png";
                frmPinLogin.imgPinVerify1entry4.src = "empty.png";
                frmPinLogin.imgPinVerify1entry5.src = "empty.png";
                frmPinLogin.imgPinVerify2entry1.src = "empty.png";
                frmPinLogin.imgPinVerify2entry2.src = "empty.png";
                frmPinLogin.imgPinVerify2entry3.src = "empty.png";
                frmPinLogin.imgPinVerify2entry4.src = "empty.png";
                frmPinLogin.imgPinVerify2entry5.src = "empty.png";
            } else {
                frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_verifyPin");
            }
        } else if (frmPinLogin.lblPageHeader.text == voltmx.i18n.getLocalizedString("l_verifyPin")) {
            if (Global.vars.createPinNumbers == Global.vars.verifyPinNumbers) {
                frmPinLogin_LoginPin(pintotal);
            } else {
                alert(voltmx.i18n.getLocalizedString("l_pinsDoNotMatch"));
                Global.vars.createPinNumbers = "";
                Global.vars.verifyPinNumbers = "";
                frmPinLogin.lblPageHeader.text = voltmx.i18n.getLocalizedString("l_enterPin");
                frmPinLogin.imgPinVerify1entry1.src = "empty.png";
                frmPinLogin.imgPinVerify1entry2.src = "empty.png";
                frmPinLogin.imgPinVerify1entry3.src = "empty.png";
                frmPinLogin.imgPinVerify1entry4.src = "empty.png";
                frmPinLogin.imgPinVerify1entry5.src = "empty.png";
                frmPinLogin.imgPinVerify2entry1.src = "empty.png";
                frmPinLogin.imgPinVerify2entry2.src = "empty.png";
                frmPinLogin.imgPinVerify2entry3.src = "empty.png";
                frmPinLogin.imgPinVerify2entry4.src = "empty.png";
                frmPinLogin.imgPinVerify2entry5.src = "empty.png";
            }
        } else {
            frmPinLogin_LoginPin(pintotal);
        }
    }
}

function frmPinLogin_onclick_btnBack() {
    Global.vars.resetPin = false;
    back_to_Settings_Menu();
}