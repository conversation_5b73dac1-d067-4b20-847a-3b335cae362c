function frmPersonLegalEntityResult_btnDone_setVisibility(boolean) {
    voltmx.print("### frmPersonLegalEntityResult_btnDone_setVisibility");

    function btnDone_setVisibility() {
        voltmx.print("### frmPersonLegalEntityResult_btnDone_setVisibility btnDone_setVisibility: " + boolean);
        frmPersonLegalEntityResult.btnDone.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnDone_setVisibility, []);
}

function frmPersonLegalEntityResult_init() {
    voltmx.print("### frmPersonLegalEntityResult_init");
    //Utility_registerForIdleTimeout();
    frmPersonLegalEntityResult.onDeviceBack = Global_onDeviceBack;
}

function frmPersonLegalEntityResult_preshow() {
    Analytics_logScreenView("person-legal-entity-result");
    voltmx.print("### frmPersonLegalEntityResult_preshow");
    frmPersonLegalEntityResult_resetFields();
    frmPersonLegalEntityResult_setGlobalsToFields();
    if (Global.vars.previousForm == "frmHandleReport") {
        frmPersonLegalEntityResult_btnDone_setVisibility(false);
    } else {
        frmPersonLegalEntityResult_btnDone_setVisibility(true);
    }
}

function frmPersonLegalEntityResult_setGlobalsToFields() {
    voltmx.print("### frmPersonLegalEntityResult_setGlobalsToFields");
    //LegalEntity info
    frmPersonLegalEntityResult.lblCoCNumber.text = Global.vars.gCaseLegalEntities.LegalEntityCocNumber;
    frmPersonLegalEntityResult.lblLegalEntityName.text = Global.vars.gCaseLegalEntities.LegalEntityName;
    frmPersonLegalEntityResult.lblZipCode.text = Global.vars.gCaseLegalEntities.addresses[0].Zipcode;
    frmPersonLegalEntityResult.lblStreet.text = Global.vars.gCaseLegalEntities.addresses[0].Street;
    frmPersonLegalEntityResult.lblStreetNumber.text = Global.vars.gCaseLegalEntities.addresses[0].StreetNumber;
    frmPersonLegalEntityResult.lblHouseNumberAddition.text = Global.vars.gCaseLegalEntities.addresses[0].StreetNumAdditn;
    frmPersonLegalEntityResult.lblPlaceOfResidency.text = Global.vars.gCaseLegalEntities.addresses[0].City;
}

function frmPersonLegalEntityResult_resetFields() {
    frmPersonLegalEntityResult.lblCoCNumber.text = "";
    frmPersonLegalEntityResult.lblLegalEntityName.text = "";
    frmPersonLegalEntityResult.lblZipCode.text = "";
    frmPersonLegalEntityResult.lblStreet.text = "";
    frmPersonLegalEntityResult.lblStreetNumber.text = "";
    frmPersonLegalEntityResult.lblHouseNumberAddition.text = "";
    frmPersonLegalEntityResult.lblPlaceOfResidency.text = "";
}

function frmPersonLegalEntityResult_callbackfunctionCoC(status, result) {
    //wsstatuscode 0 is ok, 1 is not found, 2 is multiple results, 99 is error
}

function frmPersonLegalEntityResult_btnback() {
    if (Global.vars.previousForm == "frmPersonLegalEntityAddress") {
        frmPersonLegalEntityAddress.show();
    } else if (Global.vars.previousForm == "frmHandleReport") {
        frmHandleReport.show();
    }
}

function frmPersonLegalEntityResult_onDone() {
    gCase.legalentities[Global.vars.gCaseLegalEntitiesIndex] = Global.vars.gCaseLegalEntities;
    voltmx.print("### frmPersonLegalEntityResult_onDone Global.vars.gCaseLegalEntitiesIndex: " + Global.vars.gCaseLegalEntitiesIndex);
    voltmx.print("### frmPersonLegalEntityResult_onDone gCase.legalentities: " + JSON.stringify(gCase.legalentities[Global.vars.gCaseLegalEntitiesIndex]));
    voltmx.print("### frmPersonLegalEntityResult_onDone gCase.legalentities index: " + gCase.legalentities[Global.vars.gCaseLegalEntitiesIndex].index);
    // set edited to true
    gCase.legalentities[Global.vars.gCaseLegalEntitiesIndex].validated = true; // once legalentity is filled set to true
    gCase.legalentities[Global.vars.gCaseLegalEntitiesIndex].edited = true; // once legalentity is filled set to true
    gCase.legalentities[Global.vars.gCaseLegalEntitiesIndex].used = true; // once legalentity is filled set to true
    frmPersonLegalEntityResult_showAlertAddAnother();
}

function frmPersonLegalEntityResult_showAlertAddAnother() {
    voltmx.ui.Alert("Wilt u nog een rechtspersoon opvoeren?", frmPersonLegalEntityResult_confirm_AddNewLegalEntity, "confirmation", voltmx.i18n.getLocalizedString("bt_yes"), voltmx.i18n.getLocalizedString("bt_no"), "Info", null);
}

function frmPersonLegalEntityResult_confirm_AddNewLegalEntity(response) {
    if (response) {
        frmPersonLegalEntityResult_AddNewLegalEntity();
    } else {
        frmHandleReport.show();
    }
}

function frmPersonLegalEntityResult_AddNewLegalEntity() {
    Global.vars.gCaseLegalEntitiesIndex = gCase.legalentities.length;
    Global.vars.gCaseLegalEntities = CaseUtil_getNewLegalEntity(Global.vars.gCaseLegalEntitiesIndex);
    gCase.legalentities.push(Global.vars.gCaseLegalEntities);
    frmPerson.show();
}