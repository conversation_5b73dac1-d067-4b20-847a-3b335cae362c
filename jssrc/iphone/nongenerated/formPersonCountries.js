function frmPersonCountries_flcSearchHolder_setVisibility(boolean) {
    voltmx.print("### frmPersonCountries_flcSearchHolder_setVisibility");

    function flcSearchHolder_setVisibility() {
        voltmx.print("### frmPersonCountries_flcSearchHolder_setVisibility flcSearchHolder_setVisibility: " + boolean);
        frmPersonCountries.flcSearchHolder.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcSearchHolder_setVisibility, []);
}

function frmPersonCountries_btnBack_setVisibility(boolean) {
    voltmx.print("### frmPersonCountries_btnBack_setVisibility");

    function btnBack_setVisibility() {
        voltmx.print("### frmPersonCountries_btnBack_setVisibility btnBack_setVisibility: " + boolean);
        frmPersonCountries.btnBack.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnBack_setVisibility, []);
}

function frmPersonCountries_init() {
    voltmx.print("#### frmPersonCountries_init");
    //Utility_registerForIdleTimeout();
    frmPersonCountries.onDeviceBack = Global_onDeviceBack;
    frmPersonCountries.txtSearch.text = "";
    frmPersonCountries.txtSearch.placeholder = frmPersonCountries.txtSearch.placeholder + " (" + voltmx.i18n.getLocalizedString("l_3Characterinput") + ")";
    frmPersonCountries.segSearch.widgetDataMap = {
        lbl1: "lbl1"
    };
}

function frmPersonCountries_preshow() {
    Analytics_logScreenView("person-countries");
    voltmx.print("### frmPersonCountries_preShow Global.vars.personCountryType: " + Global.vars.personCountryType);
    voltmx.print("### frmPersonCountries_preShow Global.vars.previousForm: " + Global.vars.previousForm);
    voltmx.print("#### Search text: " + frmPersonCountries.txtSearch.text);
    frmPersonCountries.segSearch.removeAll();
    frmPersonCountries_flcSearchHolder_setVisibility(true);
    frmPersonCountries_btnBack_setVisibility(false);
    frmPersonCountries.btnSearch.text = voltmx.i18n.getLocalizedString("bt_cancel");
    if (frmPersonCountries.txtSearch.text !== "" && frmPersonCountries.txtSearch.text != null) {
        frmPersonCountries_TextSearch();
    } else {
        frmPersonCountries_getCountries();
    }
}

function frmPersonCountries_toggleSearch() {
    if (frmPersonCountries.btnSearch.text == voltmx.i18n.getLocalizedString("l_search")) {
        frmPersonCountries_flcSearchHolder_setVisibility(true);
        frmPersonCountries_btnBack_setVisibility(false);
        frmPersonCountries.btnSearch.text = voltmx.i18n.getLocalizedString("bt_cancel");
    } else {
        frmPersonCountries_flcSearchHolder_setVisibility(false);
        frmPersonCountries_btnBack_setVisibility(true);
        frmPersonCountries.btnSearch.text = voltmx.i18n.getLocalizedString("l_search");
        frmPersonCountries.txtSearch.text = "";
        frmPersonCountries_TextSearch();
    }
}

function frmPersonCountries_clearSearch() {
    frmPersonCountries.txtSearch.text = "";
    frmPersonCountries.txtSearch.setFocus(true);
    frmPersonCountries_TextSearch();
}

function frmPersonCountries_getCountries() {
    var Countries = {
        showCount: 100,
        startRec: 0,
        endRec: 100,
        orderBy: "ORDER BY description ASC",
        whereClause: "select * from mle_v_country_m where (UPPER(description) like '%" + frmPersonCountries.txtSearch.text.toUpperCase() + "%')"
    };
    voltmx.print("### frmPersonCountries_getCountries get: " + Countries.showCount + " Countries");
    var loadingText = voltmx.i18n.getLocalizedString("l_loading") + " " + voltmx.i18n.getLocalizedString("l_countries") + " ...";
    var wcs = Utility_addTimelineToWhereClauseObjectSync(Countries.whereClause, CaseData.time.dateComponents);
    if (Global.vars.findCountryOfBirth === true) {
        wcs = Utility_addTimelineToWhereClauseObjectSync(Countries.whereClause, Global.vars.gCasePersons.birthdateComponents);
    }
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    wcs = wcs + " " + Countries.orderBy; // + " LIMIT " + Countries.showCount + " OFFSET " + Countries.startRec;
    voltmx.print("### frmPersonCountries_getCountries Countries wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonCountries_successCallback, frmPersonCountries_errorCallback);
}

function frmPersonCountries_successCallback(result) {
    voltmx.print("### frmPersonCountries_successCallback result: " + JSON.stringify(result));
    if (result.length >= 1) {
        for (var i = 0;
            ((result) != null) && i < result.length; i++) {
            var v = result[i];
            result[i].lbl1 = result[i].description;
            result[i].orderSequence = Utility_getSequenceOrder(result[i].description, frmPersonCountries.txtSearch.text);
            if (v.code === Global.vars.CountryCode) {
                v.orderSequence = "0_" + v.description;
            }
        }
        // Sort
        result.sort((a, b) => a.orderSequence.localeCompare(b.orderSequence, undefined, {
            numeric: true,
            sensitivity: 'base'
        }));
        frmPersonCountries.segSearch.setData(result);
    } else {
        var value = [{
            lbl1: voltmx.i18n.getLocalizedString("l_noCountriesFound")
        }];
        frmPersonCountries.segSearch.setData(value);
    }
}

function frmPersonCountries_errorCallback(error) {
    voltmx.print("### frmPersonCountries_errorCallback Countries error: " + error);
}

function frmPersonCountries_onClick_segment() {
    var focusedCharacteristic = frmPersonCountries.segSearch.selectedItems;
    if (focusedCharacteristic[0].lbl1 != voltmx.i18n.getLocalizedString("l_noCountriesFound")) {
        try {
            Global.vars.findCountryOfBirth = false;
            voltmx.print("#### frmPersonCountries_onClick_segment focuseditem: " + JSON.stringify(focusedCharacteristic));
            if (Global.vars.previousForm == "frmPerson" && Global.vars.personCountryType != "documentcountryfrmPersonDocument") {
                Global.vars.gCasePersons.countryIdenDoc = focusedCharacteristic[0].code;
                Global.vars.gCasePersons.countryIdenDocDesc = focusedCharacteristic[0].lbl1;
                frmPerson.lblDocumentCountry.text = focusedCharacteristic[0].lbl1;
                frmPerson.show();
            } else if (Global.vars.personCountryType == "documentcountryfrmPersonDocument") {
                Global.vars.gCasePersons.countryIdenDoc = focusedCharacteristic[0].code;
                Global.vars.gCasePersons.countryIdenDocDesc = focusedCharacteristic[0].lbl1;
                frmPersonDocument.lblDocumentCountry.text = focusedCharacteristic[0].lbl1;
                frmPersonDocument.show();
            } else if (Global.vars.previousForm == "frmPersonManualPerson" && Global.vars.personCountryType == "documentcountry") {
                Global.vars.gCasePersons.countryIdenDoc = focusedCharacteristic[0].code;
                Global.vars.gCasePersons.countryIdenDocDesc = focusedCharacteristic[0].lbl1;
                frmPersonManualPerson.lblDocumentCountry.text = focusedCharacteristic[0].lbl1;
                Global.vars.documentCountryPersonManual = true;
                Global.vars.personCountryType = "";
                frmPersonManualPerson.show();
            } else if (Global.vars.previousForm == "frmPersonManualPerson" && Global.vars.personCountryType !== "documentcountry") {
                Global.vars.gCasePersons.countryOfBirth = focusedCharacteristic[0].code;
                Global.vars.gCasePersons.countryOfBirthDesc = focusedCharacteristic[0].lbl1;
                frmPersonManualPerson.lblCountryOfOrigin.text = focusedCharacteristic[0].lbl1;
                if (Global.vars.gCasePersons.countryOfBirth == Global.vars.CountryCode) {
                    frmPersonManualPerson_flcMunicipalityOfBirth_setVisibility(true);
                    frmPersonManualPerson_flcPlaceOfBirth_setVisibility(false);
                    frmPersonManualPerson.txtPlaceOfBirth.text = "";
                    Global.vars.gCasePersons.Birthplace = "";
                } else {
                    frmPersonManualPerson_flcMunicipalityOfBirth_setVisibility(false);
                    frmPersonManualPerson.lblMunicipalityOfBirth.text = "Geboorte gemeente"; //voltmx.i18n.getLocalizedString("l_municipalityOfBirth");
                    frmPersonManualPerson_flcPlaceOfBirth_setVisibility(true);
                    Global.vars.gCasePersons.birthMunicipNL = "";
                    Global.vars.gCasePersons.birthMunicipNLDesc = "";
                    frmPersonManualPerson.txtPlaceOfBirth.text = "";
                }
                frmPersonManualPerson.show();
            } else if (Global.vars.previousForm == "frmPersonManualAddress") {
                Global.vars.gCasePersons.addresses[0].countryCode = focusedCharacteristic[0].code;
                Global.vars.gCasePersons.addresses[0].country = focusedCharacteristic[0].lbl1;
                frmPersonManualAddress.lblCountryAdress.text = focusedCharacteristic[0].lbl1;
                frmPersonManualAddress.show();
            }
            frmPersonCountries.txtSearch.text = "";
        } catch (error) {
            voltmx.print("#### frmPersonCountries_onClick_segment error: " + JSON.stringify(error));
            voltmx.print("#### frmPersonCountries_onClick_segment error msg: " + JSON.stringify(error.message));
        }
    }
}
// Search Country function
function frmPersonCountries_TextSearch() {
    if (frmPersonCountries.txtSearch.text.length > 2 || frmPersonCountries.txtSearch.text.length === 0) {
        frmPersonCountries_getCountries();
    }
}

function frmPersonCountries_onTextChange() {
    var textlength = frmPersonCountries.txtSearch.text.length;
    if (textlength < 1) {
        frmPersonCountries_getCountries();
    } else if (textlength > 0) {
        frmPersonCountries_TextSearch();
    }
}

function frmPersonCountries_btnback() {
    frmPersonCountries.txtSearch.text = "";
    Global.vars.findCountryOfBirth = false;
    if (Global.vars.previousForm == "frmPerson" && Global.vars.personCountryType != "documentcountryfrmPersonDocument") {
        frmPerson.show();
    } else if (Global.vars.personCountryType == "documentcountryfrmPersonDocument") {
        frmPersonDocument.show();
    } else if (Global.vars.previousForm == "frmPersonManualPerson" && Global.vars.personCountryType == "documentcountry") {
        frmPersonManualPerson.show();
    } else if (Global.vars.previousForm == "frmPersonManualPerson" && Global.vars.personCountryType !== "documentcountry") {
        frmPersonManualPerson.show();
    } else if (Global.vars.previousForm == "frmPersonManualAddress") {
        frmPersonManualAddress.show();
    }
}