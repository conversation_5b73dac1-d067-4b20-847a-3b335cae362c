function frmPersonAlternativeSearch_calDateOfBirth_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_calDateOfBirth_setVisibility");

    function calDateOfBirth_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_calDateOfBirth_setVisibility calDateOfBirth_setVisibility: " + boolean);
        frmPersonAlternativeSearch.calDateOfBirth.setVisibility(boolean);
    }
    voltmx.runOnMainThread(calDateOfBirth_setVisibility, []);
}

function frmPersonAlternativeSearch_lblDateOfBirth_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_lblDateOfBirth_setVisibility");

    function lblDateOfBirth_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_lblDateOfBirth_setVisibility lblDateOfBirth_setVisibility: " + boolean);
        frmPersonAlternativeSearch.lblDateOfBirth.setVisibility(boolean);
    }
    voltmx.runOnMainThread(lblDateOfBirth_setVisibility, []);
}

function frmPersonAlternativeSearch_btnManualDate_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_btnManualDate_setVisibility");

    function btnManualDate_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_btnManualDate_setVisibility btnManualDate_setVisibility: " + boolean);
        frmPersonAlternativeSearch.btnManualDate.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnManualDate_setVisibility, []);
}

function frmPersonAlternativeSearch_editdatetime_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_editdatetime_setVisibility");

    function editdatetime_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_editdatetime_setVisibility editdatetime_setVisibility: " + boolean);
        frmPersonAlternativeSearch.editdatetime.setVisibility(boolean);
    }
    voltmx.runOnMainThread(editdatetime_setVisibility, []);
}

function frmPersonAlternativeSearch_flcToggleSearchMethod_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_flcToggleSearchMethod_setVisibility");

    function flcToggleSearchMethod_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_flcToggleSearchMethod_setVisibility flcToggleSearchMethod_setVisibility: " + boolean);
        frmPersonAlternativeSearch.flcToggleSearchMethod.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcToggleSearchMethod_setVisibility, []);
}

function frmPersonAlternativeSearch_flcFirstName_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_flcFirstName_setVisibility");

    function flcFirstName_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_flcFirstName_setVisibility flcFirstName_setVisibility: " + boolean);
        frmPersonAlternativeSearch.flcFirstName.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcFirstName_setVisibility, []);
}

function frmPersonAlternativeSearch_flcMiddleName_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_flcMiddleName_setVisibility");

    function flcMiddleName_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_flcMiddleName_setVisibility flcMiddleName_setVisibility: " + boolean);
        frmPersonAlternativeSearch.flcMiddleName.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcMiddleName_setVisibility, []);
}

function frmPersonAlternativeSearch_flcSurname_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_flcSurname_setVisibility");

    function flcSurname_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_flcSurname_setVisibility flcSurname_setVisibility: " + boolean);
        frmPersonAlternativeSearch.flcSurname.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcSurname_setVisibility, []);
}

function frmPersonAlternativeSearch_flcZipCode_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_flcZipCode_setVisibility");

    function flcZipCode_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_flcZipCode_setVisibility flcZipCode_setVisibility: " + boolean);
        frmPersonAlternativeSearch.flcZipCode.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcZipCode_setVisibility, []);
}

function frmPersonAlternativeSearch_flcStreetNumber_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_flcStreetNumber_setVisibility");

    function flcStreetNumber_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_flcStreetNumber_setVisibility flcStreetNumber_setVisibility: " + boolean);
        frmPersonAlternativeSearch.flcStreetNumber.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcStreetNumber_setVisibility, []);
}

function frmPersonAlternativeSearch_flcCompleteManual_setVisibility(boolean) {
    voltmx.print("### frmPersonAlternativeSearch_flcCompleteManual_setVisibility");

    function flcCompleteManual_setVisibility() {
        voltmx.print("### frmPersonAlternativeSearch_flcCompleteManual_setVisibility flcCompleteManual_setVisibility: " + boolean);
        frmPersonAlternativeSearch.flcCompleteManual.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcCompleteManual_setVisibility, []);
}

function frmPersonAlternativeSearch_init() {
    voltmx.print("#### frmPersonAlternativeSearch_init");
    //Utility_registerForIdleTimeout();
    frmPersonAlternativeSearch.onDeviceBack = Global_onDeviceBack;
    frmPersonAlternativeSearch_setGenders();
    frmPersonAlternativeSearch_setDate();
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
        frmPersonAlternativeSearch.lbxGender.centerX = 52 + "%";
        frmPersonAlternativeSearch.calDateOfBirth.centerX = 52 + "%";
    }
    // RL-384
    frmPersonAlternativeSearch.lbxGender.expandListItemToParentWidth = true;
    //
    if (Global.vars.setDateManual === true) {
        frmPersonAlternativeSearch_calDateOfBirth_setVisibility(false);
        frmPersonAlternativeSearch_lblDateOfBirth_setVisibility(true);
        frmPersonAlternativeSearch_btnManualDate_setVisibility(true);
    } else {
        frmPersonAlternativeSearch_calDateOfBirth_setVisibility(true);
        frmPersonAlternativeSearch_lblDateOfBirth_setVisibility(false);
        frmPersonAlternativeSearch_btnManualDate_setVisibility(false);
    }
    frmPersonAlternativeSearch.editdatetime.flcFooterMainHandleTimeCancel.setVisibility(true);
    frmPersonAlternativeSearch.editdatetime.btnCancelEditTime.onClick = frmPersonAlternativeSearch_hideShowEditTime;
}

function frmPersonAlternativeSearch_preshow() {
    Analytics_logScreenView("person-alternative-search");
    voltmx.print("#### frmPersonAlternativeSearch_preshow");
    frmPersonAlternativeSearch_clearDateOfBirth();
    voltmx.print("#### frmPersonAlternativeSearch_preshow: " + JSON.stringify(Global.vars.gCasePersons));
    frmPersonAlternativeSearch_flcToggleSearchMethod_setVisibility(true);
    frmPersonAlternativeSearch_onclick_btnButAlternativeMethod1();
    if (Global.vars.gCasePersons.nationalityDesc != null && Global.vars.gCasePersons.nationalityDesc !== "") {
        frmPersonAlternativeSearch.lblPersonNationality.text = Global.vars.gCasePersons.nationalityDesc;
        frmPersonAlternativeSearch.flcPersonNationality.skin = flcFieldEdge;
        frmPersonAlternativeSearch.lblPersonNationality.skin = lblFieldInfo;
    } else {
        frmPersonAlternativeSearch_getNationality(true);
    }
    if (Global.vars.gCasePersons.surname != null && Global.vars.gCasePersons.surname !== "") {
        frmPersonAlternativeSearch.txtSurname.text = Global.vars.gCasePersons.surname;
    }
    if (Global.vars.gCasePersons.birthdateComponents.length > 0) {
        voltmx.print("#### frmPersonAlternativeSearch_preshow birthdateComponents.length > 0");
        frmPersonAlternativeSearch.calDateOfBirth.dateComponents = Global.vars.gCasePersons.birthdateComponents;
        if (Global.vars.setDateManual === true) {
            frmPersonAlternativeSearch.editdatetime.txtDay.text = Global.vars.gCasePersons.birthdateComponents[0].toString().lpad("0", 2);
            frmPersonAlternativeSearch.editdatetime.txtMonth.text = Global.vars.gCasePersons.birthdateComponents[1].toString().lpad("0", 2);
            frmPersonAlternativeSearch.editdatetime.txtYear.text = Global.vars.gCasePersons.birthdateComponents[2].toString().lpad("0", 4);
            frmPersonAlternativeSearch.lblDateOfBirth.text = frmPersonAlternativeSearch.editdatetime.txtDay.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtYear.text;
            frmPersonAlternativeSearch.lblDateOfBirth.skin = lblFieldInfo;
        }
    } else {
        voltmx.print("#### frmPersonAlternativeSearch_preshow set a date for easy search");
        frmPersonAlternativeSearch.lblDateOfBirth.skin = lblFieldNotFilled;
        if (Global.vars.setDateManual === false) {
            var birthdate = [30, 6, "1997", 0, 0, 0];
            frmPersonAlternativeSearch.calDateOfBirth.dateComponents = birthdate;
        }
        //       	if(Global.vars.setDateManual === true){
        //           frmPersonAlternativeSearch.editdatetime.txtDay.text = birthdate[0].toString().lpad("0", 2);
        //           frmPersonAlternativeSearch.editdatetime.txtMonth.text = birthdate[1].toString().lpad("0", 2);
        //           frmPersonAlternativeSearch.editdatetime.txtYear.text = birthdate[2].toString().lpad("0", 4);
        //           frmPersonAlternativeSearch.lblDateOfBirth.text = frmPersonAlternativeSearch.editdatetime.txtDay.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtYear.text; 
        //         }
    }
    if (Global.vars.gCasePersons.gender !== undefined && Global.vars.gCasePersons.gender != null && Global.vars.gCasePersons.gender !== "") {
        frmPersonAlternativeSearch.lbxGender.selectedKey = Global.vars.gCasePersons.gender.toString();
        frmPersonAlternativeSearch_onSelectGender();
    }
    if (Global.vars.gCasePersons.addresses[0].zipcode != null && Global.vars.gCasePersons.addresses[0].zipcode !== "") {
        frmPersonAlternativeSearch.txtZipCode.text = Global.vars.gCasePersons.addresses[0].zipcode;
    }
    if (Global.vars.gCasePersons.addresses[0].streetNumber != null && Global.vars.gCasePersons.addresses[0].streetNumber !== "") {
        frmPersonAlternativeSearch.txtStreetNumber.text = Global.vars.gCasePersons.addresses[0].streetNumber;
    }
    //   	if(Global.vars.personAlternativeEnableExtrafields === true){
    //       frmPersonAlternativeSearch.txtMiddleName.text = Global.vars.gCasePersons.middlename;
    //       frmPersonAlternativeSearch.txtFirstName.text = Global.vars.gCasePersons.givenNames;
    //       frmPersonAlternativeSearch_enableExtraFields();
    //     }else{
    frmPersonAlternativeSearch_flcFirstName_setVisibility(false);
    frmPersonAlternativeSearch_flcMiddleName_setVisibility(false);
    frmPersonAlternativeSearch.txtMiddleName.text = "";
    frmPersonAlternativeSearch.txtFirstName.text = "";
    //    }
    if (Global.vars.readIDScanned === true) {
        voltmx.print("#### frmPersonAlternativeSearch_preshow eanble button to go to manual search");
        frmPersonAlternativeSearch_flcCompleteManual_setVisibility(true);
        frmPersonAlternativeSearch.flcSearch.width = "46%";
        frmPersonAlternativeSearch.flcSearch.centerX = "75%";
    } else {
        frmPersonAlternativeSearch_flcCompleteManual_setVisibility(false);
        frmPersonAlternativeSearch.flcSearch.width = "94%";
        frmPersonAlternativeSearch.flcSearch.centerX = "50%";
    }
}

function frmPersonAlternativeSearch_onclick_btnCompleteManual() {
    voltmx.print("#### frmPersonAlternativeSearch_onclick_btnCompleteManual");
    frmPersonAlternativeSearch_goToManual(true);
}

function frmPersonAlternativeSearch_clearDateOfBirth() {
    if (Global.vars.setDateManual === true) {
        frmPersonAlternativeSearch.editdatetime.txtDay.text = "";
        frmPersonAlternativeSearch.editdatetime.txtMonth.text = "";
        frmPersonAlternativeSearch.editdatetime.txtYear.text = "";
        //frmPersonAlternativeSearch.lblDateOfBirth.text = frmPersonAlternativeSearch.editdatetime.txtDay.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtYear.text;
        frmPersonAlternativeSearch.lblDateOfBirth.text = "Geboortedatum";
        frmPersonAlternativeSearch.lblDateOfBirth.skin = lblFieldNotFilled;
    }
}

function frmPersonAlternativeSearch_clearFirstName() {
    frmPersonAlternativeSearch.txtFirstName.text = "";
    frmPersonAlternativeSearch.txtFirstName.setFocus(true);
}

function frmPersonAlternativeSearch_clearMiddleName() {
    frmPersonAlternativeSearch.txtMiddleName.text = "";
    frmPersonAlternativeSearch.txtMiddleName.setFocus(true);
}

function frmPersonAlternativeSearch_clearSurname() {
    frmPersonAlternativeSearch.txtSurname.text = "";
    frmPersonAlternativeSearch.txtSurname.setFocus(true);
}

function frmPersonAlternativeSearch_clearZipCode() {
    frmPersonAlternativeSearch.txtZipCode.text = "";
    frmPersonAlternativeSearch.txtZipCode.setFocus(true);
}

function frmPersonAlternativeSearch_clearStreetNumber() {
    frmPersonAlternativeSearch.txtStreetNumber.text = "";
    frmPersonAlternativeSearch.txtStreetNumber.setFocus(true);
}

function frmPersonAlternativeSearch_clearAllFields() {
    frmPersonAlternativeSearch_getNationality(false);
    frmPersonAlternativeSearch_clearDateOfBirth();
    frmPersonAlternativeSearch.txtFirstName.text = "";
    frmPersonAlternativeSearch.txtMiddleName.text = "";
    frmPersonAlternativeSearch.txtSurname.text = "";
    frmPersonAlternativeSearch.txtZipCode.text = "";
    frmPersonAlternativeSearch.txtStreetNumber.text = "";
}

function frmPersonAlternativeSearch_setGenders() {
    var genderdata = [];
    voltmx.print("### frmPersonAlternativeSearch_setGenders Global.vars.gPersonGenderResult: " + JSON.stringify(Global.vars.gPersonGenderResult));
    for (var i in Global.vars.gPersonGenderResult) {
        genderdata.push({
            key: Global.vars.gPersonGenderResult[i].number_value.toString(),
            value: Global.vars.gPersonGenderResult[i].descripton
        });
    }
    voltmx.print("### frmPersonAlternativeSearch_setGenders genderdata: " + JSON.stringify(genderdata));
    frmPersonAlternativeSearch.lbxGender.masterDataMap = [genderdata, "key", "value"];
    if (Global.vars.gPersonGenderResult.length > 0) {
        frmPersonAlternativeSearch.lbxGender.selectedKey = Global.vars.gPersonGenderResult[0].number_value.toString();
    } else {
        alert("De data voor geslacht is niet opgehaald. Herstart de applicatie en synchroniseer, helpt dit niet probeer dan de database te resetten in het menu instellingen onder synchronisatie.");
    }
}

function frmPersonAlternativeSearch_onSelectGender() {
    //fill the globals
    Global.vars.gCasePersons.gender = Number(frmPersonAlternativeSearch.lbxGender.selectedKey);
    Global.vars.gCasePersons.genderDesc = frmPersonAlternativeSearch.lbxGender.selectedKeyValue[1];
}

function frmPersonAlternativeSearch_setDate() {
    // Set date
    //Set valid dates
    var startdate = new Date();
    startdate.setDate(startdate.getDate() - 54750); // about 150 years ago
    voltmx.print("#### frmPersonAlternativeSearch_setDate startdate: " + startdate); //now.format("m/dd/yy");
    //format the  start date
    var form_date = startdate.getDate();
    var form_month = startdate.getMonth();
    form_month++;
    var form_year = startdate.getFullYear();
    var valid_startdate = [form_date, form_month, form_year];
    voltmx.print("#### frmPersonAlternativeSearch_setDate  valid startdate: " + valid_startdate);
    //format the current date
    var d = new Date();
    var curr_date = d.getDate();
    var curr_month = d.getMonth();
    curr_month++;
    var curr_year = d.getFullYear();
    var curr_hour = d.getHours();
    var curr_min = d.getMinutes();
    var enddate = [curr_date, curr_month, curr_year, curr_hour, curr_min, 0]; // current date and time
    if (Global.vars.setDateManual === true) {
        frmPersonAlternativeSearch.editdatetime.txtDay.text = "";
        frmPersonAlternativeSearch.editdatetime.txtMonth.text = "";
        frmPersonAlternativeSearch.editdatetime.txtYear.text = "";
        //frmPersonAlternativeSearch.lblDateOfBirth.text = frmPersonAlternativeSearch.editdatetime.txtDay.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtYear.text;
        frmPersonAlternativeSearch.lblDateOfBirth.text = "Geboortedatum";
        frmPersonAlternativeSearch.lblDateOfBirth.skin = lblFieldNotFilled;
    } else {
        frmPersonAlternativeSearch.calDateOfBirth.validStartDate = valid_startdate;
        frmPersonAlternativeSearch.calDateOfBirth.validEndDate = enddate;
        voltmx.print("#### frmPersonAlternativeSearch_setDate Date of birth set start and end date");
        //frmPerson.calDateOfBirth.placeholder = voltmx.i18n.getLocalizedString("l_dateofbirth");
        //End set
        voltmx.print("### frmPersonAlternativeSearch_setDate ###");
        // set date
        //frmPerson.calDateOfBirth.dateComponents = [];//[0,0,0,0,0,0];
        frmPersonAlternativeSearch.calDateOfBirth.clear();
        var birthdate = [30, 06, 1997, 0, 0, 0];
        frmPersonAlternativeSearch.calDateOfBirth.dateComponents = birthdate;
        //frmPersonAlternativeSearch.calDateOfBirth.dateComponents = [15,6,1990,0,0,0];
        voltmx.print("### frmPersonAlternativeSearch_setDate datecomponents: " + frmPersonAlternativeSearch.calDateOfBirth.dateComponents + " format: " + frmPersonAlternativeSearch.calDateOfBirth.dateFormat);
    }
    // End set date
}

function frmPersonAlternativeSearch_getNationality(fillGLobal) {
    //prefill nationality
    voltmx.print("##### frmPersonAlternativeSearch_getNationality ");
    voltmx.print("#### getspecificNationality");
    if (Global.vars.gCasePersons.idenDocTypeDesc == "W-document") {
        voltmx.print("### frmPersonAlternativeSearch_getNationality W-document dus geen nationality vullen");
        frmPersonAlternativeSearch.lblPersonNationality.text = "Kies";
        frmPersonAlternativeSearch.lblPersonNationality.skin = lblFieldNotFilled;
        frmPersonAlternativeSearch.flcPersonNationality.skin = flcFieldEdgeRed;
    } else {
        voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loading") + "...", "center", true, true, {
            enablemenukey: true,
            enablebackkey: true
        });
        var wcs = "select * from mle_v_nationality_m WHERE code = '" + Global.vars.nationalitypreset + "'";
        wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
        if (fillGLobal === true) {
            KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonAlternativeSearch_getNationalitySuccessCallbackfrm, frmPersonAlternativeSearch_getNationalityErrorCallbackfrm);
        } else {
            KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonAlternativeSearch_getNationalitySuccessCallback, frmPersonAlternativeSearch_getNationalityErrorCallbackfrm);
        }
    }
    voltmx.application.dismissLoadingScreen();
}

function frmPersonAlternativeSearch_getNationalitySuccessCallbackfrm(resultnationality) {
    voltmx.print("##### frmPersonAlternativeSearch_getNationalitySuccessCallbackfrm getNationalitySuccessCallbackfrm ");
    voltmx.print("#### frmPersonAlternativeSearch_getNationalitySuccessCallbackfrm getNationalitySuccessCallbackfrm result lblPersonNationality: " + frmPersonAlternativeSearch.lblPersonNationality.text);
    voltmx.print("#### frmPersonAlternativeSearch_getNationalitySuccessCallbackfrm getNationalitySuccessCallbackfrm result nationality: " + JSON.stringify(resultnationality));
    if (resultnationality != null) {
        frmPersonAlternativeSearch.lblPersonNationality.text = resultnationality[0].description;
        frmPersonAlternativeSearch.lblPersonNationality.skin = lblFieldInfo;
        //fill globals
        voltmx.print("### frmPersonAlternativeSearch_getNationalitySuccessCallbackfrm NationalityCode resultnationality[0].code" + resultnationality[0].code + " = " + Global.vars.nationalitypreset);
        Global.vars.gCasePersons.nationality = resultnationality[0].code;
        if (resultnationality[0].code == Global.vars.nationalitypreset) {
            voltmx.print("### frmPersonAlternativeSearch_getNationalitySuccessCallbackfrm NationalityCode resultnationality[0].code" + resultnationality[0].code + " = " + Global.vars.nationalitypreset);
            Global.vars.gCasePersons.indicationDutch = true;
        }
        Global.vars.gCasePersons.nationalityDesc = resultnationality[0].description;
        frmPersonAlternativeSearch.lblPersonNationality.text = Global.vars.gCasePersons.nationalityDesc;
        frmPersonAlternativeSearch.lblPersonNationality.skin = lblFieldInfo;
        frmPersonAlternativeSearch.flcPersonNationality.skin = flcFieldEdge;
        //end fill globals
    } else {
        frmPersonAlternativeSearch.lblPersonNationality.text = "Kies";
        frmPersonAlternativeSearch.lblPersonNationality.skin = lblFieldNotFilled;
        frmPersonAlternativeSearch.flcPersonNationality.skin = flcFieldEdgeRed;
    }
    voltmx.print("#### frmPersonAlternativeSearch_getNationalitySuccessCallbackfrm getNationalitySuccessCallbackfrm result Global.vars.gCasePersons.nationality: " + Global.vars.gCasePersons.nationality);
    voltmx.print("#### frmPersonAlternativeSearch_getNationalitySuccessCallbackfrm getNationalitySuccessCallbackfrm result ");
    voltmx.application.dismissLoadingScreen();
    voltmx.print("##### frmPersonAlternativeSearch_getNationalitySuccessCallbackfrm getNationalitySuccessCallbackfrm ");
}

function frmPersonAlternativeSearch_getNationalitySuccessCallback(resultnationality) {
    voltmx.print("##### frmPersonAlternativeSearch_getNationalitySuccessCallback getNationalitySuccessCallback ");
    voltmx.print("#### frmPersonAlternativeSearch_getNationalitySuccessCallback getNationalitySuccessCallback result lblPersonNationality: " + frmPersonAlternativeSearch.lblPersonNationality.text);
    voltmx.print("#### frmPersonAlternativeSearch_getNationalitySuccessCallback getNationalitySuccessCallback result nationality: " + JSON.stringify(resultnationality));
    if (resultnationality != null) {
        frmPersonAlternativeSearch.lblPersonNationality.text = resultnationality[0].description;
        frmPersonAlternativeSearch.lblPersonNationality.skin = lblFieldInfo;
        frmPersonAlternativeSearch.lblPersonNationality.text = Global.vars.gCasePersons.nationalityDesc;
        frmPersonAlternativeSearch.lblPersonNationality.skin = lblFieldInfo;
        frmPersonAlternativeSearch.flcPersonNationality.skin = flcFieldEdge;
        //end fill globals
    } else {
        frmPersonAlternativeSearch.lblPersonNationality.text = "Kies";
        frmPersonAlternativeSearch.lblPersonNationality.skin = lblFieldNotFilled;
        frmPersonAlternativeSearch.flcPersonNationality.skin = flcFieldEdgeRed;
    }
    voltmx.print("#### frmPersonAlternativeSearch_getNationalitySuccessCallback getNationalitySuccessCallback result ");
    voltmx.application.dismissLoadingScreen();
    voltmx.print("##### frmPersonAlternativeSearch_getNationalitySuccessCallback getNationalitySuccessCallback ");
}

function frmPersonAlternativeSearch_getNationalityErrorCallbackfrm(error) {
    voltmx.print("##### frmPersonAlternativeSearch_getNationalityErrorCallbackfrm ");
    voltmx.print("#### frmPersonAlternativeSearch_getNationalityErrorCallbackfrm Nationality error: " + error);
    voltmx.application.dismissLoadingScreen();
}
// end nationality prefill
function frmPersonAlternativeSearch_onclickbtnNationality() {
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    frmPersonNationalities.show();
}

function frmPersonAlternativeSearch_onDone() {
    Global.vars.personAlternativeEnableExtrafields = false;
    if (frmPersonAlternativeSearch.flcPersonNationality.skin === flcFieldEdgeRed) {
        voltmx.print("#### frmPersonAlternativeSearch_onDone Alternative SSN others: " + voltmx.i18n.getLocalizedString("l_not_all_fields_filled"));
        voltmx.application.dismissLoadingScreen();
        alert(voltmx.i18n.getLocalizedString("l_not_all_fields_filled"));
    } else if (frmPersonAlternativeSearch.txtFirstName.isVisible === false && (frmPersonAlternativeSearch.txtSurname.text != null && frmPersonAlternativeSearch.txtSurname.text !== "") && (frmPersonAlternativeSearch.calDateOfBirth.dateComponents != [0, 0, 0, 0, 0, 0] || Global.vars.setDateManual === true)) {
        voltmx.print("#### frmPersonAlternativeSearch_onDone Alternative SSN call 1");
        //voltmx.print("#### frmPersonAlternativeSearch_onDone date: " + frmPersonAlternativeSearch.calDateOfBirth.year + "-" + frmPersonAlternativeSearch.calDateOfBirth.month + "-" + frmPersonAlternativeSearch.calDateOfBirth.day);
        var birthDate = "";
        if (Global.vars.setDateManual === true) {
            birthDate = frmPersonAlternativeSearch.editdatetime.txtYear.text + "-" + frmPersonAlternativeSearch.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtDay.text.lpad("0", 2);
        } else {
            birthDate = frmPersonAlternativeSearch.calDateOfBirth.year + "-" + frmPersonAlternativeSearch.calDateOfBirth.month.toString().lpad("0", 2) + "-" + frmPersonAlternativeSearch.calDateOfBirth.day.toString().lpad("0", 2);
        }
        //Alternative SSN call 1
        var params = {
            surname: frmPersonAlternativeSearch.txtSurname.text,
            birthDate: birthDate,
            genderCode: Number(frmPersonAlternativeSearch.lbxGender.selectedKey),
            prefixes: frmPersonAlternativeSearch.txtMiddleName.text
        };
        //CallSocialSecurityNr_service();
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        service_GetPersonInfoOther(params, frmPersonResult_callbackfunctionBSN);
    } else if (frmPersonAlternativeSearch.txtFirstName.isVisible === false && (frmPersonAlternativeSearch.calDateOfBirth.dateComponents != [0, 0, 0, 0, 0, 0] || Global.vars.setDateManual === true) && (frmPersonAlternativeSearch.txtStreetNumber.text != null && frmPersonAlternativeSearch.txtStreetNumber.text !== "") && (frmPersonAlternativeSearch.txtZipCode.text != null && frmPersonAlternativeSearch.txtZipCode.text !== "")) {
        voltmx.print("#### frmPersonAlternativeSearch_onDone Alternative SSN call 2");
        //voltmx.print("#### frmPersonAlternativeSearch_onDone date: " + frmPersonAlternativeSearch.calDateOfBirth.year + "-" + frmPersonAlternativeSearch.calDateOfBirth.month + "-" + frmPersonAlternativeSearch.calDateOfBirth.day);
        //Alternative SSN call 2
        var birthDate2 = "";
        if (Global.vars.setDateManual === true) {
            birthDate2 = frmPersonAlternativeSearch.editdatetime.txtYear.text + "-" + frmPersonAlternativeSearch.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtDay.text.lpad("0", 2);
        } else {
            birthDate2 = frmPersonAlternativeSearch.calDateOfBirth.year + "-" + frmPersonAlternativeSearch.calDateOfBirth.month.toString().lpad("0", 2) + "-" + frmPersonAlternativeSearch.calDateOfBirth.day.toString().lpad("0", 2);
        }
        var params2 = {
            genderCode: Number(frmPersonAlternativeSearch.lbxGender.selectedKey),
            houseNumber: frmPersonAlternativeSearch.txtStreetNumber.text,
            birthDate: birthDate2,
            zipcode: frmPersonAlternativeSearch.txtZipCode.text.replace(" ", "")
        };
        //CallSocialSecurityNr_service();
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        service_GetPersonInfoOther(params2, frmPersonResult_callbackfunctionBSN);
    } else if (frmPersonAlternativeSearch.txtFirstName.isVisible === true) {
        voltmx.print("#### frmPersonAlternativeSearch_onDone Alternative SSN call 3");
        voltmx.print("#### frmPersonAlternativeSearch_onDone Alternative SSN call 3 Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
        //voltmx.print("#### frmPersonAlternativeSearch_onDone date: " + frmPersonAlternativeSearch.calDateOfBirth.year + "-" + frmPersonAlternativeSearch.calDateOfBirth.month + "-" + frmPersonAlternativeSearch.calDateOfBirth.day);
        //Alternative SSN call 2
        var birthDate3 = "";
        if (Global.vars.setDateManual === true) {
            birthDate3 = frmPersonAlternativeSearch.editdatetime.txtYear.text + "-" + frmPersonAlternativeSearch.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtDay.text.lpad("0", 2);
        } else {
            birthDate3 = frmPersonAlternativeSearch.calDateOfBirth.year + "-" + frmPersonAlternativeSearch.calDateOfBirth.month.toString().lpad("0", 2) + "-" + frmPersonAlternativeSearch.calDateOfBirth.day.toString().lpad("0", 2);
        }
        var params3 = {
            genderCode: Number(frmPersonAlternativeSearch.lbxGender.selectedKey),
            birthDate: birthDate3,
            givenNames: frmPersonAlternativeSearch.txtFirstName.text,
            prefixes: frmPersonAlternativeSearch.txtMiddleName.text,
            surname: frmPersonAlternativeSearch.txtSurname.text,
            zipcode: frmPersonAlternativeSearch.txtZipCode.text.replace(" ", ""),
            houseNumber: frmPersonAlternativeSearch.txtStreetNumber.text
        };
        //CallSocialSecurityNr_service();
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        service_GetPersonInfoOther(params3, frmPersonResult_callbackfunctionBSN);
    } else {
        voltmx.print("#### frmPersonAlternativeSearch_onDone Alternative SSN others: " + voltmx.i18n.getLocalizedString("l_not_all_fields_filled"));
        voltmx.application.dismissLoadingScreen();
        alert(voltmx.i18n.getLocalizedString("l_not_all_fields_filled"));
    }
}

function frmPersonAlternativeSearch_btnBack() {
    if (Global.vars.readIDScanned === true) {
        Global.vars.gCasePersonsIndex = 0;
        Global.vars.gCasePersons = CaseData_setNewperson(Global.vars.gCasePersonsIndex);
        Global.vars.readIDScanned = false;
    }
    frmPerson.show();
}

function frmPersonAlternativeSearch_onclick_btnButAlternativeMethod2() {
    frmPersonAlternativeSearch.btnButAlternativeMethod1.skin = btnGreyToggle;
    frmPersonAlternativeSearch.btnButAlternativeMethod2.skin = btnGreyToggleFocus;
    frmPersonAlternativeSearch_flcSurname_setVisibility(false);
    frmPersonAlternativeSearch_flcZipCode_setVisibility(true);
    frmPersonAlternativeSearch_flcStreetNumber_setVisibility(true);
    //clear fields
    frmPersonAlternativeSearch.txtSurname.text = "";
    if (Global.vars.gCasePersons.surname != null && Global.vars.gCasePersons.surname !== "") {
        frmPersonAlternativeSearch.txtSurname.text = Global.vars.gCasePersons.surname;
    }
}

function frmPersonAlternativeSearch_onclick_btnButAlternativeMethod1() {
    frmPersonAlternativeSearch.btnButAlternativeMethod1.skin = btnGreyToggleFocus;
    frmPersonAlternativeSearch.btnButAlternativeMethod2.skin = btnGreyToggle;
    frmPersonAlternativeSearch_flcSurname_setVisibility(true);
    frmPersonAlternativeSearch_flcZipCode_setVisibility(false);
    frmPersonAlternativeSearch_flcStreetNumber_setVisibility(false);
    frmPersonAlternativeSearch.txtZipCode.text = "";
    if (Global.vars.gCasePersons.addresses != null && Global.vars.gCasePersons.addresses[0].zipcode != null && Global.vars.gCasePersons.addresses[0].zipcode !== "") {
        frmPersonAlternativeSearch.txtZipCode.text = Global.vars.gCasePersons.addresses[0].zipcode;
    }
    frmPersonAlternativeSearch.txtStreetNumber.text = "";
    if (Global.vars.gCasePersons.addresses != null && Global.vars.gCasePersons.addresses[0].streetNumber != null && Global.vars.gCasePersons.addresses[0].streetNumber !== "") {
        frmPersonAlternativeSearch.txtStreetNumber.text = Global.vars.gCasePersons.addresses[0].streetNumber;
    }
}

function frmPersonAlternativeSearch_onDoneZipcode() {
    if (frmPersonAlternativeSearch.txtZipCode.text != null && frmPersonAlternativeSearch.txtZipCode.text.length > 0) {
        var validatedZipcode = Utility_checkZipcode(frmPersonAlternativeSearch.txtZipCode.text);
        if (!validatedZipcode) {
            alert(voltmx.i18n.getLocalizedString("l_notValidZipCode"));
        } else {
            frmPersonAlternativeSearch.txtZipCode.text = validatedZipcode;
        }
    }
}

function frmPersonAlternativeSearch_enableExtraFields() {
    voltmx.print("### frmPersonAlternativeSearch_enableExtraFields more then one person found");
    frmPersonAlternativeSearch_flcToggleSearchMethod_setVisibility(false);
    //voornaam
    frmPersonAlternativeSearch_flcFirstName_setVisibility(true);
    //tussenvoegsel
    frmPersonAlternativeSearch_flcMiddleName_setVisibility(true);
    frmPersonAlternativeSearch_flcZipCode_setVisibility(true);
    frmPersonAlternativeSearch_flcStreetNumber_setVisibility(true);
    frmPersonAlternativeSearch_flcSurname_setVisibility(true);
}

function frmPersonAlternativeSearch_Surname_onDone() {
    frmPersonAlternativeSearch.txtSurname.text = Utility_CapatilizeNames(frmPersonAlternativeSearch.txtSurname.text);
}

function frmPersonAlternativeSearch_Firstname_onDone() {
    frmPersonAlternativeSearch.txtFirstName.text = Utility_CapatilizeNames(frmPersonAlternativeSearch.txtFirstName.text);
}

function frmPersonAlternativeSearch_checkDaysManualOnEdit() {
    if (frmPersonAlternativeSearch.editdatetime.txtDay.text.length > 2) {
        frmPersonAlternativeSearch.editdatetime.txtDay.text = frmPersonAlternativeSearch.editdatetime.txtDay.text.substring(0, 2);
    }
    var days = Number(frmPersonAlternativeSearch.editdatetime.txtDay.text);
    if (days < 0 || frmPersonAlternativeSearch.editdatetime.txtDay.text == "00") {
        frmPersonAlternativeSearch.editdatetime.txtDay.text = "01";
    }
    if (days > 31) {
        frmPersonAlternativeSearch.editdatetime.txtDay.text = "31";
    }
}

function frmPersonAlternativeSearch_checkDaysManualOnEnd() {
    var days = Number(frmPersonAlternativeSearch.editdatetime.txtDay.text);
    if (days < 0 || days === 0) {
        frmPersonAlternativeSearch.editdatetime.txtDay.text = "01";
    }
    if (days > 31) {
        frmPersonAlternativeSearch.editdatetime.txtDay.text = "31";
    }
    if (days < 10) {
        frmPersonAlternativeSearch.editdatetime.txtDay.text = frmPersonAlternativeSearch.editdatetime.txtDay.text.lpad("0", 2);
    }
}

function frmPersonAlternativeSearch_checkMonthManualOnEdit() {
    if (frmPersonAlternativeSearch.editdatetime.txtMonth.text.length > 2) {
        frmPersonAlternativeSearch.editdatetime.txtMonth.text = frmPersonAlternativeSearch.editdatetime.txtMonth.text.substring(0, 2);
    }
    var month = Number(frmPersonAlternativeSearch.editdatetime.txtMonth.text);
    if (month < 0 || frmPersonAlternativeSearch.editdatetime.txtMonth.text == "00") {
        frmPersonAlternativeSearch.editdatetime.txtMonth.text = "01";
    }
    if (month > 12) {
        frmPersonAlternativeSearch.editdatetime.txtMonth.text = "12";
    }
}

function frmPersonAlternativeSearch_checkMonthManualOnEnd() {
    var month = Number(frmPersonAlternativeSearch.editdatetime.txtMonth.text);
    if (month < 0 || month === 0) {
        frmPersonAlternativeSearch.editdatetime.txtMonth.text = "01";
    }
    if (month > 12) {
        frmPersonAlternativeSearch.editdatetime.txtMonth.text = "12";
    }
    if (month < 10) {
        frmPersonAlternativeSearch.editdatetime.txtMonth.text = frmPersonAlternativeSearch.editdatetime.txtMonth.text.lpad("0", 2);
    }
    //Do day check for specific month
    var year = Number(frmPersonAlternativeSearch.editdatetime.txtYear.text);
    var days = Number(frmPersonAlternativeSearch.editdatetime.txtDay.text);
    var daysInMonth = new Date(year, month, 0).getDate();
    voltmx.print("### frmPersonAlternativeSearch_checkMonthManualOnEdit daysInMonth: " + daysInMonth);
    if (days > daysInMonth) {
        alert("Deze maand heeft geen " + days + " dagen");
    }
}

function frmPersonAlternativeSearch_checkYearManualOnEdit() {
    var year = Number(frmPersonAlternativeSearch.editdatetime.txtYear.text);
    if (year < 0 || frmPersonAlternativeSearch.editdatetime.txtYear.text == "0000") {
        frmPersonAlternativeSearch.editdatetime.txtYear.text = "0001";
    }
    if (frmPersonAlternativeSearch.editdatetime.txtYear.text.length > 4) {
        frmPersonAlternativeSearch.editdatetime.txtYear.text = frmPersonAlternativeSearch.editdatetime.txtYear.text.substring(0, 4);
    }
}

function frmPersonAlternativeSearch_checkYearManualOnEnd() {
    var year = Number(frmPersonAlternativeSearch.editdatetime.txtYear.text);
    if (year < 0 || year === 0) {
        frmPersonAlternativeSearch.editdatetime.txtYear.text = "0001";
    }
    if (year > 9999) {
        frmPersonAlternativeSearch.editdatetime.txtYear.text = "9999";
    }
    if (frmPersonAlternativeSearch.editdatetime.txtYear.text.length < 4) {
        frmPersonAlternativeSearch.editdatetime.txtYear.text = frmPersonAlternativeSearch.editdatetime.txtYear.text.lpad("0", 4);
    }
}

function frmPersonAlternativeSearch_showEditTime() {
    try {
        //set correct data
        frmPersonAlternativeSearch.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmPersonAlternativeSearch.forceLayout();
        frmPersonAlternativeSearch_editdatetime_setVisibility(true);
        frmPersonAlternativeSearch_showEditTime_preAnim();
        frmPersonAlternativeSearch_showEditTime_animationStart();
    } catch (e) {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime error: " + JSON.stringify(e));
    }
}

function frmPersonAlternativeSearch_showEditTime_preAnim() {
    try {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_preAnim");
        var trans1 = voltmx.ui.makeAffineTransform();
        trans1.scale(0.1, 0.1);
        var trans2 = voltmx.ui.makeAffineTransform();
        trans2.translate(0, 10);
    } catch (e) {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_preAnim error: " + JSON.stringify(e));
    }
}

function frmPersonAlternativeSearch_showEditTime_arrangeWidgets() {
    try {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_arrangeWidgets");
        //popup fields
        frmPersonAlternativeSearch.editdatetime.imgPopupLogo1.isVisible = false;
        frmPersonAlternativeSearch.editdatetime.forceLayout();
        frmPersonAlternativeSearch.editdatetime.flcEditTime.isVisible = false;
    } catch (e) {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_arrangeWidgets error: " + JSON.stringify(e));
    }
}

function frmPersonAlternativeSearch_showEditTime_animationStart(eventobject) {
    try {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_animationStart");
        frmPersonAlternativeSearch_editdatetime_setVisibility(true);
        frmPersonAlternativeSearch.editdatetime.flcEditTime.isVisible = true;
        var trans100 = voltmx.ui.makeAffineTransform();
        trans100.scale(1, 1);
        frmPersonAlternativeSearch.editdatetime.flcEditTime.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans100,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.25
        }, {
            "animationEnd": voltmx.runOnMainThread(frmPersonAlternativeSearch_showEditTime_animLogo)
        });
    } catch (e) {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_animationStart error: " + JSON.stringify(e));
    }
}

function frmPersonAlternativeSearch_showEditTime_animLogo() {
    try {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_animLogo");
        frmPersonAlternativeSearch_showEditTime_animOtherWidgets(frmPersonAlternativeSearch.editdatetime.flcFooterMainHandleTime);
        frmPersonAlternativeSearch.editdatetime.imgPopupLogo1.isVisible = true;
        frmPersonAlternativeSearch.forceLayout();
    } catch (e) {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_animLogo error: " + JSON.stringify(e));
    }
}

function frmPersonAlternativeSearch_showEditTime_animOtherWidgets(widget) {
    try {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_animOtherWidgets");
        var trans1 = voltmx.ui.makeAffineTransform();
        trans1.translate(1, 1);
        //trans1.translate(1, -10);
        widget.isVisible = true;
        widget.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans1,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.25
        }, {
            "animationEnd": function() {}
        });
        frmPersonAlternativeSearch.editdatetime.flcFooterMainHandleTime.isVisible = true;
        frmPersonAlternativeSearch.editdatetime.flcFooterMainHandleTime.setEnabled(true);
        frmPersonAlternativeSearch.forceLayout();
    } catch (e) {
        voltmx.print("### frmPersonAlternativeSearch_showEditPlate_animOtherWidgets error: " + JSON.stringify(e));
    }
}

function frmPersonAlternativeSearch_showEditTime_animLogoBack() {
    try {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_animLogoBack");
        var trans = voltmx.ui.makeAffineTransform();
        trans.scale(1, 1);
        frmPersonAlternativeSearch.editdatetime.imgPopupLogo1.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.15
        }, {
            "animationEnd": function() {}
        });
        //frmPersonAlternativeSearch.forceLayout();
    } catch (e) {
        voltmx.print("### frmPersonAlternativeSearch_showEditTime_animLogoBack error: " + JSON.stringify(e));
    }
}

function frmPersonAlternativeSearch_hideShowEditTime() {
    //activate footer and mainpage
    frmPersonAlternativeSearch.flcMainPage.setEnabled(true);
    voltmx.print("### flcMainPage enabled");
    frmPersonAlternativeSearch_editdatetime_setVisibility(false);
}

function frmPersonAlternativeSearch_onDoneEditDateTime() {
    //check date time
    frmPersonAlternativeSearch.editdatetime.txtYear.text = frmPersonAlternativeSearch.editdatetime.txtYear.text.lpad("0", 4);
    var startdate = new Date();
    startdate.setDate(startdate.getDate() - 54750); // about 150 years ago
    voltmx.print("#### frmPersonAlternativeSearch_onDoneEditDateTime startdate: " + startdate);
    var curr_date = new Date();
    var selected_date = Number(frmPersonAlternativeSearch.editdatetime.txtDay.text);
    var selected_month = Number(frmPersonAlternativeSearch.editdatetime.txtMonth.text);
    var selected_year = frmPersonAlternativeSearch.editdatetime.txtYear.text;
    var selected_hours = 0;
    var selected_minutes = 0;
    var selected_seconds = 0;
    var selected_dateJavascript = new Date(selected_year, selected_month - 1, selected_date, selected_hours, selected_minutes, selected_seconds, 0);
    var checkValidDate = new Date(selected_year, selected_month - 1, selected_date, selected_hours, selected_minutes, selected_seconds, 0);
    voltmx.print("#### frmPersonAlternativeSearch_onDoneEditDateTime checkValidDate: " + checkValidDate);
    //date checks
    var daysInMonth = new Date(selected_year, selected_month, 0).getDate();
    voltmx.print("### frmPersonAlternativeSearch_onDoneEditDateTime daysInMonth: " + daysInMonth);
    if (frmPersonAlternativeSearch.editdatetime.txtYear.text.length < 4) {
        alert("Dit is geen geldige datum"); //i18n
    } else if (Number(frmPersonAlternativeSearch.editdatetime.txtYear.text) < 1000) {
        alert("Datum ligt te ver in het verleden");
    } else if (selected_date > daysInMonth) {
        alert("Deze maand heeft geen " + selected_date + " dagen");
    } else if (isNaN(selected_dateJavascript) === true) {
        alert("Dit is geen geldige datum"); //i18n
    } else if (selected_dateJavascript < startdate) {
        alert("Datum ligt te ver in het verleden"); //i18n
    } else if (selected_dateJavascript > curr_date) {
        alert("Datum ligt te ver in de toekomst"); //i18n
    } else {
        frmPersonAlternativeSearch.lblDateOfBirth.text = frmPersonAlternativeSearch.editdatetime.txtDay.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonAlternativeSearch.editdatetime.txtYear.text;
        frmPersonAlternativeSearch.lblDateOfBirth.skin = lblFieldInfo;
        frmPersonAlternativeSearch_hideShowEditTime();
    }
}

function frmPersonAlternativeSearch_goToManual(response) {
    if (response) {
        voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loading") + "...", "center", false, true, {
            enablemenukey: true,
            enablebackkey: true
        });
        try {
            voltmx.timer.schedule("frmPersonAlternativeSearch_goToManualPerson", frmPersonAlternativeSearch_goToManualPerson, 1, false);
        } catch (e) {}
    }
}

function frmPersonAlternativeSearch_goToManualPerson() {
    voltmx.print("### frmPerson_goToManualPerson go to frmPersonManualPerson");
    voltmx.print("### frmPerson_goToManualPerson Global.vars.readID: " + JSON.stringify(Global.vars.readID.nationality));
    try {
        voltmx.timer.cancel("frmPersonAlternativeSearch_goToManualPerson");
    } catch (e) {}
    voltmx.application.dismissLoadingScreen();
    //set nationality globals again
    if (Global.vars.readID.nationality !== 0) {
        Global.vars.gCasePersons.nationality = Global.vars.readID.nationality;
        Global.vars.gCasePersons.nationalityDesc = Global.vars.readID.nationalityDesc;
    }
    if (frmPersonAlternativeSearch.txtSurname.text !== "") {
        Global.vars.gCasePersons.surname = frmPersonAlternativeSearch.txtSurname.text;
    }
    Global.vars.previousForm = "frmPerson";
    frmPersonManualPerson.show();
    frmPersonAlternativeSearch_clearAllFields();
    alert("Uit te lezen gegevens overgenomen, controleer de gegevens en vul aan.");
}