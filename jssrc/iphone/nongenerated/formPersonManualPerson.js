function frmPersonManualPerson_flcMunicipalityOfBirth_setVisibility(boolean) {
    voltmx.print("### frmPersonManualPerson_flcMunicipalityOfBirth_setVisibility");

    function flcMunicipalityOfBirth_setVisibility() {
        voltmx.print("### frmPersonManualPerson_flcMunicipalityOfBirth_setVisibility flcMunicipalityOfBirth_setVisibility: " + boolean);
        frmPersonManualPerson.flcMunicipalityOfBirth.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcMunicipalityOfBirth_setVisibility, []);
}

function frmPersonManualPerson_flcPlaceOfBirth_setVisibility(boolean) {
    voltmx.print("### frmPersonManualPerson_flcPlaceOfBirth_setVisibility");

    function flcPlaceOfBirth_setVisibility() {
        voltmx.print("### frmPersonManualPerson_flcPlaceOfBirth_setVisibility flcPlaceOfBirth_setVisibility: " + boolean);
        frmPersonManualPerson.flcPlaceOfBirth.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcPlaceOfBirth_setVisibility, []);
}

function frmPersonManualPerson_editdatetime_setVisibility(boolean) {
    voltmx.print("### frmPersonManualPerson_editdatetime_setVisibility");

    function editdatetime_setVisibility() {
        voltmx.print("### frmPersonManualPerson_editdatetime_setVisibility editdatetime_setVisibility: " + boolean);
        frmPersonManualPerson.editdatetime.setVisibility(boolean);
    }
    voltmx.runOnMainThread(editdatetime_setVisibility, []);
}

function frmPersonManualPerson_flcDocumentCountry_setVisibility(boolean) {
    voltmx.print("### frmPersonManualPerson_flcDocumentCountry_setVisibility");

    function flcDocumentCountry_setVisibility() {
        voltmx.print("### frmPersonManualPerson_flcDocumentCountry_setVisibility flcDocumentCountry_setVisibility: " + boolean);
        frmPersonManualPerson.flcDocumentCountry.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcDocumentCountry_setVisibility, []);
}

function frmPersonManualPerson_btnManualDate_setVisibility(boolean) {
    voltmx.print("### frmPersonManualPerson_btnManualDate_setVisibility");

    function btnManualDate_setVisibility() {
        voltmx.print("### frmPersonManualPerson_btnManualDate_setVisibility btnManualDate_setVisibility: " + boolean);
        frmPersonManualPerson.btnManualDate.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnManualDate_setVisibility, []);
}

function frmPersonManualPerson_calDateOfBirth_setVisibility(boolean) {
    voltmx.print("### frmPersonManualPerson_calDateOfBirth_setVisibility");

    function calDateOfBirth_setVisibility() {
        voltmx.print("### frmPersonManualPerson_calDateOfBirth_setVisibility calDateOfBirth_setVisibility: " + boolean);
        frmPersonManualPerson.calDateOfBirth.setVisibility(boolean);
    }
    voltmx.runOnMainThread(calDateOfBirth_setVisibility, []);
}

function frmPersonManualPerson_lblDateOfBirth_setVisibility(boolean) {
    voltmx.print("### frmPersonManualPerson_lblDateOfBirth_setVisibility");

    function lblDateOfBirth_setVisibility() {
        voltmx.print("### frmPersonManualPerson_lblDateOfBirth_setVisibility lblDateOfBirth_setVisibility: " + boolean);
        frmPersonManualPerson.lblDateOfBirth.setVisibility(boolean);
    }
    voltmx.runOnMainThread(lblDateOfBirth_setVisibility, []);
}

function frmPersonManualPerson_init() {
    voltmx.print("### frmPersonManualPerson_init");
    //Utility_registerForIdleTimeout();
    frmPersonManualPerson.onDeviceBack = Global_onDeviceBack;
    frmPersonManualPerson_setGenders();
    frmPersonManualPerson_setDate();
    frmPersonManualPerson_getCountryOfOrigin();
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
        frmPersonManualPerson.lbxGender.centerX = 52 + "%";
        frmPersonManualPerson.calDateOfBirth.centerX = 52 + "%";
    }
    // RL-384
    frmPersonManualPerson.lbxGender.expandListItemToParentWidth = true;
    //
    if (Global.vars.setDateManual === true) {
        frmPersonManualPerson_calDateOfBirth_setVisibility(false);
        frmPersonManualPerson_lblDateOfBirth_setVisibility(true);
        frmPersonManualPerson_btnManualDate_setVisibility(true);
    } else {
        frmPersonManualPerson_calDateOfBirth_setVisibility(true);
        frmPersonManualPerson_lblDateOfBirth_setVisibility(false);
        frmPersonManualPerson_btnManualDate_setVisibility(false);
    }
    frmPersonManualPerson.editdatetime.flcFooterMainHandleTimeCancel.setVisibility(true);
    frmPersonManualPerson.editdatetime.btnCancelEditTime.onClick = frmPersonManualPerson_hideShowEditTime;
}

function frmPersonManualPerson_clearFields() {
    frmPersonManualPerson.txtFirstName.text = "";
    frmPersonManualPerson.lblInitials.text = voltmx.i18n.getLocalizedString("l_initials");
    frmPersonManualPerson.lblInitials.skin = lblFieldNotFilled;
    frmPersonManualPerson.txtMiddleName.text = "";
    frmPersonManualPerson.txtPlaceOfBirth.text = "";
    frmPersonManualPerson.txtSSN.text = "";
    frmPersonManualPerson.txtSurname.text = "";
    frmPersonManualPerson.txtYearOfBirth.text = "";
    frmPersonManualPerson_setGenders();
    frmPersonManualPerson_setDate();
    frmPersonManualPerson_getCountryOfOrigin();
    voltmx.print("### frmPersonManualPerson_clearFields Global.vars.gCasePersons.nationalityDesc: " + Global.vars.gCasePersons.nationalityDesc);
    if (Global.vars.gCasePersons.nationalityDesc === null || Global.vars.gCasePersons.nationalityDesc === "") {
        //frmPersonManualPerson_getNationality();
        frmPersonManualPerson.lblPersonNationality.text = "Kies";
        frmPersonManualPerson.lblPersonNationality.skin = lblFieldNotFilled;
        frmPersonManualPerson.flcPersonNationality.skin = flcFieldEdgeRed;
    }
}

function frmPersonManualPerson_resetGlobals() {
    voltmx.print("### frmPersonManualPerson_resetGlobals");
    // empty globals
    //preserve document info
    var doctypedesc = Global.vars.gCasePersons.idenDocTypeDesc;
    var doctype = Global.vars.gCasePersons.idenDocType;
    var docnumber = Global.vars.gCasePersons.documentNumber;
    var docCountry = Global.vars.gCasePersons.countryIdenDoc;
    var docCountryDesc = Global.vars.gCasePersons.countryIdenDocDesc;
    //
    Global.vars.gCasePersons = {};
    if (CaseData.person[Global.vars.gCasePersonsIndex] !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].edited === true) {
        Global.vars.gCasePersons = JSON.parse(JSON.stringify(CaseData.person[Global.vars.gCasePersonsIndex]));
    } else {
        Global.vars.gCasePersons = CaseData_setNewperson();
        CaseData.offence.person = true;
    }
    //set preserved document info
    Global.vars.gCasePersons.idenDocTypeDesc = doctypedesc;
    Global.vars.gCasePersons.idenDocType = doctype;
    Global.vars.gCasePersons.documentNumber = docnumber;
    Global.vars.gCasePersons.countryIdenDoc = docCountry;
    Global.vars.gCasePersons.countryIdenDocDesc = docCountryDesc;
}

function frmPersonManualPerson_clearDateOfBirth() {
    if (Global.vars.setDateManual === true) {
        frmPersonManualPerson.editdatetime.txtDay.text = "";
        frmPersonManualPerson.editdatetime.txtMonth.text = "";
        frmPersonManualPerson.editdatetime.txtYear.text = "";
        //frmPersonManualPerson.lblDateOfBirth.text = frmPersonManualPerson.editdatetime.txtDay.text.lpad("0", 2) + "-" + frmPersonManualPerson.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonManualPerson.editdatetime.txtYear.text;
        frmPersonManualPerson.lblDateOfBirth.text = "Geboortedatum";
        frmPersonManualPerson.lblDateOfBirth.skin = lblFieldNotFilled;
    }
}

function frmPersonManualPerson_clearFirstName() {
    frmPersonManualPerson.txtFirstName.text = "";
    frmPersonManualPerson_clearInitails();
    frmPersonManualPerson.txtFirstName.setFocus(true);
    Global.vars.gCasePersons.givenNames = null;
}

function frmPersonManualPerson_clearInitails() {
    frmPersonManualPerson.lblInitials.text = voltmx.i18n.getLocalizedString("l_initials");
    frmPersonManualPerson.lblInitials.skin = lblFieldNotFilled;
    Global.vars.gCasePersons.initials = null;
}

function frmPersonManualPerson_clearMiddleName() {
    frmPersonManualPerson.txtMiddleName.text = "";
    frmPersonManualPerson.txtMiddleName.setFocus(true);
    Global.vars.gCasePersons.middlename = null;
}

function frmPersonManualPerson_clearPlaceOfBirth() {
    frmPersonManualPerson.txtPlaceOfBirth.text = "";
    frmPersonManualPerson.txtPlaceOfBirth.setFocus(true);
    Global.vars.gCasePersons.birthplace = null;
}

function frmPersonManualPerson_clearSSN() {
    frmPersonManualPerson.txtSSN.text = "";
    frmPersonManualPerson.txtSSN.setFocus(true);
    Global.vars.gCasePersons.ssn = null;
}

function frmPersonManualPerson_clearSurname() {
    frmPersonManualPerson.txtSurname.text = "";
    frmPersonManualPerson.txtSurname.setFocus(true);
    Global.vars.gCasePersons.surname = null;
    if (frmPersonManualPerson.txtFirstName.text === null || frmPersonManualPerson.txtFirstName.text === "") {
        frmPersonManualPerson_clearInitails();
    }
    frmPersonManualPerson_checkFieldsFilled();
}

function frmPersonManualPerson_clearYearOfBirth() {
    frmPersonManualPerson.txtYearOfBirth.text = "";
    frmPersonManualPerson.txtYearOfBirth.setFocus(true);
    Global.vars.gCasePersons.yearOfBirth = null;
    frmPersonManualPerson_checkFieldsFilled();
}

function frmPersonManualPerson_preshow() {
    Analytics_logScreenView("person-manual-person");
    voltmx.print("### frmPersonManualPerson_preshow");
    frmPersonManualPerson_clearDateOfBirth();
    frmPersonManualPerson_setGenders();
    voltmx.print("### frmPersonManualPerson_preshow Global.vars.previousform: " + Global.vars.previousForm);
    if (Global.vars.previousForm == "frmPerson") {
        frmPersonManualPerson_clearFields();
    }
    voltmx.print("### frmPersonManualPerson_preshow Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    if (Global.vars.gCasePersons.countryIdenDoc === null && (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType.toString().startsWith("99") === false)) {
        frmPersonManualPerson_flcDocumentCountry_setVisibility(true);
        frmPersonManualPerson.lblDocumentCountry.text = "Kies";
        frmPersonManualPerson.lblDocumentCountry.skin = lblFieldNotFilled;
        frmPersonManualPerson.flcDocumentCountry.skin = flcFieldEdgeRed;
    } else {
        frmPersonManualPerson.flcDocumentCountry.skin = flcFieldEdge;
        frmPersonManualPerson.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
        frmPersonManualPerson.lblDocumentCountry.skin = lblFieldInfo;
        if (Global.vars.documentCountryPersonManual === false) {
            frmPersonManualPerson_flcDocumentCountry_setVisibility(false);
        }
    }
    if (Global.vars.gCasePersons.nationalityDesc === null || Global.vars.gCasePersons.nationalityDesc === "") {
        frmPersonManualPerson.lblPersonNationality.text = "Kies";
        frmPersonManualPerson.lblPersonNationality.skin = lblFieldNotFilled;
        frmPersonManualPerson.flcPersonNationality.skin = flcFieldEdgeRed;
    } else {
        frmPersonManualPerson.lblPersonNationality.text = Global.vars.gCasePersons.nationalityDesc;
        frmPersonManualPerson.lblPersonNationality.skin = lblFieldInfo;
    }
    if (Global.vars.gCasePersons.ssn != null && Global.vars.gCasePersons.ssn !== "") {
        frmPersonManualPerson.txtSSN.text = Global.vars.gCasePersons.ssn;
    }
    if (Global.vars.gCasePersons.surname != null && Global.vars.gCasePersons.surname !== "") {
        frmPersonManualPerson.txtSurname.text = Global.vars.gCasePersons.surname;
    }
    if (Global.vars.gCasePersons.middlename != null && Global.vars.gCasePersons.middlename !== "") {
        frmPersonManualPerson.txtMiddleName.text = Global.vars.gCasePersons.middlename;
    }
    if (Global.vars.gCasePersons.givenNames != null && Global.vars.gCasePersons.givenNames !== "") {
        frmPersonManualPerson.txtFirstName.text = Global.vars.gCasePersons.givenNames;
        frmPersonManualPerson_setInitials();
        frmPersonManualPerson.lblInitials.text = Global.vars.gCasePersons.initials;
        frmPersonManualPerson.lblInitials.skin = lblFieldInfo;
    }
    if (Global.vars.gCasePersons.birthdateComponents.length > 0) {
        voltmx.print("### frmPersonManualPerson_preshow birthdateComponents length > 0");
        voltmx.print("### frmPersonManualPerson_preshow birthdateComponents: " + Global.vars.gCasePersons.birthdateComponents);
        voltmx.print("### frmPersonManualPerson_preshow birthdateDesc: " + Global.vars.gCasePersons.birthdateDesc);
        if (Global.vars.setDateManual === true) {
            frmPersonManualPerson.editdatetime.txtDay.text = Global.vars.gCasePersons.birthdateComponents[0].toString().lpad("0", 2);
            frmPersonManualPerson.editdatetime.txtMonth.text = Global.vars.gCasePersons.birthdateComponents[1].toString().lpad("0", 2);
            frmPersonManualPerson.editdatetime.txtYear.text = Global.vars.gCasePersons.birthdateComponents[2].toString().lpad("0", 4);
            frmPersonManualPerson.lblDateOfBirth.text = Global.vars.gCasePersons.birthdateDesc;
            frmPersonManualPerson.lblDateOfBirth.skin = lblFieldInfo;
        }
        frmPersonManualPerson.calDateOfBirth.dateComponents = Global.vars.gCasePersons.birthdateComponents;
        if (Global.vars.gCasePersons.birthdate != null) {
            Global.vars.gCasePersons.birthDateSet = true;
        } else {
            Global.vars.gCasePersons.birthDateSet = false;
        }
    } else {
        voltmx.print("### frmPersonManualPerson_preshow set a date for easy search");
        frmPersonManualPerson.lblDateOfBirth.skin = lblFieldNotFilled;
        if (Global.vars.setDateManual === false) {
            var birthdate = [30, 6, "1990", 0, 0, 0];
            frmPersonManualPerson.calDateOfBirth.dateComponents = birthdate;
        }
        //       	if(Global.vars.setDateManual === true){
        //           frmPersonManualPerson.editdatetime.txtDay.text = birthdate[0].toString().lpad("0", 2);
        //           frmPersonManualPerson.editdatetime.txtMonth.text = birthdate[1].toString().lpad("0", 2);
        //           frmPersonManualPerson.editdatetime.txtYear.text = birthdate[2].toString().lpad("0", 4);
        //           frmPersonManualPerson.lblDateOfBirth.text = frmPersonManualPerson.editdatetime.txtDay.text.lpad("0", 2) + "-" + frmPersonManualPerson.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonManualPerson.editdatetime.txtYear.text; 
        //         }
        Global.vars.gCasePersons.birthDateSet = false;
    }
    voltmx.print("### frmPersonManualPerson_preshow Global.vars.gCasePersons.birthDateSet: " + Global.vars.gCasePersons.birthDateSet);
    if (Global.vars.gCasePersons.birthdateComponents.length > 0 && Global.vars.gCasePersons.yearOfBirth == null && Global.vars.gCasePersons.birthDateSet === false) {
        frmPersonManualPerson_getYearFromBirthDate();
    } else if (Global.vars.gCasePersons.yearOfBirth != null) {
        frmPersonManualPerson.txtYearOfBirth.text = Number(Global.vars.gCasePersons.yearOfBirth).toString();
    } else if (Global.vars.gCasePersons.yearOfBirth === null) {
        frmPersonManualPerson.txtYearOfBirth.text = "";
    }
    if (Global.vars.gCasePersons.gender !== undefined && Global.vars.gCasePersons.gender != null && Global.vars.gCasePersons.gender !== "") {
        frmPersonManualPerson.lbxGender.selectedKey = Global.vars.gCasePersons.gender.toString();
        //frmPersonManualPerson_onSelectGender();
    } else {
        //frmPersonManualPerson_setGenders();
    }
    frmPersonManualPerson.lblMunicipalityOfBirth.text = Global.vars.gCasePersons.birthMunicipNLDesc;
    frmPersonManualPerson.lblMunicipalityOfBirth.skin = lblFieldInfo;
    if (Global.vars.gCasePersons.birthMunicipNLDesc === null || Global.vars.gCasePersons.birthMunicipNLDesc === "") {
        frmPersonManualPerson.lblMunicipalityOfBirth.text = frmPersonManualPerson.lblMunicipalityOfBirthHeader.text;
        frmPersonManualPerson.lblMunicipalityOfBirth.skin = lblFieldNotFilled;
    }
    if (Global.vars.gCasePersons.birthplace != null || Global.vars.gCasePersons.birthplace !== "") {
        frmPersonManualPerson.txtPlaceOfBirth.text = Global.vars.gCasePersons.birthplace;
    } else {
        frmPersonManualPerson.txtPlaceOfBirth.text = "";
    }
    frmPersonManualPerson_setCountryOfOriginFields();
    if (Global.vars.gCasePersons.countryOfBirth === null || Global.vars.gCasePersons.countryOfBirth === "") {
        frmPersonManualPerson_getCountryOfOrigin();
    }
    frmPersonManualPerson_checkFieldsFilled();
}

function frmPersonManualPerson_checkFieldsFilled() {
    voltmx.print("### frmPersonManualPerson_checkFieldsFilled ###");
    voltmx.print("### frmPersonManualPerson_checkFieldsFilled gender key: " + frmPersonManualPerson.lbxGender.selectedKey);
    var lvalidated = true;
    frmPersonManualPerson.flcPersonNationality.skin = flcFieldEdge;
    frmPersonManualPerson.flcSurname.skin = flcFieldEdge;
    frmPersonManualPerson.flcDateOfBirth.skin = flcFieldEdge;
    frmPersonManualPerson.flcYearOfBirth.skin = flcFieldEdge;
    frmPersonManualPerson.flcMunicipalityOfBirth.skin = flcFieldEdge;
    frmPersonManualPerson.flcGender.skin = flcFieldEdge;
    frmPersonManualPerson.flcCountryOfOrigin.skin = flcFieldEdge;
    frmPersonManualPerson.flcPlaceOfBirth.skin = flcFieldEdge;
    if (frmPersonManualPerson.lblDocumentCountry.text == "Kies") {
        lvalidated = false;
        frmPersonManualPerson.flcDocumentCountry.skin = flcFieldEdgeRed;
    }
    if (frmPersonManualPerson.lblPersonNationality.text == "Kies") {
        lvalidated = false;
        frmPersonManualPerson.flcPersonNationality.skin = flcFieldEdgeRed;
    }
    if (frmPersonManualPerson.txtSurname.text === "" || frmPersonManualPerson.txtSurname.text === null) {
        lvalidated = false;
        frmPersonManualPerson.flcSurname.skin = flcFieldEdgeRed;
    }
    if (frmPersonManualPerson.lblDateOfBirth.text === "Geboortedatum" && Global.vars.setDateManual === true) {
        lvalidated = false;
        frmPersonManualPerson.flcDateOfBirth.skin = flcFieldEdgeRed;
        frmPersonManualPerson.flcYearOfBirth.skin = flcFieldEdgeRed;
    }
    if ((frmPersonManualPerson.lblCountryOfOrigin.text == "Nederland") && (frmPersonManualPerson.lblMunicipalityOfBirth.text === "Geboortegemeente")) {
        lvalidated = false;
        frmPersonManualPerson.flcMunicipalityOfBirth.skin = flcFieldEdgeRed;
    }
    if (frmPersonManualPerson.lbxGender.selectedKey === null) {
        lvalidated = false;
        frmPersonManualPerson.flcGender.skin = flcFieldEdgeRed;
        frmPersonManualPerson.lbxGender.placeholder = voltmx.i18n.getLocalizedString("l_select");
    }
    if (frmPersonManualPerson.lblCountryOfOrigin.text == "Kies") {
        lvalidated = false;
        frmPersonManualPerson.flcCountryOfOrigin.skin = flcFieldEdgeRed;
    }
    if ((Global.vars.countryOfBirth != Global.vars.CountryCode) && (frmPersonManualPerson.txtPlaceOfBirth.text === "" || frmPersonManualPerson.txtPlaceOfBirth.text === null)) {
        lvalidated = false;
        frmPersonManualPerson.flcPlaceOfBirth.skin = flcFieldEdgeRed;
    }
}

function frmPersonManualPerson_setGenders() {
    var genderdata = [];
    for (var i = 0; i < Global.vars.gPersonGenderResult.length; i++) {
        genderdata.push({
            key: Global.vars.gPersonGenderResult[i].number_value.toString(),
            value: Global.vars.gPersonGenderResult[i].descripton
        });
    }
    voltmx.print("### frmPersonManualPerson_setGenders genderdata: " + JSON.stringify(genderdata));
    frmPersonManualPerson.lbxGender.masterDataMap = [genderdata, "key", "value"];
    if (Global.vars.gPersonGenderResult.length > 0) {
        //frmPersonManualPerson.lbxGender.selectedKey = Global.vars.gPersonGenderResult[0].number_value.toString();
        //frmPersonManualPerson_onSelectGender();
    }
}

function frmPersonManualPerson_onSelectGender() {
    //fill the globals
    Global.vars.gCasePersons.gender = Number(frmPersonManualPerson.lbxGender.selectedKey);
    Global.vars.gCasePersons.genderDesc = frmPersonManualPerson.lbxGender.selectedKeyValue[1];
    frmPersonManualPerson_checkFieldsFilled();
}

function frmPersonManualPerson_setDate() {
    // Set date
    //Set valid dates
    var startdate = new Date();
    startdate.setDate(startdate.getDate() - 54750); // about 150 years ago
    voltmx.print("### frmPersonManualPerson_setDate startdate: " + startdate); //now.format("m/dd/yy");
    //format the  start date
    var form_date = startdate.getDate();
    var form_month = startdate.getMonth();
    form_month++;
    var form_year = startdate.getFullYear();
    var valid_startdate = [form_date, form_month, form_year];
    voltmx.print("### frmPersonManualPerson_setDate  valid startdate: " + valid_startdate);
    //format the current date
    var d = new Date();
    var curr_date = d.getDate();
    var curr_month = d.getMonth();
    curr_month++;
    var curr_year = d.getFullYear();
    var curr_hour = d.getHours();
    var curr_min = d.getMinutes();
    var enddate = [curr_date, curr_month, curr_year, curr_hour, curr_min, 0]; // current date and time
    if (Global.vars.setDateManual === true) {
        frmPersonManualPerson.editdatetime.txtDay.text = "";
        frmPersonManualPerson.editdatetime.txtMonth.text = "";
        frmPersonManualPerson.editdatetime.txtYear.text = "";
        frmPersonManualPerson.lblDateOfBirth.text = "Geboortedatum";
        frmPersonManualPerson.lblDateOfBirth.skin = lblFieldNotFilled;
    } else {
        frmPersonManualPerson.calDateOfBirth.validStartDate = valid_startdate;
        frmPersonManualPerson.calDateOfBirth.validEndDate = enddate;
        voltmx.print("### frmPersonManualPerson_setDate Date of birth set start and end date");
        //frmPerson.calDateOfBirth.placeholder = voltmx.i18n.getLocalizedString("l_dateofbirth");
        //End set
        voltmx.print("### frmPersonManualPerson_setDate ###");
        // set date
        //frmPerson.calDateOfBirth.dateComponents = [];//[0,0,0,0,0,0];
        //frmPersonManualPerson.calDateOfBirth.clear();
        frmPersonManualPerson.calDateOfBirth.dateComponents = [30, 6, 1990, 0, 0, 0];
        voltmx.print("### frmPersonManualPerson_setDate datecomponents: " + frmPersonManualPerson.calDateOfBirth.dateComponents + " format: " + frmPersonManualPerson.calDateOfBirth.dateFormat);
    }
    // End set date
}

function frmPersonManualPerson_getNationality() {
    //prefill nationality
    _nationalitypreset = Global.vars.nationalitypreset;
    voltmx.print("### frmPersonManualPerson_getNationality ");
    voltmx.print("### frmPersonManualPerson_getNationality getspecificNationality");
    if (Global.vars.gCasePersons.nationality !== null || Global.vars.gCasePersons.nationality !== "") {
        _nationalitypreset = Global.vars.gCasePersons.nationality;
    }
    voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loading") + "...", "center", true, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    var wcs = "select * from mle_v_nationality_m WHERE code = '" + _nationalitypreset + "'";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonManualPerson_getNationalitySuccessCallbackfrm, frmPersonManualPerson_getNationalityErrorCallbackfrm);
    voltmx.application.dismissLoadingScreen();
}

function frmPersonManualPerson_getNationalitySuccessCallbackfrm(resultnationality) {
    voltmx.print("### frmPersonManualPerson_getNationalitySuccessCallbackfrm ");
    voltmx.print("### frmPersonManualPerson_getNationalitySuccessCallbackfrm result lblPersonNationality: " + frmPersonManualPerson.lblPersonNationality.text);
    voltmx.print("### frmPersonManualPerson_getNationalitySuccessCallbackfrm result nationality: " + JSON.stringify(resultnationality));
    if (resultnationality != null) {
        frmPersonManualPerson.lblPersonNationality.text = resultnationality[0].description;
        frmPersonManualPerson.lblPersonNationality.skin = lblFieldInfo;
        //fill globals
        voltmx.print("### frmPersonManualPerson_getNationalitySuccessCallbackfrm NationalityCode resultnationality[0].Code" + resultnationality[0].code + " = " + Global.vars.nationalitypreset);
        Global.vars.gCasePersons.nationality = resultnationality[0].code;
        if (resultnationality[0].code == Global.vars.nationalitypreset) {
            voltmx.print("### frmPersonManualPerson_getNationalitySuccessCallbackfrm NationalityCode resultnationality[0].Code" + resultnationality[0].code + " = " + Global.vars.nationalitypreset);
            Global.vars.gCasePersons.indicationDutch = true;
        }
        Global.vars.gCasePersons.nationalityDesc = resultnationality[0].description;
        //end fill globals
    } else {
        frmPersonManualPerson.lblPersonNationality.text = "Kies";
        frmPersonManualPerson.lblPersonNationality.skin = lblFieldNotFilled;
        frmPersonManualPerson.flcPersonNationality.skin = flcFieldEdgeRed;
    }
    voltmx.print("### frmPersonManualPerson_getNationalitySuccessCallbackfrm result Global.vars.gCasePersons.Nationality: " + Global.vars.gCasePersons.nationality);
    voltmx.application.dismissLoadingScreen();
}

function frmPersonManualPerson_getNationalityErrorCallbackfrm(error) {
    voltmx.print("### frmPersonManualPerson_getNationalityErrorCallbackfrm ");
    voltmx.print("### frmPersonManualPerson_getNationalityErrorCallbackfrm error: " + JSON.stringify(error));
    frmPersonManualPerson.lblPersonNationality.text = "Kies";
    frmPersonManualPerson.lblPersonNationality.skin = lblFieldNotFilled;
    frmPersonManualPerson.flcPersonNationality.skin = flcFieldEdgeRed;
    voltmx.application.dismissLoadingScreen();
}
// end nationality prefill
function frmPersonManualPerson_onclickbtnNationality() {
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    frmPersonNationalities.show();
}

function frmPersonManualPerson_btnBack() {
    frmPersonManualPerson_resetGlobals();
    frmPersonManualPerson_clearFields();
    if (Global.vars.previousForm == "frmPersonResult") {
        frmPersonResult.show();
    } else {
        if (Global.vars.readIDScanned === true) {
            Global.vars.gCasePersonsIndex = 0;
            Global.vars.gCasePersons = CaseData_setNewperson(Global.vars.gCasePersonsIndex);
            Global.vars.readIDScanned = false;
        }
        frmPerson.show();
    }
}

function frmPersonManualPerson_onDoneSSN() {}

function frmPersonManualPerson_onEndEditingSSN() {
    //Do eleven check
    var ssn = "0";
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iPa") === true) {
        frmPersonManualPerson.txtSSN.text = frmPersonManualPerson.txtSSN.text.replace(/[-,.]/g, "");
        voltmx.print("### frmPersonManualPerson_onEndEditingSSN iPad ssn number: " + frmPersonManualPerson.txtSSN.text);
    }
    if (frmPersonManualPerson.txtSSN.text.length > 0) { // && Global.vars.gCasePersons.indicationDutch === true){
        ssn = voltmx.os.toNumber(frmPersonManualPerson.txtSSN.text).toString();
        if ((ssn.length > 7 && ssn.length < 10) && (ssn != "00000000" && ssn != "000000000" && ssn != "99999999" && ssn != "999999999")) {
            if (ssn.length < 9) {
                ssn = "0" + ssn;
                voltmx.print("### concatenated number: " + ssn);
            }
            var sseleven = Utility_ElevenCheck(ssn);
            voltmx.print("### sseleven: " + sseleven);
            if (!sseleven) {
                alert(voltmx.i18n.getLocalizedString("l_notAnSSN"));
            }
        } else {
            alert(voltmx.i18n.getLocalizedString("l_notAnSSN"));
        }
    }
}

function frmPersonManualPerson_onTextChangeFirstName() {
    var _text = Utility_regexpRestrictedInputPersonName(frmPersonManualPerson.txtFirstName.text);
    _text = _text.replace(" - ", "-");
    _text = _text.replace("- ", "-");
    _text = _text.replace(" -", "-");
    frmPersonManualPerson.txtFirstName.text = _text.replace(/\s{2,}/g, ' ');
}

function frmPersonManualPerson_onTextChangeSurName() {
    frmPersonManualPerson.txtSurname.text = Utility_regexpRestrictedInputPersonName(frmPersonManualPerson.txtSurname.text);
    frmPersonManualPerson_checkFieldsFilled();
}

function frmPersonManualPerson_onTextChangeMiddleName() {
    frmPersonManualPerson.txtMiddleName.text = Utility_regexpRestrictedInputPersonName(frmPersonManualPerson.txtMiddleName.text);
}

function frmPersonManualPerson_onTextChangePlaceOfBirth() {
    frmPersonManualPerson.txtPlaceOfBirth.text = frmPersonManualPerson.txtPlaceOfBirth.text;
    Global.vars.gCasePersons.birthplace = frmPersonManualPerson.txtPlaceOfBirth.text.trim();
    frmPersonManualPerson_checkFieldsFilled();
}

function frmPersonManualPerson_setInitials() {
    Global.vars.gCasePersons.givenNames = frmPersonManualPerson.txtFirstName.text.trim();
    //get intitials from names
    if (Global.vars.gCasePersons.givenNames !== "" && Global.vars.gCasePersons.givenNames != "null") {
        var initials = "";
        var str = Global.vars.gCasePersons.givenNames + "";
        str = str.split(" ");
        for (i = 0; i < str.length; i++) {
            initials += str[i].substr(0, 1) + ".";
            initials = initials.toUpperCase();
        }
        voltmx.print("### frmPersonManualPerson_setInitials: " + initials);
        Global.vars.gCasePersons.initials = initials;
        frmPersonManualPerson.lblInitials.text = Global.vars.gCasePersons.initials;
        frmPersonManualPerson.lblInitials.skin = lblFieldInfo;
    }
    //end get initials
}

function frmPersonManualPerson_setBirthDate() {
    voltmx.print("### frmPersonManualPerson_setBirthDate");
    var _birthDate = null;
    var _birthYear = null;
    var _age = null;
    voltmx.print("### frmPersonManualPerson_setBirthDate frmPersonManualPerson.calDateOfBirth: " + frmPersonManualPerson.calDateOfBirth);
    var date = [];
    var year = null;
    var month = null;
    var day = null;
    if (Global.vars.setDateManual === true) {
        day = Number(frmPersonManualPerson.editdatetime.txtDay.text);
        month = Number(frmPersonManualPerson.editdatetime.txtMonth.text);
        year = Number(frmPersonManualPerson.editdatetime.txtYear.text);
        date = [day, month, year, 0, 0, 0, 0];
        frmPersonManualPerson.calDateOfBirth.dateComponents = date;
    } else {
        year = frmPersonManualPerson.calDateOfBirth.year;
        month = frmPersonManualPerson.calDateOfBirth.month;
        day = frmPersonManualPerson.calDateOfBirth.day;
        date = frmPersonManualPerson.calDateOfBirth.dateComponents;
    }
    voltmx.print("### frmPersonManualPerson_setBirthDate year: " + year);
    voltmx.print("### frmPersonManualPerson_setBirthDate Global.vars.gCasePersons.birthDateSet: " + Global.vars.gCasePersons.birthDateSet);
    if (Global.vars.gCasePersons.birthDateSet === false) {
        day = 01;
        month = 01;
        year = frmPersonManualPerson.txtYearOfBirth.text;
        // if year only
        // set birthdate
        _birthDate = (year + "-" + month + "-" + day).toDate("yyyy-mm-dd");
        voltmx.print("### _birthDate: " + JSON.stringify(_birthDate));
        //end set birthdate
        Global.vars.gCasePersons.age = Utility_getAge(_birthDate);
        Global.vars.gCasePersons.birthdate = null;
        Global.vars.gCasePersons.birthdateDesc = year + "";
    } else {
        //if full date
        // set birthdate
        var birthDateJavascript = new Date((year + "/" + month + "/" + day));
        voltmx.print("### frmPersonResult_callbackfunctionBSN birthDateJavascript: " + birthDateJavascript);
        _birthDate = (year + "-" + month + "-" + day).toDate("yyyy-mm-dd");
        voltmx.print("### _birthDate: " + JSON.stringify(_birthDate));
        //end set birthdate
        _birthYear = _birthDate.year;
        Global.vars.gCasePersons.age = Utility_getAge(_birthDate);
        Global.vars.gCasePersons.birthdate = Utility_getDateTimeString(_birthDate);
        Global.vars.gCasePersons.birthdateDesc = Utility_getLocaleShortDateString(birthDateJavascript);
    }
    // set datecomponent
    var receiveddate = [_birthDate.day, _birthDate.month, _birthDate.year, 0, 0, 0];
    Global.vars.gCasePersons.birthdateComponents = receiveddate;
    voltmx.print("### receiveddate: " + JSON.stringify(receiveddate));
    // end set to datecomponent
    if (Global.vars.gCasePersons.birthDateSet === false) {
        Global.vars.gCasePersons.yearOfBirth = frmPersonManualPerson.txtYearOfBirth.text;
    } else {
        Global.vars.gCasePersons.yearOfBirth = year + "";
    }
    voltmx.print("### frmPersonManualPerson_setBirthDate Global.vars.gCasePersons.birthDateSet: " + Global.vars.gCasePersons.birthDateSet);
    voltmx.print("### frmPersonManualPerson_setBirthDate birthdateComponents: " + Global.vars.gCasePersons.birthdateComponents);
    voltmx.print("### frmPersonManualPerson_setBirthDate YearOfBirth: " + Global.vars.gCasePersons.yearOfBirth);
    voltmx.print("### frmPersonManualPerson_setBirthDate Birthdate: " + Global.vars.gCasePersons.birthdate);
    voltmx.print("### frmPersonManualPerson_setBirthDate BirthdateDesc: " + Global.vars.gCasePersons.birthdateDesc);
    voltmx.print("### frmPersonManualPerson_setBirthDate Age: " + Global.vars.gCasePersons.age);
    voltmx.print("### frmPersonManualPerson_setBirthDate Birthplace: " + Global.vars.gCasePersons.birthplace);
    frmPersonManualPerson_recheckbirthMunicipNL();
    //frmPersonManualPerson_clearBirthMunicipNL();
    frmPersonManualPerson_checkFieldsFilled();
}

function frmPersonManualPerson_getYearFromBirthDate() {
    voltmx.print("### frmPersonManualPerson_getYearFromBirthDate ");
    var yearOfBirth = frmPersonManualPerson.calDateOfBirth.year + "";
    if (Global.vars.setDateManual === true) {
        yearOfBirth = frmPersonManualPerson.editdatetime.txtYear.text;
    }
    voltmx.print("### frmPersonManualPerson_getYearFromBirthDate Date of birth Global.vars.gCasePersons.birthDateSet = true");
    voltmx.print("### frmPersonManualPerson_getYearFromBirthDate Calendar year: " + yearOfBirth);
    voltmx.print("### frmPersonManualPerson_getYearFromBirthDate Calendar components" + frmPersonManualPerson.calDateOfBirth.dateComponents);
    Global.vars.gCasePersons.birthDateSet = true;
    Global.vars.gCasePersons.yearOfBirth = yearOfBirth;
    frmPersonManualPerson.txtYearOfBirth.text = yearOfBirth;
    frmPersonManualPerson_setBirthDate();
}

function frmPersonManualPerson_getBirthDateFromYear() {
    voltmx.print("### frmPersonManualPerson_getBirthDateFromYear ");
    Global.vars.gCasePersons.birthDateSet = false;
    voltmx.print("### frmPersonManualPerson_getBirthDateFromYear Calendar year: " + frmPersonManualPerson.txtYearOfBirth.text);
    voltmx.print("### frmPersonManualPerson_getBirthDateFromYear Calendar components" + frmPersonManualPerson.calDateOfBirth.dateComponents);
    if (frmPersonManualPerson.txtYearOfBirth.text.length == 4) {
        var birthdate = [01, 01, frmPersonManualPerson.txtYearOfBirth.text, 0, 0, 0];
        voltmx.print("### frmPersonManualPerson_getBirthDateFromYear birthdate: " + JSON.stringify(birthdate));
        frmPersonManualPerson.calDateOfBirth.dateComponents = birthdate;
        Global.vars.gCasePersons.yearOfBirth = frmPersonManualPerson.txtYearOfBirth.text;
        // RKA RED-120
        frmPersonManualPerson.editdatetime.txtDay.text = birthdate[0].toString().lpad("0", 2);
        frmPersonManualPerson.editdatetime.txtMonth.text = birthdate[1].toString().lpad("0", 2);
        frmPersonManualPerson.editdatetime.txtYear.text = birthdate[2].toString().lpad("0", 4);
        frmPersonManualPerson.lblDateOfBirth.text = frmPersonManualPerson.editdatetime.txtYear.text;
        frmPersonManualPerson.lblDateOfBirth.skin = lblFieldInfo;
        //  
        frmPersonManualPerson_setBirthDate();
    }
}

function frmPersonManualPerson_onclick_btnDocumentCountry() {
    Global.vars.personCountryType = "documentcountry";
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    voltmx.print("### frmPersonManualPerson_onclick_btnDocumentCountry " + Global.vars.personCountryType);
    frmPersonCountries.show();
}

function frmPersonManualPerson_onclick_countryOfOrigin() {
    if (Global.vars.gCasePersons.birthDateSet === true) {
        Global.vars.findCountryOfBirth = true;
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        Global.vars.personCountryType = "";
        frmPersonCountries.show();
    } else if (Global.vars.gCasePersons.birthDateSet === false && Global.vars.gCasePersons.yearOfBirth != null && Global.vars.gCasePersons.birthdate == null) {
        Global.vars.findCountryOfBirth = true;
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        Global.vars.personCountryType = "";
        frmPersonCountries.show();
    } else {
        alert("Geen geboortedatum ingevuld");
    }
}

function frmPersonManualPerson_getCountryOfOrigin() {
    voltmx.print("### frmPersonManualPerson_getCountryOfOrigin");
    voltmx.print("### frmPersonManualPerson_getCountryOfOrigin Current Country of birth: " + Global.vars.gCasePersons.countryOfBirth);
    voltmx.print("### frmPersonManualPerson_getCountryOfOrigin Current CountryOfBirthDesc: " + Global.vars.gCasePersons.countryOfBirthDesc);
    voltmx.print("### frmPersonManualPerson_getCountryOfOrigin Current countryIdenDoc: " + Global.vars.gCasePersons.countryIdenDoc);
    voltmx.print("### frmPersonManualPerson_getCountryOfOrigin Current nationality: " + Global.vars.gCasePersons.nationality);
    //   	//TestData
    //   	Global.vars.gCasePersons.countryOfBirthDesc = "Nederland";
    //   	frmPersonManualPerson.lblCountryOfOrigin.text = Global.vars.gCasePersons.countryOfBirthDesc;
    //     Global.vars.gCasePersons.countryOfBirth = 6030;
    //   	//
    function getCountryOfOriginSuccessCallback(resultcountryid) {
        voltmx.print("### frmPersonManualPerson_getCountryOfOrigin getCountryOfOriginSuccessCallback ");
        voltmx.print("### frmPersonManualPerson_getCountryOfOrigin resultcountryid: " + JSON.stringify(resultcountryid));
        if ((resultcountryid.length === 1)) {
            Global.vars.gCasePersons.countryOfBirthDesc = resultcountryid[0].description;
            Global.vars.gCasePersons.countryOfBirth = resultcountryid[0].code;
            frmPersonManualPerson.lblCountryOfOrigin.text = Global.vars.gCasePersons.countryOfBirthDesc;
            frmPersonManualPerson.lblCountryOfOrigin.skin = lblFieldInfo;
            frmPersonManualPerson.flcCountryOfOrigin.skin = flcFieldEdge;
            frmPersonManualPerson_setCountryOfOriginFields();
            voltmx.print("### frmPersonManualPerson_getCountryOfOrigin country succes and set ");
        } else {
            voltmx.print("### frmPersonManualPerson_getCountryOfOrigin no results found for Country Info");
            frmPersonManualPerson.lblCountryOfOrigin.text = "Kies";
            frmPersonManualPerson.lblCountryOfOrigin.skin = lblFieldNotFilled;
            frmPersonManualPerson.flcCountryOfOrigin.skin = flcFieldEdgeRed;
        }
        voltmx.application.dismissLoadingScreen();
    }

    function getCountryOfOriginErrorCallback(error) {
        voltmx.print("### frmPersonManualPerson_getCountryOfOrigin getCountryOfOriginErrorCallback ");
        voltmx.print("### frmPersonManualPerson_getCountryOfOrigin Country Info error: " + error);
        frmPersonManualPerson.lblCountryOfOrigin.text = "Kies";
        frmPersonManualPerson.lblCountryOfOrigin.skin = lblFieldNotFilled;
        frmPersonManualPerson.flcCountryOfOrigin.skin = flcFieldEdgeRed;
        voltmx.application.dismissLoadingScreen();
    }
    if (Global.vars.gCasePersons.countryOfBirthDesc != null && Global.vars.gCasePersons.countryOfBirthDesc !== "") {
        frmPersonManualPerson.lblCountryOfOrigin.text = Global.vars.gCasePersons.countryOfBirthDesc;
        frmPersonManualPerson.lblCountryOfOrigin.skin = lblFieldInfo;
    } else {
        var lCountrywhereClause = "select * from mle_v_country_m where code = '" + Global.vars.gCasePersons.countryIdenDoc + "'";
        if (Global.vars.gCasePersons.nationality !== undefined && Global.vars.gCasePersons.nationality != null && Global.vars.gCasePersons.nationality !== "") {
            if (Global.vars.gCasePersons.countryIdenDoc === Global.vars.CountryCode && Global.vars.gCasePersons.nationality === Global.vars.nationalitypreset) {
                lCountrywhereClause = lCountrywhereClause + " and nny_code = '" + Global.vars.gCasePersons.nationality + "'";
            } else {
                lCountrywhereClause = "select * from mle_v_country_m where nny_code = '" + Global.vars.gCasePersons.nationality + "'";
            }
        }
        if (Global.vars.gCasePersons.idenDocTypeDesc == "W-document") {
            voltmx.print("### frmPersonManualPerson_getCountryOfOrigin W-document dus geen country of origin vullen");
            frmPersonManualPerson.lblCountryOfOrigin.text = "Kies";
            frmPersonManualPerson.lblCountryOfOrigin.skin = lblFieldNotFilled;
            frmPersonManualPerson.flcCountryOfOrigin.skin = flcFieldEdgeRed;
        } else {
            lCountrywhereClause = Utility_addLanguageToWhereClauseObjectSync(lCountrywhereClause);
            voltmx.print("###frmPersonManualPerson_getCountryOfOrigin  Country clause: " + lCountrywhereClause);
            KNYMobileFabric.OfflineObjects.executeSelectQuery(lCountrywhereClause, getCountryOfOriginSuccessCallback, getCountryOfOriginErrorCallback);
        }
    }
    voltmx.print("### frmPersonManualPerson_getCountryOfOrigin");
}

function frmPersonManualPerson_setCountryOfOriginFields() {
    if (Global.vars.gCasePersons.countryOfBirth == Global.vars.CountryCode) {
        frmPersonManualPerson_flcMunicipalityOfBirth_setVisibility(true);
        frmPersonManualPerson_flcPlaceOfBirth_setVisibility(false);
        frmPersonManualPerson.txtPlaceOfBirth.text = "";
    } else if (Global.vars.gCasePersons.countryOfBirth != Global.vars.CountryCode) {
        frmPersonManualPerson_flcMunicipalityOfBirth_setVisibility(false);
        frmPersonManualPerson.lblMunicipalityOfBirth.text = frmPersonManualPerson.lblMunicipalityOfBirthHeader.text;
        frmPersonManualPerson.lblMunicipalityOfBirth.skin = lblFieldInfo;
        frmPersonManualPerson_flcPlaceOfBirth_setVisibility(true);
        if (Global.vars.gCasePersons.birthplace != null && Global.vars.gCasePersons.birthplace !== "") {
            frmPersonManualPerson.txtPlaceOfBirth.text = Global.vars.gCasePersons.birthplace;
        }
    } else if (Global.vars.gCasePersons.countryOfBirth === null || Global.vars.gCasePersons.countryOfBirth === "") {
        frmPersonManualPerson_getCountryOfOrigin();
    }
}

function frmPersonManualPerson_onclick_municipalityOfBirth() {
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    frmPersonMunicipalities.show();
}

function frmPersonManualPerson_onclickNext() {
    if ((Global.vars.gCasePersons.initials === "" || Global.vars.gCasePersons.initials === null) && (frmPersonManualPerson.txtSurname.text !== "" && frmPersonManualPerson.txtSurname.text != null)) {
        voltmx.ui.Alert("U heeft geen voornaam ingevuld en daardoor zijn er geen initialen vastgelegd. Wilt u de eerste letter van de achternaam als intitiaal gebruiken?", frmPersonManualPerson_confirm_LastNameInitials, "confirmation", voltmx.i18n.getLocalizedString("bt_yes"), voltmx.i18n.getLocalizedString("bt_no"), "Info", null);
    } else {
        frmPersonManualPerson_SetGlobals();
    }
}

function frmPersonManualPerson_confirm_LastNameInitials(response) {
    if (response) {
        var initials = "";
        var str = frmPersonManualPerson.txtSurname.text.trim();
        initials = str.substr(0, 1) + ".";
        initials = initials.toUpperCase();
        voltmx.print("### frmPersonManualPerson_setInitials: " + initials);
        Global.vars.gCasePersons.initials = initials;
        frmPersonManualPerson.lblInitials.text = Global.vars.gCasePersons.initials;
        frmPersonManualPerson.lblInitials.skin = lblFieldInfo;
        frmPersonManualPerson_onclickNext();
    }
}

function frmPersonManualPerson_SetGlobals() {
    voltmx.print("#### frmPersonManualPerson_SetGlobals");
    Global.vars.gCasePersons.ssn = frmPersonManualPerson.txtSSN.text;
    if (frmPersonManualPerson.txtFirstName.text != null && frmPersonManualPerson.txtFirstName.text !== "") {
        Global.vars.gCasePersons.givenNames = frmPersonManualPerson.txtFirstName.text;
        Global.vars.gCasePersons.givenNames = Global.vars.gCasePersons.givenNames.trim();
    } else {
        Global.vars.gCasePersons.givenNames = "";
    }
    if (frmPersonManualPerson.lblInitials.text !== voltmx.i18n.getLocalizedString("l_initials")) {
        Global.vars.gCasePersons.initials = frmPersonManualPerson.lblInitials.text;
    }
    Global.vars.gCasePersons.surname = Utility_titleCase(frmPersonManualPerson.txtSurname.text.trim());
    if (frmPersonManualPerson.txtMiddleName.text != null && frmPersonManualPerson.txtMiddleName.text !== "") {
        var formatMiddlename = frmPersonManualPerson.txtMiddleName.text.toLowerCase();
        Global.vars.gCasePersons.middlename = formatMiddlename.trim();
    } else {
        Global.vars.gCasePersons.middlename = "";
    }
    var _birthDate = null;
    var _birthYear = null;
    var _age = null;
    voltmx.print("### frmPersonManualPerson_SetGlobals frmPersonManualPerson.calDateOfBirth: " + frmPersonManualPerson.calDateOfBirth);
    var date = frmPersonManualPerson.calDateOfBirth.dateComponents;
    var year = frmPersonManualPerson.calDateOfBirth.year;
    var month = frmPersonManualPerson.calDateOfBirth.month;
    var day = frmPersonManualPerson.calDateOfBirth.day;
    if (Global.vars.setDateManual === true) {
        day = Number(frmPersonManualPerson.editdatetime.txtDay.text);
        month = Number(frmPersonManualPerson.editdatetime.txtMonth.text);
        year = Number(frmPersonManualPerson.editdatetime.txtYear.text);
        date = [day, month, year, 0, 0, 0, 0];
    }
    voltmx.print("### year: " + year);
    voltmx.print("### frmPersonManualPerson_SetGlobals Global.vars.gCasePersons.birthDateSet: " + Global.vars.gCasePersons.birthDateSet);
    if (Global.vars.gCasePersons.birthDateSet === false) {
        day = 01;
        month = 01;
        year = frmPersonManualPerson.txtYearOfBirth.text;
        // if year only
        // set birthdate
        _birthDate = (year + "-" + month + "-" + day).toDate("yyyy-mm-dd");
        voltmx.print("### _birthDate: " + JSON.stringify(_birthDate));
        Global.vars.gCasePersons.age = Utility_getAge(_birthDate);
        Global.vars.gCasePersons.birthdate = null;
        Global.vars.gCasePersons.birthdateDesc = year + "";
    } else {
        //if full date
        // set birthdate
        _birthDate = (year + "-" + month + "-" + day).toDate("yyyy-mm-dd");
        voltmx.print("### _birthDate: " + JSON.stringify(_birthDate));
        var birthDateJavascript = new Date((year + "/" + month + "/" + day));
        voltmx.print("### frmPersonResult_callbackfunctionBSN birthDateJavascript: " + birthDateJavascript);
        //end set birthdate
        _birthYear = _birthDate.year;
        Global.vars.gCasePersons.age = Utility_getAge(_birthDate);
        Global.vars.gCasePersons.birthdate = Utility_getDateTimeString(_birthDate);
        Global.vars.gCasePersons.birthdateDesc = Utility_getLocaleShortDateString(birthDateJavascript);
    }
    // set datecomponent
    var receiveddate = [_birthDate.day, _birthDate.month, _birthDate.year, 0, 0, 0];
    Global.vars.gCasePersons.birthdateComponents = receiveddate;
    voltmx.print("### receiveddate: " + JSON.stringify(receiveddate));
    // end set to datecomponent
    if (Global.vars.gCasePersons.birthDateSet === false) {
        Global.vars.gCasePersons.yearOfBirth = frmPersonManualPerson.txtYearOfBirth.text;
    } else {
        Global.vars.gCasePersons.yearOfBirth = year + "";
    }
    voltmx.print("### frmPersonManualPerson_SetGlobals YearOfBirth: " + Global.vars.gCasePersons.yearOfBirth);
    voltmx.print("### frmPersonManualPerson_SetGlobals Birthdate: " + Global.vars.gCasePersons.birthdate);
    voltmx.print("### frmPersonManualPerson_SetGlobals BirthdateDesc: " + Global.vars.gCasePersons.birthdateDesc);
    voltmx.print("### frmPersonManualPerson_SetGlobals Age: " + Global.vars.gCasePersons.age);
    voltmx.print("### frmPersonManualPerson_SetGlobals Birthplace: " + Global.vars.gCasePersons.birthplace);
    if (frmPersonManualPerson.txtPlaceOfBirth.text !== "") {
        Global.vars.gCasePersons.birthplace = frmPersonManualPerson.txtPlaceOfBirth.text;
    }
    var validated = frmPersonManualPerson_validateSetValues(Global.vars.gCasePersons);
    if (validated.validated) {
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        Global.vars.personInputMethod = Global.vars.personInputMethod === "" ? "Manual" : Global.vars.personInputMethod + "-Manual";
        frmPersonManualAddress.show();
        if (Global.vars.gCasePersons.age < Global.vars.minAge) {
            voltmx.ui.Alert("Let op! Persoon is jonger dan " + Number(Global.vars.minAge).toString(), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
        } else if (Global.vars.gCasePersons.age > Global.vars.maxAge) {
            voltmx.ui.Alert("Let op! Persoon is ouder dan " + Number(Global.vars.maxAge).toString(), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
        }
    } else {
        alert(validated.errorMsg);
    }
}

function frmPersonManualPerson_validateSetValues(inputPerson) {
    voltmx.print("### frmPersonManualPerson_validateSetValues ###");
    voltmx.print("### frmPersonManualPerson_validateSetValues inputPerson: " + JSON.stringify(inputPerson));
    var lvalidated = true;
    var lmessage = "";
    var lResult = {
        entity: "person",
        validated: lvalidated,
        errorCode: "",
        errorMsg: lmessage
    };
    if (inputPerson.countryIdenDoc === null || inputPerson.countryIdenDoc === "") {
        if (lmessage.length === 0) {
            lmessage = "Land van uitgifte document";
        } else {
            lmessage = lmessage.trim() + ", " + "Land van uitgifte document";
        }
    }
    if (inputPerson.nationality === null || inputPerson.nationality === "") {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = "Nationaliteit";
        } else {
            lmessage = lmessage.trim() + ", " + "Nationaliteit";
        }
    }
    if (inputPerson.ssn !== "" && inputPerson.ssn != null) // && inputPerson.indicationDutch === true)
    {
        if ((inputPerson.ssn.length > 7 && inputPerson.ssn.length < 10) && (inputPerson.ssn != "00000000" && inputPerson.ssn != "000000000" && inputPerson.ssn != "99999999" && inputPerson.ssn != "999999999")) {
            var sseleven = Utility_ElevenCheck(inputPerson.ssn);
            voltmx.print("### frmPersonManualPerson_validateSetValues sseleven: " + sseleven);
            if (!sseleven) {
                lvalidated = false;
                if (lmessage.length === 0) {
                    lmessage = voltmx.i18n.getLocalizedString("l_notAnSSN");
                } else {
                    lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_notAnSSN");
                }
            }
        } else {
            lvalidated = false;
            if (lmessage.length === 0) {
                lmessage = voltmx.i18n.getLocalizedString("l_notAnSSN");
            } else {
                lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_notAnSSN");
            }
        }
    }
    if (inputPerson.initials === "" || inputPerson.initials === null) {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = voltmx.i18n.getLocalizedString("l_initials");
        } else {
            lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_initials");
        }
    }
    if (inputPerson.surname === "" || inputPerson.surname === null) {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = voltmx.i18n.getLocalizedString("l_surname");
        } else {
            lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_surname");
        }
    }
    if (inputPerson.gender === null) {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = voltmx.i18n.getLocalizedString("l_gender");
        } else {
            lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_gender");
        }
    }
    if (inputPerson.yearOfBirth === null || inputPerson.yearOfBirth === "") {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = "Persoon heeft geen geboortedatum of geboortejaar"; //voltmx.i18n.getLocalizedString("l_yearofbirth");
        } else {
            lmessage = lmessage.trim() + ", " + "Persoon heeft geen geboortedatum of geboortejaar"; //voltmx.i18n.getLocalizedString("l_yearofbirth");
        }
    }
    if (inputPerson.countryOfBirth === null || inputPerson.countryOfBirth === "") {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = voltmx.i18n.getLocalizedString("l_countryoforigin");
        } else {
            lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_countryoforigin");
        }
    }
    if ((inputPerson.countryOfBirth == Global.vars.CountryCode) && (inputPerson.birthMunicipNL === "" || inputPerson.birthMunicipNL === null)) {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = voltmx.i18n.getLocalizedString("l_municipalityOfBirth");
        } else {
            lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_municipalityOfBirth");
        }
    } else if ((inputPerson.countryOfBirth != Global.vars.CountryCode) && (inputPerson.birthplace === "" || inputPerson.birthplace === null)) {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = "Geboorteplaats";
        } else {
            lmessage = lmessage.trim() + ", " + "Geboorteplaats";
        }
    }
    if (lvalidated === false) {
        lmessage = voltmx.i18n.getLocalizedString("e_mand0002") + " " + lmessage.trim();
        lResult = {
            entity: "person",
            validated: lvalidated,
            errorCode: "e_mand0002",
            errorMsg: lmessage
        }; //check errorcode
    }
    voltmx.print("### frmPersonManualPerson_validateSetValues result: " + JSON.stringify(lResult));
    return lResult;
}

function frmPersonManualPerson_checkDaysManualOnEdit() {
    if (frmPersonManualPerson.editdatetime.txtDay.text.length > 2) {
        frmPersonManualPerson.editdatetime.txtDay.text = frmPersonManualPerson.editdatetime.txtDay.text.substring(0, 2);
    }
    var days = Number(frmPersonManualPerson.editdatetime.txtDay.text);
    if (days < 0 || frmPersonManualPerson.editdatetime.txtDay.text == "00") {
        frmPersonManualPerson.editdatetime.txtDay.text = "01";
    }
    if (days > 31) {
        frmPersonManualPerson.editdatetime.txtDay.text = "31";
    }
}

function frmPersonManualPerson_checkDaysManualOnEnd() {
    var days = Number(frmPersonManualPerson.editdatetime.txtDay.text);
    if (days < 0 || days === 0) {
        frmPersonManualPerson.editdatetime.txtDay.text = "01";
    }
    if (days > 31) {
        frmPersonManualPerson.editdatetime.txtDay.text = "31";
    }
    if (days < 10) {
        frmPersonManualPerson.editdatetime.txtDay.text = frmPersonManualPerson.editdatetime.txtDay.text.lpad("0", 2);
    }
}

function frmPersonManualPerson_checkMonthManualOnEdit() {
    if (frmPersonManualPerson.editdatetime.txtMonth.text.length > 2) {
        frmPersonManualPerson.editdatetime.txtMonth.text = frmPersonManualPerson.editdatetime.txtMonth.text.substring(0, 2);
    }
    var month = Number(frmPersonManualPerson.editdatetime.txtMonth.text);
    if (month < 0 || frmPersonManualPerson.editdatetime.txtMonth.text == "00") {
        frmPersonManualPerson.editdatetime.txtMonth.text = "01";
    }
    if (month > 12) {
        frmPersonManualPerson.editdatetime.txtMonth.text = "12";
    }
}

function frmPersonManualPerson_checkMonthManualOnEnd() {
    var month = Number(frmPersonManualPerson.editdatetime.txtMonth.text);
    if (month < 0 || month === 0) {
        frmPersonManualPerson.editdatetime.txtMonth.text = "01";
    }
    if (month > 12) {
        frmPersonManualPerson.editdatetime.txtMonth.text = "12";
    }
    if (month < 10) {
        frmPersonManualPerson.editdatetime.txtMonth.text = frmPersonManualPerson.editdatetime.txtMonth.text.lpad("0", 2);
    }
    //Do day check for specific month
    var year = Number(frmPersonManualPerson.editdatetime.txtYear.text);
    var days = Number(frmPersonManualPerson.editdatetime.txtDay.text);
    var daysInMonth = new Date(year, month, 0).getDate();
    voltmx.print("### frmPersonManualPerson_checkMonthManualOnEdit daysInMonth: " + daysInMonth);
    if (days > daysInMonth) {
        alert("Deze maand heeft geen " + days + " dagen");
    }
}

function frmPersonManualPerson_checkYearManualOnEdit() {
    var year = Number(frmPersonManualPerson.editdatetime.txtYear.text);
    if (year < 0 || frmPersonManualPerson.editdatetime.txtYear.text == "0000") {
        frmPersonManualPerson.editdatetime.txtYear.text = "0001";
    }
    if (frmPersonManualPerson.editdatetime.txtYear.text.length > 4) {
        frmPersonManualPerson.editdatetime.txtYear.text = frmPersonManualPerson.editdatetime.txtYear.text.substring(0, 4);
    }
}

function frmPersonManualPerson_checkYearManualOnEnd() {
    var year = Number(frmPersonManualPerson.editdatetime.txtYear.text);
    if (year < 0 || year === 0) {
        frmPersonManualPerson.editdatetime.txtYear.text = "0001";
    }
    if (year > 9999) {
        frmPersonManualPerson.editdatetime.txtYear.text = "9999";
    }
    if (frmPersonManualPerson.editdatetime.txtYear.text.length < 4) {
        frmPersonManualPerson.editdatetime.txtYear.text = frmPersonManualPerson.editdatetime.txtYear.text.lpad("0", 4);
    }
}

function frmPersonManualPerson_showEditTime() {
    try {
        // take available date
        if (Global.vars.gCasePersons.birthdateComponents.length > 0) {
            voltmx.print("### frmPersonManualPerson_preshow birthdateComponents length > 0");
            if (Global.vars.setDateManual === true) {
                frmPersonManualPerson.editdatetime.txtDay.text = Global.vars.gCasePersons.birthdateComponents[0].toString().lpad("0", 2);
                frmPersonManualPerson.editdatetime.txtMonth.text = Global.vars.gCasePersons.birthdateComponents[1].toString().lpad("0", 2);
                frmPersonManualPerson.editdatetime.txtYear.text = Global.vars.gCasePersons.birthdateComponents[2].toString().lpad("0", 4);
            }
        }
        //set correct data
        frmPersonManualPerson.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmPersonManualPerson.forceLayout();
        frmPersonManualPerson_editdatetime_setVisibility(true);
        frmPersonManualPerson_showEditTime_preAnim();
        frmPersonManualPerson_showEditTime_animationStart();
    } catch (e) {
        voltmx.print("### frmPersonManualPerson_showEditTime error: " + JSON.stringify(e));
    }
}

function frmPersonManualPerson_showEditTime_preAnim() {
    try {
        voltmx.print("### frmPersonManualPerson_showEditTime_preAnim");
        var trans1 = voltmx.ui.makeAffineTransform();
        trans1.scale(0.1, 0.1);
        var trans2 = voltmx.ui.makeAffineTransform();
        trans2.translate(0, 10);
        //frmPersonManualPerson.editdatetime.flcEditTime.transform = trans1;
        //frmPersonManualPerson.editdatetime.imgPopupLogo1.transform = trans1;
    } catch (e) {
        voltmx.print("### frmPersonManualPerson_showEditTime_preAnim error: " + JSON.stringify(e));
    }
}

function frmPersonManualPerson_showEditTime_arrangeWidgets() {
    try {
        voltmx.print("### frmPersonManualPerson_showEditTime_arrangeWidgets");
        //popup fields
        frmPersonManualPerson.editdatetime.imgPopupLogo1.isVisible = false;
        frmPersonManualPerson.editdatetime.forceLayout();
        frmPersonManualPerson.editdatetime.flcEditTime.isVisible = false;
    } catch (e) {
        voltmx.print("### frmPersonManualPerson_showEditTime_arrangeWidgets error: " + JSON.stringify(e));
    }
}

function frmPersonManualPerson_showEditTime_animationStart(eventobject) {
    try {
        voltmx.print("### frmPersonManualPerson_showEditTime_animationStart");
        frmPersonManualPerson_editdatetime_setVisibility(true);
        frmPersonManualPerson.editdatetime.flcEditTime.isVisible = true;
        var trans100 = voltmx.ui.makeAffineTransform();
        trans100.scale(1, 1);
        frmPersonManualPerson.editdatetime.flcEditTime.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans100,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.25
        }, {
            "animationEnd": voltmx.runOnMainThread(frmPersonManualPerson_showEditTime_animLogo)
        });
    } catch (e) {
        voltmx.print("### frmPersonManualPerson_showEditTime_animationStart error: " + JSON.stringify(e));
    }
}

function frmPersonManualPerson_showEditTime_animLogo() {
    try {
        voltmx.print("### frmPersonManualPerson_showEditTime_animLogo");
        frmPersonManualPerson_showEditTime_animOtherWidgets(frmPersonManualPerson.editdatetime.flcFooterMainHandleTime);
        frmPersonManualPerson.editdatetime.imgPopupLogo1.isVisible = true;
        frmPersonManualPerson.forceLayout();
    } catch (e) {
        voltmx.print("### frmPersonManualPerson_showEditTime_animLogo error: " + JSON.stringify(e));
    }
}

function frmPersonManualPerson_showEditTime_animOtherWidgets(widget) {
    try {
        voltmx.print("### frmPersonManualPerson_showEditTime_animOtherWidgets");
        var trans1 = voltmx.ui.makeAffineTransform();
        trans1.translate(1, 1);
        //trans1.translate(1, -10);
        widget.isVisible = true;
        widget.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans1,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.25
        }, {
            "animationEnd": function() {}
        });
        frmPersonManualPerson.editdatetime.flcFooterMainHandleTime.isVisible = true;
        frmPersonManualPerson.editdatetime.flcFooterMainHandleTime.setEnabled(true);
        frmPersonManualPerson.forceLayout();
    } catch (e) {
        voltmx.print("### frmPersonManualPerson_showEditPlate_animOtherWidgets error: " + JSON.stringify(e));
    }
}

function frmPersonManualPerson_showEditTime_animLogoBack() {
    try {
        voltmx.print("### frmPersonManualPerson_showEditTime_animLogoBack");
        var trans = voltmx.ui.makeAffineTransform();
        trans.scale(1, 1);
        frmPersonManualPerson.editdatetime.imgPopupLogo1.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.15
        }, {
            "animationEnd": function() {}
        });
        //frmPersonManualPerson.forceLayout();
    } catch (e) {
        voltmx.print("### frmPersonManualPerson_showEditTime_animLogoBack error: " + JSON.stringify(e));
    }
}

function frmPersonManualPerson_hideShowEditTime() {
    //activate footer and mainpage
    frmPersonManualPerson.flcMainPage.setEnabled(true);
    voltmx.print("### flcMainPage enabled");
    frmPersonManualPerson_editdatetime_setVisibility(false);
}

function frmPersonManualPerson_onDoneEditDateTime() {
    //check date time
    frmPersonManualPerson.editdatetime.txtYear.text = frmPersonManualPerson.editdatetime.txtYear.text.lpad("0", 4);
    var startdate = new Date();
    startdate.setDate(startdate.getDate() - 54750); // about 150 years ago
    voltmx.print("### frmPersonManualPerson_onDoneEditDateTime startdate: " + startdate);
    var curr_date = new Date();
    var selected_date = Number(frmPersonManualPerson.editdatetime.txtDay.text);
    var selected_month = Number(frmPersonManualPerson.editdatetime.txtMonth.text);
    var selected_year = Number(frmPersonManualPerson.editdatetime.txtYear.text);
    var selected_hours = 0;
    var selected_minutes = 0;
    var selected_seconds = 0;
    var selected_dateJavascript = new Date(selected_year, selected_month - 1, selected_date, selected_hours, selected_minutes, selected_seconds, 0);
    var checkValidDate = new Date(selected_year, selected_month - 1, selected_date, selected_hours, selected_minutes, selected_seconds, 0);
    voltmx.print("### frmPersonManualPerson_onDoneEditDateTime checkValidDate: " + checkValidDate);
    //date checks
    var daysInMonth = new Date(selected_year, selected_month, 0).getDate();
    voltmx.print("### frmPersonManualPerson_onDoneEditDateTime daysInMonth: " + daysInMonth);
    if (frmPersonManualPerson.editdatetime.txtYear.text.length < 4) {
        alert("Dit is geen geldig jaar"); //i18n
    } else if (frmPersonManualPerson.editdatetime.txtMonth.length == 0 || frmPersonManualPerson.editdatetime.txtMonth.text == "" || frmPersonManualPerson.editdatetime.txtMonth.text == "00") {
        alert("Dit is geen geldige datum"); //i18n
    } else if (frmPersonManualPerson.editdatetime.txtDay.length == 0 || frmPersonManualPerson.editdatetime.txtDay.text == "" || frmPersonManualPerson.editdatetime.txtDay.text == "00") {
        alert("Dit is geen geldige datum"); //i18n
    } else if (Number(frmPersonManualPerson.editdatetime.txtYear.text) < 1000) {
        alert("Datum ligt te ver in het verleden");
    } else if (selected_date > daysInMonth) {
        alert("Deze maand heeft geen " + selected_date + " dagen");
    } else if (isNaN(selected_dateJavascript) === true) {
        alert("Dit is geen geldige datum"); //i18n
    } else if (selected_dateJavascript < startdate) {
        alert("Datum ligt te ver in het verleden"); //i18n
    } else if (selected_dateJavascript > curr_date) {
        alert("Datum ligt te ver in de toekomst"); //i18n
    } else {
        frmPersonManualPerson.lblDateOfBirth.text = frmPersonManualPerson.editdatetime.txtDay.text.lpad("0", 2) + "-" + frmPersonManualPerson.editdatetime.txtMonth.text.lpad("0", 2) + "-" + frmPersonManualPerson.editdatetime.txtYear.text;
        frmPersonManualPerson.lblDateOfBirth.skin = lblFieldInfo;
        frmPersonManualPerson_getYearFromBirthDate();
        frmPersonManualPerson_hideShowEditTime();
        frmPersonManualPerson_checkFieldsFilled();
        if (frmPersonManualPerson.lblCountryOfOrigin.text !== "Kies") {
            voltmx.print("### frmPersonManualPerson_onDoneEditDateTime recheck country of origin");
            frmPersonManualPerson_recheckCountryOfOrigin();
        }
        frmPersonManualPerson_recheckbirthMunicipNL();
    }
}

function frmPersonManualPerson_clearBirthMunicipNL() {
    Global.vars.gCasePersons.birthMunicipNL = "";
    Global.vars.gCasePersons.birthMunicipNLDesc = "";
    frmPersonManualPerson.lblMunicipalityOfBirth.text = frmPersonManualPerson.lblMunicipalityOfBirthHeader.text;
    frmPersonManualPerson.lblMunicipalityOfBirth.skin = lblFieldNotFilled;
    frmPersonManualPerson_checkFieldsFilled();
}

function frmPersonManualPerson_recheckCountryOfOrigin() {
    voltmx.print("### frmPersonManualPerson_recheckCountryOfOrigin");
    voltmx.print("### frmPersonManualPerson_recheckCountryOfOrigin Current Country of birth: " + Global.vars.gCasePersons.countryOfBirth);
    voltmx.print("### frmPersonManualPerson_recheckCountryOfOrigin Current CountryOfBirthDesc: " + Global.vars.gCasePersons.countryOfBirthDesc);
    voltmx.print("### frmPersonManualPerson_recheckCountryOfOrigin Current birthdatecomponents: " + Global.vars.gCasePersons.birthdateComponents);

    function getCountryOfOriginSuccessCallback(resultrecheck) {
        voltmx.print("### frmPersonManualPerson_getCountryOfOrigin resultrecheck: " + JSON.stringify(resultrecheck));
        if ((resultrecheck.length !== 0)) {
            voltmx.print("### frmPersonManualPerson_recheckCountryOfOrigin country succes and valid");
        } else {
            voltmx.print("### frmPersonManualPerson_recheckCountryOfOrigin no results found for Country");
            frmPersonManualPerson.lblCountryOfOrigin.text = "Kies";
            frmPersonManualPerson.lblCountryOfOrigin.skin = lblFieldNotFilled;
            frmPersonManualPerson.flcCountryOfOrigin.skin = flcFieldEdgeRed;
            Global.vars.gCasePersons.countryOfBirth = null;
            Global.vars.gCasePersons.countryOfBirthDesc = null;
        }
        voltmx.application.dismissLoadingScreen();
    }

    function getCountryOfOriginErrorCallback(error) {
        voltmx.print("### frmPersonManualPerson_recheckCountryOfOrigin Country Info error: " + error);
        frmPersonManualPerson.lblCountryOfOrigin.text = "Kies";
        frmPersonManualPerson.lblCountryOfOrigin.skin = lblFieldNotFilled;
        frmPersonManualPerson.flcCountryOfOrigin.skin = flcFieldEdgeRed;
        Global.vars.gCasePersons.countryOfBirth = null;
        Global.vars.gCasePersons.countryOfBirthDesc = null;
        voltmx.application.dismissLoadingScreen();
    }
    var lCountrywhereClause = "select * from mle_v_country_m where code = '" + Global.vars.gCasePersons.countryOfBirth + "'";
    lCountrywhereClause = Utility_addLanguageToWhereClauseObjectSync(lCountrywhereClause);
    lCountrywhereClause = Utility_addTimelineToWhereClauseObjectSync(lCountrywhereClause, Global.vars.gCasePersons.birthdateComponents);
    voltmx.print("### frmPersonManualPerson_recheckCountryOfOrigin Country clause: " + lCountrywhereClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lCountrywhereClause, getCountryOfOriginSuccessCallback, getCountryOfOriginErrorCallback);
}

function frmPersonManualPerson_recheckbirthMunicipNL() {
    voltmx.print("### frmPersonManualPerson_recheckbirthMunicipNL");
    voltmx.print("### frmPersonManualPerson_recheckbirthMunicipNL Current Municipality of birth: " + Global.vars.gCasePersons.birthMunicipNL);
    voltmx.print("### frmPersonManualPerson_recheckbirthMunicipNL Current birthMunicipNLDesc: " + Global.vars.gCasePersons.birthMunicipNLDesc);
    voltmx.print("### frmPersonManualPerson_recheckbirthMunicipNL Current birthdatecomponents: " + Global.vars.gCasePersons.birthdateComponents);

    function getMunicipalitySuccessCallback(resultrecheck) {
        voltmx.print("### frmPersonManualPerson_recheckbirthMunicipNL resultrecheck: " + JSON.stringify(resultrecheck));
        if ((resultrecheck.length !== 0)) {
            voltmx.print("### frmPersonManualPerson_recheckbirthMunicipNL country succes and valid");
        } else {
            voltmx.print("### frmPersonManualPerson_recheckbirthMunicipNL no results found for Country");
            frmPersonManualPerson.lblMunicipalityOfBirth.text = frmPersonManualPerson.lblMunicipalityOfBirthHeader.text;
            frmPersonManualPerson.lblMunicipalityOfBirth.skin = lblFieldInfo;
            frmPersonManualPerson.flcMunicipalityOfBirth.skin = flcFieldEdgeRed;
            Global.vars.gCasePersons.birthMunicipNL = null;
            Global.vars.gCasePersons.birthMunicipNLDesc = null;
        }
        voltmx.application.dismissLoadingScreen();
    }

    function getMunicipalityErrorCallback(error) {
        voltmx.print("### frmPersonManualPerson_recheckbirthMunicipNL Info error: " + error);
        frmPersonManualPerson.lblMunicipalityOfBirth.text = frmPersonManualPerson.lblMunicipalityOfBirthHeader.text;
        frmPersonManualPerson.lblMunicipalityOfBirth.skin = lblFieldNotFilled;
        frmPersonManualPerson.flcMunicipalityOfBirth.skin = flcFieldEdgeRed;
        Global.vars.gCasePersons.birthMunicipNL = null;
        Global.vars.gCasePersons.birthMunicipNLDesc = null;
        voltmx.application.dismissLoadingScreen();
    }
    if (frmPersonManualPerson.lblMunicipalityOfBirth.text != frmPersonManualPerson.lblMunicipalityOfBirthHeader.text) {
        var lMunicipalitywhereClause = "select * from mle_v_municipality_m where code = '" + Global.vars.gCasePersons.birthMunicipNL + "'";
        lMunicipalitywhereClause = Utility_addTimelineToWhereClauseObjectSync(lMunicipalitywhereClause, Global.vars.gCasePersons.birthdateComponents, false);
        voltmx.print("### frmPersonManualPerson_recheckbirthMunicipNL Country clause: " + lMunicipalitywhereClause);
        KNYMobileFabric.OfflineObjects.executeSelectQuery(lMunicipalitywhereClause, getMunicipalitySuccessCallback, getMunicipalityErrorCallback);
    }
}