function frmPersonSSN_init() {
    voltmx.print("#### frmPersonSSN_init");
    //Utility_registerForIdleTimeout();
    frmPersonSSN.onDeviceBack = Global_onDeviceBack;
}

function frmPersonSSN_preshow() {
    Analytics_logScreenView("person-ssn");
    voltmx.print("#### frmPersonSSN_preshow");
    frmPersonSSN.lblPersonNationality.text = Global.vars.gCasePersons.nationalityDesc;
    if (Global.vars.gCasePersons.ssn != null && Global.vars.gCasePersons.ssn !== "") {
        frmPersonSSN.txtSSN.text = Global.vars.gCasePersons.ssn;
    } else {
        frmPersonSSN.txtSSN.text = "";
    }
    if (Global.vars.gCasePersons.nationalityDesc === null || Global.vars.gCasePersons.nationalityDesc === "") {
        frmPersonSSN_getNationality();
    }
}

function frmPersonSSN_postshow() {
    voltmx.print("#### frmPersonSSN_postShow");
    if (Global.vars.gCasePersons.ssn != null && Global.vars.gCasePersons.ssn !== "") {
        voltmx.print("#### frmPersonSSN_postShow ssn already filled");
    } else {
        frmPersonSSN.txtSSN.setFocus(true);
    }
}

function frmPersonSSN_clearSSN() {
    frmPersonSSN.txtSSN.text = "";
    Global.vars.gCasePersons.ssn = null;
    frmPersonSSN.txtSSN.setFocus(true);
}

function frmPersonSSN_getNationality() {
    //prefill nationality
    voltmx.print("#### frmPersonSSN_getNationality ");
    voltmx.print("#### frmPersonSSN_getNationality getspecificNationality");
    voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loading") + "...", "center", true, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    var wcs = "select * from mle_v_nationality_m WHERE code = '" + Global.vars.nationalitypreset + "'";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonSSN_getNationalitySuccessCallbackfrm, frmPersonSSN_getNationalityErrorCallbackfrm);
    voltmx.application.dismissLoadingScreen();
    voltmx.print("##### frmPerson_getNationality ");
}

function frmPersonSSN_getNationalitySuccessCallbackfrm(resultnationality) {
    voltmx.print("#### frmPersonSSN_getNationalitySuccessCallbackfrm");
    voltmx.print("#### frmPersonSSN_getNationalitySuccessCallbackfrm result lblPersonNationality: " + frmPersonSSN.lblPersonNationality.text);
    voltmx.print("#### frmPersonSSN_getNationalitySuccessCallbackfrm result nationality: " + JSON.stringify(resultnationality));
    if (resultnationality != null) {
        frmPersonSSN.lblPersonNationality.text = resultnationality[0].description;
        //fill globals
        voltmx.print("NationalityCode resultnationality[0].Code" + resultnationality[0].code + " = " + Global.vars.nationalitypreset);
        Global.vars.gCasePersons.nationality = resultnationality[0].code;
        if (resultnationality[0].code == Global.vars.nationalitypreset) {
            voltmx.print("NationalityCode resultnationality[0].Code" + resultnationality[0].code + " = " + Global.vars.nationalitypreset);
            Global.vars.gCasePersons.indicationDutch = true;
        }
        Global.vars.gCasePersons.nationalityDesc = resultnationality[0].description;
        //end fill globals
    }
    voltmx.print("#### frmPersonSSN_getNationalitySuccessCallbackfrm result Global.vars.gCasePersons.Nationality: " + Global.vars.gCasePersons.nationality);
    voltmx.application.dismissLoadingScreen();
    voltmx.print("##### frmPersonSSN_getNationalitySuccessCallbackfrm end");
}

function frmPersonSSN_getNationalityErrorCallbackfrm(error) {
    voltmx.print("##### getNationalityErrorCallbackfrm ");
    voltmx.print("#### Nationality error: " + error);
    voltmx.application.dismissLoadingScreen();
}
// end nationality prefill
function frmPersonSSN_onclickbtnNationality() {
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    frmPersonNationalities.show();
}

function frmPersonSSN_onDoneSSN() {
    //Do eleven check
    var ssn = "0";
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iPa") === true) {
        frmPersonSSN.txtSSN.text = frmPersonSSN.txtSSN.text.replace(/[-,.]/g, "");
        voltmx.print("### frmPersonSSN_onDoneSSN iPad ssn number: " + frmPersonManualPerson.txtSSN.text);
    }
    if (frmPersonSSN.txtSSN.text.length > 0) {
        ssn = frmPersonSSN.txtSSN.text;
    }
    if ((ssn.length > 7 && ssn.length < 10) && (ssn != "00000000" && ssn != "000000000" && ssn != "99999999" && ssn != "999999999")) {
        if (ssn.length < 9) {
            ssn = "0" + ssn;
            voltmx.print("#### concatenated number: " + ssn);
        }
        var sseleven = Utility_ElevenCheck(ssn);
        voltmx.print("#### sseleven: " + sseleven);
        if (sseleven) {
            Global.vars.previousForm = "frmPersonSSN";
            service_GetPersonInfoSSN(ssn, frmPersonResult_callbackfunctionBSN);
        } else {
            alert(voltmx.i18n.getLocalizedString("l_notAnSSN"));
        }
    } else {
        alert(voltmx.i18n.getLocalizedString("l_notAnSSN"));
    }
}

function frmPersonSSN_btnBack() {
    frmPerson.show();
}