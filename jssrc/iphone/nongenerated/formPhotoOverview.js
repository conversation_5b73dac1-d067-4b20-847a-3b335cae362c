function frmPhotoOverview_contentOffset() {
    frmPhotoOverview.contentSize = {
        height: "100%",
        width: "100%"
    };
    frmPhotoOverview.contentOffset = {
        "x": "0px",
        "y": "0px"
    };
}

function frmPhotoOverview_flcSetReceipt_setVisibility(boolean) {
    voltmx.print("### frmPhotoOverview_flcSetReceipt_setVisibility");

    function flcSetReceipt_setVisibility() {
        voltmx.print("### frmPhotoOverview_flcSetReceipt_setVisibility flcSetReceipt_setVisibility: " + boolean);
        frmPhotoOverview.flcSetReceipt.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcSetReceipt_setVisibility, []);
}

function frmPhotoOverview_btnNext_setVisibility(boolean) {
    voltmx.print("### frmPhotoOverview_btnNext_setVisibility");

    function btnNext_setVisibility() {
        voltmx.print("### frmPhotoOverview_btnNext_setVisibility btnNext_setVisibility: " + boolean);
        frmPhotoOverview.btnNext.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnNext_setVisibility, []);
}

function frmPhotoOverview_flcRemovePhotos_setVisibility(boolean) {
    voltmx.print("### frmPhotoOverview_flcRemovePhotos_setVisibility");

    function flcRemovePhotos_setVisibility() {
        voltmx.print("### frmPhotoOverview_flcRemovePhotos_setVisibility flcRemovePhotos_setVisibility: " + boolean);
        frmPhotoOverview.flcRemovePhotos.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcRemovePhotos_setVisibility, []);
}

function frmPhotoOverview_settext_setVisibility(boolean) {
    voltmx.print("### frmPhotoOverview_settext_setVisibility");

    function settext_setVisibility() {
        voltmx.print("### frmPhotoOverview_settext_setVisibility settext_setVisibility: " + boolean);
        frmPhotoOverview.settext.setVisibility(boolean);
    }
    voltmx.runOnMainThread(settext_setVisibility, []);
}

function frmPhotoOverview_frmPhotoOverview_setVisibility(boolean) {
    voltmx.print("### frmPhotoOverview_frmPhotoOverview_setVisibility");

    function frmPhotoOverview_setVisibility() {
        voltmx.print("### frmPhotoOverview_frmPhotoOverview_setVisibility frmPhotoOverview_setVisibility: " + boolean);
        frmPhotoOverview.frmPhotoOverview.setVisibility(boolean);
    }
    voltmx.runOnMainThread(frmPhotoOverview_setVisibility, []);
}

function frmPhotoOverview_init() {
    // Disable Back Button
    frmPhotoOverview.onDeviceBack = Global_onDeviceBack;
    //
    voltmx.print("### frmPhotoOverview init");
    frmPhotoOverview.contentSize = {
        height: "100%",
        width: "100%"
    };
    voltmx.print("### end of init");
    var swipeSettings = {
        fingers: 1,
        swipedistance: 75,
        swipevelocity: 75
    };
    var swipeGesture = frmPhotoOverview.setGestureRecognizer(2, swipeSettings, frmPhotoOverview_handleGesture);
}

function frmPhotoOverview_preShow() {
    Analytics_logScreenView("photo-overview");
    voltmx.print("### frmPhotoOverview_preShow Global.vars.selectedPhoto: " + JSON.stringify(Global.vars.selectedPhoto.id));
    if ((CaseData.caseinfo.indPayed === true && (CaseData.caseinfo.onStreetPaymentMethodKey == "2" || CaseData.caseinfo.onStreetPaymentMethodKey == "3")) || CaseData.processinfo.lastTaskProcessed.taskOutcome == "betaling_nhas" || CaseData.processinfo.lastTaskProcessed.taskOutcome == "ontklemd") {
        frmPhotoOverview.flcRemovePhotos.centerX = 75 + '%';
        frmPhotoOverview_flcSetReceipt_setVisibility(true);
    } else {
        frmPhotoOverview.flcRemovePhotos.centerX = 50 + '%';
        frmPhotoOverview_flcSetReceipt_setVisibility(false);
    }
    frmPhotoOverview_btnNext_setVisibility(false);
    frmPhotoOverview.imgPhoto.base64 = Global.vars.selectedPhoto.imgPhoto.base64;
    Global.vars.setPhotoHeightCount = 0;
    frmPhotoOverview.imgPhoto.width = '100%';
    frmPhotoOverview.imgPhoto.height = '100%';
    //frmPhotoOverview.imgPhoto.doLayout = frmPhotoOverview_getHeight;
    frmPhotoOverview_flcRemovePhotos_setVisibility(true);
    for (var i in CaseData.multimedia) {
        var v = CaseData.multimedia[i];
        voltmx.print("### frmPhotoOverview_preShow v: " + JSON.stringify(v));
        voltmx.print("### frmPhotoOverview_preShow filename: " + v.fileName);
        if (Global.vars.selectedPhoto != null && Global.vars.selectedPhoto.id != null && v.fileName != null) {
            if (Global.vars.selectedPhoto.id.endsWith(v.fileName) === true) {
                voltmx.print("#### frmPhotoOverview_preShow description: " + v.description);
                frmPhotoOverview.lblDescription.text = v.description;
            }
        }
        if (v.base64 === undefined || v.base64 === null) {
            voltmx.print("#### frmPhotoOverview_preShow base64 is null so photo is already uploaded");
            //label
            if (Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_checklabel")) {
                var nonRemovalTask = false;
                for (var j in Global.vars.nonPhotoRemovalTasks) {
                    var w = Global.vars.nonPhotoRemovalTasks[j];
                    if (CaseData.processinfo.activeTaskType == w.task && Global.vars.selectedPhoto != null && Global.vars.selectedPhoto.id != null && v.fileName != null) {
                        if (Global.vars.selectedPhoto.id.endsWith(v.fileName) === true) {
                            nonRemovalTask = true;
                            break;
                        }
                    }
                }
                if (nonRemovalTask === true) {
                    voltmx.print("#### frmPhotoOverview_preShow photo is nonremoval photo");
                    frmPhotoOverview_flcRemovePhotos_setVisibility(false);
                }
            }
        }
    }
}

function frmPhotoOverview_handleGesture(myWidget, gestureInfo) {
    voltmx.print("#### frmPhotoOverview_handleGesture: " + gestureInfo.swipeDirection);
    if (gestureInfo.swipeDirection == 2) {
        voltmx.print("### swipe direction 2");
        //frmPhotoOverview_onclick_btnBack();
    }
}

function frmPhotoOverview_getHeight() {
    voltmx.print("### frmPhotoOverview_getHeight: " + frmPhotoOverview.imgPhoto.frame.height);
    voltmx.print("### frmPhotoOverview_getHeight width: " + frmPhotoOverview.imgPhoto.frame.width);
    if (Global.vars.setPhotoHeightCount === 0) {
        voltmx.print("### frmPhotoOverview_getHeight set height");
        var height = frmPhotoOverview.imgPhoto.frame.height;
        Global.vars.setPhotoHeight = height;
        var width = frmPhotoOverview.imgPhoto.frame.width;
        frmPhotoOverview.flcDescription.top = height + 20;
        frmPhotoOverview.imgPhoto.height = height + 'dp';
        frmPhotoOverview.imgPhoto.width = width;
        Global.vars.setPhotoHeightCount = 1;
    }
}

function frmPhotoOverview_onclick_btnBack() {
    frmPhotos.show();
}

function frmPhotoOverview_onclick_btnRemove() {
    voltmx.print("#### frmPhotoOverview_remove: " + JSON.stringify(Global.vars.selectedPhoto.id));
    var selectedItems = Global.vars.selectedPhoto;
    voltmx.print("#### frmPhotoOverview_remove selectedItems: " + JSON.stringify(selectedItems.id));
    Global.vars.deletePhotos.push(selectedItems.id);
    try {
        voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_remove") + "...", "center", false, true, {
            enablemenukey: true,
            enablebackkey: true
        });
        //frmPhotoOverview_deleteFiles();
        frmPhotoOverview_deleteFilesInline();
    } catch (err) {}
}

function frmPhotoOverview_deleteFilesInline() {
    voltmx.print("### frmPhotoOverview_deleteFilesInline");
    var deletePhoto = null;
    var docId = "";
    var docRev = "";
    var claimedDocID = Global.vars.claimedDocID;
    if (Global.vars.deletePhotos.length > 0) {
        deletePhoto = Global.vars.deletePhotos.pop();
        voltmx.print("### frmPhotoOverview_deleteFilesInline deletePhoto: " + deletePhoto);
        //delete foto from foto array
        for (var i in Global.vars.newPhotos) {
            var v = Global.vars.newPhotos[i];
            if (v.id == deletePhoto) {
                Global.vars.newPhotos.splice(i, 1);
            }
        }
        //delete foto from multimedia
        var locphotoindex = null;
        voltmx.print("### frmPhotoOverview_deleteFilesInline CaseData.multimedia 1: " + JSON.stringify(CaseData.multimedia));
        for (var p in CaseData.multimedia) {
            var w = CaseData.multimedia[p];
            if ((w.fileName == deletePhoto)) {
                voltmx.print("#### frmPhotoOverview_deleteFilesInline photo exists: " + w.fileName + " index: " + p);
                locphotoindex = p;
                break;
            } else if ((w.attachmentId == deletePhoto)) {
                voltmx.print("#### frmPhotoOverview_deleteFilesInline photo exists: " + w.fileName + " index: " + p);
                locphotoindex = p;
                break;
            }
        }
        if (locphotoindex != null) {
            voltmx.print("### frmPhotoOverview_deleteFilesInline delete record from multimedia");
            CaseData.multimedia.splice(locphotoindex, 1);
            Utility_deletePhotoFromFileSystem(claimedDocID, deletePhoto);
        }
        var photoData = [];
        for (var j = 0; j < Global.vars.newPhotos.length; j++) {
            voltmx.print("### frmPhotoOverview_deleteFilesInline Global.vars.newPhotos signature check length: " + Global.vars.newPhotos.length);
            var x = Global.vars.newPhotos[j];
            if (x.id.startsWith("signature") === false) {
                photoData.push(x);
            }
        }
        frmPhotos.segPhotos.setData(photoData);
        frmPhotoOverview_deleteFilesInline();
    } else {
        frmPhotos_reset();
        voltmx.print("### frmPhotoOverview_deleteFiles CaseData.multimedia 2: " + JSON.stringify(CaseData.multimedia));
        frmPhotoOverview_finishedUpdateMultimedia();
        voltmx.runOnMainThread(frmPhotos_setPhotosOnForm, []);
        try {
            voltmx.timer.cancel("deletefile");
        } catch (err) {}
    }
}

function frmPhotoOverview_finishedUpdateMultimedia() {
    voltmx.application.dismissLoadingScreen();
    frmPhotos.show();
}

function frmPhotoOverview_onclickReceipt() {
    voltmx.print("#### frmPhotoOverview_onclickReceipt");
    var selectedItems = Global.vars.selectedPhoto;
    voltmx.print("#### frmPhotoOverview_onclickReceipt selectedItems: " + JSON.stringify(selectedItems.id));
    frmPhotos.show();
    frmPhotos_setReceipt(selectedItems);
}

function frmPhotoOverview_showSetText() {
    voltmx.print("### frmPhotoOverview_showSetText");
    try {
        frmPhotoOverview.settext.textarea.lblTextHeader.text = frmPhotoOverview.lblDescriptionHeader.text;
        frmPhotoOverview.settext.textarea.TextAreaText.text = frmPhotoOverview.lblDescription.text;
        //deactivate footer and mainpage
        frmPhotoOverview.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmPhotoOverview_settext_setVisibility(true);
        frmPhotoOverview_showSetText_preAnim();
        frmPhotoOverview_showSetText_animationStart();
    } catch (e) {
        voltmx.print("### frmPhotoOverview_showSetText error: " + JSON.stringify(e));
    }
}

function frmPhotoOverview_showSetText_preAnim() {
    try {
        voltmx.print("### frmPhotoOverview_showSetText_preAnim");
        var trans1 = voltmx.ui.makeAffineTransform();
        trans1.scale(0.1, 0.1);
        var trans2 = voltmx.ui.makeAffineTransform();
        trans2.translate(0, 10);
        //frmPhotoOverview.settext.flcDetail.transform = trans1;
        //frmPhotoOverview.settext.imgPopupLogo1.transform = trans1;
        //frmPhotoOverview.settext.flcTextDetails.transform = trans1;
    } catch (e) {
        voltmx.print("### frmPhotoOverview_showSetText_preAnim error: " + JSON.stringify(e));
    }
}

function frmPhotoOverview_showSetText_arrangeWidgets() {
    try {
        voltmx.print("### frmPhotoOverview_showSetText_arrangeWidgets");
        //popup fields
        frmPhotoOverview.settext.imgPopupLogo1.isVisible = false;
        frmPhotoOverview.settext.flcDetail.isVisible = false;
        frmPhotoOverview.settext.flcTextDetails.isVisible = false;
        frmPhotoOverview.settext.lbl1.isVisible = false;
        frmPhotoOverview.settext.flcFooterSetText.isVisible = false;
        frmPhotoOverview_settext_setVisibility(false);
        frmPhotoOverview.settext.flcFooterSetText.setEnabled(false);
        frmPhotoOverview.settext.forceLayout();
    } catch (e) {
        voltmx.print("### frmPhotoOverview_showSetText_preAnim error: " + JSON.stringify(e));
    }
}

function frmPhotoOverview_showSetText_animationStart(eventobject) {
    try {
        voltmx.print("### frmPhotoOverview_showSetText_animationStart");
        frmPhotoOverview_settext_setVisibility(true);
        frmPhotoOverview.settext.flcDetail.isVisible = true;
        var trans100 = voltmx.ui.makeAffineTransform();
        trans100.scale(1, 1);
        frmPhotoOverview.settext.flcDetail.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans100,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.25
        }, {
            "animationEnd": voltmx.runOnMainThread(frmPhotoOverview_showSetText_animLogo)
        });
    } catch (e) {
        voltmx.print("### frmPhotoOverview_showSetText_animationStart error: " + JSON.stringify(e));
    }
}

function frmPhotoOverview_showSetText_animLogo() {
    try {
        voltmx.print("### frmPhotoOverview_showSetText_animLogo");
        //     var trans = voltmx.ui.makeAffineTransform();
        //     trans.scale(1.2, 1.2);
        //     frmPhotoOverview.settext.imgPopupLogo1.animate(
        //       voltmx.ui.createAnimation({
        //         "100": {
        //           "anchorPoint": {
        //             "x": 0.5,
        //             "y": 0.5
        //           },
        //           "stepConfig": {
        //             "timingFunction": voltmx.anim.EASE
        //           },
        //           "transform": trans,
        //         }
        //       }), {
        //         "delay": 0,
        //         "iterationCount": 1,
        //         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        //         "duration": 0.25
        //       }, {
        //         "animationEnd": function (){
        //           frmPhotoOverview_showSetText_animOtherWidgets(frmPhotoOverview.settext.flcTextDetails);
        //           frmPhotoOverview_showSetText_animOtherWidgets(frmPhotoOverview.settext.lbl1);
        //           frmPhotoOverview_showSetText_animLogoBack();
        //         }
        //       });
        frmPhotoOverview_showSetText_animOtherWidgets(frmPhotoOverview.settext.flcTextDetails);
        frmPhotoOverview_showSetText_animOtherWidgets(frmPhotoOverview.settext.lbl1);
        frmPhotoOverview.settext.imgPopupLogo1.isVisible = true;
        frmPhotoOverview.forceLayout();
    } catch (e) {
        voltmx.print("### frmPhotoOverview_showSetText_animLogo error: " + JSON.stringify(e));
    }
}

function frmPhotoOverview_showSetText_animOtherWidgets(widget) {
    try {
        voltmx.print("### frmPhotoOverview_showSetText_animOtherWidgets");
        var trans1 = voltmx.ui.makeAffineTransform();
        trans1.translate(1, 1);
        //trans1.translate(1, -10);
        widget.isVisible = true;
        widget.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans1,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.25
        }, {
            "animationEnd": function() {}
        });
        frmPhotoOverview.settext.flcTextDetails.isVisible = true;
        frmPhotoOverview.settext.lbl1.isVisible = true;
        frmPhotoOverview.settext.flcFooterSetText.isVisible = true;
        frmPhotoOverview.settext.flcFooterSetText.setEnabled(true);
        frmPhotoOverview.forceLayout();
    } catch (e) {
        voltmx.print("### frmPhotoOverview_showSetText_animOtherWidgets error: " + JSON.stringify(e));
    }
}

function frmPhotoOverview_showSetText_animLogoBack() {
    try {
        voltmx.print("### frmPhotoOverview_showSetText_animLogoBack");
        var trans = voltmx.ui.makeAffineTransform();
        trans.scale(1, 1);
        frmPhotoOverview.settext.imgPopupLogo1.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.15
        }, {
            "animationEnd": function() {}
        });
        frmPhotoOverview.forceLayout();
    } catch (e) {
        voltmx.print("### frmPhotoOverview_showSetText_animLogoBack error: " + JSON.stringify(e));
    }
}

function frmPhotoOverview_hideSetText() {
    //activate footer and mainpage
    frmPhotoOverview.flcMainPage.setEnabled(true);
    voltmx.print("### flcMainPage enabled");
    frmPhotoOverview_settext_setVisibility(false);
    frmPhotoOverview_contentOffset();
}

function frmPhotoOverview_clearTextAreaText() {
    frmPhotoOverview.settext.textarea.TextAreaText.text = "";
    frmPhotoOverview.settext.textarea.TextAreaText.setFocus(true);
}

function frmPhotoOverview_onEndEditPhotoText() {
    voltmx.print("#### frmPhotoOverview_onEndEditPhotoText");
    frmPhotoOverview.lblDescription.text = frmPhotoOverview.settext.textarea.TextAreaText.text;
    if (frmPhotoOverview.settext.textarea.TextAreaText.text.length > 30) {
        frmPhotoOverview.lblDescription.text = frmPhotoOverview.settext.textarea.TextAreaText.text.substring(0, 30) + "...";
    }
    for (var i in CaseData.multimedia) {
        var v = CaseData.multimedia[i];
        if (v.fileName == Global.vars.selectedPhoto.id) {
            voltmx.print("#### frmPhotoOverview_onEndEditPhotoText: " + v.description);
            if (v.description !== frmPhotoOverview.lblDescription.text) {
                frmPhotoOverview_btnNext_setVisibility(true);
            } else {
                frmPhotoOverview_btnNext_setVisibility(false);
            }
        }
    }
    frmPhotoOverview_hideSetText();
}

function frmPhotoOverview_onDone() {
    for (var i in CaseData.multimedia) {
        var v = CaseData.multimedia[i];
        if (v.fileName == Global.vars.selectedPhoto.id) {
            voltmx.print("#### frmPhotoOverview_onEndEditPhotoText: " + v.description);
            if (v.description !== frmPhotoOverview.lblDescription.text) {
                if (v.description !== frmPhotoOverview.settext.textarea.TextAreaText.text) {
                    v.description = frmPhotoOverview.settext.textarea.TextAreaText.text;
                }
            }
        }
    }
    frmPhotos.show();
}

function frmPhotoOverview_postshow() {
    var x = {
        continuousEvents: true
    };
    try {
        // frmPhotoOverview.flcPhoto.addGestureRecognizer(6, x,frmPhotoOverview_formGesture);
        //drag
        //       	frmPhotoOverview.flcPhoto.onTouchStart = null;
        //         frmPhotoOverview.flcPhoto.onTouchMove = null;
        //         frmPhotoOverview.flcPhoto.onTouchEnd = null;   
        //         try{ 
        //             voltmx.timer.schedule("setMainTouch",frmPhotoOverview_setMainTouch,0.3,false);
        //         }catch(err){}
        //             //voltmx.application.setGestureRecognizerForAllForms(6, x,formGesture);
    } catch (err) {
        //alert(typeof err);
        voltmx.print("error in function callbackSingleTapGesture: " + err.message);
    }
}
//Defining the function
g_zoomIn = 100;
g_zoomOut = 100;
g_scaleStart = 0;
g_scaleEnd = 0;

function frmPhotoOverview_formGesture(widgetID, gestureInfo) {
    var y = voltmx.type(gestureInfo); //expected value of y = table
    var z = voltmx.type(gestureInfo.gesturesetUpParams); //expected values of z = table
    var a = gestureInfo.gestureType;
    var b = gestureInfo.gesturesetUpParams;
    var c = gestureInfo.gestureState;
    var d = gestureInfo.scale;
    // var e = gestureInfo.swipeDirection ;
    var f = gestureInfo.velocity;
    //voltmx.print("*******************************************");
    voltmx.print("### gestureInfo " + JSON.stringify(gestureInfo));
    voltmx.print("### gestureType " + a);
    voltmx.print("### gesturesetUpParams " + b);
    voltmx.print("### gestureState " + c);
    voltmx.print("### scale " + d);
    voltmx.print("### velocity " + f);
    if (voltmx.os.toNumber(c) == 1) {
        g_scaleStart = voltmx.os.toNumber(d);
    }
    if (voltmx.os.toNumber(a) == 6 && voltmx.os.toNumber(c) == 2) {
        g_scaleEnd = voltmx.os.toNumber(d);
        voltmx.print(g_scaleEnd + "====" + g_scaleStart + (g_scaleEnd > g_scaleStart));
        if (g_scaleEnd > g_scaleStart) {
            g_zoomIn = g_zoomIn + 2;
            frmPhotoOverview.imgPhoto.animate(voltmx.ui.createAnimation({
                "100": {
                    "width": g_zoomIn + "%",
                    "height": g_zoomIn + "%",
                    "left": "0dp",
                    "top": "0dp",
                    "rectified": true
                }
            }), {
                "delay": 0,
                "iterationCount": 1,
                "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
                "duration": 0
            }, {
                "animationEnd": function() {}
            });
        } else {
            g_zoomOut = g_zoomIn;
            if (g_zoomOut > 100) {
                g_zoomOut = g_zoomOut - 3; //was -1
                g_zoomIn = g_zoomOut;
                frmPhotoOverview.imgPhoto.animate(voltmx.ui.createAnimation({
                    "100": {
                        "width": g_zoomOut + "%",
                        "height": g_zoomOut + "%",
                        "left": "0dp",
                        "top": "0dp",
                        "rectified": true
                    }
                }), {
                    "delay": 0,
                    "iterationCount": 1,
                    "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
                    "duration": 0
                }, {
                    "animationEnd": function() {}
                });
            }
        }
    }
}
//Drag page
function frmPhotoOverview_dragMainOnStart(source, x, y) {
    voltmx.print("### dragMainOnStart");
    Global.vars.init_x = frmPhotoOverview.imgPhoto.left;
}
maxTop = 0;

function frmPhotoOverview_dragMainOnMove(source, x, y) {
    voltmx.print("### frmPhotoOverview_dragMainOnMove");
    Global.vars.headerTouchCounter = Global.vars.headerTouchCounter + 1;
    var nowLeft = frmPhotoOverview.imgPhoto.left;
    var nowTop = frmPhotoOverview.imgPhoto.top;
    Global.vars.newLeft = (parseInt(nowLeft.replace("px", "")) + (x * Global.vars.P_P_DP));
    Global.vars.newTop = (parseInt(nowTop.replace("px", "")) + (y * Global.vars.P_H_DP));
    var photoHeigth = frmPhotoOverview.imgPhoto.height;
    if (Global.vars.headerTouchCounter == 1) {
        Global.vars.firstLeft = Global.vars.newLeft;
        Global.vars.firstTop = Global.vars.newTop;
        Global.vars.startx = (x * Global.vars.P_P_DP);
        Global.vars.starty = (y * Global.vars.P_H_DP);
        voltmx.print("### frmPhotoOverview_dragMainOnMove imgPhoto.height: " + frmPhotoOverview.imgPhoto.height);
        maxTop = (0 - (parseInt(photoHeigth) * (Global.vars.deviceInfo.deviceHeight / 100))) + Global.vars.deviceInfo.deviceHeight;
    }
    var Xdifference = ((x * Global.vars.P_P_DP) - Global.vars.startx);
    var Ydifference = ((y * Global.vars.P_H_DP) - Global.vars.starty);
    voltmx.print("### dragMainOnMove Xdifference: " + Xdifference + ", Ydifference: " + Ydifference);
    if (Global.vars.firstLeft != null) {
        voltmx.print("### dragMainOnMove Left");
        var newLeft = parseInt(nowLeft.replace("px", "")) + Xdifference;
        voltmx.print("### dragMainOnMove newLeft: " + newLeft);
        if (Xdifference > 0) {
            voltmx.print("### dragMainOnMove going right");
            if (newLeft > 0) {
                frmPhotoOverview.imgPhoto.left = 0 + "px";
            } else {
                frmPhotoOverview.imgPhoto.left = newLeft + "px";
            }
        } else {
            voltmx.print("### dragMainOnMove going left");
            voltmx.print("### dragMainOnMove image width: " + frmPhotoOverview.imgPhoto.width);
            var photoWidth = frmPhotoOverview.imgPhoto.width;
            var maxLeft = (0 - (parseInt(photoWidth) * (Global.vars.deviceInfo.deviceWidth / 100))) + Global.vars.deviceInfo.deviceWidth;
            voltmx.print("### dragMainOnMove maxLeft: " + maxLeft);
            if (newLeft < maxLeft) {
                frmPhotoOverview.imgPhoto.left = maxLeft + "px";
            } else {
                frmPhotoOverview.imgPhoto.left = newLeft + "px";
            }
        }
        //       	voltmx.print("### dragMainOnMove image height: " + frmPhotoOverview.imgPhoto.referenceHeight);
        //       	var imageRawBytes = frmPhotoOverview.imgPhoto.rawBytes;
        //       	voltmx.print("### dragMainOnMove image rawBytes: " + imageRawBytes);
        //       	var imageforsize = voltmx.image.createImage(imageRawBytes);
        //       	voltmx.print("### dragMainOnMove imageforsize: " + imageforsize);
        //       	var imageheight = (parseInt(photoHeigth)*(Global.vars.deviceInfo.deviceHeight/100));
        //       	voltmx.print("### dragMainOnMove imageheight: " + imageheight);
        var middleTop = Global.vars.deviceInfo.deviceHeight - (parseInt(photoHeigth) * (Global.vars.deviceInfo.deviceHeight / 100));
        voltmx.print("### dragMainOnMove middleTop1: " + middleTop);
        middleTop = middleTop / 2;
        voltmx.print("### dragMainOnMove middleTop2: " + middleTop);
        frmPhotoOverview.imgPhoto.top = middleTop + 'px';
        voltmx.print("### dragMainOnMove Top: " + frmPhotoOverview.imgPhoto.top);
    }
}

function frmPhotoOverview_setMainTouch() {
    voltmx.print("### frmPhotoOverview_setMainTouch");
    frmPhotoOverview.imgPhoto.onTouchStart = frmPhotoOverview_dragMainOnStart;
    frmPhotoOverview.imgPhoto.onTouchMove = frmPhotoOverview_dragMainOnMove;
    frmPhotoOverview.imgPhoto.onTouchEnd = frmPhotoOverview_dragMainOnStop;
    try {
        voltmx.timer.cancel("setMainTouch");
    } catch (err) {}
}
// Once stopped dragging on the MainView, this will be called
function frmPhotoOverview_dragMainOnStop(source, x, y) {
    voltmx.print("### dragMainOnStop");
    Global.vars.headerTouchCounter = 0;
    Global.vars.firstTouchMain = false;
}
//end of drag