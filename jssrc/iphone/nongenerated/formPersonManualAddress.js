function frmPersonManualAddress_flcCountryAdress_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcCountryAdress_setVisibility");

    function flcCountryAdress_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcCountryAdress_setVisibility flcCountryAdress_setVisibility: " + boolean);
        frmPersonManualAddress.flcCountryAdress.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcCountryAdress_setVisibility, []);
}

function frmPersonManualAddress_flcZipCode_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcZipCode_setVisibility");

    function flcZipCode_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcZipCode_setVisibility flcZipCode_setVisibility: " + boolean);
        frmPersonManualAddress.flcZipCode.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcZipCode_setVisibility, []);
}

function frmPersonManualAddress_flcStreetNumber_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcStreetNumber_setVisibility");

    function flcStreetNumber_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcStreetNumber_setVisibility flcStreetNumber_setVisibility: " + boolean);
        frmPersonManualAddress.flcStreetNumber.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcStreetNumber_setVisibility, []);
}

function frmPersonManualAddress_flcHouseNumberAddition_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcHouseNumberAddition_setVisibility");

    function flcHouseNumberAddition_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcHouseNumberAddition_setVisibility flcHouseNumberAddition_setVisibility: " + boolean);
        frmPersonManualAddress.flcHouseNumberAddition.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcHouseNumberAddition_setVisibility, []);
}

function frmPersonManualAddress_flcButZipcode_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcButZipcode_setVisibility");

    function flcButZipcode_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcButZipcode_setVisibility flcButZipcode_setVisibility: " + boolean);
        frmPersonManualAddress.flcButZipcode.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcButZipcode_setVisibility, []);
}

function frmPersonManualAddress_flcStreet_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcStreet_setVisibility");

    function flcStreet_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcStreet_setVisibility flcStreet_setVisibility: " + boolean);
        frmPersonManualAddress.flcStreet.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcStreet_setVisibility, []);
}

function frmPersonManualAddress_flcPlaceOfResidency_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcPlaceOfResidency_setVisibility");

    function flcPlaceOfResidency_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcPlaceOfResidency_setVisibility flcPlaceOfResidency_setVisibility: " + boolean);
        frmPersonManualAddress.flcPlaceOfResidency.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcPlaceOfResidency_setVisibility, []);
}

function frmPersonManualAddress_flcAddressline1_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcAddressline1_setVisibility");

    function flcAddressline1_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcAddressline1_setVisibility flcAddressline1_setVisibility: " + boolean);
        frmPersonManualAddress.flcAddressline1.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcAddressline1_setVisibility, []);
}

function frmPersonManualAddress_flcAddressline2_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcAddressline2_setVisibility");

    function flcAddressline2_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcAddressline2_setVisibility flcAddressline2_setVisibility: " + boolean);
        frmPersonManualAddress.flcAddressline2.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcAddressline2_setVisibility, []);
}

function frmPersonManualAddress_flcAddressline3_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcAddressline3_setVisibility");

    function flcAddressline3_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcAddressline3_setVisibility flcAddressline3_setVisibility: " + boolean);
        frmPersonManualAddress.flcAddressline3.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcAddressline3_setVisibility, []);
}

function frmPersonManualAddress_flcWarning_setVisibility(boolean) {
    voltmx.print("### frmPersonManualAddress_flcWarning_setVisibility");

    function flcWarning_setVisibility() {
        voltmx.print("### frmPersonManualAddress_flcWarning_setVisibility flcWarning_setVisibility: " + boolean);
        frmPersonManualAddress.flcWarning.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcWarning_setVisibility, []);
}

function frmPersonManualAddress_init() {
    voltmx.print("#### frmPersonManualAddress_init");
    //Utility_registerForIdleTimeout();
    frmPersonManualAddress.onDeviceBack = Global_onDeviceBack;
    frmPersonManualAddress_fillAddressTypes();
    frmPersonManualAddress_clearAddress();
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
        frmPersonManualAddress.lbxAdressType.centerX = 52 + "%";
    }
    // RL-384
    frmPersonManualAddress.lbxAdressType.expandListItemToParentWidth = true;
    //
}

function frmPersonManualAddress_preshow() {
    Analytics_logScreenView("person-manual-address");
    voltmx.print("#### frmPersonManualAddress_preshow");
    voltmx.print("### frmPersonManualAddress_preshow Global.vars.gCasePersons.addresses[0] 1" + JSON.stringify(Global.vars.gCasePersons.addresses[0]));
    voltmx.print("### frmPersonManualAddress_preshow Global.vars.Personsaddresses 1" + JSON.stringify(Global.vars.Personsaddresses));
    if (Global.vars.gCasePersons.addresses[0].country === null || Global.vars.gCasePersons.addresses[0].country === "null" || (Global.vars.gCasePersons.addresses[0].country === "" && Global.vars.gCasePersons.addresses[0].addressType == addressType.stayAddress.value)) {
        voltmx.print("### frmPersonManualAddress_preshow frmPersonManualAddress_getCountryAddress");
        frmPersonManualAddress_getCountryAddress();
    } else {
        if (Global.vars.gCasePersons.addresses[0].countryCode === Global.vars.Personsaddresses.countryCode) {
            voltmx.print("### frmPersonManualAddress_preshow do not clear adress same country was choosen");
            // do not clear adress same country was choosen
        } else if (Global.vars.gCasePersons.addresses[0].countryCode !== Global.vars.Personsaddresses.countryCode && Global.vars.Personsaddresses.countryCode != null && Global.vars.gCasePersons.addresses[0].countryCode !== Global.vars.CountryCode && Global.vars.Personsaddresses.countryCode !== Global.vars.CountryCode) {
            voltmx.print("### frmPersonManualAddress_preshow do nothing change from one foreign country code to another");
            // do nothing change from one foreign country code to another
        } else if (Global.vars.Personsaddresses.countryCode == null) {
            voltmx.print("### frmPersonManualAddress_preshow do nothing do nothing first time country choosen");
            // do nothing first time country choosen
        } else {
            voltmx.print("### frmPersonManualAddress_preshow clear address fields");
            // clear address fields
            frmPersonManualAddress_clearAddress();
            frmPersonManualAddress_clearAddressGlobals();
        }
        frmPersonManualAddress.lblCountryAdress.text = Global.vars.gCasePersons.addresses[0].country;
        if (Global.vars.gCasePersons.addresses[0].countryCode !== Global.vars.CountryCode) {
            voltmx.print("### frmPersonManualAddress_preshow frmPersonManualAddress_setForeignCountryAdresslines");
            frmPersonManualAddress_setForeignCountryAdresslines();
        } else {
            voltmx.print("### frmPersonManualAddress_preshow frmPersonManualAddress_setCountryAdressFields");
            frmPersonManualAddress_setCountryAdressFields();
        }
        voltmx.print("### frmPersonManualAddress_preshow frmPersonManualAddress_fillFields");
        frmPersonManualAddress_fillFields();
    }
    if (Global.vars.gCasePersons.addresses[0].addressType == addressType.withoutFixedStay.value || Global.vars.gCasePersons.addresses[0].addressType == addressType.stayAddress.value) {
        frmPersonManualAddress.lbxAdressType.selectedKey = Global.vars.gCasePersons.addresses[0].addressType;
        voltmx.print("### frmPersonManualAddress_preshow frmPersonManualAddress_setSelectedAddressType");
        frmPersonManualAddress_setSelectedAddressType();
    } else if (Global.vars.gCasePersons.addresses[0].addressType == "null" || Global.vars.gCasePersons.addresses[0].addressType === null || Global.vars.gCasePersons.addresses[0].addressType === "") {
        Global.vars.gCasePersons.addresses[0].addressType = addressType.stayAddress.value;
        frmPersonManualAddress.lbxAdressType.selectedKey = Global.vars.gCasePersons.addresses[0].addressType;
        voltmx.print("### frmPersonManualAddress_preshow frmPersonManualAddress_setSelectedAddressType");
        frmPersonManualAddress_setSelectedAddressType();
    }
    if (Global.vars.gCasePersons.addresses[0].country === null || Global.vars.gCasePersons.addresses[0].country === "null" || (Global.vars.gCasePersons.addresses[0].country === "" && Global.vars.gCasePersons.addresses[0].addressType == addressType.stayAddress.value)) {
        voltmx.print("### frmPersonManualAddress_preshow Global.vars.gCasePersons.addresses[0] 2" + JSON.stringify(Global.vars.gCasePersons.addresses[0]));
    } else {
        voltmx.print("### frmPersonManualAddress_preshow Global.vars.gCasePersons.addresses[0] 2" + JSON.stringify(Global.vars.gCasePersons.addresses[0]));
        frmPersonManualAddress_fillFields();
    }
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_checkFieldsFilled() {
    voltmx.print("#### frmPersonManualAddress_checkFieldsFilled ####");
    var lvalidated = true;
    frmPersonManualAddress.flcStreetNumber.skin = flcFieldEdge;
    frmPersonManualAddress.flcStreet.skin = flcFieldEdge;
    frmPersonManualAddress.flcPlaceOfResidency.skin = flcFieldEdge;
    frmPersonManualAddress.flcAddressline1.skin = flcFieldEdge;
    frmPersonManualAddress.flcAddressline2.skin = flcFieldEdge;
    frmPersonManualAddress.flcAddressline3.skin = flcFieldEdge;
    if ((Global.vars.gCasePersons.addresses[0].countryCode == Global.vars.CountryCode) && (Global.vars.gCasePersons.addresses[0].addressType != "05")) {
        if (frmPersonManualAddress.txtStreetNumber.text === "" || frmPersonManualAddress.txtStreetNumber.text === null) {
            lvalidated = false;
            frmPersonManualAddress.flcStreetNumber.skin = flcFieldEdgeRed;
        }
        if (frmPersonManualAddress.txtStreet.text === "" || frmPersonManualAddress.txtStreet.text === null) {
            lvalidated = false;
            frmPersonManualAddress.flcStreet.skin = flcFieldEdgeRed;
        }
        if (frmPersonManualAddress.lblPlaceOfResidency.text == voltmx.i18n.getLocalizedString("l_city")) {
            lvalidated = false;
            frmPersonManualAddress.flcPlaceOfResidency.skin = flcFieldEdgeRed;
        }
    } else if ((Global.vars.gCasePersons.addresses[0].countryCode !== 0 && Global.vars.gCasePersons.addresses[0].countryCode != Global.vars.CountryCode) && (Global.vars.gCasePersons.addresses[0].addressType != "05")) {
        if (frmPersonManualAddress.txtStreetNumber.text === "" || frmPersonManualAddress.txtStreetNumber.text === null) {
            lvalidated = false;
            frmPersonManualAddress.flcStreetNumber.skin = flcFieldEdgeRed;
        }
        if (frmPersonManualAddress.txtStreet.text === "" || frmPersonManualAddress.txtStreet.text === null) {
            lvalidated = false;
            frmPersonManualAddress.flcStreet.skin = flcFieldEdgeRed;
        }
        if (frmPersonManualAddress.txtAddressline3.text === "" || frmPersonManualAddress.txtAddressline3.text === null) {
            lvalidated = false;
            frmPersonManualAddress.flcAddressline3.skin = flcFieldEdgeRed;
        }
    }
}

function frmPersonManualAddress_fillAddressTypes() {
    var addresstypedata = [];
    var selectedKey = null;
    for (var i = 0; i < Global.vars.gPersonAdressType.length; i++) {
        addresstypedata.push({
            key: Global.vars.gPersonAdressType[i].string_value,
            value: Global.vars.gPersonAdressType[i].descripton
        });
        if (Global.vars.gPersonAdressType[i].string_value == addressType.stayAddress.value) {
            selectedKey = Global.vars.gPersonAdressType[i].string_value;
        }
    }
    voltmx.print("### frmPersonManualAddress addresstypedata: " + JSON.stringify(addresstypedata));
    frmPersonManualAddress.lbxAdressType.masterDataMap = [addresstypedata, "key", "value"];
    if (frmPersonManualAddress.lbxAdressType.selectedKey === null && selectedKey != null) {
        frmPersonManualAddress.lbxAdressType.selectedKey = selectedKey;
        Global.vars.gCasePersons.addresses[0].addressTypeDesc = frmPersonManualAddress.lbxAdressType.selectedKeyValue[1];
        Global.vars.gCasePersons.addresses[0].addressType = frmPersonManualAddress.lbxAdressType.selectedKey;
    } else {
        Global.vars.gCasePersons.addresses[0].addressType = addressType.stayAddress.value;
        Global.vars.gCasePersons.addresses[0].addressTypeDesc = "woon- of verblijfadres";
    }
}

function frmPersonManualAddress_setSelectedAddressType() {
    voltmx.print("### frmPersonManualAddress_setSelectedAddressType frmPersonManualAddress.lbxAdressType.selectedKey: " + frmPersonManualAddress.lbxAdressType.selectedKey);
    voltmx.print("### frmPersonManualAddress_setSelectedAddressType Global.vars.gCasePersons.addresses[0].addressType: " + Global.vars.gCasePersons.addresses[0].addressType);
    if (Global.vars.gCasePersons.addresses[0].addressType.toString() !== frmPersonManualAddress.lbxAdressType.selectedKey.toString()) {
        frmPersonManualAddress_clearAddress();
    }
    Global.vars.gCasePersons.addresses[0].addressTypeDesc = frmPersonManualAddress.lbxAdressType.selectedKeyValue[1];
    Global.vars.gCasePersons.addresses[0].addressType = frmPersonManualAddress.lbxAdressType.selectedKey;
    if (Global.vars.gCasePersons.addresses[0].addressType == addressType.withoutFixedStay.value) {
        frmPersonManualAddress_setZVWOVHTL();
    } else {
        frmPersonManualAddress_setAddressManual();
    }
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_setZVWOVHTL() {
    Global.vars.gCasePersons.addresses[0].country = "";
    Global.vars.gCasePersons.addresses[0].countryCode = 0;
    Global.vars.gCasePersons.addresses[0].addressSubType = null;
    frmPersonManualAddress_clearAddressGlobals();
    //hide all adressfields
    frmPersonManualAddress_flcCountryAdress_setVisibility(false);
    frmPersonManualAddress_flcZipCode_setVisibility(false);
    frmPersonManualAddress_flcStreetNumber_setVisibility(false);
    frmPersonManualAddress_flcHouseNumberAddition_setVisibility(false);
    frmPersonManualAddress_flcButZipcode_setVisibility(false);
    frmPersonManualAddress_flcStreet_setVisibility(false);
    frmPersonManualAddress_flcPlaceOfResidency_setVisibility(false);
    frmPersonManualAddress_flcAddressline1_setVisibility(false);
    frmPersonManualAddress_flcAddressline2_setVisibility(false);
    frmPersonManualAddress_flcAddressline3_setVisibility(false);
    frmPersonManualAddress_flcWarning_setVisibility(false);
    voltmx.print("### frmPersonManualAddress_setZVWOVHTL Global.vars.gCasePersons na: " + JSON.stringify(Global.vars.gCasePersons.addresses[0]));
}

function frmPersonManualAddress_setAddressManual() {
    voltmx.print("##### frmPersonManualAddress_setAddressManual");
    frmPersonManualAddress_flcCountryAdress_setVisibility(true);
    frmPersonManualAddress_flcZipCode_setVisibility(true);
    frmPersonManualAddress_flcStreetNumber_setVisibility(true);
    frmPersonManualAddress_flcHouseNumberAddition_setVisibility(true);
    frmPersonManualAddress_flcButZipcode_setVisibility(true);
    frmPersonManualAddress_flcStreet_setVisibility(true);
    frmPersonManualAddress_flcPlaceOfResidency_setVisibility(true);
    frmPersonManualAddress_flcAddressline1_setVisibility(false);
    frmPersonManualAddress_flcAddressline2_setVisibility(false);
    frmPersonManualAddress_flcAddressline3_setVisibility(false);
    frmPersonManualAddress_flcWarning_setVisibility(false);
    voltmx.print("### frmPersonManualAddress_setAddressManual Global.vars.gCasePersons na: " + JSON.stringify(Global.vars.gCasePersons.addresses));
    //voltmx.print("### frmPersonManualAddress_setAddressManual frmPersonManualAddress_getCountryAddress");
    if (Global.vars.gCasePersons.addresses[0].addressType == addressType.emptyDataGBA.value) {
        Global.vars.gCasePersons.addresses[0].addressType = addressType.stayAddress.value;
        Global.vars.gCasePersons.addresses[0].addressTypeDesc = "woon- of verblijfadres";
    }
    if (Global.vars.gCasePersons.addresses[0].country === null || Global.vars.gCasePersons.addresses[0].country === "null" || (Global.vars.gCasePersons.addresses[0].country === "" && Global.vars.gCasePersons.addresses[0].addressType == addressType.stayAddress.value)) {
        voltmx.print("### frmPersonManualAddress_setAddressManual frmPersonManualAddress_getCountryAddress");
        frmPersonManualAddress_getCountryAddress();
    }
    if (Global.vars.gCasePersons.addresses[0].countryCode !== Global.vars.CountryCode) {
        voltmx.print("### frmPersonManualAddress_setAddressManual frmPersonManualAddress_setForeignCountryAdresslines");
        frmPersonManualAddress_setForeignCountryAdresslines();
    } else {
        voltmx.print("### frmPersonManualAddress_setAddressManual frmPersonManualAddress_setCountryAdressFields");
        frmPersonManualAddress_setCountryAdressFields();
    }
}

function frmPersonManualAddress_clearAddress() {
    voltmx.print("##### frmPersonManualAddress_clearAddress ");
    frmPersonManualAddress.lblPlaceOfResidency.text = voltmx.i18n.getLocalizedString("l_city");
    frmPersonManualAddress.lblPlaceOfResidency.skin = lblFieldNotFilled;
    frmPersonManualAddress.txtStreet.text = "";
    frmPersonManualAddress.txtZipCode.text = "";
    frmPersonManualAddress.txtStreetNumber.text = "";
    frmPersonManualAddress.txtHouseNumberAddition.text = "";
    frmPersonManualAddress.txtAddressline1.text = "";
    frmPersonManualAddress.txtAddressline2.text = "";
    frmPersonManualAddress.txtAddressline3.text = "";
}

function frmPersonManualAddress_clearAddressGlobals() {
    //clear Globals
    voltmx.print("### frmPersonManualAddress_clearAddressGlobals");
    Global.vars.gCasePersons.addresses[0].addressLine1 = null;
    Global.vars.gCasePersons.addresses[0].addressLine2 = null;
    Global.vars.gCasePersons.addresses[0].addressLine3 = null;
    Global.vars.gCasePersons.addresses[0].streetNumAdditn = null;
    Global.vars.gCasePersons.addresses[0].street = null;
    Global.vars.gCasePersons.addresses[0].streetNumber = null;
    Global.vars.gCasePersons.addresses[0].zipcode = null;
    Global.vars.gCasePersons.addresses[0].cityCode = null;
    Global.vars.gCasePersons.addresses[0].city = null;
    Global.vars.gCasePersons.addresses[0].municipality = null;
    Global.vars.gCasePersons.addresses[0].municipalCode = null;
    Global.vars.Personsaddresses = {
        country: null,
        countryCode: null,
        street: null,
        streetNumber: null,
        streetNumAdditn: null,
        zipcode: null,
        city: null,
        cityCode: null,
        addressline1: null,
        addressline2: null,
        addressline3: null
    };
}

function frmPersonManualAddress_clearAddressGlobalsLocal() {
    //clear Globals
    voltmx.print("### frmPersonManualAddress_clearAddressGlobalsLocal");
    Global.vars.gCasePersons.addresses[0].streetNumAdditn = null;
    Global.vars.gCasePersons.addresses[0].street = null;
    Global.vars.gCasePersons.addresses[0].streetNumber = null;
    Global.vars.gCasePersons.addresses[0].zipcode = null;
    Global.vars.gCasePersons.addresses[0].cityCode = null;
    Global.vars.gCasePersons.addresses[0].city = null;
    Global.vars.gCasePersons.addresses[0].municipality = null;
    Global.vars.gCasePersons.addresses[0].municipalCode = null;
    voltmx.print("### frmPersonManualAddress_clearAddressGlobalsLocal addresses: " + JSON.stringify(Global.vars.gCasePersons.addresses[0]));
}

function frmPersonManualAddress_clearAddressGlobalsForeign() {
    //clear Globals
    voltmx.print("### frmPersonManualAddress_clearAddressGlobalsForeign");
    Global.vars.gCasePersons.addresses[0].streetNumAdditn = null;
    Global.vars.gCasePersons.addresses[0].street = null;
    Global.vars.gCasePersons.addresses[0].streetNumber = null;
    Global.vars.gCasePersons.addresses[0].zipcode = null;
    Global.vars.gCasePersons.addresses[0].cityCode = null;
    Global.vars.gCasePersons.addresses[0].city = null;
    Global.vars.gCasePersons.addresses[0].municipality = null;
    Global.vars.gCasePersons.addresses[0].municipalCode = null;
    Global.vars.gCasePersons.addresses[0].addressLine1 = null;
    Global.vars.gCasePersons.addresses[0].addressLine2 = null;
    Global.vars.gCasePersons.addresses[0].addressLine3 = null;
}

function frmPersonManualAddress_setForeignCountryAdresslines() {
    voltmx.print("### frmPersonManualAddress_setForeignCountryAdresslines");
    frmPersonManualAddress_clearAddress();
    //frmPersonManualAddress_clearAddressGlobalsLocal();
    frmPersonManualAddress_flcStreetNumber_setVisibility(true);
    frmPersonManualAddress_flcHouseNumberAddition_setVisibility(true);
    frmPersonManualAddress_flcButZipcode_setVisibility(false);
    frmPersonManualAddress_flcStreet_setVisibility(true);
    frmPersonManualAddress_flcPlaceOfResidency_setVisibility(false);
    frmPersonManualAddress_flcAddressline1_setVisibility(true);
    frmPersonManualAddress_flcAddressline2_setVisibility(true);
    frmPersonManualAddress_flcAddressline3_setVisibility(true);
    frmPersonManualAddress_flcWarning_setVisibility(true);
    frmPersonManualAddress_setLayout(true);
}

function frmPersonManualAddress_setCountryAdressFields() {
    voltmx.print("### frmPersonManualAddress_setCountryAdressFields");
    frmPersonManualAddress_clearAddress();
    //frmPersonManualAddress_clearAddressGlobalsForeign();
    frmPersonManualAddress_flcStreetNumber_setVisibility(true);
    frmPersonManualAddress_flcHouseNumberAddition_setVisibility(true);
    frmPersonManualAddress_flcButZipcode_setVisibility(true);
    frmPersonManualAddress_flcStreet_setVisibility(true);
    frmPersonManualAddress_flcPlaceOfResidency_setVisibility(true);
    frmPersonManualAddress_flcAddressline1_setVisibility(false);
    frmPersonManualAddress_flcAddressline2_setVisibility(false);
    frmPersonManualAddress_flcAddressline3_setVisibility(false);
    frmPersonManualAddress_setLayout(false);
}

function frmPersonManualAddress_setLayout(foreignAddress) {
    foreignAddress = foreignAddress === undefined ? false : foreignAddress;
    voltmx.print("### frmPersonManualAddress_setLayout : " + foreignAddress);
    // Reference to the parent FlexContainer
    var parentContainer = frmPersonManualAddress.flcLayout;
    // Store references to the FlexContainers to be moved
    var zipcode = parentContainer.flcZipCode;
    // Remove the containers from their current positions
    parentContainer.remove(zipcode);
    if (foreignAddress) {
        // Add them back at the new positions (1-based index in VoltMX)
        zipcode.txtZipCode.maxTextLength = 12;
        zipcode.lblZipCodeHeader.text = voltmx.i18n.getLocalizedString("l_zipCode12chars");
        parentContainer.addAt(zipcode, 7);
    } else {
        zipcode.txtZipCode.maxTextLength = 7;
        zipcode.lblZipCodeHeader.text = voltmx.i18n.getLocalizedString("l_zipCode");
        parentContainer.addAt(zipcode, 2);
    }
    frmPersonManualAddress.flcLayout = parentContainer;
    // Force layout refresh (optional, depending on your layout)
    frmPersonManualAddress.forceLayout();
    /* 
    	placed frmPersonManualAddress_flcZipCode_setVisibility(true) inside frmPersonManualAddress_setLayout 
        because it is running on main thread and can be executed while it is temporary removed from parentContainer 
        and then app will crash
    */
    frmPersonManualAddress_flcZipCode_setVisibility(true);
}

function frmPersonManualAddress_onDoneZipcode() {
    if (frmPersonManualAddress.txtZipCode.text != null && frmPersonManualAddress.txtZipCode.text.length > 0) {
        if (Global.vars.gCasePersons.addresses[0].countryCode !== Global.vars.CountryCode) {
            if (frmPersonManualAddress.txtZipCode.text.length > 20) {
                alert("Dit is geen geldige postcode");
            }
        } else {
            var validatedZipcode = Utility_checkZipcode(frmPersonManualAddress.txtZipCode.text);
            if (!validatedZipcode) {
                alert("Dit is geen geldige postcode");
            } else {
                frmPersonManualAddress.txtZipCode.text = validatedZipcode;
            }
        }
    }
}

function frmPersonManualAddress_onTextChangeStreet() {
    //frmPersonManualAddress.txtStreet.text = Utility_titleCase(frmPersonManualAddress.txtStreet.text);
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_onTextChangeAddressline1() {
    frmPersonManualAddress.txtAddressline1.text = Utility_regexpRestrictedInputAddressLine1(frmPersonManualAddress.txtAddressline1.text);
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_searchStreet() {
    voltmx.print("### frmPersonManualAddress_searchStreet");
    if (frmPersonManualAddress.txtZipCode.text != null && frmPersonManualAddress.txtZipCode.text.length > 0) {
        if (Global.vars.gCasePersons.addresses[0].countryCode === Global.vars.CountryCode) {
            var validatedZipcode = Utility_checkZipcode(frmPersonManualAddress.txtZipCode.text);
            if (!validatedZipcode) {
                alert("Dit is geen geldige postcode");
            } else {
                frmPersonManualAddress.txtZipCode.text = validatedZipcode;
                // service call
                var zipcode = frmPersonManualAddress.txtZipCode.text.replace(" ", "");
                voltmx.print("### frmPersonManualAddress_searchStreet do servicecall");
                service_Location_GetStreetFromZipcode(zipcode, frmPersonManualAddress_searchStreetCallback);
            }
        }
    }
}

function frmPersonManualAddress_searchStreetCallback(result) {
    voltmx.print("### frmPersonManualAddress_searchStreetCallback result: " + JSON.stringify(result));
    if (result.opstatus === 0 && result.httpStatusCode == 200) {
        if (result.serviceStatus[0].code !== 0) {
            voltmx.print("### frmPersonManualAddress_searchStreetCallback code not 0: " + JSON.stringify(result.serviceStatus));
            alert(result.serviceStatus[0].description);
        } else {
            voltmx.print("### frmPersonManualAddress_searchStreetCallback addressData: " + JSON.stringify(result.addressData));
            var noValidResult = false;
            if (result.addressData[0].street != null && result.addressData[0].street !== "" && result.addressData[0].street !== "null") {
                frmPersonManualAddress.txtStreet.text = result.addressData[0].street;
                Global.vars.gCasePersons.addresses[0].street = result.addressData[0].street;
            } else {
                noValidResult = true;
            }
            if (result.addressData[0].cityCode != null && result.addressData[0].cityCode !== "" && result.addressData[0].cityCode !== "null") {
                Global.vars.gCasePersons.addresses[0].cityCode = result.addressData[0].cityCode;
            } else {
                noValidResult = true;
            }
            if (result.addressData[0].city != null && result.addressData[0].city !== "" && result.addressData[0].city !== "null") {
                Global.vars.gCasePersons.addresses[0].city = result.addressData[0].city;
                frmPersonManualAddress.lblPlaceOfResidency.text = result.addressData[0].city;
                frmPersonManualAddress.lblPlaceOfResidency.skin = lblFieldInfo;
            } else {
                noValidResult = true;
            }
            if (result.addressData[0].municipality != null && result.addressData[0].municipality !== "" && result.addressData[0].municipality !== "null") {
                Global.vars.gCasePersons.addresses[0].municipality = result.addressData[0].municipality;
            } else {
                noValidResult = true;
            }
            if (result.addressData[0].municipalityCode != null && result.addressData[0].municipalityCode !== "" && result.addressData[0].municipalityCode !== "null") {
                Global.vars.gCasePersons.addresses[0].municipalCode = result.addressData[0].municipalityCode;
            } else {
                noValidResult = true;
            }
            if (noValidResult) {
                alert("De postcode service levert geen geldig resultaat op, vul een andere postcode in");
                frmPersonManualAddress.txtStreet.text = "";
                Global.vars.gCasePersons.addresses[0].street = null;
                Global.vars.gCasePersons.addresses[0].cityCode = null;
                Global.vars.gCasePersons.addresses[0].city = null;
                frmPersonManualAddress.lblPlaceOfResidency.text = voltmx.i18n.getLocalizedString("l_city");
                frmPersonManualAddress.lblPlaceOfResidency.skin = lblFieldNotFilled;
                Global.vars.gCasePersons.addresses[0].municipality = null;
                Global.vars.gCasePersons.addresses[0].municipalCode = null;
            }
        }
        voltmx.application.dismissLoadingScreen();
        frmPersonManualAddress_checkFieldsFilled();
    } else {
        voltmx.application.dismissLoadingScreen();
        voltmx.ui.Alert("Zoek postcode straat - " + voltmx.i18n.getLocalizedString("e_ser0001"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
    }
}

function frmPersonManualAddress_clearAddressline1() {
    frmPersonManualAddress.txtAddressline1.text = "";
    frmPersonManualAddress.txtAddressline1.setFocus(true);
    Global.vars.gCasePersons.addresses[0].addressLine1 = null;
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_clearAddressline2() {
    frmPersonManualAddress.txtAddressline2.text = "";
    frmPersonManualAddress.txtAddressline2.setFocus(true);
    Global.vars.gCasePersons.addresses[0].addressLine2 = null;
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_clearAddressline3() {
    frmPersonManualAddress.txtAddressline3.text = "";
    frmPersonManualAddress.txtAddressline3.setFocus(true);
    Global.vars.gCasePersons.addresses[0].addressLine3 = null;
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_OnEditAddresslines() {
    voltmx.print("### frmPersonManualAddress_OnEditAddresslines");
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_clearHouseNumberAddition() {
    frmPersonManualAddress.txtHouseNumberAddition.text = "";
    frmPersonManualAddress.txtHouseNumberAddition.setFocus(true);
    Global.vars.gCasePersons.addresses[0].streetNumAdditn = null;
}

function frmPersonManualAddress_clearStreet() {
    frmPersonManualAddress.txtStreet.text = "";
    frmPersonManualAddress.txtStreet.setFocus(true);
    Global.vars.gCasePersons.addresses[0].street = null;
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_clearStreetNumber() {
    frmPersonManualAddress.txtStreetNumber.text = "";
    frmPersonManualAddress.txtStreetNumber.setFocus(true);
    Global.vars.gCasePersons.addresses[0].streetNumber = null;
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_OnEditStreetNumber() {
    frmPersonManualAddress_checkFieldsFilled();
}

function frmPersonManualAddress_clearZipCode() {
    frmPersonManualAddress.txtZipCode.text = "";
    frmPersonManualAddress.txtZipCode.setFocus(true);
    Global.vars.gCasePersons.addresses[0].zipcode = null;
}

function frmPersonManualAddress_getCountryAddress() {
    voltmx.print("##### frmPersonManualAddress_getCountryAddress");
    voltmx.print("### frmPersonManualAddress_getCountryAddress Current Country: " + Global.vars.gCasePersons.addresses[0].country);
    voltmx.print("### frmPersonManualAddress_getCountryAddress Current CountryCode: " + Global.vars.gCasePersons.addresses[0].countryCode);

    function getCountryAdressSuccessCallback(resultcountryid) {
        voltmx.print("##### frmPersonManualAddress_getCountryAddress getCountryAdressSuccessCallback ");
        if ((resultcountryid.length !== 0)) {
            voltmx.print("### frmPersonManualAddress_getCountryAddress resultcountryid: " + JSON.stringify(resultcountryid));
            Global.vars.gCasePersons.addresses[0].country = resultcountryid[0].description;
            Global.vars.gCasePersons.addresses[0].countryCode = resultcountryid[0].code;
            frmPersonManualAddress.lblCountryAdress.text = Global.vars.gCasePersons.addresses[0].country;
            frmPersonManualAddress.lblCountryAdress.skin = lblFieldInfo;
            if (Global.vars.gCasePersons.addresses[0].countryCode !== Global.vars.CountryCode) {
                voltmx.print("##### frmPersonManualAddress_getCountryAddress country not Global.vars.CountryCode");
                frmPersonManualAddress_setForeignCountryAdresslines();
            } else {
                voltmx.print("##### frmPersonManualAddress_getCountryAddress country is Global.vars.CountryCode");
                frmPersonManualAddress_setCountryAdressFields();
            }
            voltmx.print("### frmPersonManualAddress_getCountryAddress frmPersonManualAddress_fillFields");
            frmPersonManualAddress_fillFields();
            voltmx.print("#### frmPersonManualAddress_getCountryAddress country succes and set ");
        } else {
            voltmx.print("#### frmPersonManualAddress_getCountryAddress no results found for Country Info");
        }
        voltmx.print("##### frmPersonManualAddress_getCountryAddress getCountryAdressSuccessCallback2 ");
    }

    function getCountryAdressErrorCallback(error) {
        voltmx.print("##### frmPersonManualAddress_getCountryAddress getCountryAdressErrorCallback ");
        voltmx.print("### frmPersonManualAddress_getCountryAddress Country Info error: " + error);
    }
    if ((Global.vars.gCasePersons.addresses[0].country != null && Global.vars.gCasePersons.addresses[0].country !== "")) {
        frmPersonManualAddress.lblCountryAdress.text = Global.vars.gCasePersons.addresses[0].country;
        frmPersonManualAddress.lblCountryAdress.skin = lblFieldInfo;
    } else {
        var lCountrywhereClause = "select * from mle_v_country_m where code = '" + Global.vars.CountryCode + "'";
        if (Global.vars.gCasePersons.countryIdenDoc != null) {
            lCountrywhereClause = "select * from mle_v_country_m where code = '" + Global.vars.gCasePersons.countryIdenDoc + "'";
        }
        lCountrywhereClause = Utility_addLanguageToWhereClauseObjectSync(lCountrywhereClause);
        voltmx.print("### frmPersonManualAddress_getCountryAddress Country clause: " + lCountrywhereClause);
        KNYMobileFabric.OfflineObjects.executeSelectQuery(lCountrywhereClause, getCountryAdressSuccessCallback, getCountryAdressErrorCallback);
    }
    voltmx.print("##### frmPersonManualAddress_getCountryAddress2");
}

function frmPersonManualAddress_onclick_countryAddress() {
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    frmPersonManualAddress_setGlobalsBeforeGoingToOtherForm();
    frmPersonCountries.show();
}

function frmPersonManualAddress_onclick_municipalityAddress() {
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    frmPersonManualAddress_setGlobalsBeforeGoingToOtherForm();
    frmPersonMunicipalities.show();
}

function frmPersonManualAddress_onclick_cityAddress() {
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    frmPersonManualAddress_setGlobalsBeforeGoingToOtherForm();
    frmPersonCities.show();
}

function frmPersonManualAddress_setGlobalsBeforeGoingToOtherForm() {
    Global.vars.Personsaddresses.country = Global.vars.gCasePersons.addresses[0].country;
    Global.vars.Personsaddresses.countryCode = Global.vars.gCasePersons.addresses[0].countryCode;
    Global.vars.Personsaddresses.street = frmPersonManualAddress.txtStreet.text === "" ? null : frmPersonManualAddress.txtStreet.text;
    Global.vars.Personsaddresses.streetNumber = frmPersonManualAddress.txtStreetNumber.text === "" ? null : frmPersonManualAddress.txtStreetNumber.text;
    Global.vars.Personsaddresses.streetNumAdditn = frmPersonManualAddress.txtHouseNumberAddition.text === "" ? null : frmPersonManualAddress.txtHouseNumberAddition.text;
    Global.vars.Personsaddresses.zipcode = frmPersonManualAddress.txtZipCode.text.replace(/\s+/g, '') === "" ? null : frmPersonManualAddress.txtZipCode.text.replace(/\s+/g, '');
    Global.vars.Personsaddresses.city = frmPersonManualAddress.lblPlaceOfResidency.text === ("" || voltmx.i18n.getLocalizedString("l_city")) ? null : frmPersonManualAddress.lblPlaceOfResidency.text;
    Global.vars.Personsaddresses.cityCode = Global.vars.gCasePersons.addresses[0].cityCode;
    Global.vars.Personsaddresses.addressline1 = frmPersonManualAddress.txtAddressline1.text === "" ? null : frmPersonManualAddress.txtAddressline1.text;
    Global.vars.Personsaddresses.addressline2 = frmPersonManualAddress.txtAddressline2.text === "" ? null : frmPersonManualAddress.txtAddressline2.text;
    Global.vars.Personsaddresses.addressline3 = frmPersonManualAddress.txtAddressline3.text === "" ? null : frmPersonManualAddress.txtAddressline3.text;
}

function frmPersonManualAddress_onclickDone() {
    frmPersonManualAddress_SetGlobals();
}

function frmPersonManualAddress_SetGlobals() {
    Global.vars.gCasePersons.indManual = true;
    Global.vars.gCasePersons.addresses[0].indSecret = false;
    Global.vars.gCasePersons.addresses[0].source = "03"; // source 01 basisregister, use 03 for manual, use 02 for orther register
    Global.vars.gCasePersons.addresses[0].indInResearch = 0;
    if (Global.vars.gCasePersons.addresses[0].countryCode != Global.vars.CountryCode) { //check if foreign address is filled
        voltmx.print("#### frmPersonManualAddress_SetGlobals foreign address");
        Global.vars.gCasePersons.addresses[0].street = frmPersonManualAddress.txtStreet.text;
        Global.vars.gCasePersons.addresses[0].streetNumber = frmPersonManualAddress.txtStreetNumber.text;
        Global.vars.gCasePersons.addresses[0].streetNumAdditn = frmPersonManualAddress.txtHouseNumberAddition.text;
        Global.vars.gCasePersons.addresses[0].zipcode = frmPersonManualAddress.txtZipCode.text.replace(/\s+/g, '');
        Global.vars.gCasePersons.addresses[0].addressLine1 = frmPersonManualAddress.txtAddressline1.text;
        Global.vars.gCasePersons.addresses[0].addressLine2 = frmPersonManualAddress.txtAddressline2.text;
        Global.vars.gCasePersons.addresses[0].addressLine3 = frmPersonManualAddress.txtAddressline3.text;
        Global.vars.gCasePersons.addresses[0].addressSubType = null;
        //	Global.vars.gCasePersons.addresses[0].City = frmPerson.txtPlaceOfResidency.text;
    } else {
        voltmx.print("#### frmPersonManualAddress_SetGlobals local address");
        Global.vars.gCasePersons.addresses[0].street = frmPersonManualAddress.txtStreet.text;
        Global.vars.gCasePersons.addresses[0].streetNumber = frmPersonManualAddress.txtStreetNumber.text;
        Global.vars.gCasePersons.addresses[0].streetNumAdditn = frmPersonManualAddress.txtHouseNumberAddition.text;
        Global.vars.gCasePersons.addresses[0].zipcode = frmPersonManualAddress.txtZipCode.text.replace(/\s+/g, '');
        Global.vars.gCasePersons.addresses[0].city = frmPersonManualAddress.lblPlaceOfResidency.text;
    }
    var validated = frmPersonManualAddress_validateSetValues(Global.vars.gCasePersons);
    if (validated.validated) {
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        Global.vars.gCasePersons.indRegCalled = false;
        Global.vars.gCasePersons.personFilled = true;
        frmPersonResult.show();
    } else {
        alert(validated.errorMsg);
    }
}

function frmPersonManualAddress_validateSetValues(inputPerson) {
    voltmx.print("#### frmPersonManualAddress_validateSetValues ####");
    var lvalidated = true;
    var lmessage = "";
    var _lmessage = "";
    var lResult = {
        entity: "person",
        validated: lvalidated,
        errorCode: "",
        errorMsg: lmessage
    };
    if ((inputPerson.addresses[0].countryCode === 0 && inputPerson.addresses[0].addressType != "05") || inputPerson.addresses[0].countryCode === null) // && (inputPerson.PersonFilled == 0))
    {
        lvalidated = false;
        if (lmessage.length === 0) {
            lmessage = voltmx.i18n.getLocalizedString("l_countryadress");
        } else {
            lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_countryadress");
        }
    } else {
        if ((inputPerson.addresses[0].addressType != "05") && (inputPerson.addresses[0].streetNumber === "" || inputPerson.addresses[0].streetNumber === null)) // && (inputPerson.PersonFilled == 0))
        {
            lvalidated = false;
            if (lmessage.length === 0) {
                lmessage = voltmx.i18n.getLocalizedString("l_streetnumber");
            } else {
                lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_streetnumber");
            }
        }
        if ((inputPerson.addresses[0].addressType != "05") && (inputPerson.addresses[0].street === "" || inputPerson.addresses[0].street === null)) // && (inputPerson.PersonFilled == 0))
        {
            lvalidated = false;
            if (lmessage.length === 0) {
                lmessage = voltmx.i18n.getLocalizedString("l_street");
            } else {
                lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_street");
            }
        }
        if ((inputPerson.addresses[0].countryCode == Global.vars.CountryCode) && (inputPerson.addresses[0].addressType != "05") && (inputPerson.addresses[0].cityCode === "" || inputPerson.addresses[0].cityCode === null)) // && (inputPerson.PersonFilled == 0))
        {
            lvalidated = false;
            if (lmessage.length === 0) {
                lmessage = voltmx.i18n.getLocalizedString("l_placeofresidency");
            } else {
                lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_placeofresidency");
            }
        }
        voltmx.print("#### Validate.person address: " + JSON.stringify(inputPerson.addresses[0]));
        if ((inputPerson.addresses[0].countryCode !== 0) && (inputPerson.addresses[0].countryCode != Global.vars.CountryCode) && (inputPerson.addresses[0].addressType != "05") && (inputPerson.addresses[0].addressLine3 === "" || inputPerson.addresses[0].addressLine3 === null)) // && (inputPerson.PersonFilled == 0))
        {
            lvalidated = false;
            if (lmessage.length === 0) {
                lmessage = voltmx.i18n.getLocalizedString("l_placeofresidency");
            } else {
                lmessage = lmessage.trim() + ", " + voltmx.i18n.getLocalizedString("l_placeofresidency");
            }
        }
    }
    if (lvalidated === false) {
        if (_lmessage != "" && lmessage != "") {
            lmessage = voltmx.i18n.getLocalizedString("e_mand0002") + " " + lmessage.trim();
            lmessage = lmessage + "\r\n" + _lmessage;
        } else if (_lmessage == "" && lmessage != "") {
            lmessage = voltmx.i18n.getLocalizedString("e_mand0002") + " " + lmessage.trim();
        } else if (_lmessage != "" && lmessage == "") {
            lmessage = _lmessage;
        }
        lResult = {
            entity: "person",
            validated: lvalidated,
            errorCode: "e_mand0002",
            errorMsg: lmessage
        }; //check errorcode
    }
    return lResult;
}

function frmPersonManualAddress_btnBack() {
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    Global.vars.Personsaddresses = {
        country: null,
        countryCode: null,
        street: null,
        streetNumber: null,
        streetNumAdditn: null,
        zipcode: null,
        city: null,
        cityCode: null,
        addressline1: null,
        addressline2: null,
        addressline3: null
    };
    Global.vars.gCasePersons.indManual = false;
    frmPersonManualPerson.show();
}

function frmPersonManualAddress_fillFields() {
    voltmx.print("### frmPersonManualAddress_fillFields");
    if (Global.vars.gCasePersons.addresses[0].zipcode != null && Global.vars.gCasePersons.addresses[0].zipcode !== "") {
        frmPersonManualAddress.txtZipCode.text = Global.vars.gCasePersons.addresses[0].zipcode;
    } else if (Global.vars.Personsaddresses.zipcode != null) {
        frmPersonManualAddress.txtZipCode.text = Global.vars.Personsaddresses.zipcode;
    }
    if (Global.vars.gCasePersons.addresses[0].streetNumber != null && Global.vars.gCasePersons.addresses[0].streetNumber !== "") {
        frmPersonManualAddress.txtStreetNumber.text = Global.vars.gCasePersons.addresses[0].streetNumber;
    } else if (Global.vars.Personsaddresses.streetNumber != null) {
        frmPersonManualAddress.txtStreetNumber.text = Global.vars.Personsaddresses.streetNumber;
    }
    if (Global.vars.gCasePersons.addresses[0].streetNumAdditn != null && Global.vars.gCasePersons.addresses[0].streetNumAdditn !== "") {
        frmPersonManualAddress.txtHouseNumberAddition.text = Global.vars.gCasePersons.addresses[0].streetNumAdditn;
    } else if (Global.vars.Personsaddresses.streetNumAdditn != null) {
        frmPersonManualAddress.txtHouseNumberAddition.text = Global.vars.Personsaddresses.streetNumAdditn;
    }
    if (Global.vars.gCasePersons.addresses[0].street != null && Global.vars.gCasePersons.addresses[0].street !== "") {
        frmPersonManualAddress.txtStreet.text = Global.vars.gCasePersons.addresses[0].street;
    } else if (Global.vars.Personsaddresses.street != null) {
        frmPersonManualAddress.txtStreet.text = Global.vars.Personsaddresses.street;
    }
    if (Global.vars.gCasePersons.addresses[0].city != null && Global.vars.gCasePersons.addresses[0].city !== "") {
        frmPersonManualAddress.lblPlaceOfResidency.text = Global.vars.gCasePersons.addresses[0].city;
        frmPersonManualAddress.lblPlaceOfResidency.skin = lblFieldInfo;
    } else if (Global.vars.Personsaddresses.city != null && Global.vars.Personsaddresses.city != voltmx.i18n.getLocalizedString("l_city")) {
        Global.vars.gCasePersons.addresses[0].cityCode = Global.vars.Personsaddresses.cityCode;
        frmPersonManualAddress.lblPlaceOfResidency.text = Global.vars.Personsaddresses.city;
        frmPersonManualAddress.lblPlaceOfResidency.skin = lblFieldInfo;
    } else {
        frmPersonManualAddress.lblPlaceOfResidency.text = voltmx.i18n.getLocalizedString("l_city");
        frmPersonManualAddress.lblPlaceOfResidency.skin = lblFieldNotFilled;
    }
    if (Global.vars.gCasePersons.addresses[0].addressLine1 != null && Global.vars.gCasePersons.addresses[0].addressLine1 !== "") {
        frmPersonManualAddress.txtAddressline1.text = Global.vars.gCasePersons.addresses[0].addressLine1;
    } else if (Global.vars.Personsaddresses.addressline1 != null) {
        frmPersonManualAddress.txtAddressline1.text = Global.vars.Personsaddresses.addressline1;
    }
    if (Global.vars.gCasePersons.addresses[0].addressLine2 != null && Global.vars.gCasePersons.addresses[0].addressLine2 !== "") {
        frmPersonManualAddress.txtAddressline2.text = Global.vars.gCasePersons.addresses[0].addressLine2;
    } else if (Global.vars.Personsaddresses.addressline2 != null) {
        frmPersonManualAddress.txtAddressline2.text = Global.vars.Personsaddresses.addressline2;
    }
    if (Global.vars.gCasePersons.addresses[0].addressLine3 != null && Global.vars.gCasePersons.addresses[0].addressLine3 !== "") {
        frmPersonManualAddress.txtAddressline3.text = Global.vars.gCasePersons.addresses[0].addressLine3;
    } else if (Global.vars.Personsaddresses.addressline3 != null) {
        frmPersonManualAddress.txtAddressline3.text = Global.vars.Personsaddresses.addressline3;
    }
    if (Global.vars.gCasePersons.addresses[0].countryCode == Global.vars.CountryCode && frmPersonManualAddress.txtZipCode.text !== "" && frmPersonManualAddress.txtZipCode.text != null) { //&& (Global.vars.gCasePersons.addresses[0].cityCode === null || Global.vars.gCasePersons.addresses[0].cityCode === "") && (Global.vars.gCasePersons.addresses[0].municipalityCode === null || Global.vars.gCasePersons.addresses[0].municipalityCode === "")){
        voltmx.print("### frmPersonManualAddress_fillFields frmPersonManualAddress_searchStreet");
        frmPersonManualAddress_searchStreet();
    }
    frmPersonManualAddress_checkFieldsFilled();
}