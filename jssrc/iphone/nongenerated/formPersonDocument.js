function frmPersonDocument_contentOffset() {
    frmPersonDocument.contentSize = {
        height: "100%",
        width: "100%"
    };
    frmPersonDocument.contentOffset = {
        "x": "0px",
        "y": "0px"
    };
}

function frmPersonDocument_flcDocumentCountry_setVisibility(boolean) {
    voltmx.print("### frmPersonDocument_flcDocumentCountry_setVisibility");

    function flcDocumentCountry_setVisibility() {
        voltmx.print("### frmPersonDocument_flcDocumentCountry_setVisibility flcDocumentCountry_setVisibility: " + boolean);
        frmPersonDocument.flcDocumentCountry.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcDocumentCountry_setVisibility, []);
}

function frmPersonDocument_flcDocumentNumber_setVisibility(boolean) {
    voltmx.print("### frmPersonDocument_flcDocumentNumber_setVisibility");

    function flcDocumentNumber_setVisibility() {
        voltmx.print("### frmPersonDocument_flcDocumentNumber_setVisibility flcDocumentNumber_setVisibility: " + boolean);
        frmPersonDocument.flcDocumentNumber.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcDocumentNumber_setVisibility, []);
}

function frmPersonDocument_flcDocumentTypeAdditional_setVisibility(boolean) {
    voltmx.print("### frmPersonDocument_flcDocumentTypeAdditional_setVisibility");

    function flcDocumentTypeAdditional_setVisibility() {
        voltmx.print("### frmPersonDocument_flcDocumentTypeAdditional_setVisibility flcDocumentTypeAdditional_setVisibility: " + boolean);
        frmPersonDocument.flcDocumentTypeAdditional.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcDocumentTypeAdditional_setVisibility, []);
}

function frmPersonDocument_documentdescription_setVisibility(boolean) {
    voltmx.print("### frmPersonDocument_documentdescription_setVisibility");

    function documentdescription_setVisibility() {
        voltmx.print("### frmPersonDocument_documentdescription_setVisibility documentdescription_setVisibility: " + boolean);
        frmPersonDocument.documentdescription.setVisibility(boolean);
    }
    voltmx.runOnMainThread(documentdescription_setVisibility, []);
}

function frmPersonDocument_btnDone_setVisibility(boolean) {
    voltmx.print("### frmPersonDocument_btnDone_setVisibility");

    function btnDone_setVisibility() {
        voltmx.print("### frmPersonDocument_btnDone_setVisibility btnDone_setVisibility: " + boolean);
        frmPersonDocument.btnDone.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnDone_setVisibility, []);
}

function frmPersonDocument_settext_setVisibility(boolean) {
    voltmx.print("### frmPersonDocument_settext_setVisibility");

    function settext_setVisibility() {
        voltmx.print("### frmPersonDocument_settext_setVisibility settext_setVisibility: " + boolean);
        frmPersonDocument.settext.setVisibility(boolean);
    }
    voltmx.runOnMainThread(settext_setVisibility, []);
}

function frmPersonDocument_init() {
    voltmx.print("#### frmPersonDocument_init");
    //Utility_registerForIdleTimeout();
    frmPersonDocument.onDeviceBack = Global_onDeviceBack;
    frmPersonDocument.lbxDocumentTypeAdditional.centerX = 52 + "%";
    frmPersonDocument.lbxDocumentType.centerX = 52 + "%";
    // RL-384
    frmPersonDocument.lbxDocumentTypeAdditional.expandListItemToParentWidth = true;
    frmPersonDocument.lbxDocumentType.expandListItemToParentWidth = true;
    //
}

function frmPersonDocument_preshow() {
    Analytics_logScreenView("person-document");
    voltmx.print("#### frmPersonDocument_preshow");
    if (Global.vars.documentTypes.length > 0) {
        frmPersonDocument.lbxDocumentType.masterDataMap = [Global.vars.documentTypes, "key", "value"];
        frmPersonDocument_fillDocumentTypeAdditional();
    } else {
        Utility_getPersonDocIdentificationTypes(frmPersonDocument_getPersonDocIdentificationTypes);
    }
    frmPersonDocument.txtDocumentNumber.text = Global.vars.gCasePersons.documentNumber;
    voltmx.print("### frmPersonDocument_preshow Global.vars.gCasePersons.idenDocType: " + Global.vars.gCasePersons.idenDocType);
    voltmx.print("### frmPersonDocument_preshow Global.vars.gCasePersons.idenDocTypeDesc: " + Global.vars.gCasePersons.idenDocTypeDesc);
    frmPersonDocument_setFields();
    //frmPersonDocument_fillFields();
    frmPersonDocument_togglePersonCharacteristics();
}

function frmPersonDocument_setFields() {
    voltmx.print("### frmPersonDocument_setFields persons object Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    voltmx.print("### frmPersonDocument_setFields Global.vars.gCasePersons.idenDocType: " + Global.vars.gCasePersons.idenDocType);
    voltmx.print("### frmPersonDocument_setFields Global.vars.gCasePersons.idenDocTypeDesc: " + Global.vars.gCasePersons.idenDocTypeDesc);
    voltmx.print("### frmPersonDocument_setFields Global.vars.validatePersonDocument: " + Global.vars.validatePersonDocument);
    var docOthers = Global.vars.gCasePersons.idenDocType.toString().startsWith("99");
    var docMandatory = false;
    var index = Global.vars.additionalDocumentTypes.map(function(e) {
        return e.numbervalue;
    }).indexOf(Global.vars.gCasePersons.idenDocType);
    if (index > -1) {
        docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
    }
    voltmx.print("### frmPersonDocument_setFields docMandatory: " + docMandatory);
    if (docOthers === false && Global.vars.gCasePersons.idenDocType != 20 && Global.vars.gCasePersons.idenDocType !== 0) {
        voltmx.print("### frmPersonDocument_setFields IF");
        frmPersonDocument_flcDocumentCountry_setVisibility(true);
        frmPersonDocument.btnDocumentCountry.onClick = null;
        frmPersonDocument.imgDocumentCountryRight.src = "empty.png";
        frmPersonDocument_flcDocumentNumber_setVisibility(true);
        frmPersonDocument_flcDocumentTypeAdditional_setVisibility(false);
        frmPersonDocument_fillDocumentTypeAdditional();
        frmPersonDocument_documentdescription_setVisibility(false);
        frmPersonDocument_getCountryIdentification();
    } else if (docMandatory === true) {
        voltmx.print("### frmPersonDocument_setFields ELSE IF");
        frmPersonDocument_flcDocumentCountry_setVisibility(true);
        frmPersonDocument.btnDocumentCountry.onClick = frmPersonDocument_onclick_btnDocumentCountry;
        frmPersonDocument.imgDocumentCountryRight.src = "arrowrightmini.png";
        frmPersonDocument_flcDocumentNumber_setVisibility(true);
        frmPersonDocument_flcDocumentTypeAdditional_setVisibility(true);
        frmPersonDocument_documentdescription_setVisibility(true);
        frmPersonDocument_getCountryIdentification();
    } else {
        voltmx.print("### frmPersonDocument_setFields ELSE");
        frmPersonDocument_flcDocumentCountry_setVisibility(false);
        frmPersonDocument.btnDocumentCountry.onClick = frmPersonDocument_onclick_btnDocumentCountry;
        frmPersonDocument.imgDocumentCountryRight.src = "arrowrightmini.png";
        frmPersonDocument_flcDocumentNumber_setVisibility(false);
        frmPersonDocument_flcDocumentTypeAdditional_setVisibility(true);
        frmPersonDocument_documentdescription_setVisibility(true);
    }
    //     if (Global.vars.validatePersonDocument === false && docOthers === true){
    //       voltmx.print("### frmPersonDocument_setFields >> frmPersonDocument_fillFields");
    frmPersonDocument_fillFields();
    //     }
}

function frmPersonDocument_clearDocumentNumber() {
    frmPersonDocument.txtDocumentNumber.text = "";
    frmPersonDocument.txtDocumentNumber.setFocus(true);
    Global.vars.gCasePersons.documentNumber = null;
    frmPersonDocument_togglePersonCharacteristics();
}

function frmPersonDocument_fillFields() {
    voltmx.print("### frmPersonDocument_fillFields");
    //voltmx.print("### frmPersonDocument_fillFields Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    voltmx.print("### frmPersonDocument_fillFields CaseData.person: " + JSON.stringify(CaseData.person));
    voltmx.print("### frmPersonDocument_fillFields Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    if (Global.vars.gCasePersons.documentNumber !== undefined && Global.vars.gCasePersons.documentNumber != null && Global.vars.gCasePersons.documentNumber !== "") {
        voltmx.print("### frmPersonDocument_fillFields document number is filled");
        if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType.toString().startsWith("99")) {
            frmPersonDocument.lbxDocumentType.selectedKey = "99";
            if (Global.vars.gCasePersons.idenDocType == 99) {
                frmPersonDocument.lbxDocumentTypeAdditional.selectedKey = "9900";
            } else {
                frmPersonDocument.lbxDocumentTypeAdditional.selectedKey = Global.vars.gCasePersons.idenDocType.toString();
            }
            //frmPersonDocument_onselect_lbxDocumentType();
        } else {
            frmPersonDocument_deleteDocumentDescription();
            if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null) {
                frmPersonDocument.lbxDocumentType.selectedKey = Global.vars.gCasePersons.idenDocType.toString();
            }
        }
        frmPersonDocument.txtDocumentNumber.text = Global.vars.gCasePersons.documentNumber;
        voltmx.print("### frmPersonDocument_fillFields Global.vars.gCasePersons.countryIdenDocDesc: " + Global.vars.gCasePersons.countryIdenDocDesc);
        frmPersonDocument.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
        frmPersonDocument.lblDocumentCountry.skin = lblFieldInfo;
        if (Global.vars.gCasePersons.countryIdenDocDesc === "" && Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && (Global.vars.gCasePersons.idenDocType.toString().startsWith("99") === false)) {
            frmPersonDocument_getCountryIdentification();
        }
    } else {
        voltmx.print("### frmPersonDocument_fillFields no documentnumber");
        if (Global.vars.gCasePersons.countryIdenDocDesc !== undefined && Global.vars.gCasePersons.countryIdenDocDesc != null && Global.vars.gCasePersons.countryIdenDocDesc !== "") {
            frmPersonDocument.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
            frmPersonDocument.lblDocumentCountry.skin = lblFieldInfo;
        }
        if (Global.vars.gCasePersons.idenDocType === undefined || Global.vars.gCasePersons.idenDocType === null || Global.vars.gCasePersons.idenDocType === "") {
            frmPersonDocument.lbxDocumentType.selectedKey = null;
        } else if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType.toString().startsWith("99")) {
            voltmx.print("### frmPersonDocument_fillFields idenDocType startsWith 99");
            frmPersonDocument.lbxDocumentType.selectedKey = "99";
            if (Global.vars.gCasePersons.idenDocType == 99) {
                frmPersonDocument.lbxDocumentTypeAdditional.selectedKey = "9900";
            } else {
                frmPersonDocument.lbxDocumentTypeAdditional.selectedKey = Global.vars.gCasePersons.idenDocType.toString();
            }
            var docMandatory = false;
            var index = Global.vars.additionalDocumentTypes.map(function(e) {
                return e.numbervalue;
            }).indexOf(Global.vars.gCasePersons.idenDocType);
            if (index > -1) {
                docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
            }
            frmPersonDocument_flcDocumentCountry_setVisibility(docMandatory);
            frmPersonDocument_flcDocumentNumber_setVisibility(docMandatory);
            frmPersonDocument_flcDocumentTypeAdditional_setVisibility(true);
            frmPersonDocument_documentdescription_setVisibility(true);
            if (docMandatory === false) {
                Global.vars.gCasePersons.countryIdenDoc = null;
                Global.vars.gCasePersons.countryIdenDocDesc = null;
                frmPersonDocument.lblDocumentCountry.text = voltmx.i18n.getLocalizedString("l_country");
                frmPersonDocument.lblDocumentCountry.skin = lblFieldNotFilled;
                Global.vars.gCasePersons.documentNumber = "";
                frmPersonDocument.txtDocumentNumber.text = "";
            }
        }
    }
    if (frmPersonDocument.documentdescription.isVisible === true) {
        var documentDescription = "";
        for (var p = 0;
            ((CaseData.text) != null) && p < CaseData.text.length; p++) {
            var v = CaseData.text[p];
            if ((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))) { //beschrijving documenten
                voltmx.print("#### frmPersonDocument_fillFields: Finding document description: " + JSON.stringify(v) + " index: " + p);
                documentDescription = v.value.replace("Beschrijving document(en): ", "");
                break;
            }
        }
        voltmx.print("#### frmPersonDocument_fillFields: documentDescription: " + documentDescription);
        if (Global.vars.validatePersonDocument === false && documentDescription === voltmx.i18n.getLocalizedString("l_noDocument")) {
            frmPersonDocument.documentdescription.lblText.text = documentDescription;
            frmPersonDocument.documentdescription.lblText.skin = lblFieldNotFilled;
        } else if (documentDescription === "") {
            frmPersonDocument.documentdescription.lblText.text = "Beschrijving document(en)";
            frmPersonDocument.documentdescription.lblText.skin = lblFieldNotFilled;
        } else {
            frmPersonDocument.documentdescription.lblText.text = documentDescription;
            frmPersonDocument.documentdescription.lblText.skin = lblFieldInfo;
        }
    }
    voltmx.print("### frmPersonDocument_fillFields end");
}

function frmPersonDocument_onselect_lbxDocumentType() {
    voltmx.print("### frmPersonDocument_onselect_lbxDocumentType");
    try {
        voltmx.timer.cancel("selectDoc");
    } catch (err) {}
    if (frmPersonDocument.lbxDocumentType.selectedKeyValue != null && Number(frmPersonDocument.lbxDocumentType.selectedKeyValue[0]) !== -1) {
        if (frmPersonDocument.lbxDocumentType.selectedKeyValue != null) {
            if (Global.vars.gCasePersons.idenDocType !== Number(frmPersonDocument.lbxDocumentType.selectedKeyValue[0])) {
                Global.vars.gCasePersons.documentNumber = "";
                frmPersonDocument.txtDocumentNumber.text = "";
            }
            Global.vars.gCasePersons.idenDocTypeDesc = frmPersonDocument.lbxDocumentType.selectedKeyValue[1];
            Global.vars.gCasePersons.idenDocType = Number(frmPersonDocument.lbxDocumentType.selectedKeyValue[0]);
        }
        voltmx.print("### frmPersonDocument_onselect_lbxDocumentType Global.vars.gCasePersons.idenDocType: " + Global.vars.gCasePersons.idenDocType);
        if (Global.vars.gCasePersons.idenDocType !== 99 && Global.vars.gCasePersons.idenDocType != 20 && Global.vars.gCasePersons.idenDocType !== 0) {
            Global.vars.gCasePersons.countryIdenDoc = 6030;
            Global.vars.gCasePersons.countryIdenDocDesc = "Nederland";
            frmPersonDocument.lblDocumentCountry.text = "Nederland";
            frmPersonDocument_deleteDocumentDescription();
            frmPersonDocument_fillDocumentTypeAdditional();
            frmPersonDocument_flcDocumentCountry_setVisibility(true);
            frmPersonDocument.lblDocumentCountry.skin = lblFieldInfo;
            frmPersonDocument.btnDocumentCountry.onClick = null;
            frmPersonDocument.imgDocumentCountryRight.src = "empty.png";
            frmPersonDocument_flcDocumentNumber_setVisibility(true);
            frmPersonDocument_flcDocumentTypeAdditional_setVisibility(false);
            frmPersonDocument_documentdescription_setVisibility(false);
        } else {
            Global.vars.gCasePersons.countryIdenDoc = null;
            Global.vars.gCasePersons.countryIdenDocDesc = null;
            frmPersonDocument.lblDocumentCountry.text = voltmx.i18n.getLocalizedString("l_country");
            frmPersonDocument.lblDocumentCountry.skin = lblFieldNotFilled;
            Global.vars.gCasePersons.documentNumber = "";
            frmPersonDocument.txtDocumentNumber.text = "";
            frmPersonDocument_flcDocumentCountry_setVisibility(false);
            frmPersonDocument.btnDocumentCountry.onClick = frmPersonDocument_onclick_btnDocumentCountry;
            frmPersonDocument.imgDocumentCountryRight.src = "arrowrightmini.png";
            frmPersonDocument_flcDocumentNumber_setVisibility(false);
            frmPersonDocument_flcDocumentTypeAdditional_setVisibility(true);
            frmPersonDocument_documentdescription_setVisibility(true);
        }
        if (frmPersonDocument.lbxDocumentType.selectedKeyValue != null) {
            Utility_getPersonDocIdentificationTypeById(Number(frmPersonDocument.lbxDocumentType.selectedKeyValue[0]), frmPersonDocument_getPersonDocIdentificationTypeById);
        }
        frmPersonDocument_togglePersonCharacteristics();
    } else if (frmPersonDocument.lbxDocumentType.selectedKeyValue != null && Number(frmPersonDocument.lbxDocumentType.selectedKeyValue[0]) === -1) {
        Global.vars.gCasePersons.countryIdenDoc = null;
        Global.vars.gCasePersons.countryIdenDocDesc = null;
        frmPersonDocument.lblDocumentCountry.text = voltmx.i18n.getLocalizedString("l_country");
        Global.vars.gCasePersons.documentNumber = "";
        frmPersonDocument.txtDocumentNumber.text = "";
        frmPersonDocument_deleteDocumentDescription();
        frmPersonDocument_fillDocumentTypeAdditional();
        frmPersonDocument_flcDocumentCountry_setVisibility(true);
        frmPersonDocument.lblDocumentCountry.skin = lblFieldNotFilled;
        frmPersonDocument.btnDocumentCountry.onClick = null;
        frmPersonDocument.imgDocumentCountryRight.src = "empty.png";
        frmPersonDocument_flcDocumentNumber_setVisibility(true);
        frmPersonDocument_flcDocumentTypeAdditional_setVisibility(false);
        frmPersonDocument_documentdescription_setVisibility(false);
    }
    frmPersonDocument.lbxDocumentType.setFocus(false);
}

function frmPersonDocument_onselect_lbxDocumentTypeAdditional() {
    try {
        voltmx.timer.cancel("selectDoc");
    } catch (err) {}
    Global.vars.gCasePersons.idenDocTypeDesc = frmPersonDocument.lbxDocumentTypeAdditional.selectedKeyValue[1];
    Global.vars.gCasePersons.idenDocType = Number(frmPersonDocument.lbxDocumentTypeAdditional.selectedKeyValue[0]);
    var docMandatory = false;
    var index = Global.vars.additionalDocumentTypes.map(function(e) {
        return e.numbervalue;
    }).indexOf(Global.vars.gCasePersons.idenDocType);
    if (index > -1) {
        docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
    }
    voltmx.print("### frmPersonDocument_onselect_lbxDocumentTypeAdditional docMandatory: " + docMandatory);
    if (docMandatory === true) {
        voltmx.print("### frmPersonDocument_onselect_lbxDocumentTypeAdditional IF");
        frmPersonDocument_flcDocumentCountry_setVisibility(true);
        frmPersonDocument_flcDocumentNumber_setVisibility(true);
        frmPersonDocument_flcDocumentTypeAdditional_setVisibility(true);
        frmPersonDocument_documentdescription_setVisibility(true);
        if (frmPersonDocument.documentdescription.lblText.text === voltmx.i18n.getLocalizedString("l_noDocument")) {
            frmPersonDocument_deleteDocumentDescription();
        }
        //     if (Global.vars.originalPersonDocumentInfo.countryIdenDoc != null){
        //       Global.vars.gCasePersons.countryIdenDoc = Global.vars.originalPersonDocumentInfo.countryIdenDoc;
        //       Global.vars.gCasePersons.countryIdenDocDesc = Global.vars.originalPersonDocumentInfo.countryIdenDocDesc;
        //       Global.vars.gCasePersons.documentNumber = Global.vars.originalPersonDocumentInfo.documentNumber;
        //       frmPersonDocument.txtDocumentNumber.text = Global.vars.originalPersonDocumentInfo.documentNumber;
        //     }
    } else {
        voltmx.print("### frmPersonDocument_onselect_lbxDocumentTypeAdditional ELSE");
        frmPersonDocument_flcDocumentCountry_setVisibility(false);
        frmPersonDocument_flcDocumentNumber_setVisibility(false);
        frmPersonDocument_flcDocumentTypeAdditional_setVisibility(true);
        frmPersonDocument_documentdescription_setVisibility(true);
        Global.vars.gCasePersons.countryIdenDoc = null;
        Global.vars.gCasePersons.countryIdenDocDesc = null;
        frmPersonDocument.lblDocumentCountry.text = voltmx.i18n.getLocalizedString("l_country");
        frmPersonDocument.lblDocumentCountry.skin = lblFieldNotFilled;
        Global.vars.gCasePersons.documentNumber = "";
        frmPersonDocument.txtDocumentNumber.text = "";
    }
    frmPersonDocument_togglePersonCharacteristics();
    frmPersonDocument.lbxDocumentTypeAdditional.setFocus(false);
    voltmx.print("#### frmPersonDocument_onselect_lbxDocumentTypeAdditional frmPersonDocument.txtDocumentNumber.text: " + frmPersonDocument.txtDocumentNumber.text);
    voltmx.print("### frmPersonDocument_onselect_lbxDocumentTypeAdditional Global.vars.gCasePersons.idenDocType: " + Global.vars.gCasePersons.idenDocType);
    voltmx.print("### frmPersonDocument_onselect_lbxDocumentTypeAdditional Global.vars.gCasePersons.idenDocTypeDesc: " + Global.vars.gCasePersons.idenDocTypeDesc);
}

function frmPersonDocument_getPersonDocIdentificationTypeById(result) {
    voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypeById ###");
    voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypeById PersonDocIdentificationTypeIdDescription: " + JSON.stringify(result));
    Global.vars.checkdocument = "";
    if (result.length > 0) {
        var v = result[0];
        var sub1 = "_travel_";
        var sub2 = "_driving_";
        var sub3 = "_alien_";
        if (v.code.indexOf(sub1) !== -1) {
            Global.vars.checkdocument = "TRAVEL_DOCUMENT";
        } else if (v.code.indexOf(sub2) !== -1) {
            Global.vars.checkdocument = "DRIVING_LICENSE";
        } else if (v.code.indexOf(sub3) !== -1) {
            Global.vars.checkdocument = "ALIEN_DOCUMENT";
        }
    }
    voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypeById Global.vars.checkdocument: " + Global.vars.checkdocument);
}

function frmPersonDocument_getPersonDocIdentificationTypes(result) {
    voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypes ###");
    voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypes PersonDocIdentificationTypeIdDescription: " + JSON.stringify(result));
    var documentTypeData = [];
    var selectedkey = null;
    documentTypeData.push({
        key: -1,
        value: voltmx.i18n.getLocalizedString("l_choose")
    });
    Global.vars.checkdocument = "";
    for (var j in result) {
        var v = result[j];
        documentTypeData.push({
            key: v.number_value.toString(),
            value: v.descripton
        });
    }
    Global.vars.documentTypes = documentTypeData;
    voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypes Global.vars.checkdocument: " + Global.vars.checkdocument);
    voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypes documentTypeData: " + JSON.stringify(documentTypeData));
    frmPersonDocument.lbxDocumentType.masterDataMap = [documentTypeData, "key", "value"];
    voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypes frmPersonDocument.lbxDocumentType.masterDataMap: " + JSON.stringify(frmPersonDocument.lbxDocumentType.masterDataMap));
    if (selectedkey != null) {
        voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypes set selectedkey: " + selectedkey);
        frmPersonDocument.lbxDocumentType.selectedKey = selectedkey;
        try {
            voltmx.timer.schedule("selectDoc", frmPersonDocument_onselect_lbxDocumentType, 0.5, false);
        } catch (err) {}
    }
    //fill documenttypeAdditional listbox
    frmPersonDocument_fillDocumentTypeAdditional();
}

function frmPersonDocument_fillDocumentTypeAdditional() {
    voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypes additionalDocumentType: " + JSON.stringify(Global.vars.additionalDocumentType));
    var documentTypeDataAdditional = [];
    var selectedkeyAdditional = null;
    for (var k in Global.vars.additionalDocumentTypes) {
        var x = Global.vars.additionalDocumentTypes[k];
        documentTypeDataAdditional.push({
            key: x.numbervalue.toString(),
            value: x.description
        });
        if (x.description == "Overig") {
            selectedkeyAdditional = x.numbervalue.toString();
        }
    }
    voltmx.print("### frmPersonDocument_getPersonDocIdentificationTypes documentTypeDataAdditional: " + JSON.stringify(documentTypeDataAdditional));
    frmPersonDocument.lbxDocumentTypeAdditional.masterDataMap = [documentTypeDataAdditional, "key", "value"];
    frmPersonDocument.lbxDocumentTypeAdditional.selectedKey = selectedkeyAdditional;
    //  frmPersonDocument_fillFields();
}

function frmPersonDocument_getCountryIdentification() {
    voltmx.print("##### frmPersonDocument_getCountryIdentification ");
    voltmx.print("### frmPersonDocument_getCountryIdentification Current Country: " + Global.vars.gCasePersons.countryIdenDocDesc);
    voltmx.print("### frmPersonDocument_getCountryIdentification Current CountryCode: " + Global.vars.gCasePersons.countryIdenDoc);

    function getCountryIdentificationSuccessCallback(resultcountryid) {
        voltmx.print("##### frmPersonDocument_getCountryIdentification getCountryIdentificationSuccessCallback ");
        if ((resultcountryid.length !== 0)) {
            voltmx.print("### frmPersonDocument_getCountryIdentification resultcountryid: " + JSON.stringify(resultcountryid));
            Global.vars.gCasePersons.countryIdenDocDesc = resultcountryid[0].description;
            frmPersonDocument.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
            frmPersonDocument.lblDocumentCountry.skin = lblFieldInfo;
            voltmx.print("#### frmPersonDocument_getCountryIdentification coutry succes and set ");
        } else {
            voltmx.print("#### frmPersonDocument_getCountryIdentification no results found for Country Info");
        }
        voltmx.print("##### frmPersonDocument_getCountryIdentification getCountryIdentificationSuccessCallback ");
    }

    function getCountryIdentificationErrorCallback(error) {
        voltmx.print("##### frmPersonDocument_getCountryIdentification getCountryIdentificationErrorCallback ");
        voltmx.print("### frmPersonDocument_getCountryIdentification Country Info error: " + error);
    }
    if (Global.vars.gCasePersons.countryIdenDocDesc !== "" && Global.vars.gCasePersons.countryIdenDocDesc != null && Global.vars.gCasePersons.countryIdenDocDesc !== undefined) {
        frmPersonDocument.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
        frmPersonDocument.lblDocumentCountry.skin = lblFieldInfo;
    } else {
        var lCountrywhereClause = "select * from mle_v_country_m where code = '" + Global.vars.gCasePersons.countryIdenDoc + "'";
        lCountrywhereClause = Utility_addLanguageToWhereClause(lCountrywhereClause);
        voltmx.print("### Country clause: " + lCountrywhereClause);
        if (Global.vars.gCasePersons.countryIdenDoc !== undefined || Global.vars.gCasePersons.countryIdenDoc != null || Global.vars.gCasePersons.countryIdenDoc !== "") {
            KNYMobileFabric.OfflineObjects.executeSelectQuery(lCountrywhereClause, getCountryIdentificationSuccessCallback, getCountryIdentificationErrorCallback);
        }
    }
    voltmx.print("##### frmPersonDocument_getCountryIdentification end");
}

function frmPersonDocument_onclick_btnDocumentCountry() {
    Global.vars.personCountryType = "documentcountryfrmPersonDocument";
    voltmx.print("### frmPersonDocument_onclick_btnDocumentCountry " + Global.vars.personCountryType);
    frmPersonCountries.show();
}

function frmPersonDocument_validatePersonDocumentCountry() {
    voltmx.print("### frmPersonDocument_validatePersonDocumentCountry");
    voltmx.print("### frmPersonDocument_validatePersonDocumentCountry text: " + frmPersonDocument.lblDocumentCountry.text);
    var validated = false;
    if (frmPersonDocument.flcDocumentCountry.isVisible === false) {
        validated = true;
    } else if (frmPersonDocument.lblDocumentCountry.text !== "" && frmPersonDocument.lblDocumentCountry.text != null && frmPersonDocument.lblDocumentCountry.text !== voltmx.i18n.getLocalizedString("l_country")) {
        validated = true;
    }
    if (validated === true) {
        frmPersonDocument.flcDocumentCountry.skin = flcFieldEdge;
    } else {
        frmPersonDocument.flcDocumentCountry.skin = flcFieldEdgeRed;
    }
    return validated;
}

function frmPersonDocument_validateOtherDocumentDescription() {
    var validated = false;
    var docMandatory = false;
    var index = Global.vars.additionalDocumentTypes.map(function(e) {
        return e.numbervalue;
    }).indexOf(Global.vars.gCasePersons.idenDocType);
    if (index > -1) {
        docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
    }
    if (frmPersonDocument.documentdescription.isVisible === false || docMandatory === true) {
        validated = true;
    } else if (frmPersonDocument.documentdescription.lblText.text !== "" && frmPersonDocument.documentdescription.lblText.text != null && frmPersonDocument.documentdescription.lblText.text !== "Beschrijving document(en)") {
        validated = true;
    }
    if (validated === true) {
        frmPersonDocument.documentdescription.skin = flcFieldEdge;
    } else {
        frmPersonDocument.documentdescription.skin = flcFieldEdgeRed;
    }
    return validated;
}

function frmPersonDocument_setGlobalsPersonDocument() {
    voltmx.print("### frmPersonDocument_setGlobalsPersonDocument");
    Global.vars.gCasePersons.documentNumber = frmPersonDocument.txtDocumentNumber.text;
    if (frmPersonDocument.flcDocumentTypeAdditional.isVisible === true) {
        Global.vars.gCasePersons.idenDocType = Number(frmPersonDocument.lbxDocumentTypeAdditional.selectedKey);
        Global.vars.gCasePersons.idenDocTypeDesc = frmPersonDocument.lbxDocumentTypeAdditional.selectedKeyValue[1];
    } else {
        if (frmPersonDocument.lbxDocumentType.selectedKey != null) {
            Global.vars.gCasePersons.idenDocType = Number(frmPersonDocument.lbxDocumentType.selectedKey);
            Global.vars.gCasePersons.idenDocTypeDesc = frmPersonDocument.lbxDocumentType.selectedKeyValue[1];
        }
    }
    if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType.toString().startsWith("99") === false) {
        Global.vars.gCasePersons.countryIdenDocDesc = frmPersonDocument.lblDocumentCountry.text;
    }
}

function frmPersonDocument_onTextChangeDocumentNumber() {
    //frmLocation.txtDetailHouseletter.text = frmLocation.txtDetailHouseletter.text;
    var _text = frmPersonDocument.txtDocumentNumber.text;
    voltmx.print("### frmPersonDocument_onTextChangeDocumentNumber start text: " + _text);
    if (_text.length > 0) {
        _text = _text.replace(/[^a-zA-Z0-9]/gi, '');
    }
    voltmx.print("### frmPersonDocument_onTextChangeDocumentNumber text: " + _text);
    frmPersonDocument.txtDocumentNumber.text = _text;
}

function frmPersonDocument_onclick_btnBack() {
    voltmx.print("### frmPersonDocument_onclick_btnBack");
    Global.vars.readIDScanned = false;
    Global.vars.gCasePersons.idenDocType = Global.vars.originalPersonDocumentInfo.idenDocType;
    Global.vars.gCasePersons.idenDocTypeDesc = Global.vars.originalPersonDocumentInfo.idenDocTypeDesc;
    Global.vars.gCasePersons.countryIdenDoc = Global.vars.originalPersonDocumentInfo.countryIdenDoc;
    Global.vars.gCasePersons.countryIdenDocDesc = Global.vars.originalPersonDocumentInfo.countryIdenDocDesc;
    Global.vars.gCasePersons.documentNumber = Global.vars.originalPersonDocumentInfo.documentNumber;
    Global.vars.gCasePersons.documentTypeCheckable = Global.vars.originalPersonDocumentInfo.documentTypeCheckable;
    Global.vars.gCasePersons.documentNumberChecked = Global.vars.originalPersonDocumentInfo.documentNumberChecked;
    Global.vars.gCasePersons.documentNumberValid = Global.vars.originalPersonDocumentInfo.documentNumberValid;
    var loctextindex = null;
    for (var p = 0;
        ((CaseData.text) != null) && p < CaseData.text.length; p++) {
        var v = CaseData.text[p];
        if ((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))) { //beschrijving documenten
            voltmx.print("#### frmPersonDocument_onclick_btnBack: Finding officer present: " + v + " index: " + p);
            loctextindex = p;
            break;
        }
    }
    var laddrecord = CaseData_setNewtext();
    laddrecord.inserted = true;
    laddrecord.edited = true;
    laddrecord.type = 3; //beschrijving documenten
    laddrecord.value = "Beschrijving document(en): " + Global.vars.originalPersonDocumentInfo.documentAdditionalDescription;
    if (loctextindex === null) {
        CaseData.text.splice(0, 0, laddrecord);
    } else {
        CaseData.text.splice(loctextindex, 1, laddrecord);
    }
    voltmx.print("#### frmPersonDocument_onclick_btnBack documentdescription.lblText CaseData.text after: " + JSON.stringify(CaseData.text));
    if (Global.vars.originalPersonDocumentInfo.formToGoBackTo === "frmPersonResult") {
        frmPersonResult.show();
    } else if (Global.vars.originalPersonDocumentInfo.formToGoBackTo === "frmResume") {
        CaseData.person[Global.vars.gCasePersonsIndex] = Global.vars.gCasePersons;
        frmResume.show();
    }
    Global.vars.originalPersonDocumentInfo = {};
    voltmx.print("#### frmPersonDocument_onclick_btnBack Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
}

function frmPersonDocument_onEndEditDocumentNumber() {
    Global.vars.gCasePersons.documentNumber = frmPersonDocument.txtDocumentNumber.text;
    frmPersonDocument_togglePersonCharacteristics();
}

function frmPersonDocument_togglePersonCharacteristics() {
    var isDocValidated = frmPersonDocument_validateDocumentNumber();
    var isDescriptionValidated = frmPersonDocument_validateOtherDocumentDescription();
    var isKindOfDocValidated = frmPersonDocument_validateKindOfDocument();
    var isDocumentCountryValidated = frmPersonDocument_validatePersonDocumentCountry();
    voltmx.print("### frmPersonDocument_togglePersonCharacteristics isDocValidated: " + isDocValidated);
    voltmx.print("### frmPersonDocument_togglePersonCharacteristics isDescriptionValidated: " + isDescriptionValidated);
    voltmx.print("### frmPersonDocument_togglePersonCharacteristics isKindOfDocValidated: " + isKindOfDocValidated);
    voltmx.print("### frmPersonDocument_togglePersonCharacteristics isDocumentCountryValidated: " + isDocumentCountryValidated);
    if (isDocValidated === true && isDescriptionValidated === true && isKindOfDocValidated === true && isDocumentCountryValidated === true) {
        frmPersonDocument_btnDone_setVisibility(true);
    } else {
        frmPersonDocument_btnDone_setVisibility(false);
    }
}

function frmPersonDocument_validateDocumentNumber() {
    voltmx.print("### frmPersonDocument_validateDocumentNumber Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    voltmx.print("### frmPersonDocument_validateDocumentNumber txtDocumentNumber: " + frmPersonDocument.txtDocumentNumber.text);
    var validate = false;
    var docMandatory = false;
    var index = Global.vars.additionalDocumentTypes.map(function(e) {
        return e.numbervalue;
    }).indexOf(Global.vars.gCasePersons.idenDocType);
    if (index > -1) {
        docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
    }
    if (Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType !== undefined) {
        if (docMandatory === true && (frmPersonDocument.txtDocumentNumber.text !== "" && frmPersonDocument.txtDocumentNumber.text != null)) {
            validate = true;
        } else if (Global.vars.gCasePersons.idenDocType.toString().startsWith("99") === true && docMandatory === false) {
            validate = true;
        } else if (Global.vars.gCasePersons.idenDocType != null && Global.vars.gCasePersons.idenDocType !== 99 && Global.vars.gCasePersons.idenDocType != 20 && Global.vars.gCasePersons.idenDocType !== 0 && (frmPersonDocument.txtDocumentNumber.text !== "" && frmPersonDocument.txtDocumentNumber.text != null)) {
            validate = true;
        } else if (Global.vars.gCasePersons.idenDocType !== undefined && Global.vars.gCasePersons.idenDocType != null && (Global.vars.gCasePersons.idenDocType === 99 || Global.vars.gCasePersons.idenDocType == 20 || Global.vars.gCasePersons.idenDocType === 0)) {
            validate = true;
        }
    } else if (frmPersonDocument.txtDocumentNumber.text !== undefined && frmPersonDocument.txtDocumentNumber.text != null && frmPersonDocument.txtDocumentNumber.text !== "") {
        validate = true;
    }
    if (validate === true) {
        frmPersonDocument.flcDocumentNumber.skin = flcFieldEdge;
    } else {
        frmPersonDocument.flcDocumentNumber.skin = flcFieldEdgeRed;
    }
    return validate;
}

function frmPersonDocument_validateKindOfDocument() {
    voltmx.print("### frmPersonDocument_validateKindOfDocument Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    var validate = false;
    if (Global.vars.gCasePersons.idenDocType != null) {
        validate = true;
    }
    if (Global.vars.gCasePersons.idenDocType === undefined || Global.vars.gCasePersons.idenDocType === null || Global.vars.gCasePersons.idenDocType === "") {
        frmPersonDocument.lbxDocumentType.selectedKey = null;
    }
    if (validate === true) {
        frmPersonDocument.flcDocumentType.skin = flcFieldEdge;
    } else {
        frmPersonDocument.flcDocumentType.skin = flcFieldEdgeRed;
    }
    return validate;
}

function frmPersonDocument_checkIfValidDocument() {
    voltmx.print("##### frmPersonDocument_checkIfValidDocument");
    if (frmPersonDocument.txtDocumentNumber.text != null && frmPersonDocument.txtDocumentNumber.text !== "") {
        service_GetPersonInfoValidateDocument(frmPersonDocument.txtDocumentNumber.text, frmPersonDocument_checkIfValidDocumentcallback);
    }
}

function frmPersonDocument_checkIfValidDocumentcallback(result) {
    voltmx.print("##### frmPersonDocument_checkIfValidDocumentcallback: " + JSON.stringify(result));
    Global.vars.indDcocumentChecked = true;
    voltmx.application.dismissLoadingScreen();
    if (result.valid === "false") {
        Global.vars.indDcocumentValidated = false;
        alert(result.resultDescription);
    } else if (result.valid === "true") {
        Global.vars.indDcocumentValidated = true;
        alert(result.resultDescription);
    }
}

function frmPersonDocument_showSetText() {
    voltmx.print("### frmPersonDocument_showSetText");
    try {
        //deactivate footer and mainpage
        frmPersonDocument.settext.textarea.TextAreaText.text = frmPersonDocument.documentdescription.lblText.text;
        frmPersonDocument.settext.textarea.TextAreaText.maxTextLength = 200;
        if (frmPersonDocument.documentdescription.lblText.text == "Beschrijving document(en)") {
            frmPersonDocument.settext.textarea.TextAreaText.text = "";
        }
        frmPersonDocument.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmPersonDocument_settext_setVisibility(true);
        frmPersonDocument_showSetText_preAnim();
        frmPersonDocument_showSetText_animationStart();
    } catch (e) {
        voltmx.print("### frmPersonDocument_showSetText error: " + JSON.stringify(e));
    }
}

function frmPersonDocument_showSetText_preAnim() {
    try {
        voltmx.print("### frmPersonDocument_showSetText_preAnim");
        var trans1 = voltmx.ui.makeAffineTransform();
        trans1.scale(0.1, 0.1);
        var trans2 = voltmx.ui.makeAffineTransform();
        trans2.translate(0, 10);
        //frmPersonDocument.settext.flcDetail.transform = trans1;
        //frmPersonDocument.settext.imgPopupLogo1.transform = trans1;
        //frmPersonDocument.settext.flcTextDetails.transform = trans1;
    } catch (e) {
        voltmx.print("### frmPersonDocument_showSetText_preAnim error: " + JSON.stringify(e));
    }
}

function frmPersonDocument_showSetText_arrangeWidgets() {
    try {
        voltmx.print("### frmPersonDocument_showSetText_arrangeWidgets");
        //popup fields
        frmPersonDocument.settext.imgPopupLogo1.isVisible = false;
        frmPersonDocument.settext.flcDetail.isVisible = false;
        frmPersonDocument.settext.flcTextDetails.isVisible = false;
        frmPersonDocument.settext.lbl1.isVisible = false;
        frmPersonDocument.settext.flcFooterSetText.isVisible = false;
        frmPersonDocument_settext_setVisibility(false);
        frmPersonDocument.settext.flcFooterSetText.setEnabled(false);
        frmPersonDocument.settext.forceLayout();
    } catch (e) {
        voltmx.print("### frmPersonDocument_showSetText_preAnim error: " + JSON.stringify(e));
    }
}

function frmPersonDocument_showSetText_animationStart(eventobject) {
    try {
        voltmx.print("### frmPersonDocument_showSetText_animationStart");
        frmPersonDocument_settext_setVisibility(true);
        frmPersonDocument.settext.flcDetail.isVisible = true;
        var trans100 = voltmx.ui.makeAffineTransform();
        trans100.scale(1, 1);
        frmPersonDocument.settext.flcDetail.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans100,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.25
        }, {
            "animationEnd": voltmx.runOnMainThread(frmPersonDocument_showSetText_animLogo)
        });
    } catch (e) {
        voltmx.print("### frmPersonDocument_showSetText_animationStart error: " + JSON.stringify(e));
    }
}

function frmPersonDocument_showSetText_animLogo() {
    try {
        voltmx.print("### frmPersonDocument_showSetText_animLogo");
        //     var trans = voltmx.ui.makeAffineTransform();
        //     trans.scale(1.2, 1.2);
        //     frmPersonDocument.settext.imgPopupLogo1.animate(
        //       voltmx.ui.createAnimation({
        //         "100": {
        //           "anchorPoint": {
        //             "x": 0.5,
        //             "y": 0.5
        //           },
        //           "stepConfig": {
        //             "timingFunction": voltmx.anim.EASE
        //           },
        //           "transform": trans,
        //         }
        //       }), {
        //         "delay": 0,
        //         "iterationCount": 1,
        //         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        //         "duration": 0.25
        //       }, {
        //         "animationEnd": function (){
        //           frmPersonDocument_showSetText_animOtherWidgets(frmPersonDocument.settext.flcTextDetails);
        //           frmPersonDocument_showSetText_animOtherWidgets(frmPersonDocument.settext.lbl1);
        //           frmPersonDocument_showSetText_animLogoBack();
        //         }
        //       });
        frmPersonDocument_showSetText_animOtherWidgets(frmPersonDocument.settext.flcTextDetails);
        frmPersonDocument_showSetText_animOtherWidgets(frmPersonDocument.settext.lbl1);
        frmPersonDocument.settext.imgPopupLogo1.isVisible = true;
        frmPersonDocument.forceLayout();
    } catch (e) {
        voltmx.print("### frmPersonDocument_showSetText_animLogo error: " + JSON.stringify(e));
    }
}

function frmPersonDocument_showSetText_animOtherWidgets(widget) {
    try {
        voltmx.print("### frmPersonDocument_showSetText_animOtherWidgets");
        var trans1 = voltmx.ui.makeAffineTransform();
        trans1.translate(1, 1);
        //trans1.translate(1, -10);
        widget.isVisible = true;
        widget.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans1,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.25
        }, {
            "animationEnd": function() {}
        });
        frmPersonDocument.settext.flcTextDetails.isVisible = true;
        frmPersonDocument.settext.lbl1.isVisible = true;
        frmPersonDocument.settext.flcFooterSetText.isVisible = true;
        frmPersonDocument.settext.flcFooterSetText.setEnabled(true);
        frmPersonDocument.forceLayout();
    } catch (e) {
        voltmx.print("### frmPersonDocument_showSetText_animOtherWidgets error: " + JSON.stringify(e));
    }
}

function frmPersonDocument_showSetText_animLogoBack() {
    try {
        voltmx.print("### frmPersonDocument_showSetText_animLogoBack");
        var trans = voltmx.ui.makeAffineTransform();
        trans.scale(1, 1);
        frmPersonDocument.settext.imgPopupLogo1.animate(voltmx.ui.createAnimation({
            "100": {
                "anchorPoint": {
                    "x": 0.5,
                    "y": 0.5
                },
                "stepConfig": {
                    "timingFunction": voltmx.anim.EASE
                },
                "transform": trans,
            }
        }), {
            "delay": 0,
            "iterationCount": 1,
            "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
            "duration": 0.15
        }, {
            "animationEnd": function() {}
        });
        frmPersonDocument.forceLayout();
    } catch (e) {
        voltmx.print("### frmPersonDocument_showSetText_animLogoBack error: " + JSON.stringify(e));
    }
}

function frmPersonDocument_hideSetText() {
    //activate footer and mainpage
    frmPersonDocument.flcMainPage.setEnabled(true);
    voltmx.print("### flcMainPage enabled");
    frmPersonDocument_contentOffset();
    frmPersonDocument_settext_setVisibility(false);
    frmPersonDocument_togglePersonCharacteristics();
}

function frmPersonDocument_setTextDone() {
    voltmx.print("#### frmPersonDocument_setTextDone CaseData text before: " + JSON.stringify(CaseData.text));
    if (frmPersonDocument.settext.textarea.TextAreaText.text != null && frmPersonDocument.settext.textarea.TextAreaText.text !== "") {
        frmPersonDocument.documentdescription.lblText.text = frmPersonDocument.settext.textarea.TextAreaText.text;
        frmPersonDocument.documentdescription.lblText.skin = lblFieldInfo;
    } else {
        frmPersonDocument.documentdescription.lblText.text = "Beschrijving document(en)";
        frmPersonDocument.documentdescription.lblText.skin = lblFieldNotFilled;
    }
    voltmx.print("#### frmPersonDocument_setTextDone Text: " + frmPersonDocument.documentdescription.lblText.text);
    if (frmPersonDocument.documentdescription.lblText.text != null && frmPersonDocument.documentdescription.lblText.text !== "" && frmPersonDocument.documentdescription.lblText.text !== "Beschrijving document(en)") {
        // add record to CaseData.text
        var loctextindex = null;
        for (var p = 0;
            ((CaseData.text) != null) && p < CaseData.text.length; p++) {
            var v = CaseData.text[p];
            if ((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))) { //beschrijving documenten
                voltmx.print("#### frmPersonDocument_setTextDone: Finding officer present: " + v + " index: " + p);
                loctextindex = p;
                break;
            }
        }
        var laddrecord = CaseData_setNewtext();
        laddrecord.inserted = true;
        laddrecord.edited = true;
        laddrecord.type = 3; //beschrijving documenten
        laddrecord.value = "Beschrijving document(en): " + frmPersonDocument.documentdescription.lblText.text;
        if (loctextindex === null) {
            CaseData.text.splice(0, 0, laddrecord);
        } else {
            CaseData.text.splice(loctextindex, 1, laddrecord);
        }
        voltmx.print("#### frmPersonDocument_setTextDone documentdescription.lblText CaseData.text after: " + JSON.stringify(CaseData.text));
    }
    frmPersonDocument_hideSetText();
    frmPersonDocument_togglePersonCharacteristics();
}

function frmPersonDocument_clearTextAreaText() {
    frmPersonDocument.settext.textarea.TextAreaText.text = "";
    frmPersonDocument.settext.textarea.TextAreaText.setFocus(true);
}

function frmPersonDocument_deleteDocumentDescription() {
    voltmx.print("#### frmPersonDocument_deleteDocumentDescription CaseData text before: " + JSON.stringify(CaseData.text));
    frmPersonDocument.settext.textarea.TextAreaText.text = "";
    frmPersonDocument.documentdescription.lblText.text = "Beschrijving document(en)";
    frmPersonDocument.documentdescription.lblText.skin = lblFieldNotFilled;
    for (var p = 0;
        ((CaseData.text) != null) && p < CaseData.text.length; p++) {
        var v = CaseData.text[p];
        if ((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))) { //beschrijving documenten
            CaseData.text.splice(p, 1);
        }
    }
    voltmx.print("#### frmPersonDocument_setTextDone documentdescription.lblText CaseData.text after: " + JSON.stringify(CaseData.text));
    frmPersonDocument_togglePersonCharacteristics();
}

function frmPersonDocument_silentDocumentCheck() {
    voltmx.print("#### frmPersonDocument_silentDocumentCheck Global.vars.checkdocument: " + Global.vars.checkdocument);
    if (Global.vars.checkdocument !== "") {
        if (frmPersonDocument.txtDocumentNumber.text != null && frmPersonDocument.txtDocumentNumber.text !== "") {
            Global.vars.documentTypeCheckable = Global.vars.checkdocument;
            Global.vars.gCasePersons.documentTypeCheckable = Global.vars.documentTypeCheckable;
            service_GetPersonInfoValidateDocumentSilent(frmPersonDocument.txtDocumentNumber.text, frmPersonDocument_checkIfValidDocumentcallbackSilent, frmPersonDocument_checkIfValidDocumenterrorcallbackSilent);
        }
    }
}

function frmPersonDocument_checkIfValidDocumentcallbackSilent(result) {
    voltmx.print("### frmPersonDocument_checkIfValidDocumentcallbackSilent: " + JSON.stringify(result));
    Global.vars.indDcocumentChecked = true;
    Global.vars.gCasePersons.documentNumberChecked = Global.vars.indDcocumentChecked;
    Global.vars.gCasePersons.documentNumberValid = result.valid;
    voltmx.application.dismissLoadingScreen();
    if (result.valid === "false") {
        Global.vars.indDcocumentValidated = false;
    } else if (result.valid === "true") {
        Global.vars.indDcocumentValidated = true;
    }
}

function frmPersonDocument_checkIfValidDocumenterrorcallbackSilent(error) {
    voltmx.print("##### frmPersonDocument_checkIfValidDocumenterrorcallbackSilent: " + JSON.stringify(error));
    voltmx.application.dismissLoadingScreen();
}

function frmPersonDocument_onclick_btnDone() {
    voltmx.print("### frmPersonDocument_onclick_btnDone Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    Global.vars.gCasePersons.documentNumber = frmPersonDocument.txtDocumentNumber.text;
    if (Global.vars.originalPersonDocumentInfo.formToGoBackTo === "frmPersonResult") {
        frmPersonResult.show();
    } else if (Global.vars.originalPersonDocumentInfo.formToGoBackTo === "frmResume") {
        CaseData.person[Global.vars.gCasePersonsIndex] = Global.vars.gCasePersons;
        frmResume.show();
    }
    Global.vars.originalPersonDocumentInfo = {};
}