function Utility_clearOptions() {
    voltmx.print("### Utility_clearOptions");
    CaseData.option = [];
    Global.vars.optionvariablesText = "";
    Global.vars.optionvariablesSet = false;
    Global.vars.variableTimeField = "";
    Global.vars.selectedOption = {};
}

function Utility_clearPayment() {
    voltmx.print("### Utility_clearPayment");
    CaseData.payment = CaseData_setNewPayment();
    CaseData.caseinfo.onStreetPaymentMethodValue = null;
    CaseData.caseinfo.onStreetPaymentMethodKey = null;
    CaseData.caseinfo.indPayed = false;
}

function Utility_resetToOriginalCaseType() {
    voltmx.print("### Utility_resetToOriginalCaseType");
    if (Global.vars.originalCaseInfo !== undefined) {
        CaseData.caseinfo.caseTypeCategory = Global.vars.originalCaseInfo.caseTypeCategory;
        CaseData.caseinfo.caseTypeCategoryId = Global.vars.originalCaseInfo.caseTypeCategoryId;
        if (Global.vars.originalCaseInfo.caseTypeCategoryDescription !== undefined) {
            CaseData.caseinfo.caseTypeCategoryDescription = Global.vars.originalCaseInfo.caseTypeCategoryDescription;
        }
        CaseData.caseinfo.caseType = Global.vars.originalCaseInfo.caseType;
        CaseData.caseinfo.caseTypeId = Global.vars.originalCaseInfo.caseTypeId;
        if (Global.vars.originalCaseInfo.caseTypeDescription !== undefined) {
            CaseData.caseinfo.caseTypeDescription = Global.vars.originalCaseInfo.caseTypeDescription;
        }
    }
}
// compress function available in secure save
function Utility_decompress(string) {
    var newString = "",
        char,
        codeStr,
        firstCharCode,
        lastCharCode;
    string = decodeURIComponent(escape(atob(string)));
    for (var i = 0; i < string.length; i++) {
        char = string.charCodeAt(i);
        if (char > 132) {
            codeStr = char.toString(10);
            firstCharCode = parseInt(codeStr.substring(0, codeStr.length - 2), 10);
            lastCharCode = parseInt(codeStr.substring(codeStr.length - 2, codeStr.length), 10) + 31;
            newString += String.fromCharCode(firstCharCode) + String.fromCharCode(lastCharCode);
        } else {
            newString += string.charAt(i);
        }
    }
    return newString;
}

function Utility_hoursOrDaysBetween(datetime) {
    voltmx.print("### datetime input: " + datetime);
    var hoursordaysbetween = 0;
    //calculate hours between
    var datenow = new Date();
    var hours = Math.floor((datenow - datetime) / 36e5);
    voltmx.print("### hours between: " + hours);
    if (hours > 23) {
        //calculate days between
        var days = Math.floor((datenow - datetime) / (1000 * 60 * 60 * 24));
        hoursordaysbetween = days + " " + voltmx.i18n.getLocalizedString("i_daysago");
    } else {
        hoursordaysbetween = hours + " " + voltmx.i18n.getLocalizedString("i_hoursago");
    }
    return hoursordaysbetween;
}

function Utility_resetPerson() {
    voltmx.print("### Utility_resetPerson");
    CaseData.person = [];
    Global.vars.personInputMethod = "";
    Global.vars.gCasePersons = {};
    Global.vars.gCasePersonsIndex = 0;
    CaseData.offence.person = false;
    voltmx.print("### Utility_resetPerson text pre: " + JSON.stringify(CaseData.text));
    var array = JSON.parse(JSON.stringify(CaseData.text));
    if (Array.isArray(array)) {
        const filtered = array.filter(item => ![3].includes(item.type));
        CaseData.text = filtered.length === 0 ? [] : filtered;
    } else {
        CaseData.text = [];
    }
    voltmx.print("### Utility_resetPerson text post: " + JSON.stringify(CaseData.text));
}

function Utility_resetOffencePerson() {
    voltmx.print("### Utility_resetOffencePerson CaseData.offence.person: " + CaseData.offence.person);
    var _noCautionPersonCaseType = Utility_getIndexIfObjWithAttr(Global.vars.noCautionPersonCaseType, "caseType", CaseData.caseinfo.caseType);
    if (CaseData.offence.person === false || _noCautionPersonCaseType !== -1) {
        voltmx.print("### Utility_resetOffencePerson text pre: " + JSON.stringify(CaseData.text));
        CaseData.offence.rightsRead = null;
        CaseData.offence.offenceCommunicated = null;
        CaseData.offence.legalAssistCommunicated = null;
        CaseData.offence.usesLegalAssistance = null;
        CaseData.offence.declinedLegalAssistance = null;
        CaseData.offence.interpreterCommunicated = null;
        CaseData.offence.usesInterpreter = null;
        CaseData.offence.interpreter = null;
        CaseData.offence.translationLanguage = null;
        CaseData.offence.translationLanguageDesc = null;
        CaseData.offence.statementEdited = false;
        CaseData.offence.statementKey = null;
        CaseData.offence.statementText = null;
        CaseData.offence.statementNoPledge = null;
        CaseData.offence.offenceCommunicatedNoStatement = null;
        CaseData.offence.execByPartner = false;
        voltmx.print("### Utility_resetOffencePerson CaseData.offence: " + JSON.stringify(CaseData.offence));
        var array = JSON.parse(JSON.stringify(CaseData.text));
        if (Array.isArray(array)) {
            const filtered = array.filter(item => ![20, 2].includes(item.type));
            CaseData.text = filtered.length === 0 ? [] : filtered;
        } else {
            CaseData.text = [];
        }
        voltmx.print("### Utility_resetOffencePerson text post: " + JSON.stringify(CaseData.text));
    }
}

function Utility_checkOffenceTypeCode() {
    voltmx.print("### Utility_checkOffenceTypeCode");
    voltmx.print("### Utility_checkOffenceTypeCode CaseData.offence.offenceTypeCode: " + CaseData.offence.offenceTypeCode);
    if (CaseData.offence.offenceTypeCode == "1" || CaseData.offence.offenceTypeCode == "10") {
        CaseData.offence.legalAssistCommunicated = null;
        CaseData.offence.usesLegalAssistance = null;
        CaseData.offence.declinedLegalAssistance = null;
        Global.vars.legalAssistCommunicated = null;
        Global.vars.usesLegalAssistance = null;
        Global.vars.declinedLegalAssistance = null;
    }
    if (CaseData.offence.offenceTypeCode == "10") {
        CaseData.offence.interpreterCommunicated = Global.vars.interpreterCommunicated = false;
        CaseData.offence.usesInterpreter = Global.vars.usesInterpreter = false;
        CaseData.offence.interpreter = null;
        Global.vars.interpreterCommunicated = false;
        Global.vars.usesInterpreter = false;
        Global.vars.interpreter = null;
    }
}

function Utility_preserveOffenceKeepStatement() {
    voltmx.print("### Utility_preserveOffenceKeepStatement ###");
    if (Global.vars.preserve === [] || Utility_isEmptyObject(Global.vars.preserve) === true) {
        voltmx.print("### Utility_preserveOffenceKeepStatement continue ###");
        // preserve offence data
        Global.vars.preserve["offence"] = JSON.parse(JSON.stringify(CaseData.offence));
        Global.vars.preserve["option"] = JSON.parse(JSON.stringify(CaseData.option));
        var offence = {
            offenceCode: null,
            //code of the offence
            offenceDescription: null,
            //Offense Description
            offenceShortDescription: null,
            //Offense Short Description
            inserted: Global.vars.preserve.offence.inserted,
            //indication that offence is inserted
            edited: Global.vars.preserve.offence.edited,
            //indication that offence is edited
            validated: Global.vars.preserve.offence.validated,
            //indication that offence is validated
            person: Global.vars.preserve.offence.person,
            //indication that a person is linked to the offence
            vehicle: Global.vars.preserve.offence.vehicle,
            //indication that a vehicle is linked to the offence
            caseId: Global.vars.preserve.offence.caseId,
            //Unique GUID of the case
            rightsRead: Global.vars.preserve.offence.offenceCommunicated,
            //indication that a rights are read to a person
            offenceCommunicated: Global.vars.preserve.offence.offenceCommunicated,
            //indication that a offence is communicated to a person
            legalAssistCommunicated: Global.vars.preserve.offence.legalAssistCommunicated,
            //indication that legal assistance is communicated to a person
            usesLegalAssistance: Global.vars.preserve.offence.usesLegalAssistance,
            //indication that a person is using legal assistance
            declinedLegalAssistance: Global.vars.preserve.offence.declinedLegalAssistance,
            //type: boolean description: if a person declined legal assistance, did you communicate chance to repeal
            interpreterCommunicated: Global.vars.preserve.offence.interpreterCommunicated,
            //indication that interpretation is communicated to a person
            usesInterpreter: Global.vars.preserve.offence.usesInterpreter,
            //indication that a person is using interpretation
            interpreter: Global.vars.preserve.offence.interpreter,
            //interprator
            translationLanguage: Global.vars.preserve.offence.translationLanguage,
            //languagecode of the translation
            translationLanguageDesc: Global.vars.preserve.offence.translationLanguageDesc,
            //description of language of the translation
            statementEdited: Global.vars.preserve.offence.statementEdited,
            //indication if the statement is edited
            statementKey: Global.vars.preserve.offence.statementKey,
            //string of the keyvalue chosen in the statement listbox
            statementText: Global.vars.preserve.offence.statementText,
            //type: string description: the statement text (type 1)
            statementNoPledge: Global.vars.preserve.offence.statementNoPledge,
            //type: string description: statement why there is no pledge (Cautie Nee) (type 20 text)
            offenceCommunicatedNoStatement: Global.vars.preserve.offence.offenceCommunicatedNoStatement,
            //type: string description: statement why there is no offence comunicated (Reden voor het niet geven van mededeling) (type 20 text)
            execByPartner: Global.vars.preserve.offence.execByPartner,
            //indication if the statement is hearded by the second officer
            countryLicense: Global.vars.preserve.offence.countryLicense,
            //country code of the license plate
            identNumber: Global.vars.preserve.offence.identNumber,
            //string of the license plate number
            app_offenceModus: null,
            //offence modus
            apvRequired: false,
            //indication if apv is required
            apvCode: null,
            //code of the apv
            apvDesc: null,
            //description of the apv
            apvOffenceCode: null,
            //code of the offence
            apvOffenceDesc: null,
            //description of the apv offence
            apvNumber: null,
            //number of the apv
            offenceTypeCode: null,
            //code of the offence type
            optionUsage: "",
            //indication if option is used example 1,1
            offenceCatCode: null,
            //code of the offence categorie
            offenceCatSuspects: null,
            //code of the offence categorie suspects
            offenceCatDesc: null,
            //description of the offence categorie
            amount: null,
            //amount of the offence
            amountTax: null,
            //tax amount of the offence
            amountExtra: null,
            //extra amount of the offence
            amountDisplay: null,
            //display of the amount of the offence
            amountTaxDisplay: null,
            //display of the tax amount of the offence
            amountExtraDisplay: null,
            //display of the extra amount of the offence
            ageCatCode: null,
            //age category code for the person linked to the offence
            sanctionType: null,
            //sanction type
            sanctionPartType: null,
            //sanction part type
            sanctionPartUnit: null,
            //sanction part unit
            ticketTypeId: null
                //the ttt_id for the offence, type integer int32
        };
        voltmx.print("### Utility_preserveOffenceKeepStatement Global.vars.preserve.offence: " + JSON.stringify(Global.vars.preserve.offence));
        voltmx.print("### Utility_preserveOffenceKeepStatement offence: " + JSON.stringify(offence));
        CaseData.offence = JSON.parse(JSON.stringify(offence));
        CaseData.option = [];
    }
}

function Utility_restorePreservedOffence() {
    voltmx.print("### Utility_restorePreservedOffence ###");
    if (Global.vars.preserve.offence != null && CaseData.offence.offenceCode === null && Global.vars.preserve.offence.offenceCode != null) {
        // preserve offence data
        if (Utility_isEmptyObject(Global.vars.preserve.option) === false && Global.vars.preserve.option != null) {
            CaseData.option = JSON.parse(JSON.stringify(Global.vars.preserve.option));
        }
        const offence = JSON.parse(JSON.stringify(CaseData.offence));
        var restoredOffence = {
            offenceCode: Global.vars.preserve.offence.offenceCode,
            //code of the offence
            offenceDescription: Global.vars.preserve.offence.offenceDescription,
            //Offense Description
            offenceShortDescription: Global.vars.preserve.offence.offenceShortDescription,
            //Offense Short Description
            inserted: offence.inserted,
            //indication that offence is inserted
            edited: offence.edited,
            //indication that offence is edited
            validated: offence.validated,
            //indication that offence is validated
            person: offence.person,
            //indication that a person is linked to the offence
            vehicle: offence.vehicle,
            //indication that a vehicle is linked to the offence
            caseId: offence.caseId,
            //Unique GUID of the case
            rightsRead: offence.offenceCommunicated,
            //indication that a rights are read to a person
            offenceCommunicated: offence.offenceCommunicated,
            //indication that a offence is communicated to a person
            legalAssistCommunicated: offence.legalAssistCommunicated,
            //indication that legal assistance is communicated to a person
            usesLegalAssistance: offence.usesLegalAssistance,
            //indication that a person is using legal assistance
            declinedLegalAssistance: offence.declinedLegalAssistance,
            //type: boolean description: if a person declined legal assistance, did you communicate chance to repeal
            interpreterCommunicated: offence.interpreterCommunicated,
            //indication that interpretation is communicated to a person
            usesInterpreter: offence.usesInterpreter,
            //indication that a person is using interpretation
            interpreter: offence.interpreter,
            //interprator
            translationLanguage: offence.translationLanguage,
            //languagecode of the translation
            translationLanguageDesc: offence.translationLanguageDesc,
            //description of language of the translation
            statementEdited: offence.statementEdited,
            //indication if the statement is edited
            statementKey: offence.statementKey,
            //string of the keyvalue chosen in the statement listbox
            statementText: offence.statementText,
            //type: string description: the statement text (type 1)
            statementNoPledge: offence.statementNoPledge,
            //type: string description: statement why there is no pledge (Cautie Nee) (type 20 text)
            offenceCommunicatedNoStatement: offence.offenceCommunicatedNoStatement,
            //type: string description: statement why there is no offence comunicated (Reden voor het niet geven van mededeling) (type 20 text)
            execByPartner: offence.execByPartner,
            //indication if the statement is hearded by the second officer
            countryLicense: offence.countryLicense,
            //country code of the license plate
            identNumber: offence.identNumber,
            //string of the license plate number
            appoffenceModus: Global.vars.preserve.offence.appoffenceModus,
            //offence modus
            apvRequired: Global.vars.preserve.offence.apvRequired,
            //indication if apv is required
            apvCode: Global.vars.preserve.offence.apvCode,
            //code of the apv
            apvDesc: Global.vars.preserve.offence.apvDesc,
            //description of the apv
            apvOffenceCode: Global.vars.preserve.offence.apvOffenceCode,
            //code of the offence
            apvOffenceDesc: Global.vars.preserve.offence.apvOffenceDesc,
            //description of the apv offence
            apvNumber: Global.vars.preserve.offence.apvNumber,
            //number of the apv
            offenceTypeCode: Global.vars.preserve.offence.offenceTypeCode,
            //code of the offence type
            optionUsage: Global.vars.preserve.offence.optionUsage,
            //indication if option is used example 1,1
            offenceCatCode: Global.vars.preserve.offence.offenceCatCode,
            //code of the offence categorie
            offenceCatSuspects: Global.vars.preserve.offence.offenceCatSuspects,
            //code of the offence categorie suspects
            offenceCatDesc: Global.vars.preserve.offence.offenceCatDesc,
            //description of the offence categorie
            amount: Global.vars.preserve.offence.amount,
            //amount of the offence
            amountTax: Global.vars.preserve.offence.amountTax,
            //tax amount of the offence
            amountExtra: Global.vars.preserve.offence.amountExtra,
            //extra amount of the offence
            amountDisplay: Global.vars.preserve.offence.amountDisplay,
            //display of the amount of the offence
            amountTaxDisplay: Global.vars.preserve.offence.amountTaxDisplay,
            //display of the tax amount of the offence
            amountExtraDisplay: Global.vars.preserve.offence.amountExtraDisplay,
            //display of the extra amount of the offence
            ageCatCode: Global.vars.preserve.offence.ageCatCode,
            //age category code for the person linked to the offence
            sanctionType: Global.vars.preserve.offence.sanctionType,
            //sanction type
            sanctionPartType: Global.vars.preserve.offence.sanctionPartType,
            //sanction part type
            sanctionPartUnit: Global.vars.preserve.offence.sanctionPartUnit,
            //sanction part unit
            ticketTypeId: Global.vars.preserve.offence.ticketTypeId
                //the ttt_id for the offence, type integer int32
        };
        Global.vars.preserve = [];
        voltmx.print("### Utility_restorePreservedOffence restoredOffence: " + JSON.stringify(restoredOffence));
        CaseData.offence = JSON.parse(JSON.stringify(restoredOffence));
    }
}

function Utility_convertStringToBase64(data) {
    var b64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    var o1,
        o2,
        o3,
        h1,
        h2,
        h3,
        h4,
        bits,
        i = 0,
        ac = 0,
        enc = "",
        tmp_arr = [];
    if (!data) {
        return data;
    }
    do {
        // pack three octets into four hexets
        o1 = data.charCodeAt(i++);
        o2 = data.charCodeAt(i++);
        o3 = data.charCodeAt(i++);
        bits = (o1 << 16) | (o2 << 8) | o3;
        h1 = (bits >> 18) & 0x3f;
        h2 = (bits >> 12) & 0x3f;
        h3 = (bits >> 6) & 0x3f;
        h4 = bits & 0x3f;
        // use hexets to index into b64, and append result to encoded string
        tmp_arr[ac++] = b64.charAt(h1) + b64.charAt(h2) + b64.charAt(h3) + b64.charAt(h4);
    } while (i < data.length);
    enc = tmp_arr.join("");
    var r = data.length % 3;
    return (r ? enc.slice(0, r - 3) : enc) + "===".slice(r || 3);
}

function Utility_arrayBufferToBase64(buffer) {
    var binary = "";
    var bytes = new Uint8Array(buffer);
    var len = bytes.byteLength;
    for (var i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}

function Utility_getDateTimeString(inputDateTime) {
    var dateTime = inputDateTime;
    if (dateTime === null) {
        dateTime = voltmx.os.date("*t");
    }
    voltmx.print("### Utility_getDateTimeString table: " + JSON.stringify(dateTime));
    var lDateTime = "";
    voltmx.print("### Utility_getDateTimeString: " + lDateTime);
    lDateTime = dateTime.year + "-" + dateTime.month.toString().lpad("0", 2) + "-" + dateTime.day.toString().lpad("0", 2) + " " + dateTime.hour.toString().lpad("0", 2) + ":" + dateTime.min.toString().lpad("0", 2) + ":" + dateTime.sec.toString().lpad("0", 2);
    voltmx.print("### Utility_getDateTimeString: " + lDateTime);
    return lDateTime;
}

function Utility_checkInput(input, kindofinput) {
    voltmx.print("#### kindofinput: " + kindofinput);
    // only contains A-Z and 0-9 and between 2 and 12 characters
    var pattern = new RegExp("^[A-Z0-9]{2,12}$");
    voltmx.print("### Utility_checkInput: input - " + input + " - pattern: " + pattern);
    var result = pattern.test(input);
    voltmx.print("### Utility_checkInput: " + result);
    return result;
}

function Utility_getCurrentLocale() {
    try {
        var currentLocales = voltmx.i18n.getCurrentLocale() + "";
        var deviceLocale = voltmx.i18n.getCurrentDeviceLocale() + "";
        voltmx.print("#### CurrentLocale :" + currentLocales);
        voltmx.print("#### Current Device Locale :" + deviceLocale);
        Global.vars.LanguageCode = currentLocales.substring(0, 2);
        voltmx.print("#### LanguageCode:  " + Global.vars.LanguageCode);
    } catch (i18nError) {
        voltmx.print("#### Exception While getting currentLocale  : " + i18nError);
    }
}
// function Utility_createDateTimeId(inputDateTime){
// 	voltmx.print("### Utility_createDateTimeId InstanceID+Date+hours+Min+seconds");
// 	voltmx.print("#### inputed Datecomponent: " + inputDateTime);
// 	var dateTime = {};
// 	if(inputDateTime === undefined){
// 		dateTime = voltmx.os.date("*t");
// 	} else {
// 		dateTime = inputDateTime;//{"year":inputDateTime[2],"month":inputDateTime[1],"day":inputDateTime[0],"hour":inputDateTime[3],"min":inputDateTime[4],"sec":inputDateTime[5]};
// 	}
// 	voltmx.print("### Utility_createDateTimeId date1: " + JSON.stringify(dateTime));
// 	var outputDateTimeID = SyncUtil.gInstance.substring(2, SyncUtil.gInstance.length) + dateTime.year + dateTime.hour.toString().lpad("0",2) + dateTime.month.toString().lpad("0",2) + dateTime.day.toString().lpad("0",2) + dateTime.min.toString().lpad("0",2) + dateTime.sec.toString().lpad("0",2) + Global.vars.status.toString().lpad("0",2);
// 	voltmx.print("### Utility_createDateTimeId date2: " + outputDateTimeID);
// 	return outputDateTimeID;
// }
function Utility_addTimelineToWhereClause(whereClause, referenceDate) {
    voltmx.print("### Utility_addTimelineToWhereClause - wcs: " + whereClause + " - referenceDate: " + referenceDate);
    // declare locals
    var lNewWhereClause = "";
    var lReferenceWhere = "";
    // construct timeline where clause
    if (referenceDate === undefined || referenceDate === null || referenceDate === "" || referenceDate[0] === undefined || referenceDate[0] === null) {
        lReferenceWhere = "(date(validfrom) <= date('now') AND (date(validto) >= date('now') OR validto = 'NULL'))";
    } else {
        var lReferenceDate = "";
        if (referenceDate.year === undefined) {
            var date = referenceDate[0];
            var month = referenceDate[1];
            var year = referenceDate[2];
            var hours = referenceDate[3];
            var minutes = referenceDate[4];
            lReferenceDate = year + "-" + month.toString().lpad("0", 2) + "-" + date.toString().lpad("0", 2);
        } else {
            lReferenceDate = referenceDate.year + "-" + referenceDate.month.toString().lpad("0", 2) + "-" + referenceDate.day.toString().lpad("0", 2);
        }
        voltmx.print("### Utility_addTimelineToWhereClause lReferenceDate: " + lReferenceDate);
        lReferenceWhere = "(date(validfrom) <= date('" + lReferenceDate + "') AND (date(validto) >= date('" + lReferenceDate + "') OR validto = 'NULL'))";
    }
    // new or concat whereclause
    if (whereClause != null && voltmx.string.len(whereClause) >= 1) {
        lNewWhereClause = whereClause + " AND " + lReferenceWhere;
    } else {
        lNewWhereClause = " WHERE " + lReferenceWhere;
    }
    // return where clause
    voltmx.print("### Utility_addTimelineToWhereClause: " + lNewWhereClause);
    return lNewWhereClause;
}

function Utility_addTimelineToWhereClauseObjectSync(whereClause, referenceDate, addWhere) {
    voltmx.print("### Utility_addTimelineToWhereClauseObjectSync - wcs: " + whereClause + " - referenceDate: " + referenceDate);
    var _addWhere = addWhere === undefined ? false : addWhere;
    // declare locals
    var lNewWhereClause = "";
    var lReferenceWhere = "";
    // construct timeline where clause
    if (referenceDate === undefined || referenceDate === null || referenceDate === "" || referenceDate[0] === undefined || referenceDate[0] === null) {
        lReferenceWhere = "(date(valid_from) <= date('now') AND (date(valid_to) >= date('now') OR valid_to is null))";
    } else {
        var lReferenceDate = "";
        if (referenceDate.year === undefined) {
            var date = referenceDate[0];
            var month = referenceDate[1];
            var year = referenceDate[2];
            var hours = referenceDate[3];
            var minutes = referenceDate[4];
            lReferenceDate = year + "-" + month.toString().lpad("0", 2) + "-" + date.toString().lpad("0", 2);
        } else {
            lReferenceDate = referenceDate.year + "-" + referenceDate.month.toString().lpad("0", 2) + "-" + referenceDate.day.toString().lpad("0", 2);
        }
        voltmx.print("### Utility_addTimelineToWhereClauseObjectSync lReferenceDate: " + lReferenceDate);
        lReferenceWhere = "date('" + lReferenceDate + "') BETWEEN date(valid_from) AND  ifnull(date(valid_to), date('" + lReferenceDate + "'))";
    }
    // new or concat whereclause
    if (_addWhere === false) {
        lNewWhereClause = whereClause + " AND " + lReferenceWhere;
    } else {
        lNewWhereClause = whereClause + " WHERE " + lReferenceWhere;
    }
    // return where clause
    voltmx.print("### Utility_addTimelineToWhereClauseObjectSync: " + lNewWhereClause);
    return lNewWhereClause;
}

function Utility_sortNumberIndex(valueA, valueB) {
    result = false;
    if (valueA.index < valueB.index) {
        result = true;
    }
    return result;
}

function Utility_sortNumberSort(valueA, valueB) {
    result = false;
    if (valueA.Sort < valueB.Sort) {
        result = true;
    }
    return result;
}

function Utility_checkDate_Expired(dateobject, minutes) {
    var lResult = false;
    //
    var d = new Date();
    voltmx.print("#### Global_checkDate_Expired date:" + d);
    voltmx.print("#### Global_checkDate_Expired dateobject:" + dateobject);
    var date_set = null;
    if (dateobject === null) {
        date_set = d;
    } else {
        date_set = Utility_addMinutes(dateobject, minutes);
    }
    voltmx.print("#### Global_checkDate_Expired date_set:" + date_set);

    function z(s) {
        s = "" + s;
        return s.length > 1 ? s : "0" + s;
    }
    if (date_set > d) {
        lResult = false;
    } else {
        lResult = true;
    }
    voltmx.print("#### Global_checkDate_Expired result: " + lResult);
    return lResult;
}

function Utility_addMinutes(date, minutes) {
    return new Date(date.getTime() + minutes * 60000);
}

function Utility_stringToBoolean(inputString) {
    if (inputString != null && typeof inputString == "string") {
        inputString = inputString.toLowerCase();
        switch (inputString) {
            case "false":
            case "no":
            case "0":
            case "":
            case null:
                return false;
            default:
                return true;
        }
    } else if (inputString != null && typeof inputString == "number") {
        switch (inputString) {
            case 0:
            case null:
                return false;
            default:
                return true;
        }
    } else if (typeof inputString == "boolean") {
        return inputString;
    }
}

function Utility_getIndexIfObjWithAttr(array, attr, value) {
    voltmx.print("#### Utility_getIndexIfObjWithAttr array: " + JSON.stringify(array));
    voltmx.print("#### Utility_getIndexIfObjWithAttr attr: " + attr);
    voltmx.print("#### Utility_getIndexIfObjWithAttr value: " + value);
    if (value != null) {
        for (var i = 0; i < array.length; i++) {
            if (array[i][attr] === value) {
                return i;
            }
        }
    }
    return -1;
}

function Utility_reformatDateStringToTimestamp(dateString) {
    try {
        voltmx.print("### Utility_reformatDateStringToTimestamp dateString: " + dateString);
        var lDay = dateString.substring(0, 2);
        var lMonth = dateString.substring(3, 5);
        var lYear = dateString.substring(6, 10);
        var lResult = lYear + "-" + lMonth + "-" + lDay + " " + dateString.substring(11);
        voltmx.print("### Utility_reformatDateStringToTimestamp: " + lResult);
        return lResult;
    } catch (error) {
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_reformatDateFailed"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
    }
}

function Utility_reformatTimestampToDateString(timestamp) {
    voltmx.print("### Utility_reformatTimestampToDateString timestamp: " + timestamp);
    var dateTime = new Date(timestamp);
    var lDay = dateTime.getDate();
    var lMonth = dateTime.getMonth() + 1;
    var lYear = dateTime.getFullYear();
    var lHour = dateTime.getHours();
    var lMinute = dateTime.getMinutes();
    var lResult = lDay.toString().lpad("0", 2) + "-" + lMonth.toString().lpad("0", 2) + "-" + lYear + " " + lHour.toString().lpad("0", 2) + ":" + lMinute.toString().lpad("0", 2);
    voltmx.print("### Utility_reformatTimestampToDateString: " + lResult);
    return lResult;
}

function Utility_reformatToRegDate(timestamp) {
    voltmx.print("### Utility_reformatToRegDate: " + timestamp);
    var lDay = timestamp.substring(8, 10);
    var lMonth = timestamp.substring(5, 7);
    var lYear = timestamp.substring(0, 4);
    var lResult = lMonth + "-" + lDay + "-" + lYear + " " + timestamp.substring(11);
    voltmx.print("### Utility_reformatToRegDate: " + lResult);
    return lResult;
}

function Utility_getSequenceOrder(input, searchText) {
    // order range 1, 2 or 3
    var order = 3;
    // first, starting point of input
    if (input !== undefined && input != null && searchText !== undefined && searchText != null) {
        if (voltmx.string.startsWith(input, searchText)) {
            order = 1;
        } else if (input.indexOf(" " + searchText) != -1) {
            order = 2;
        } else if (input.indexOf(" " + searchText.toLowerCase()) != -1) {
            order = 3;
        } else {
            order = 4;
        }
        return order + "_" + input;
    }
}

function Utility_applyTimelineToResultset(resultset, referenceDate) {
    voltmx.print("### Utility_applyTimelineToResultset - referenceDate: " + referenceDate);
    result = [];
    resultset.forEach(function(row, index) {
        if (row.ValidFrom == null) {
            // record does not contain ValidFrom so remove from resultset
            voltmx.print("### Utility_applyTimelineToResultset - no ValidFrom, remove " + index);
            //resultset.splice(index,1);
        } else {
            // create ValidFrom date
            var validFromString = row.ValidFrom;
            validFromString = row.ValidFrom.replace(/-/g, "/");
            voltmx.print("### validFromString 1: " + validFromString);
            if (validFromString.indexOf(".", 0) != -1) {
                validFromString = validFromString.substring(0, validFromString.indexOf(".", 0));
            }
            var validFrom = new Date(validFromString);
            voltmx.print("### Utility_applyTimelineToResultset - ValidFrom: " + validFrom);
            if (referenceDate >= validFrom) {
                // ValidFrom is valid, now check ValidTo
                if (row.ValidTo != null) {
                    // create ValidTo date
                    validToString = row.ValidTo;
                    validToString = row.ValidTo.replace(/-/g, "/");
                    if (validToString.indexOf(".", 0) != -1) {
                        validToString = validToString.substring(0, validToString.indexOf(".", 0));
                    }
                    var validTo = new Date(validToString);
                    voltmx.print("### validTo: " + validTo);
                    if (referenceDate > validTo) {
                        // record is no longer valid so remove from resultset
                        voltmx.print("### Utility_applyTimelineToResultset - record past ValidTo, remove " + index);
                        //resultset.splice(index,1);
                    } else {
                        result.push(resultset[index]);
                    }
                } else {
                    result.push(resultset[index]);
                }
            } else {
                // ValidFrom not valid so remove from resultset
                voltmx.print("### Utility_applyTimelineToResultset - record before ValidFrom, remove " + index);
                //resultset.splice(index,1);
            }
        }
    });
    voltmx.print("### Utility_applyTimelineToResultset result: " + JSON.stringify(result));
    return result;
}

function Utility_registerForIdleTimeout() {
    voltmx.print("### Utility_registerForIdleTimeout: " + Global.vars.registerForIdleTimeout);
    //alert("### Utility_registerForIdleTimeout: " + Global.vars.registerForIdleTimeout);
    voltmx.application.registerForIdleTimeout(Global.vars.registerForIdleTimeout, Utility_handleTimeout);
}

function Utility_handleTimeout() {
    voltmx.print("### Utility_registerForIdleTimeout: Utility_handleTimeout");
    voltmx.application.dismissLoadingScreen();
    if (Global.vars.connectedCloud != null) {
        service_LogoutAndAssignToCloud(Utility_connectLogOutCallback);
    }
    var restartText = voltmx.i18n.getLocalizedString("i_restart");
    voltmx.ui.Alert(restartText.replace("#idletimeout#", Global.vars.registerForIdleTimeout), Utility_restartApp, "confirmation", voltmx.i18n.getLocalizedString("bt_yes"), voltmx.i18n.getLocalizedString("bt_exit"), voltmx.i18n.getLocalizedString("l_info"), null);
}

function Utility_connectLogOutCallback(result) {
    voltmx.print("### Utility_connectLogOutCallback result: " + JSON.stringify(result));
    if (result.opstatus === 0 && result.httpStatusCode == 200) {
        voltmx.print("### Utility_connectLogOutCallback done");
    } else {
        voltmx.print("### Utility_connectLogOutCallback failed");
    }
}

function Utility_restartApp(response) {
    voltmx.print("### close app or restart");
    if (response === true) {
        Global_logoutIdentity();
        Global_resetApp();
        frmStart.show();
        //destroy forms
        //frmLogin.destroy();
        //frmSynchronization.destroy();
        ////frmRegister.destroy();
        //frmVehicle.destroy();
        //frmPerson.destroy();
        ////frmTrackDown.destroy();
        //frmLocation.destroy();
        //frmHandleCharacteristic.destroy();
        //frmOffenceSelect.destroy();
        ////frmResume.destroy();
        //frmConcepts.destroy();
        //frmOpenTasks.destroy();
    } else {
        Global_exitApplication();
    }
}

function Utility_Services_retry() {
    //pupConnection.dismiss();
    voltmx.application.dismissLoadingScreen();
    voltmx.print("### Utility_Services_retry Global.vars.pupNoConnectionCallback: " + JSON.stringify(Global.vars.pupNoConnectionCallback));
    voltmx.application.showLoadingScreen(lblLoader, Global.vars.pupNoConnectionCallback.service, "center", false, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    if (voltmx.net.isNetworkAvailable(constants.NETWORK_TYPE_ANY)) {
        try {
            voltmx.print("### Utility_Services_retry inputParam: " + JSON.stringify(inputParam));
            var integrationClient = konySDK.getIntegrationService(Global.vars.pupNoConnectionCallback.service);
            Global.vars.mfRetry++;
            integrationClient.invokeOperation(Global.vars.pupNoConnectionCallback.operationName, Global.vars.pupNoConnectionCallback.headers, Global.vars.pupNoConnectionCallback.inputparam, Global.vars.pupNoConnectionCallback.recall, Global.vars.pupNoConnectionCallback.errorcallback, Global.vars.pupNoConnectionCallback.options);
        } catch (exception) {
            voltmx.print("### Utility_Services_retry Exception: " + JSON.stringify(exception));
            voltmx.print("### Utility_Services_retry Exception message: " + exception.message);
            if (exception.code !== undefined && exception.code == "INIT_FAILURE") {
                Global_setServerInfo();
            }
            voltmx.application.dismissLoadingScreen();
            Utility_noNetworkCall();
        }
    } else {
        Utility_noNetworkCall();
        voltmx.application.dismissLoadingScreen();
    }
}

function Utility_reSync() {
    frmSynchronization.show();
    frmSynchronization.flcConnection.setVisibility(false);
    Global.vars.failedSynchronisation = false;
    frmSynchronization_onClick_btnSynchronize();
}

function Utility_checkLicenseplate(licenseplate, countrycode, showonly) {
    var f = false;
    var countryAvailable = true;
    var defaultcountry = Global.vars.CountryCode;
    //	Desired format = languagecode
    var countrycode = countrycode === undefined ? defaultcountry : voltmx.os.toNumber(countrycode);
    //	license plate text
    if (typeof licenseplate !== "string") {
        licenseplate = "";
    }
    var str = licenseplate.toUpperCase();
    voltmx.print("#### Utility_checkLicenseplate: " + str + " - " + countrycode);
    var pattern_NL = [{
            patt: /^([A-Z]{2})([\d]{2})([\d]{2})$/,
            format: "$1#$2#$3"
        }, //XX-99-99
        {
            patt: /^([\d]{2})([\d]{2})([A-Z]{2})$/,
            format: "$1#$2#$3"
        }, //99-99-XX
        {
            patt: /^([\d]{2})([A-Z]{2})([\d]{2})$/,
            format: "$1#$2#$3"
        }, //99-XX-99
        {
            patt: /^([A-Z]{2})([\d]{2})([A-Z]{2})$/,
            format: "$1#$2#$3"
        }, //XX-99-XX
        {
            patt: /^([A-Z]{2})([A-Z]{2})([\d]{2})$/,
            format: "$1#$2#$3"
        }, //XX-XX-99
        {
            patt: /^([\d]{2})([A-Z]{2})([A-Z]{2})$/,
            format: "$1#$2#$3"
        }, //99-XX-XX
        {
            patt: /^([\d]{2})([A-Z]{3})([\d]{1})$/,
            format: "$1#$2#$3"
        }, //99-XXX-9
        {
            patt: /^([\d]{1})([A-Z]{3})([\d]{2})$/,
            format: "$1#$2#$3"
        }, //9-XXX-99
        {
            patt: /^([\d]{3})([A-Z]{2})([\d]{1})$/,
            format: "$1#$2#$3"
        }, //999-XX-9
        {
            patt: /^([\d]{1})([A-Z]{2})([\d]{3})$/,
            format: "$1#$2#$3"
        }, //9-XX-999
        {
            patt: /^([A-Z]{2})([\d]{3})([A-Z]{1})$/,
            format: "$1#$2#$3"
        }, //XX-999-X
        {
            patt: /^([A-Z]{3})([\d]{2})([A-Z]{1})$/,
            format: "$1#$2#$3"
        }, //XXX-99-X BF
        {
            patt: /^([A-Z]{1})([\d]{3})([A-Z]{2})$/,
            format: "$1#$2#$3"
        }, //X-999-XX
        {
            patt: /^([TV]{1})([\d]{2})([A-Z]{3})$/,
            format: "$1#$2#$3"
        }, //T-99-XXX V-99-XXX
        {
            patt: /^(CD[ABFJNST])([0-9]{1,3})$/,
            format: "$1#$2"
        }, //diplomats for example: CDB1 of CDJ45
        {
            patt: /^([A]{2})([\d]{2})([\d]{2})$/,
            format: "$1#$2#$3"
        }, //AA-99-99 Koninklijk Huis
        {
            patt: /^([A]{2})([\d]{2})$/,
            format: "$1#$2"
        }, //AA-99 Koninklijk Huis
        {
            patt: /^([F]{1})([\d]{2})([\d]{2})$/,
            format: "$1#$2#$3"
        }, //F-99-99 Kenteken voor 1 dag
        {
            patt: /^([AEHKLNPSTVWX]{1})([\d]{2})([\d]{2})$/,
            format: "$1#$2#$3"
        }, //A-99-99 eendagskenteken RL-11819
        {
            patt: /^([Z]{1})([\d]{2})([\d]{2})$/,
            format: "$1#$2#$3"
        }, //Z-99-99 Voertuigen die binnen of buiten NL worden gebracht
        {
            patt: /^(CD)([\d]{2})([\d]{2})$/,
            format: "$1#$2#$3"
        }, //CD-99-99
        {
            patt: /^([\d]{2})(CD)([\d]{2})$/,
            format: "$1#$2#$3"
        }, //99-CD-99
        {
            patt: /^(HA)([\d]{2})([\d]{2})$/,
            format: "$1#$2#$3"
        }, //HA-99-99 Handelaarskenteken
        {
            patt: /^([\d]{1})(HA)([\d]{3})$/,
            format: "$1#$2#$3"
        } //9-HA-999 Handelaarskenteken
    ];
    var pattern_GB = [
        //					{patt : /^([A-HJ-PR-Y]{2})([0][1-9]|[1-9][0-9])([A-HJ-PR-Z]{3})$/, format : "$1$2#$3"}, //AB12 RCY
        //					{patt : /^([A-HJ-PR-Y]{1})([1-9]|[1][0-9]|30|31|33|40|44|55|50|60|66|70|77|80|88|90|99)([A-HJ-PR-Z]{3})$/, format : "$1$2#$3"}, // S33 GTT
        //					{patt : /^([A-HJ-PR-Y]{1})([1-9]|[2][0-9]|111|121|123|222|321|333|444|555|666|777|888|999|100|200|300|400|500|600|700|800|900)([A-HJ-PR-Z]{3})$/, format : "$1$2#$3"} // Y999 FVB
        {
            patt: /^([A-HJ-PR-Y]{2})([0][1-9]|[1-9][0-9])([A-HJ-PR-Z]{3})$/,
            format: "$1$2#$3"
        }, //AB12 RCY
        {
            patt: /^([A-HJ-NR-TV-Y]{1})([\d]{1,3})([A-Z]{3})$/,
            format: "$1$2#$3"
        }, // previous series
        {
            patt: /^([A-Z]{3})([\d]{1,3})([A-HJ-NP-Y]{1})$/,
            format: "$1$2#$3"
        }, // previous series
        {
            patt: /^([A-Z]{1,2})([\d]{1,4})$/,
            format: "$1#$2"
        }, // letters first
        {
            patt: /^([\d]{1,4})([A-Z]{1,2})$/,
            format: "$1#$2"
        }, // old series - digits first
        {
            patt: /^([A-Z]{3})([\d]{1,4})$/,
            format: "$1#$2"
        }, // letters first
        {
            patt: /^([\d]{1,4})([A-Z]{3})$/,
            format: "$1#$2"
        } // old series - digits first
    ];
    var pattern_S = [{
            patt: /^(?!DYR|GAS|GET|GLO|GOM|GUB|GUC|GUK|HAL|HAN|HAO|HAR|HAS|HER|HES|HET|HJO|HMO|HOM|HON|HRA|HUD|HUK|HUS|HUT|JUG|JUK|JUO|JUR|KDS|LUZ|MAS|MUT|NEJ|NJA|NOS|NUP|NYS|ORA|OST|OXE|PAP|PES|PNS|PRO|PUC|PUK|PYS|REA|RUG|RUK|SAB|SAC|SAF|SAP|SAT|SEK|SOP|SSU|SWE|SYF|TAJ|TOT|UCK|UFF|UPA|USH|WTC|XUK|APA|ARG|ASS|BAJ|BSS|CUC|CUK|DUM|ETA|ETT|FAG|FAN|FEG|FEL|FEM|FES|FET|FNL|FUC|FUK|FUL|GAM|GAY|GEJ|GEY|GHB|GUD|GYN|HAT|HBT|HKH|HOR|HOT|KGB|KKK|KUC|KUF|KUG|KUK|KYK|LAM|LAT|LEM|LOJ|LSD|LUS|MAD|MAO|MEN|MES|MUS|NAZ|NRP|NSF|NYP|OND|OOO|ORM|PAJ|PKK|PLO|PMS|PUB|RAP|RAS|ROM|RPS|RUS|SEG|SEX|SJU|SOS|SPY|SUG|SUP|SUR|TBC|TOA|TOK|TRE|TYP|UFO|USA|WAM|WAR|WWW|XTC|XTZ|XXL|XXX)([A-HJ-PR-UW-Z]{3})([0-9]{3})$/,
            format: "$1#$2"
        }, //ABC 123
        {
            patt: /^(?!DYR|GAS|GET|GLO|GOM|GUB|GUC|GUK|HAL|HAN|HAO|HAR|HAS|HER|HES|HET|HJO|HMO|HOM|HON|HRA|HUD|HUK|HUS|HUT|JUG|JUK|JUO|JUR|KDS|LUZ|MAS|MUT|NEJ|NJA|NOS|NUP|NYS|ORA|OST|OXE|PAP|PES|PNS|PRO|PUC|PUK|PYS|REA|RUG|RUK|SAB|SAC|SAF|SAP|SAT|SEK|SOP|SSU|SWE|SYF|TAJ|TOT|UCK|UFF|UPA|USH|WTC|XUK|APA|ARG|ASS|BAJ|BSS|CUC|CUK|DUM|ETA|ETT|FAG|FAN|FEG|FEL|FEM|FES|FET|FNL|FUC|FUK|FUL|GAM|GAY|GEJ|GEY|GHB|GUD|GYN|HAT|HBT|HKH|HOR|HOT|KGB|KKK|KUC|KUF|KUG|KUK|KYK|LAM|LAT|LEM|LOJ|LSD|LUS|MAD|MAO|MEN|MES|MUS|NAZ|NRP|NSF|NYP|OND|OOO|ORM|PAJ|PKK|PLO|PMS|PUB|RAP|RAS|ROM|RPS|RUS|SEG|SEX|SJU|SOS|SPY|SUG|SUP|SUR|TBC|TOA|TOK|TRE|TYP|UFO|USA|WAM|WAR|WWW|XTC|XTZ|XXL|XXX)([A-HJ-PR-UW-Z]{3})([0-9]{2})([A-HJ-PR-UW-Z]{1})$/,
            format: "$1#$2$3"
        }, //ABC 12A
        {
            patt: /^(?!BR|FP|FR|FS|FT|FU|FW|FX|FY|FZ)([A-H][A-HJ-NPR-UW-Z])([\d]{3})([A-I])$/,
            format: "$1$2$3"
        }, // ML042B
        {
            patt: /^(?!TDYR|TGAS|TGET|TGLO|TGOM|TGUB|TGUC|TGUK|THAL|THAN|THAO|THAR|THAS|THER|THES|THET|THJO|THMO|THOM|THON|THRA|THUD|THUK|THUS|THUT|TJUG|TJUK|TJUO|TJUR|TKDS|TLUZ|TMAS|TMUT|TNEJ|TNJA|TNOS|TNUP|TNYS|TORA|TOST|TOXE|TPAP|TPES|TPNS|TPRO|TPUC|TPUK|TPYS|TREA|TRUG|TRUK|TSAB|TSAC|TSAF|TSAP|TSAT|TSEK|TSOP|TSSU|TSWE|TSYF|TTAJ|TTOT|TUCK|TUFF|TUPA|TUSH|TWTC|TXUK|TAPA|TARG|TASS|TBAJ|TBSS|TCUC|TCUK|TDUM|TETA|TETT|TFAG|TFAN|TFEG|TFEL|TFEM|TFES|TFET|TFNL|TFUC|TFUK|TFUL|TGAM|TGAY|TGEJ|TGEY|TGHB|TGUD|TGYN|THAT|THBT|THKH|THOR|THOT|TKGB|TKKK|TKUC|TKUF|TKUG|TKUK|TKYK|TLAM|TLAT|TLEM|TLOJ|TLSD|TLUS|TMAD|TMAO|TMEN|TMES|TMUS|TNAZ|TNRP|TNSF|TNYP|TOND|TOOO|TORM|TPAJ|TPKK|TPLO|TPMS|TPUB|TRAP|TRAS|TROM|TRPS|TRUS|TSEG|TSEX|TSJU|TSOS|TSPY|TSUG|TSUP|TSUR|TTBC|TTOA|TTOK|TTRE|TTYP|TUFO|TUSA|TWAM|TWAR|TWWW|TXTC|TXTZ|TXXL|TXXX)([T][A-HJ-PR-UW-Z]{3})([0-9]{3})$/,
            format: "$1#$2"
        }, //TABC123 Taxi
        {
            patt: /^(?!TDYR|TGAS|TGET|TGLO|TGOM|TGUB|TGUC|TGUK|THAL|THAN|THAO|THAR|THAS|THER|THES|THET|THJO|THMO|THOM|THON|THRA|THUD|THUK|THUS|THUT|TJUG|TJUK|TJUO|TJUR|TKDS|TLUZ|TMAS|TMUT|TNEJ|TNJA|TNOS|TNUP|TNYS|TORA|TOST|TOXE|TPAP|TPES|TPNS|TPRO|TPUC|TPUK|TPYS|TREA|TRUG|TRUK|TSAB|TSAC|TSAF|TSAP|TSAT|TSEK|TSOP|TSSU|TSWE|TSYF|TTAJ|TTOT|TUCK|TUFF|TUPA|TUSH|TWTC|TXUK|TAPA|TARG|TASS|TBAJ|TBSS|TCUC|TCUK|TDUM|TETA|TETT|TFAG|TFAN|TFEG|TFEL|TFEM|TFES|TFET|TFNL|TFUC|TFUK|TFUL|TGAM|TGAY|TGEJ|TGEY|TGHB|TGUD|TGYN|THAT|THBT|THKH|THOR|THOT|TKGB|TKKK|TKUC|TKUF|TKUG|TKUK|TKYK|TLAM|TLAT|TLEM|TLOJ|TLSD|TLUS|TMAD|TMAO|TMEN|TMES|TMUS|TNAZ|TNRP|TNSF|TNYP|TOND|TOOO|TORM|TPAJ|TPKK|TPLO|TPMS|TPUB|TRAP|TRAS|TROM|TRPS|TRUS|TSEG|TSEX|TSJU|TSOS|TSPY|TSUG|TSUP|TSUR|TTBC|TTOA|TTOK|TTRE|TTYP|TUFO|TUSA|TWAM|TWAR|TWWW|TXTC|TXTZ|TXXL|TXXX)([T][A-HJ-PR-UW-Z]{3})([0-9]{2})([A-HJ-PR-UW-Z]{1})$/,
            format: "$1#$2$3"
        }, //TABC123 Taxi
        {
            patt: /^([\d]{5})$/,
            format: "$1"
        } //12345 Militairy
    ];
    var pattern_D = [{
            patt: /^([ÖÜA-Z]{1,4})-([ÖÜA-Z]{1,2})-([0-9]{1,5})$/,
            format: "$1#$2#$3"
        }, {
            patt: /^([ÖÜA-Z]{1,4})-([ÖÜA-Z]{1,2})-([0-9]{1,5}[H,E,U])$/,
            format: "$1#$2#$3"
        }, {
            patt: /^([ÖÜA-Z]{1,4})-([0-9]{2,4})-([ÖÜA-Z])$/,
            format: "$1#$2#$3"
        }, // export MKK-458-C
        {
            patt: /^([ÖÜA-Z]{1,4})-([ÖÜA-Z]{1,2}[0-9]{1,5})$/,
            format: "$1#$2"
        }, {
            patt: /^([ÖÜA-Z]{1,4})-([ÖÜA-Z]{1,2}[0-9]{1,5}[H,E,U])$/,
            format: "$1#$2"
        }, {
            patt: /^([ÖÜA-Z]{1,4})-([0-9]{2,4}[ÖÜA-Z])$/,
            format: "$1#$2"
        }, // export MKK-458-C
        {
            patt: /^([ÖÜA-Z]{1,4})-([0-9]{1,8})$/,
            format: "$1#$2"
        }, {
            patt: /^([ÖÜA-Z]{1,4})-([0-9]{1,8}[H,E,U])$/,
            format: "$1#$2"
        }
    ];
    var patterns = [{
        country: 6030,
        pattern: pattern_NL,
        sep_mandatory: false,
        separator: "-"
    }, {
        country: 6039,
        pattern: pattern_GB,
        sep_mandatory: false,
        separator: " "
    }, {
        country: 5039,
        pattern: pattern_S,
        sep_mandatory: false,
        separator: " "
    }];
    if (Global.vars.germanSeparatorMandatory === true) {
        var newpattern = {
            country: 9089,
            pattern: pattern_D,
            sep_mandatory: true,
            separator: "-"
        };
        patterns.push(newpattern);
    }
    // find pattern
    var patt = [];
    for (var p = 0; p < patterns.length; p++) {
        if (patterns[p].country == countrycode) {
            countryAvailable = true;
            patt = patterns[p].pattern;
            voltmx.print("### Utility_checkLicenseplate str pre: " + str);
            voltmx.print("### Utility_checkLicenseplate patt: " + patterns[p]);
            if (!patterns[p].sep_mandatory) {
                voltmx.print("### Utility_checkLicenseplate patt false");
                str = str.replace(/-/g, "");
                str = str.replace(/ /g, "");
                str = str.replace(/#/g, "");
            } else {
                voltmx.print("### Utility_checkLicenseplate patt true");
                str = str.replace(/-/g, patterns[p].separator);
                str = str.replace(/ /g, patterns[p].separator);
                str = str.replace(/#/g, patterns[p].separator);
            }
            voltmx.print("### Utility_checkLicenseplate str post: " + str);
            for (var i = 0; i < patt.length; i++) {
                if (patt[i].patt.test(str) === true) {
                    f = true;
                    if (!patterns[p].sep_mandatory) {
                        var re = patt[i].patt;
                        var format = patt[i].format.replace(/#/g, patterns[p].separator);
                        var license = str.replace(re, format);
                        f = license;
                    } else {
                        f = str;
                    }
                    break;
                } else {
                    voltmx.print("#### no matching pattern patt" + i);
                }
            }
            break;
        } else {
            countryAvailable = false;
        }
    }
    if (!countryAvailable) {
        var pattern = /^[ÖÜÅA-Z0-9 -]+$/;
        f = licenseplate;
        if (Global.vars.checkLicensePlateInput === true && pattern.test(licenseplate) === false) {
            f = false;
        }
    }
    voltmx.print("#### Utility_checkLicenseplate format pattern license: " + f);
    if (f === false && showonly === true) {
        f = licenseplate.toUpperCase();
    }
    return f;
}

function Utility_getPersonDocIdentificationTypes(callback) {
    function getPersonDocIdentificationTypes_errorcallback(error) {
        voltmx.ui.Alert("ERROR getting document identification types: " + error.msg, null, "error", "Ok", null, "Error", null);
    }
    var wcs = "select * from mle_v_general_reference_msv WHERE name = 'id_doc_soort_code'";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    wcs = wcs + " ORDER BY number_value ASC";
    voltmx.print("### Utility_getPersonDocIdentificationTypes getPersonDocIdentificationTypes wcs: " + wcs);
    //com.redora.ReferenceData.GeneralReference.find(wcs, callback, getPersonDocIdentificationTypes_errorcallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, callback, getPersonDocIdentificationTypes_errorcallback);
}

function Utility_getPersonDocIdentificationTypes_successCallback(result) {
    voltmx.print("### Utility_getPersonDocIdentificationTypes_successCallback ###");
    voltmx.print("### Utility_getPersonDocIdentificationTypes_successCallback PersonDocIdentificationTypeIdDescription: " + JSON.stringify(result));
    var documentTypeData = [];
    var selectedkey = null;
    Global.vars.checkdocument = "";
    documentTypeData.push({
        key: -1,
        value: voltmx.i18n.getLocalizedString("l_choose")
    });
    for (var j in result) {
        var v = result[j];
        documentTypeData.push({
            key: v.number_value.toString(),
            value: v.descripton
        });
    }
    Global.vars.documentTypes = documentTypeData;
    voltmx.print("### Utility_getPersonDocIdentificationTypes_successCallback PersonDocIdentificationTypeIdDescription: " + JSON.stringify(Global.vars.documentTypes));
}

function Utility_getPersonDocIdentificationTypeById(numbervalue, callback) {
    function getPersonDocIdentificationTypeById_errorcallback(error) {
        voltmx.ui.Alert("ERROR getting document identification types: " + error.msg, null, "error", "Ok", null, "Error", null);
    }
    var wcs = "select * from mle_v_general_reference_msv WHERE name = 'id_doc_soort_code' and number_value = '" + numbervalue + "'";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### getPersonDocIdentificationTypes wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, callback, getPersonDocIdentificationTypeById_errorcallback);
}

function Utility_addLanguageToWhereClause(whereClause, language) {
    // declare locals
    var lLanguage = language == undefined ? Global.vars.databaseLocale : language;
    //
    var lNewWhereClause = "";
    var lLanguageWhere = "languagecode='" + lLanguage.replace("_", "-") + "'";
    voltmx.print("### Utility_addLanguageToWhereClause - databaseLocale: " + lLanguage);
    // Add to existing or create new where clause
    if (whereClause != null && voltmx.string.len(whereClause) >= 1) {
        voltmx.print("### Utility_addLanguageToWhereClause - whereClause: " + whereClause);
        lNewWhereClause = whereClause + " AND " + lLanguageWhere;
    } else {
        lNewWhereClause = " WHERE " + lLanguageWhere;
    }
    voltmx.print("### Utility_addLanguageToWhereClause - newWhereClause: " + lNewWhereClause);
    return lNewWhereClause;
}

function Utility_addLanguageToWhereClauseObjectSync(whereClause, language, addWhere) {
    // declare locals
    var lLanguage = language === undefined ? Global.vars.databaseLocale : language;
    var _addWhere = addWhere === undefined ? false : addWhere;
    //
    var lNewWhereClause = "";
    var lLanguageWhere = "language_code = '" + lLanguage.replace("_", "-") + "'";
    voltmx.print("### Utility_addLanguageToWhereClauseObjectSync - databaseLocale: " + lLanguage);
    // Add to existing or create new where clause
    if (whereClause != null && voltmx.string.len(whereClause) >= 1) {
        voltmx.print("### Utility_addLanguageToWhereClauseObjectSync - whereClause: " + whereClause);
        lNewWhereClause = whereClause + " AND " + lLanguageWhere;
    } else {
        lNewWhereClause = lLanguageWhere;
    }
    if (_addWhere === false) {
        lNewWhereClause = whereClause + " AND " + lLanguageWhere;
    } else {
        lNewWhereClause = whereClause + " WHERE " + lLanguageWhere;
    }
    voltmx.print("### Utility_addLanguageToWhereClauseObjectSync - newWhereClause: " + lNewWhereClause);
    return lNewWhereClause;
}

function Utility_getUTCJavascriptDate(javascriptDate) {
    voltmx.print("### Utility_getUTCJavascriptDate javascriptDate: " + javascriptDate);
    var currentDate = new Date();
    if (javascriptDate != null) {
        currentDate = javascriptDate;
    }
    var now_utc = new Date(currentDate).toISOString();
    voltmx.print("### Utility_getUTCJavascriptDate: " + now_utc);
    return now_utc;
}

function Utility_getLocalizedDateTimeString(javascriptDate, seconds) {
    var dateTime = javascriptDate; //als javascript datum
    if (dateTime === null) {
        dateTime = new Date();
    }
    voltmx.print("### Utility_getLocalizedDateTimeString dateTime: " + dateTime); // -> Sat Feb 28 2004 23:45:26 GMT-0300 (BRT)
    voltmx.print("### Utility_getLocalizedDateTimeString toLocaleDateString: " + Utility_getLocaleShortDateString(dateTime)); // -> 02/28/2004
    voltmx.print("### Utility_getLocalizedDateTimeString toLocaleDateString: " + Utility_getLocaleShortTimeString(dateTime)); // -> 02/28/2004
    lDateTime = Utility_getLocaleShortDateString(dateTime) + " " + Utility_getLocaleShortTimeString(dateTime, seconds);
    voltmx.print("### Utility_getLocalizedDateTimeString: " + lDateTime);
    return lDateTime;
}

function Utility_getLocaleShortDateString(javascriptDate) {
    if (!(javascriptDate instanceof Date)) {
        voltmx.print("### Utility_getLocaleShortDateString: Not a Date object: " + javascriptDate);
        return javascriptDate;
    }
    var f = {
        ident: "yymmdd",
        ar_SA: "dd/MM/yy",
        bg_BG: "dd.M.yyyy",
        ca_ES: "dd/MM/yyyy",
        zh_TW: "yyyy/M/d",
        cs_CZ: "d.M.yyyy",
        da_DK: "dd-MM-yyyy",
        de_DE: "dd.MM.yyyy",
        el_GR: "d/M/yyyy",
        en_US: "M/d/yyyy",
        en: "M/d/yyyy",
        fi_FI: "d.M.yyyy",
        fr_FR: "dd/MM/yyyy",
        he_IL: "dd/MM/yyyy",
        hu_HU: "yyyy. MM. dd.",
        is_IS: "d.M.yyyy",
        it_IT: "dd/MM/yyyy",
        ja_JP: "yyyy/MM/dd",
        ko_KR: "yyyy-MM-dd",
        nl_NL: "d-M-yyyy",
        nl: "d-M-yyyy",
        no: "dd-MM-yyyy",
        no_NO: "dd-MM-yyyy",
        nb_NO: "dd-MM-yyyy",
        pl_PL: "yyyy-MM-dd",
        pt_BR: "d/M/yyyy",
        ro_RO: "dd.MM.yyyy",
        ru_RU: "dd.MM.yyyy",
        hr_HR: "d.M.yyyy",
        sk_SK: "d. M. yyyy",
        sq_AL: "yyyy-MM-dd",
        sv_SE: "yyyy-MM-dd",
        th_TH: "d/M/yyyy",
        tr_TR: "dd.MM.yyyy",
        ur_PK: "dd/MM/yyyy",
        id_ID: "dd/MM/yyyy",
        uk_UA: "dd.MM.yyyy",
        be_BY: "dd.MM.yyyy",
        sl_SI: "d.M.yyyy",
        et_EE: "d.MM.yyyy",
        lv_LV: "yyyy.MM.dd.",
        lt_LT: "yyyy.MM.dd",
        fa_IR: "MM/dd/yyyy",
        vi_VN: "dd/MM/yyyy",
        hy_AM: "dd.MM.yyyy",
        az_Latn_AZ: "dd.MM.yyyy",
        eu_ES: "yyyy/MM/dd",
        mk_MK: "dd.MM.yyyy",
        af_ZA: "yyyy/MM/dd",
        ka_GE: "dd.MM.yyyy",
        fo_FO: "dd-MM-yyyy",
        hi_IN: "dd-MM-yyyy",
        ms_MY: "dd/MM/yyyy",
        kk_KZ: "dd.MM.yyyy",
        ky_KG: "dd.MM.yy",
        sw_KE: "M/d/yyyy",
        uz_Latn_UZ: "dd/MM yyyy",
        tt_RU: "dd.MM.yyyy",
        pa_IN: "dd-MM-yy",
        gu_IN: "dd-MM-yy",
        ta_IN: "dd-MM-yyyy",
        te_IN: "dd-MM-yy",
        kn_IN: "dd-MM-yy",
        mr_IN: "dd-MM-yyyy",
        sa_IN: "dd-MM-yyyy",
        mn_MN: "yy.MM.dd",
        gl_ES: "dd/MM/yy",
        kok_IN: "dd-MM-yyyy",
        syr_SY: "dd/MM/yyyy",
        dv_MV: "dd/MM/yy",
        ar_IQ: "dd/MM/yyyy",
        zh_CN: "yyyy/M/d",
        de_CH: "dd.MM.yyyy",
        en_GB: "dd/MM/yyyy",
        es_MX: "dd/MM/yyyy",
        fr_BE: "d/MM/yyyy",
        it_CH: "dd.MM.yyyy",
        nl_BE: "d/MM/yyyy",
        nn_NO: "dd.MM.yyyy",
        pt_PT: "dd-MM-yyyy",
        sr_Latn_CS: "d.M.yyyy",
        sv_FI: "d.M.yyyy",
        az_Cyrl_AZ: "dd.MM.yyyy",
        ms_BN: "dd/MM/yyyy",
        uz_Cyrl_UZ: "dd.MM.yyyy",
        ar_EG: "dd/MM/yyyy",
        zh_HK: "d/M/yyyy",
        de_AT: "dd.MM.yyyy",
        en_AU: "d/MM/yyyy",
        es_ES: "dd/MM/yyyy",
        fr_CA: "yyyy-MM-dd",
        sr_Cyrl_CS: "d.M.yyyy",
        ar_LY: "dd/MM/yyyy",
        zh_SG: "d/M/yyyy",
        de_LU: "dd.MM.yyyy",
        en_CA: "dd/MM/yyyy",
        es_GT: "dd/MM/yyyy",
        fr_CH: "dd.MM.yyyy",
        ar_DZ: "dd-MM-yyyy",
        zh_MO: "d/M/yyyy",
        de_LI: "dd.MM.yyyy",
        en_NZ: "d/MM/yyyy",
        es_CR: "dd/MM/yyyy",
        fr_LU: "dd/MM/yyyy",
        ar_MA: "dd-MM-yyyy",
        en_IE: "dd/MM/yyyy",
        es_PA: "MM/dd/yyyy",
        fr_MC: "dd/MM/yyyy",
        ar_TN: "dd-MM-yyyy",
        en_ZA: "yyyy/MM/dd",
        es_DO: "dd/MM/yyyy",
        ar_OM: "dd/MM/yyyy",
        en_JM: "dd/MM/yyyy",
        es_VE: "dd/MM/yyyy",
        ar_YE: "dd/MM/yyyy",
        en_029: "MM/dd/yyyy",
        es_CO: "dd/MM/yyyy",
        ar_SY: "dd/MM/yyyy",
        en_BZ: "dd/MM/yyyy",
        es_PE: "dd/MM/yyyy",
        ar_JO: "dd/MM/yyyy",
        en_TT: "dd/MM/yyyy",
        es_AR: "dd/MM/yyyy",
        ar_LB: "dd/MM/yyyy",
        en_ZW: "M/d/yyyy",
        es_EC: "dd/MM/yyyy",
        ar_KW: "dd/MM/yyyy",
        en_PH: "M/d/yyyy",
        es_CL: "dd-MM-yyyy",
        ar_AE: "dd/MM/yyyy",
        es_UY: "dd/MM/yyyy",
        ar_BH: "dd/MM/yyyy",
        es_PY: "dd/MM/yyyy",
        ar_QA: "dd/MM/yyyy",
        es_BO: "dd/MM/yyyy",
        es_SV: "dd/MM/yyyy",
        es_HN: "dd/MM/yyyy",
        es_NI: "dd/MM/yyyy",
        es_PR: "dd/MM/yyyy",
        am_ET: "d/M/yyyy",
        tzm_Latn_DZ: "dd-MM-yyyy",
        iu_Latn_CA: "d/MM/yyyy",
        sma_NO: "dd.MM.yyyy",
        mn_Mong_CN: "yyyy/M/d",
        gd_GB: "dd/MM/yyyy",
        en_MY: "d/M/yyyy",
        prs_AF: "dd/MM/yy",
        bn_BD: "dd-MM-yy",
        wo_SN: "dd/MM/yyyy",
        rw_RW: "M/d/yyyy",
        qut_GT: "dd/MM/yyyy",
        sah_RU: "MM.dd.yyyy",
        gsw_FR: "dd/MM/yyyy",
        co_FR: "dd/MM/yyyy",
        oc_FR: "dd/MM/yyyy",
        mi_NZ: "dd/MM/yyyy",
        ga_IE: "dd/MM/yyyy",
        se_SE: "yyyy-MM-dd",
        br_FR: "dd/MM/yyyy",
        smn_FI: "d.M.yyyy",
        moh_CA: "M/d/yyyy",
        arn_CL: "dd-MM-yyyy",
        ii_CN: "yyyy/M/d",
        dsb_DE: "d. M. yyyy",
        ig_NG: "d/M/yyyy",
        kl_GL: "dd-MM-yyyy",
        lb_LU: "dd/MM/yyyy",
        ba_RU: "dd.MM.yy",
        nso_ZA: "yyyy/MM/dd",
        quz_BO: "dd/MM/yyyy",
        yo_NG: "d/M/yyyy",
        ha_Latn_NG: "d/M/yyyy",
        fil_PH: "M/d/yyyy",
        ps_AF: "dd/MM/yy",
        fy_NL: "d-M-yyyy",
        ne_NP: "M/d/yyyy",
        se_NO: "dd.MM.yyyy",
        iu_Cans_CA: "d/M/yyyy",
        sr_Latn_RS: "d.M.yyyy",
        si_LK: "yyyy-MM-dd",
        sr_Cyrl_RS: "d.M.yyyy",
        lo_LA: "dd/MM/yyyy",
        km_KH: "yyyy-MM-dd",
        cy_GB: "dd/MM/yyyy",
        bo_CN: "yyyy/M/d",
        sms_FI: "d.M.yyyy",
        as_IN: "dd-MM-yyyy",
        ml_IN: "dd-MM-yy",
        en_IN: "dd-MM-yyyy",
        or_IN: "dd-MM-yy",
        bn_IN: "dd-MM-yy",
        tk_TM: "dd.MM.yy",
        bs_Latn_BA: "d.M.yyyy",
        mt_MT: "dd/MM/yyyy",
        sr_Cyrl_ME: "d.M.yyyy",
        se_FI: "d.M.yyyy",
        zu_ZA: "yyyy/MM/dd",
        xh_ZA: "yyyy/MM/dd",
        tn_ZA: "yyyy/MM/dd",
        hsb_DE: "d. M. yyyy",
        bs_Cyrl_BA: "d.M.yyyy",
        tg_Cyrl_TJ: "dd.MM.yy",
        sr_Latn_BA: "d.M.yyyy",
        smj_NO: "dd.MM.yyyy",
        rm_CH: "dd/MM/yyyy",
        smj_SE: "yyyy-MM-dd",
        quz_EC: "dd/MM/yyyy",
        quz_PE: "dd/MM/yyyy",
        hr_BA: "d.M.yyyy.",
        sr_Latn_ME: "d.M.yyyy",
        sma_SE: "yyyy-MM-dd",
        en_SG: "d/M/yyyy",
        ug_CN: "yyyy-M-d",
        sr_Cyrl_BA: "d.M.yyyy",
        es_US: "M/d/yyyy"
    };
    // use currentDeviceLocale as default languagecode
    var currentDeviceLocale = voltmx.i18n.getCurrentDeviceLocale();
    voltmx.print("### Utility_getLocalizedDateTimeString currentDeviceLocale: " + currentDeviceLocale);
    var currentLocale = currentDeviceLocale.language + "_" + currentDeviceLocale.country;
    if (Global.vars.gDeviceInfo.name !== undefined && voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
        currentLocale = currentDeviceLocale.replace("-", "_").substring(0, 5);
    }
    voltmx.print("### Utility_getLocalizedDateTimeString currentLocale: " + currentLocale);
    var y = javascriptDate.getFullYear(),
        m = javascriptDate.getMonth() + 1,
        d = javascriptDate.getDate();
    voltmx.print("### Utility_getLocalizedDateTimeString currentDeviceLocale: " + y);
    voltmx.print("### Utility_getLocalizedDateTimeString currentDeviceLocale: " + m);
    voltmx.print("### Utility_getLocalizedDateTimeString currentDeviceLocale: " + d);
    f = currentLocale in f ? f[currentLocale] : "M/d/yyyy";

    function z(s) {
        s = "" + s;
        return s.length > 1 ? s : "0" + s;
    }
    f = f.replace(/yyyy/, y);
    f = f.replace(/yy/, String(y).substr(2));
    f = f.replace(/MM/, z(m));
    f = f.replace(/M/, m);
    f = f.replace(/dd/, z(d));
    f = f.replace(/d/, d);
    voltmx.print("### Utility_getLocalizedDateTimeString: " + f);
    return f;
}

function Utility_getLocaleShortTimeString(javascriptDate, seconds) {
    if (!(javascriptDate instanceof Date)) {
        voltmx.print("### Utility_getLocaleShortTimeString: Not a Date object: " + javascriptDate);
        return javascriptDate;
    }
    var Seconds = seconds === undefined ? false : seconds;
    var f = {
        en_AU: "hh:mm TT",
        en_CA: "hh:mm TT",
        en_NZ: "hh:mm TT",
        en_US: "hh:mm TT",
        es_DO: "hh:mm TT",
        br_BR: "hh:mm TT",
        ka_KA: "hh:mm TT",
        en: "hh:mm TT"
    };
    // use CurrentLocale as default languagecode
    var currentDeviceLocale = voltmx.i18n.getCurrentDeviceLocale();
    voltmx.print("### Utility_getLocaleShortTimeString currentDeviceLocale: " + currentDeviceLocale);
    var currentLocale = currentDeviceLocale.language + "_" + currentDeviceLocale.country;
    if (Global.vars.gDeviceInfo.name !== undefined && voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
        currentLocale = currentDeviceLocale.replace("-", "_").substring(0, 5);
    }
    f = currentLocale in f ? f[currentLocale] : "HH:mm";
    voltmx.print("### Utility_getLocaleShortTimeString currentLocale: " + currentLocale);
    //
    var hours = javascriptDate.getHours();
    var minutes = javascriptDate.getMinutes();
    var ss = javascriptDate.getSeconds();
    ss = ss < 10 ? "0" + ss : ss;
    minutes = minutes < 10 ? "0" + minutes : minutes;
    var dateString = hours + ":" + minutes;
    if (seconds === true) {
        dateString = hours + ":" + minutes + ":" + ss;
    }
    if (voltmx.string.endsWith(f, "TT")) {
        var ampm = hours >= 12 ? "PM" : "AM";
        hours = hours % 12;
        hours = hours ? hours : 12; // the hour '0' should be '12'
        dateString = hours + ":" + minutes + " " + ampm;
        if (seconds === true) {
            dateString = hours + ":" + minutes + ":" + ss + " " + ampm;
        }
    } else if (hours < 10) {
        dateString = " " + dateString;
    }
    voltmx.print("### getLocaleShortTimeString: " + dateString);
    return dateString;
}

function Utility_formatDateTime(date) {
    if (!(date instanceof Date)) {
        voltmx.print("### Utility_formatDateTime: Not a Date object: " + date);
        return date;
    }
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are 0-indexed
    const day = date.getDate().toString().padStart(2, "0");
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const seconds = date.getSeconds().toString().padStart(2, "0");
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function Utility_CapatilizeSentence(str) {
    voltmx.print("### Utility_CapatilizeSentence: " + str);
    if (typeof str !== "string") {
        str = "";
    }
    if (str === null) {
        str = "";
    }
    str = str.toLowerCase();
    return str.replace(/\w\S*/, function(txt) {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
}

function Utility_CapatilizeNames(str, force) {
    if (typeof str !== "string") {
        str = "";
    }
    if (str === null) {
        str = "";
    }
    //   str=force ? str.toLowerCase() : str;
    //   var temp= str.split(/(\s|-)+/);
    //   for(var i=0; i<temp.length; i++) {
    //     temp[i] = temp[i].substr(0,1).toUpperCase() + temp[i].substr(1,temp[i].length-1);
    //   }
    //   voltmx.print("### Utility_CapatilizeNames result: " + temp.join(''));
    //  return temp.join('');
    return str.replace(/\w\S*/g, function(txt) {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
}

function Utility_getInstanceParameter(name, callback) {
    voltmx.print("### getInstanceParameter Name: " + name);
    var paramFound = false;
    var result = [];
    // loop over all instance parameters to find name
    for (var i = 0; i < Global.vars.instanceParameters.length; i++) {
        if (Global.vars.instanceParameters[i].name === name) {
            result.push(Global.vars.instanceParameters[i]);
            break;
        }
    }
    // execute callback
    callback(result);
}

function Utility_getOffenceCityParameter(name, cityCode, callback) {
    voltmx.print("### Utility_getOffenceCityParameter Name: " + name + " CityCode: " + cityCode);
    var result = [];
    // loop over all instance parameters to find name
    for (var i = 0; i < Global.vars.offenceCityParameters.length; i++) {
        if (Global.vars.offenceCityParameters[i].name === name && Global.vars.offenceCityParameters[i].city_code === cityCode) {
            result.push(Global.vars.offenceCityParameters[i]);
            break;
        }
    }
    // execute callback
    callback(result);
}

function Utility_getSystemCountryDetails(countryCode) {
    voltmx.print("##### Utility_getSystemCountryDetails");
    var lSystemCountryCode = countryCode === undefined ? Global.vars.CountryCode : countryCode;
    var wcs = "select * from mle_v_country_m WHERE code = " + lSystemCountryCode + "";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### Utility_getSystemCountryDetails getCountries wcs: " + wcs);
    //com.redora.Location.Country.find(wcs, Utility_getSystemCountryDetails_callback, Utility_getSystemCountryDetails_errorcallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getSystemCountryDetails_callback, Utility_getSystemCountryDetails_errorcallback);
}

function Utility_getSystemCountryDetails_callback(result) {
    voltmx.print("### Utility_getSystemCountryDetails callback: " + JSON.stringify(result));
    if (result.length == 1) {
        Global.vars.licenseplateCountryModule = result[0].license_code;
        Global.vars.licenseplateCountryDesc = result[0].description;
        Global.vars.licenseplateCountryCode = result[0].code;
    } else {
        Global.vars.licenseplateCountryModule = "";
        Global.vars.licenseplateCountryDesc = "";
        Global.vars.licenseplateCountryCode = null;
    }
    voltmx.print("### Utility_getSystemCountryDetails gSystem: " + Global.vars.licenseplateCountryModule + " - " + Global.vars.licenseplateCountryDesc);
}

function Utility_getSystemCountryDetails_errorcallback(error) {
    voltmx.print("### Utility_getSystemCountryDetails_errorcallback error: " + error);
    Global.vars.licenseplateCountryModule = "";
    Global.vars.licenseplateCountryDesc = "";
}

function Utility_getSystemUnknownCountryDetails(countryCode) {
    voltmx.print("##### Utility_getSystemUnknownCountryDetails");
    var lSystemCountryCode = countryCode === undefined ? 0 : countryCode;
    var wcs = "select * from mle_v_country_m WHERE code = " + lSystemCountryCode + "";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### Utility_getSystemUnknownCountryDetails getCountries wcs: " + wcs);
    //com.redora.Location.Country.find(wcs, Utility_getSystemUnknownCountryDetails_callback, Utility_getSystemUnknownCountryDetails_errorcallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getSystemUnknownCountryDetails_callback, Utility_getSystemUnknownCountryDetails_errorcallback);
}

function Utility_getSystemUnknownCountryDetails_callback(result) {
    voltmx.print("### Utility_getSystemUnknownCountryDetails callback: " + JSON.stringify(result));
    if (result.length == 1) {
        Global.vars.unknownVehicleCountryLicense.code = result[0].code;
        Global.vars.unknownVehicleCountryLicense.description = result[0].description;
        if (result[0].license_code !== undefined && result[0].license_code != null && result[0].license_code !== "") {
            Global.vars.unknownVehicleCountryLicense.module = result[0].license_code;
        } else {
            Global.vars.unknownVehicleCountryLicense.module = voltmx.i18n.getLocalizedString("l_countryUnknownCode");
        }
    } else {
        Global.vars.unknownVehicleCountryLicense = {
            code: 0,
            description: voltmx.i18n.getLocalizedString("l_countryUnknown"),
            module: voltmx.i18n.getLocalizedString("l_countryUnknownCode")
        };
    }
    voltmx.print("### Utility_getSystemUnknownCountryDetails defaultVehicleCountryLicense: " + JSON.stringify(Global.vars.unknownVehicleCountryLicense));
    voltmx.print("### SyncUtil_getInstanceParameterunknownCountryLicense_successcallback: " + JSON.stringify(Global.vars.unknownVehicleCountryLicense));
}

function Utility_getSystemUnknownCountryDetails_errorcallback(error) {
    voltmx.print("### Utility_getSystemUnknownCountryDetails_errorcallback error: " + error);
    Global.vars.unknownVehicleCountryLicense = {
        code: 0,
        description: voltmx.i18n.getLocalizedString("l_countryUnknown"),
        module: voltmx.i18n.getLocalizedString("l_countryUnknownCode")
    };
}

function Utility_getOffenceMunicipalities() {
    voltmx.print("### Utility_getOffenceMunicipalities");
    // alternative if mle_v_offence_municipality_msv cannot be syncronized due to lack of performance
    // var wcs = "select distinct code_mpy as municipal_code, name_mpy as name from mle_v_offence_city_msv";
    //
    var wcs = "select * from mle_v_offence_municipality_msv";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, null, true);
    voltmx.print("### Utility_getOffenceMunicipalities wcs" + wcs);
    //com.redora.Location.OffenceMunicipality.find(wcs, Utility_getOffenceMunicipalitiesSuccessCallback, Utility_getOffenceMunicipalitiesErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getOffenceMunicipalitiesSuccessCallback, Utility_getOffenceMunicipalitiesErrorCallback);
}

function Utility_getOffenceMunicipalitiesSuccessCallback(result) {
    voltmx.print("### Utility_getOffenceMunicipalitiesSuccessCallback" + JSON.stringify(result));
    if (result.length > 0) {
        Global.vars.offenceMunicipalities = result;
    } else {
        Global.vars.offenceMunicipalities = [];
    }
    voltmx.print("### Utility_getOffenceMunicipalitiesSuccessCallback Global.vars.offenceMunicipalities" + JSON.stringify(Global.vars.offenceMunicipalities));
}

function Utility_getOffenceMunicipalitiesErrorCallback(error) {
    voltmx.print("### Utility_getOffenceMunicipalitiesErrorCallback" + JSON.stringify(error));
}

function Utility_findMunicipality(municipalitycode) {
    voltmx.print("### Utility_findMunicipality municipalitycode: " + municipalitycode);
    var municipality = null;
    for (var i in Global.vars.offenceMunicipalities) {
        var v = Global.vars.offenceMunicipalities[i];
        voltmx.print("### Utility_findMunicipality v.municipal_code: " + v.municipal_code);
        if (v.municipal_code == municipalitycode) {
            municipality = v.name;
            break;
        }
    }
    return municipality;
}

function Utility_getOffenceCities() {
    voltmx.print("### Utility_getOffenceCities");
    var wcs = "select * from mle_v_offence_city_msv";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, null, true) + " ORDER BY name ASC";
    voltmx.print("### Utility_getOffenceCities wcs" + wcs);
    //com.redora.Location.OffenceCity.find(wcs, Utility_getOffenceCitiesSuccessCallback, Utility_getOffenceCitiesErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getOffenceCitiesSuccessCallback, Utility_getOffenceCitiesErrorCallback);
}

function Utility_getOffenceCitiesSuccessCallback(result) {
    voltmx.print("### Utility_getOffenceCitiesSuccessCallback" + JSON.stringify(result));
    voltmx.print("### Utility_getOffenceCitiesSuccessCallback" + JSON.stringify(Global.vars.instanceAuthorizations));
    var offenceCities = [];
    Global.vars.offenceCities = [];
    if (result.length > 0) {
        offenceCities = result;
        for (var j in offenceCities) {
            var w = offenceCities[j];
            w.lbl1 = Utility_CapatilizeNames(w.name);
            if (Global.vars.authorizedMunicipalities !== undefined && Global.vars.authorizedMunicipalities.length > 0) {
                voltmx.print("#### Utility_getOffenceCitiesSuccessCallback w: " + w.code_mpy);
                for (var i in Global.vars.authorizedMunicipalities) {
                    var x = Global.vars.authorizedMunicipalities[i];
                    voltmx.print("#### Utility_getOffenceCitiesSuccessCallback x: " + x.municipalityCode);
                    if (w.code_mpy == x.municipalityCode) {
                        w.officerNumber = x.officerId;
                        w.officerIdentification = x.officerIdentification;
                        Global.vars.offenceCities.push(w);
                        voltmx.print("#### Utility_getOffenceCitiesSuccessCallback officerNumber: " + w.officerNumber);
                        break;
                    }
                }
            } else {
                w.officerNumber = Global.vars.gOfficerNumber;
                w.officerIdentification = Global.vars.gOfficerIdentification;
                Global.vars.offenceCities.push(w);
            }
        }
    } else {
        Global.vars.offenceCities = [];
    }
    voltmx.print("### Utility_getOffenceCitiesSuccessCallback Global.vars.offenceCities" + JSON.stringify(Global.vars.offenceCities));
}

function Utility_getOffenceCitiesErrorCallback(error) {
    voltmx.print("### Utility_getOffenceCitiesErrorCallback" + JSON.stringify(error));
}

function Utility_getMunicipalityName(code, callback, errorCallback) {
    var wcs = "select * from mle_v_municipality_m where code = '" + code + "'";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
    voltmx.print("#### Utility_getMunicipalityName wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, callback, errorCallback);
}

function Utility_getCityName(code, callback, errorCallback) {
    var wcs = "select * from mle_v_city_m where code = '" + code + "'";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
    voltmx.print("#### Utility_getCityName wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, callback, errorCallback);
}

function Utility_setOfficerNumberByCity(cityCode) {
    voltmx.print("### Utility_setOfficerNumberByCity " + cityCode);
    if (cityCode !== undefined && cityCode != null) {
        for (var i in Global.vars.offenceCities) {
            var v = Global.vars.offenceCities[i];
            if (v.city_code == cityCode) {
                Global.vars.gOfficerNumber = v.officerNumber;
                Global.vars.gOfficerIdentification = v.officerIdentification;
                CaseData_setUserInformationToCaseInfo();
                voltmx.print("### Utility_setOfficerNumberByCity " + Global.vars.gOfficerNumber);
                break;
            }
        }
    }
}

function Utility_findCityCode(city) {
    voltmx.print("### Utility_setOfficerNumberByCity " + city);
    voltmx.print("### Utility_findCityCode " + city);
    var citycode = null;
    for (var i in Global.vars.offenceCities) {
        var v = Global.vars.offenceCities[i];
        if (v.name.toLowerCase() == city.toLowerCase()) {
            citycode = v.city_code;
            Utility_setOfficerNumberByCity(citycode);
            break;
        }
    }
    voltmx.print("### Utility_findCityCode citycode " + citycode);
    return citycode;
}

function Utility_getFirstMunicipalityCode() {
    voltmx.print("### Utility_getFirstMunicipalityCode");
    var municipalityCode = null;
    if (Global.vars.authorizedMunicipalities.length > 0) {
        municipalityCode = Global.vars.authorizedMunicipalities[0].municipalityCode;
        voltmx.print("### Utility_getFirstMunicipalityCode municipalityCode: " + municipalityCode);
        return municipalityCode;
    }
}

function Utility_checkCountryCity(city, callback) {
    var countrycode = null;
    for (var i in Global.vars.offenceCities) {
        var v = Global.vars.offenceCities[i];
        if (v.name.toLowerCase() == city.toLowerCase()) {
            countrycode = v.countrycode;
            break;
        }
    }

    function searchcountrycodeErrorCallback(error) {
        voltmx.print("### Utility_checkCountryCity searchcountrycodeErrorCallback: " + JSON.stringify(error));
    }
    var wcs = "select * from mle_v_country_m where code = '" + countrycode + "'";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs);
    //com.redora.Location.Country.find(wcs, callback, searchcountrycodeErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, callback, searchcountrycodeErrorCallback);
}

function Utility_getOffenceSequenceOrder(input, searchText) {
    // order range 1, 2 or 3
    var order = 5;
    // first, starting point of input
    if (searchText !== undefined && searchText.length > 0) {
        if (voltmx.string.startsWith(input, searchText)) {
            order = 1;
        } else if (input.indexOf(searchText) == 1) {
            order = 2;
        } else if (input.indexOf(searchText) == 2) {
            order = 3;
        } else if (input.indexOf(searchText) == 3) {
            order = 4;
        } else if (input.indexOf(searchText) == 4) {
            order = 5;
        } else if (input.indexOf(searchText) == 5) {
            order = 6;
        } else if (input.indexOf(" " + searchText) != -1) {
            order = 7;
        } else if (input.indexOf(" " + searchText.toLowerCase()) != -1) {
            order = 8;
        } else {
            order = 9;
        }
    }
    return order + "_" + input;
}

function Utility_getCaseTypeDescriptionFrmFollow(caseType) {
    voltmx.print("### Utility_getCaseTypeDescriptionFrmFollow");
    var description = "";

    function Utility_getCaseTypeDescriptions_successCallback(result) {
        voltmx.print("### Utility_getCaseTypeDescription_successCallback: " + JSON.stringify(result));
        if (result.length > 0) {
            description = result[0].description;
            voltmx.print("### Utility_getCaseTypeDescription_successCallback description: " + description);
            frmFollow.lblOffenceType.text = description;
        }
    }

    function Utility_getCaseTypeDescriptions_errorCallback(error) {
        voltmx.print("### Utility_getCaseTypeDescription_errorCallback: " + JSON.stringify(error));
    }
    var wcs = "select * from mle_v_case_type_m where identification = '" + caseType + "'";
    var caseTypesWcs = Utility_addTimelineToWhereClauseObjectSync(wcs);
    caseTypesWcs = Utility_addLanguageToWhereClauseObjectSync(caseTypesWcs);
    // 	com.redora.CaseManagementData.CaseType.find(caseTypesWcs,Utility_getCaseTypeDescriptions_successCallback,Utility_getCaseTypeDescriptions_errorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(caseTypesWcs, Utility_getCaseTypeDescriptions_successCallback, Utility_getCaseTypeDescriptions_errorCallback);
}

function Utility_getCaseTypeDescription(caseType, callback) {
    voltmx.print("### Utility_getCaseTypeDescription");
    var description = "";

    function Utility_getCaseTypeDescriptions_errorCallback(error) {
        voltmx.print("### Utility_getCaseTypeDescription_errorCallback: " + JSON.stringify(error));
    }
    var wcs = "select * from mle_v_case_type_m where identification = '" + caseType + "'";
    var caseTypesWcs = Utility_addTimelineToWhereClauseObjectSync(wcs);
    caseTypesWcs = Utility_addLanguageToWhereClauseObjectSync(caseTypesWcs);
    // 	com.redora.CaseManagementData.CaseType.find(caseTypesWcs,callback,Utility_getCaseTypeDescriptions_errorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(caseTypesWcs, callback, Utility_getCaseTypeDescriptions_errorCallback);
}

function Utility_setDeviceLocationToCase(callback) {
    voltmx.print("### Utility_setDeviceLocationToCase");
    Global.vars.setDeviceLocationToCaseCallback = callback;
    //check if tickenumber is set
    if (CaseData.caseinfo.ticketNumber === null || CaseData.caseinfo.ticketNumber === "") {
        Utility_setTicketNumberToCaseData();
    }
    //get my location
    voltmx.runOnMainThread(GPS_getCurrentLocation, [Utility_setDeviceLocationToCaseCallback]);
}

function Utility_setDeviceLocationToCaseCallback() {
    voltmx.print("### Utility_setDeviceLocationToCaseCallback");
    voltmx.print("### Utility_setDeviceLocationToCaseCallback CaseData: " + JSON.stringify(CaseData));
    var deviceLocationSet = false;
    if (Global.vars.gGpsFix === true) {
        if (CaseData.caseinfo.deviceLocation === null) {
            CaseData.caseinfo.deviceLocation = {
                latitude: "",
                longitude: ""
            };
        } else if (CaseData.caseinfo.deviceLocation === undefined) {
            CaseData.caseinfo["deviceLocation"] = {
                latitude: "",
                longitude: ""
            };
        }
        CaseData.caseinfo.deviceLocation.latitude = Global.vars.gLatitude + "";
        CaseData.caseinfo.deviceLocation.longitude = Global.vars.gLongitude + "";
        voltmx.print("### Utility_setDeviceLocationToCaseCallback CaseData.caseinfo.deviceLocation: " + JSON.stringify(CaseData.caseinfo.deviceLocation));
        deviceLocationSet = true;
    }
    voltmx.print("### Utility_setDeviceLocationToCaseCallback deviceLocationSet: " + deviceLocationSet);
    Global.vars.setDeviceLocationToCaseCallback();
}

function Utility_setDeviceLocationToRemoveCase(callback) {
    voltmx.print("### Utility_setDeviceLocationToRemoveCase");
    Global.vars.setDeviceLocationToRemoveCaseCallback = callback;
    //check if tickenumber is set
    if (Global.vars.removeCaseData.caseinfo.ticketNumber === null || Global.vars.removeCaseData.caseinfo.ticketNumber === "") {
        Utility_setTicketNumberToRemoveCaseData();
    }
    //get my location
    //GPS_getCurrentLocation(Utility_setDeviceLocationToRemoveCaseCallback);
    Utility_setDeviceLocationToRemoveCaseCallback();
}

function Utility_setDeviceLocationToRemoveCaseCallback() {
    voltmx.print("### Utility_setDeviceLocationToRemoveCaseCallback");
    var deviceLocationSet = false;
    if (Global.vars.gGpsFix === true) {
        if (Global.vars.removeCaseData.caseinfo.deviceLocation === null) {
            Global.vars.removeCaseData.caseinfo.deviceLocation = {
                latitude: "",
                longitude: ""
            };
        }
        Global.vars.removeCaseData.caseinfo.deviceLocation.latitude = Global.vars.gLatitude + "";
        Global.vars.removeCaseData.caseinfo.deviceLocation.longitude = Global.vars.gLongitude + "";
        voltmx.print("### Utility_setDeviceLocationToRemoveCaseCallback Global.vars.removeCaseData.caseinfo.deviceLocation: " + JSON.stringify(Global.vars.removeCaseData.caseinfo.deviceLocation));
        deviceLocationSet = true;
    }
    voltmx.print("### Utility_setDeviceLocationToRemoveCaseCallback deviceLocationSet: " + deviceLocationSet);
    Global.vars.setDeviceLocationToRemoveCaseCallback();
}

function Utility_setStatus(steidsucceeding) {
    voltmx.print("### Utility_setStatus status id: " + steidsucceeding);
    var lStatusClause = "select * from mle_v_status_type_m where id = '" + steidsucceeding + "'";
    lStatusClause = Utility_addTimelineToWhereClauseObjectSync(lStatusClause, CaseData.time.dateComponents);
    lStatusClause = Utility_addLanguageToWhereClauseObjectSync(lStatusClause);
    //   	com.redora.CaseManagementData.StatusType.find(lStatusClause, Utility_setStatusSuccesscallback,Utility_setStatusErrorcallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lStatusClause, Utility_setStatusSuccesscallback, Utility_setStatusErrorcallback);
}

function Utility_setStatusSuccesscallback(result) {
    voltmx.print("### Utility_setStatusSuccesscallback: " + JSON.stringify(result));
    if (result.length > 0) {
        var addstatus = {
            status: result[0].identification,
            //name of the taskType
            statuscode: result[0].id,
            //Id of the task outcome
            statusSetOn: Utility_getUTCJavascriptDate(null),
            //date in UTC when status was set
            statusSetBy: Global.vars.gUsername,
            //username (email) that set the status
            statusSetByName: CaseData.caseinfo.officerName
        };
        var statusAdded = false;
        for (var i in CaseData.processinfo.statuses) {
            var v = CaseData.processinfo.statuses[i];
            if (v.statuscode == result[0].id) {
                CaseData.processinfo.statuses.splice(i, 1, addstatus);
                statusAdded = true;
                break;
            }
        }
        if (statusAdded === false) {
            CaseData.processinfo.statuses.splice(0, 0, addstatus);
        }
        CaseData.status = result[0].identification;
        CaseData.statuscode = result[0].id;
    }
}

function Utility_setStatusErrorcallback(error) {
    voltmx.print("### Utility_setStatusErrorcallback: " + JSON.stringify(error));
}

function Utility_cleanStatus(steidsucceeding) {
    voltmx.print("### Utility_setStatus status id: " + steidsucceeding);
    if (steidsucceeding != null) {
        var addstatus = {
            status: null,
            //name of the taskType
            statuscode: null,
            //Id of the task outcome
            statusSetOn: null,
            //date in UTC when status was set
            statusSetBy: null,
            //username (email) that set the status
            statusSetByName: null
        };
        var statusAdded = false;
        for (var i in CaseData.processinfo.statuses) {
            var v = CaseData.processinfo.statuses[i];
            if (v.statuscode != null && v.statuscode == steidsucceeding) {
                CaseData.processinfo.statuses.splice(i, 1, addstatus);
                statusAdded = true;
                break;
            }
        }
        if (statusAdded === false) {
            CaseData.processinfo.statuses.splice(0, 0, addstatus);
        }
    }
}

function Utility_setPreviousStatus(status) {
    voltmx.print("### Utility_setPreviousStatus status id: " + status);
    //var statusRemoved = false;
    for (var i in CaseData.processinfo.statuses) {
        var v = CaseData.processinfo.statuses[i];
        if (v.status != null && v.status == status) {
            CaseData.processinfo.statuses.splice(i, 1);
            //statusRemoved = true;
            break;
        }
    }
    //if(statusRemoved === true){
    var latestStatusDate = null;
    new Date(Math.max.apply(null, CaseData.processinfo.statuses.map(function(e) {
        latestStatusDate = e.statusSetOn;
    })));
    voltmx.print("### Utility_setPreviousStatus latestStatusDate: " + latestStatusDate);
    for (var j in CaseData.processinfo.statuses) {
        var w = CaseData.processinfo.statuses[j];
        if (w.statusSetOn == latestStatusDate) {
            voltmx.print("### Utility_setPreviousStatus latest Status: " + JSON.stringify(w));
            CaseData.status = w.status;
            CaseData.statuscode = w.statuscode;
            break;
        }
    }
    //}
    voltmx.print("### Utility_setPreviousStatus status CaseData.status: " + CaseData.status);
    voltmx.print("### Utility_setPreviousStatus status CaseData.statuscode: " + CaseData.statuscode);
    voltmx.print("### Utility_setPreviousStatus status CaseData.processinfo.statuses: " + JSON.stringify(CaseData.processinfo.statuses));
}

function Utility_getRegions() {
    voltmx.print("### Utility_getRegions");

    function Utility_getRegions_successCallback(result) {
        voltmx.print("### Utility_getRegions_successCallback: " + JSON.stringify(result));
        if (result.length > 0) {
            Global.vars.regions = result;
            voltmx.print("### Utility_getRegions_successCallback Global.vars.regions: " + JSON.stringify(Global.vars.regions));
        }
    }

    function Utility_getRegions_errorCallback(error) {
        voltmx.print("### Utility_getRegions_errorCallback: " + JSON.stringify(error));
    }
    var wcs = "select * from mle_v_region_m";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, null, true);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getRegions_successCallback, Utility_getRegions_successCallback);
}

function Utility_setTicketNumberToCaseData() {
    voltmx.print("### Utility_setTicketNumberToCaseData CaseData.time: " + JSON.stringify(CaseData.time));
    CaseData.time.dateComponents = Utility_normalizeDateObject(CaseData.time.dateComponents);
    voltmx.print("### Utility_setTicketNumberToRegisterCaseData: " + JSON.stringify(CaseData.time.dateComponents));
    if (CaseData.time.dateComponents == null) {
        voltmx.print("### Utility_setTicketNumberToCaseData Empty datecomponents: " + CaseData.time.dateComponents);
        if (CaseData.time.utcDateTime !== undefined && CaseData.time.utcDateTime != null && CaseData.time.utcDateTime !== "") {
            var caseDate = new Date(CaseData.time.utcDateTime);
            voltmx.print("### frmCheckLabel_setDateTime CaseTime UTC: " + caseDate);
            var case_date = caseDate.getDate();
            var case_month = caseDate.getMonth();
            case_month++;
            var case_year = caseDate.getFullYear();
            var case_hour = caseDate.getHours();
            var case_min = caseDate.getMinutes();
            var case_sec = caseDate.getSeconds();
            CaseData.time.dateComponents = [
                case_date,
                case_month,
                case_year,
                case_hour,
                case_min,
                case_sec
            ];
        } else {
            var dateTime = voltmx.os.date("*t");
            var dateComponents = [
                dateTime.day,
                dateTime.month,
                dateTime.year,
                dateTime.hour,
                dateTime.min,
                dateTime.sec
            ];
            CaseData.time.dateComponents = dateComponents;
        }
    }
    var dateTimeComponent = Utility_getIdentificationString(CaseData.time.dateComponents, "ticket");
    voltmx.print("### Utility_setTicketNumberToCaseData dateTimeComponent: " + JSON.stringify(dateTimeComponent));
    var lpadLength = 20 - dateTimeComponent.length;
    if (typeof lpadLength == "number" && lpadLength > 0 && lpadLength < 21) {
        voltmx.print("### Utility_setTicketNumberToCaseData Valid length");
    } else {
        lpadLength = 20;
    }
    voltmx.print("### Utility_setTicketNumberToCaseData lpadLength: " + lpadLength);
    if (CaseData.caseinfo.officerNumber != null) {
        CaseData.caseinfo.ticketNumber = dateTimeComponent + (CaseData.caseinfo.officerNumber + "").lpad("0", lpadLength);
        voltmx.print("### Utility_setTicketNumberToCaseData CaseData.caseinfo.ticketNumber: " + JSON.stringify(CaseData.caseinfo.ticketNumber));
    } else if (Global.vars.gOfficerNumber != null) {
        CaseData.caseinfo.ticketNumber = dateTimeComponent + (Global.vars.gOfficerNumber + "").lpad("0", lpadLength);
        voltmx.print("### Utility_setTicketNumberToCaseData Global.vars.gOfficerNumber: " + JSON.stringify(CaseData.caseinfo.ticketNumber));
    }
}

function Utility_setTicketNumberToRemoveCaseData() {
    Global.vars.removeCaseData.time.dateComponents = Utility_normalizeDateObject(Global.vars.removeCaseData.time.dateComponents);
    voltmx.print("### Utility_setTicketNumberToRegisterCaseData: " + JSON.stringify(Global.vars.removeCaseData.time.dateComponents));
    if (Global.vars.removeCaseData.time.dateComponents == null) {
        var dateTime = voltmx.os.date("*t");
        var dateComponents = [
            dateTime.day,
            dateTime.month,
            dateTime.year,
            dateTime.hour,
            dateTime.min,
            dateTime.sec
        ];
        Global.vars.removeCaseData.time.dateComponents = dateComponents;
    }
    var dateTimeComponent = Utility_getIdentificationString(Global.vars.removeCaseData.time.dateComponents, "ticket");
    voltmx.print("### Utility_setTicketNumberToRemoveCaseData dateTimeComponent: " + JSON.stringify(dateTimeComponent));
    var lpadLength = 20 - dateTimeComponent.length;
    if (typeof lpadLength == "number" && lpadLength > 0 && lpadLength < 21) {
        voltmx.print("### Utility_setTicketNumberToRemoveCaseData Valid length");
    } else {
        lpadLength = 20;
    }
    voltmx.print("### Utility_setTicketNumberToRemoveCaseData lpadLength: " + lpadLength);
    if (Global.vars.removeCaseData.caseinfo.officerNumber !== undefined && Global.vars.removeCaseData.caseinfo.officerNumber != null) {
        Global.vars.removeCaseData.caseinfo.ticketNumber = dateTimeComponent + (Global.vars.removeCaseData.caseinfo.officerNumber + "").lpad("0", lpadLength);
        voltmx.print("### Utility_setTicketNumberToRemoveCaseData ticketNumber: " + Global.vars.removeCaseData.caseinfo.ticketNumber);
    }
}

function Utility_getIdentificationString(dateComponent, identificationType) {
    dateComponent = Utility_normalizeDateObject(dateComponent);
    voltmx.print("### Utility_getIdentificationString: " + JSON.stringify(dateComponent));
    if (dateComponent != null) {
        var f = {
            ident: "ddMMyyyyHHmmss",
            sort: "yyyyMMddHHmmss",
            ticket: "ddMMyyHHmmss",
            report: "ddMMyyHHmmss"
        };
        var dateTime = {
            year: dateComponent[2],
            month: dateComponent[1],
            day: dateComponent[0],
            hour: dateComponent[3],
            min: dateComponent[4],
            sec: dateComponent[5]
        };
        var y = dateTime.year,
            m = dateTime.month,
            d = dateTime.day,
            h = dateTime.hour,
            min = dateTime.min,
            s = dateTime.sec;
        f = identificationType in f ? f[identificationType] : "ddMMyyHHmmss";

        function z(s) {
            s = "" + s;
            return s.length > 1 ? s : "0" + s;
        }
        f = f.replace(/yyyy/, y);
        f = f.replace(/yy/, String(y).substr(2));
        f = f.replace(/MM/, z(m));
        f = f.replace(/M/, m);
        f = f.replace(/dd/, z(d));
        f = f.replace(/d/, d);
        f = f.replace(/HH/, z(h));
        f = f.replace(/H/, h);
        f = f.replace(/mm/, z(min));
        f = f.replace(/m/, min);
        f = f.replace(/ss/, z(s));
        f = f.replace(/s/, s);
        voltmx.print("### Utility_getIdentificationString " + identificationType + ":" + f);
        f = f.toString();
        return f;
    } else {
        return "000000000000";
    }
}

function Utility_encode_utf8(string) {
    return unescape(encodeURIComponent(string));
}

function Utility_decode_utf8(string) {
    return decodeURIComponent(escape(string));
}
// Validates bluetooth support and adapter enabled/disabled
function Utility_getBluetoothSupport() {
    voltmx.print("### Utility_getBluetoothSupport");
    // Validating Whether Device Supports Bluetooth or Not
    try {
        if (!context1.getPackageManager().hasSystemFeature(PackageManager.FEATURE_BLUETOOTH_LE)) {
            alert("BluetoothLE Is Not Supported on this Device");
        } else {
            // Initializes Bluetooth adapter.
            var bm = context1.getSystemService(Context.BLUETOOTH_SERVICE);
            var ba = bm.getAdapter();
            // Validating Bluetooth is enabled or not.Enable if not enable
            if (ba === null || !ba.isEnabled()) {
                var enableBTIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                context1.startActivityForResult(enableBTIntent, 2001);
            }
        }
    } catch (e) {
        alert("BluetoothLE Is Not Supported on this Device");
    }
}

function Utility_requestAndroidBLEPermissions() {
    var deviceName = voltmx.os.deviceInfo().name.toLowerCase();
    var deviceOSVersion = voltmx.os.deviceInfo().version;
    if (deviceName === "android" && deviceOSVersion != null && voltmx.os.toNumber(deviceOSVersion) < 12) {
        return;
    }
    voltmx.print("#### requestAndroidBLEPermissions");
    voltmx.application.requestPermissionSet(
        ["android.permission.BLUETOOTH_SCAN", "android.permission.BLUETOOTH_CONNECT"], Utility_androidBLEPermissionStatusCallback);
}

function Utility_androidBLEPermissionStatusCallback(response) {
    for (var i in response) {
        var result = response[i];
        if (result == voltmx.application.PERMISSION_DENIED) {
            // show message  and raise request again
            Utility_requestAndroidBLEPermissions();
        } else if (result == voltmx.application.PERMISSION_NEVER_ASK_AGAIN) {
            // show message and open settings page
            voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_nearbyDevicesPermission"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
            voltmx.application.openApplicationSettings();
        }
    }
}

function Utility_kmeans(arrayToProcess, Clusters) {
    voltmx.print("### Utility_kmeans arrayToProcess1 : " + JSON.stringify(arrayToProcess));
    voltmx.print("### Utility_kmeans Clusters: " + Clusters);
    var Groups = [];
    var Centroids = [];
    var oldCentroids = [];
    var changed = false;
    // order the input array
    arrayToProcess.sort(function(a, b) {
        return a - b;
    });
    voltmx.print("### Utility_kmeans arrayToProcess2 : " + JSON.stringify(arrayToProcess));
    // initialise group arrays
    for (initGroups = 0; initGroups < Clusters; initGroups++) {
        Groups[initGroups] = [];
    }
    voltmx.print("### Utility_kmeans Groups : " + JSON.stringify(Groups));
    // pick initial centroids
    var initialCentroids = Math.round(arrayToProcess.length / (Clusters + 1));
    for (i = 0; i < Clusters; i++) {
        Centroids[i] = arrayToProcess[initialCentroids * (i + 1)];
    }
    do {
        for (j = 0; j < Clusters; j++) {
            Groups[j] = [];
        }
        changed = false;
        var newGroup = null;
        for (i = 0; i < arrayToProcess.length; i++) {
            var distance = -1;
            var oldDistance = -1;
            for (j = 0; j < Clusters; j++) {
                distance = Math.abs(Centroids[j] - arrayToProcess[i]);
                if (oldDistance == -1) {
                    oldDistance = distance;
                    newGroup = j;
                } else if (distance <= oldDistance) {
                    newGroup = j;
                    oldDistance = distance;
                }
            }
            Groups[newGroup].push(arrayToProcess[i]);
        }
        oldCentroids = Centroids;
        for (j = 0; j < Clusters; j++) {
            var total = 0;
            var newCentroid = 0;
            for (i = 0; i < Groups[j].length; i++) {
                total += Groups[j][i];
            }
            newCentroid = total / Groups[newGroup].length;
            Centroids[j] = newCentroid;
        }
        for (j = 0; j < Clusters; j++) {
            if (Centroids[j] != oldCentroids[j]) {
                changed = true;
            }
        }
    } while (changed === true);
    return Groups;
}

function Utility_ElevenCheck(checknumber) {
    voltmx.print("#### Utility_ElevenCheck init checknumber: " + checknumber);
    var elevenchecked = false;
    var checkNumber = checknumber.toString();
    if (checkNumber.length > 7 && checkNumber.length < 10) {
        if (checkNumber.length == 8) {
            checkNumber = "0" + checkNumber;
        }
        voltmx.print("#### Utility_ElevenCheck changed checkNumber: " + checkNumber);
        var A = Number(checkNumber.substring(0, 1));
        voltmx.print("#### Utility_ElevenCheck A: " + A);
        var B = Number(checkNumber.substring(1, 2));
        var C = Number(checkNumber.substring(2, 3));
        var D = Number(checkNumber.substring(3, 4));
        var E = Number(checkNumber.substring(4, 5));
        var F = Number(checkNumber.substring(5, 6));
        var G = Number(checkNumber.substring(6, 7));
        var H = Number(checkNumber.substring(7, 8));
        var I = Number(checkNumber.substring(8));
        voltmx.print("#### Utility_ElevenCheck I: " + I);
        var lcheckeleven = 9 * A + 8 * B + 7 * C + 6 * D + 5 * E + 4 * F + 3 * G + 2 * H + -1 * I;
        voltmx.print("#### lcheckeleven: " + lcheckeleven);
        if (checkNumber == "000000000" || checkNumber == "111111110" || checkNumber == "999999990") {
            voltmx.print("#### Utility_ElevenCheck checkNumber false : This is not a valid number");
            elevenchecked = false;
        } else if (lcheckeleven % 11 != 0) {
            voltmx.print("#### Utility_ElevenCheck checkNumber false : Not dividable by 11");
            //voltmx.ui.Alert("Number is not dividable by 11",
            elevenchecked = false;
        } else {
            voltmx.print("#### Utility_ElevenCheck checkNumber true : " + checkNumber);
            elevenchecked = true;
            voltmx.print("#### Number is ok, dividable by 11");
        }
    } else {
        voltmx.print("#### Utility_ElevenCheck checkNumber false : " + checkNumber);
        elevenchecked = false;
    }
    return elevenchecked;
}

function Utility_getGenderTypes(callback) {
    function getGenderTypes_errorcallback(error) {
        voltmx.ui.Alert("ERROR getting gender types: " + error.msg, null, "error", "Ok", null, "Error", null);
    }
    var wcs = "select * from mle_v_general_reference_msv WHERE name = 'geslachtcode' ";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    wcs = wcs + " ORDER BY ref_order";
    voltmx.print("### getGenderTypes wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, callback, getGenderTypes_errorcallback);
}

function Utility_getAddressTypes(callback) {
    function getAddressTypes_errorcallback(error) {
        voltmx.ui.Alert("ERROR getting adress types: " + error.msg, null, "error", "Ok", null, "Error", null);
    }
    var wcs = "select * from mle_v_general_reference_msv WHERE name = 'adressoort_code' and string_value in ('02', '05')";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    wcs = wcs + " ORDER BY ref_order";
    voltmx.print("### getGenderTypes wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, callback, getAddressTypes_errorcallback);
}

function Utility_getRoadTypes(kindofroad, callback) {
    if (callback === undefined) {
        callback = null;
    }
    CaseData.location.roadType = Global.vars.roadTypeValue;
    CaseData.location.roadTypeDescription = Global.vars.roadTypeDescription;
    //mogelijke waarden kindofroad:
    // snelweg
    // autoweg
    // niet_auto
    // een_weg
    // onverhard
    // militair
    // fietspad
    function getRoadTypes_succescallback(result) {
        voltmx.print("### getRoadTypes_succescallback: " + JSON.stringify(result));
        if (result.length === 1) {
            CaseData.location.roadType = result[0].string_value;
            CaseData.location.roadTypeDescription = result[0].descripton;
        }
        if (callback != null) {
            callback();
        }
    }

    function getRoadTypes_errorcallback(error) {
        voltmx.print("### getRoadTypes_errorcallback: " + JSON.stringify(error));
    }
    var wcs = "select * from mle_v_general_reference_msv WHERE name = 'wegsoort_code' and code = '" + kindofroad + "'";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    wcs = wcs + " ORDER BY ref_order";
    voltmx.print("### Utility_getRoadTypes wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, getRoadTypes_succescallback, getRoadTypes_errorcallback);
}

function Utility_getRoadTypeByCode(kindofroadStringValue) {
    //mogelijke waarden kindofroad:
    // snelweg
    // autoweg
    // niet_auto
    // een_weg
    // onverhard
    // militair
    // fietspad
    function getRoadTypes_succescallback(result) {
        voltmx.print("### Utility_getRoadTypeByCode getRoadTypes_succescallback: " + JSON.stringify(result));
        if (result.length === 1) {
            CaseData.location.roadType = result[0].string_value;
            CaseData.location.roadTypeDescription = result[0].descripton;
        }
    }

    function getRoadTypes_errorcallback(error) {
        voltmx.ui.Alert("ERROR getting road types: " + error.msg, null, "error", "Ok", null, "Error", null);
    }
    var wcs = "select * from mle_v_general_reference_msv WHERE name = 'wegsoort_code' and string_value = '" + kindofroadStringValue + "'";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    wcs = wcs + " ORDER BY ref_order";
    voltmx.print("### Utility_getRoadTypes wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, getRoadTypes_succescallback, getRoadTypes_errorcallback);
}

function Utility_getAllRoadTypes() {
    //mogelijke waarden kindofroad:
    // snelweg
    // autoweg
    // niet_auto
    // een_weg
    // onverhard
    // militair
    // fietspad
    function getAllRoadTypes_succescallback(result) {
        voltmx.print("### getRoadTypes_succescallback: " + JSON.stringify(result));
        if (result.length > 0) {
            Global.vars.roadTypes = result;
        }
        Global.vars.roadTypeValue = kindOfRoad.aRoad.value;
        Global.vars.roadTypeCode = kindOfRoad.aRoad.code;
        Global.vars.roadTypeDescription = "een weg";
        Global.vars.roadTypeFilter = [];
        if (Global.vars.buildFor == "GEN" && Global.vars.gInstanceId == "NL0003" && Global.vars.environment == "development") {
            // do nothing just keep all roadtypes available
        } else {
            Utility_getInstanceParameter("roadTypeFilter", SyncUtil_getInstanceParameterRoadTypeFilter_successcallback);
        }
    }

    function getRoadTypes_errorcallback(error) {
        voltmx.ui.Alert("ERROR getting road types: " + error.msg, null, "error", "Ok", null, "Error", null);
    }
    var wcs = "select * from mle_v_general_reference_msv WHERE name = 'wegsoort_code'";
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    wcs = wcs + " ORDER BY ref_order";
    voltmx.print("### Utility_getRoadTypes wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, getAllRoadTypes_succescallback, getRoadTypes_errorcallback);
}

function Utility_checkZipcode(zipcode) {
    voltmx.print("##### Utility_checkZipcode: " + zipcode);
    var zipcodechecked = false;
    if (zipcode !== undefined && zipcode !== "" && zipcode != null && zipcode.replace(" ", "").length === 6) {
        voltmx.print("##### Utility_checkZipcode length: " + zipcode.replace(" ", "").length);
        var zipcodecheck = zipcode.replace(/\s+/g, "");
        voltmx.print("#### Utility_checkZipcode zipcodecheck: " + zipcodecheck);
        if (!zipcodecheck.match(/^[1-9][0-9]{3}[]?(?!SS|SA|SD)[A-Z]{2}$/i)) {
            ///^[1-9][0-9]{3} ?(?!sa|sd|ss)[a-z]{2}$/i
            zipcodechecked = false;
        } else {
            str = zipcodecheck;
            str = str.replace(/(\d+)/g, function(_, num) {
                return " " + num + " ";
            });
            str = str.trim();
            zipcodechecked = str.toUpperCase();
            voltmx.print("#### Utility_checkZipcode zipcodecheck 2: " + zipcodecheck);
        }
    }
    return zipcodechecked;
}

function Utility_getAge(_date) {
    voltmx.print("#### Utility.getAge _date: " + JSON.stringify(_date));
    var _age = 18;
    var _today = new Date();
    if (CaseData.time.utcDateTime != null) {
        _today = new Date(CaseData.time.utcDateTime);
    }
    voltmx.print("#### Utility.getAge _today: " + _today);
    voltmx.print("#### Utility.getAge _today.getFullYear(): " + _today.getFullYear());
    var _birthDate = _date;
    voltmx.print("#### Utility.getAge _birthDate.year: " + _birthDate.year);
    _age = Number(_today.getFullYear()) - Number(_birthDate.year);
    voltmx.print("#### Utility.getAge age 1: " + _age);
    var m = _today.getMonth() + 1 - _birthDate.month;
    voltmx.print("#### Utility.getAge m: " + m);
    voltmx.print("#### Utility.getAge _today.getDate() " + _today.getDate());
    if (m < 0 || (m === 0 && _today.getDate() < _birthDate.day)) {
        _age--;
    }
    voltmx.print("#### Utility.getAge age 2: " + _age);
    return _age;
}

function Utility_titleCase(str) {
    var string = str.trim();
    var splitStr = string.toLowerCase().split(" ");
    for (var i = 0; i < splitStr.length; i++) {
        // You do not need to check if i is larger than splitStr length, as your for does that for you
        // Assign it back to the array
        splitStr[i] = splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
    }
    // Directly return the joined string
    return splitStr.join(" ");
}

function Utility_getPrintPromptLanguage(countryLicenseCode) {
    Global.vars.printPromptLanguage = "";

    function Utility_getPrintPromptLanguage_successCallback(result) {
        voltmx.print("### Utility_getPrintPromptLanguage_successCallback result: " + JSON.stringify(result));
        if (result.length >= 1) {
            var languageExist = false;
            for (var i in Global.vars.printPromptLanguages) {
                var v = Global.vars.printPromptLanguages[i].prompt;
                if (v[0].language == result[0].default_language) {
                    languageExist = true;
                    break;
                }
            }
            if (languageExist === true) {
                Global.vars.printPromptLanguage = result[0].default_language;
            } else {
                Global.vars.printPromptLanguage = Global.vars.printPromptDefaultLanguage;
            }
        } else {
            Global.vars.printPromptLanguage = Global.vars.printPromptDefaultLanguage;
        }
        //alert(Utility_getPrintPrompt("l_nha"));
    }

    function Utility_getPrintPromptLanguage_errorCallback(error) {
        voltmx.print("### Utility_getPrintPromptLanguage_errorCallback Countries error: " + JSON.stringify(error));
        Global.vars.printPromptLanguage = Global.vars.printPromptDefaultLanguage;
    }
    var wcs = "select * from mle_v_country_m WHERE code = '" + countryLicenseCode + "'";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents, false);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### Countries wcs: " + wcs);
    //com.redora.Location.Country.find(wcs, Utility_getPrintPromptLanguage_successCallback, Utility_getPrintPromptLanguage_errorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getPrintPromptLanguage_successCallback, Utility_getPrintPromptLanguage_errorCallback);
}

function Utility_getPrintPrompt(promptkey) {
    var prompt = "";
    voltmx.print("### Utility_getPrintPrompt: " + JSON.stringify(promptkey));
    for (var i in Global.vars.printPromptLanguages) {
        var v = Global.vars.printPromptLanguages[i].prompt;
        if (v[0].language == Global.vars.printPromptLanguage) {
            voltmx.print("### Utility_getPrintPrompt v: " + JSON.stringify(v));
            for (var j in v) {
                var w = v[j];
                if (w.key == promptkey) {
                    voltmx.print("### Utility_getPrintPrompt w: " + JSON.stringify(w));
                    prompt = w.value;
                    break;
                }
            }
        }
    }
    return prompt;
}

function Utility_setIni(username, cloud) {
    voltmx.print("### Utility_setIni: [ " + username + " / " + cloud + " ]");
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === false) {
        try {
            var vCloud = cloud === undefined ? "" : cloud === null ? "" : cloud;
            var vUsername = username === undefined ? "Onbekend" : username === null ? "Onbekend" : username;
            var file = new voltmx.io.File("internal/Download/usr.ini");
            if (file.exists()) {
                voltmx.print("### Utility_setIni: userIni.exists");
                file.remove();
            }
            var create = file.createFile();
            var content = "[UserData]" + "\r\n" + "username=" + vUsername + "\r\n" + "cloud=" + vCloud;
            var write = file.write(content, false);
            voltmx.print("### Utility_setIni file: " + JSON.stringify(file));
        } catch (error) {
            voltmx.print("### Utility_setIni error: " + JSON.stringify(error));
        }
    } else {
        voltmx.print("### Utility_setIni not Android");
    }
}

function Utility_openJSONCaseData(openCaseName) {
    voltmx.print("### Utility_openJSONCaseData openCaseName: " + openCaseName);
    try {
        var d = new Date();
        var n = d.getTime();
        var filename = "";
        if (openCaseName !== undefined && openCaseName != null && openCaseName !== "") {
            filename = openCaseName;
        } else {
            filename = CaseData.caseinfo.id;
        }
        var path = voltmx.io.FileSystem.getDataDirectoryPath();
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
            if (appgrouppath != null) {
                voltmx.print("### grouppath: " + appgrouppath);
                path = appgrouppath;
            }
        }
        voltmx.print("### Utility_openJSONCaseData path: " + JSON.stringify(path));
        var folderForCaseConcepts = "/concepts/";
        var folderForCaseOutbox = "/outbox/";
        var fileDirectoryConcepts = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts);
        var fileListConcepts = fileDirectoryConcepts.getFilesList();
        if (fileListConcepts === null) {
            fileListConcepts = {};
        }
        voltmx.print("### Utility_openJSONCaseData fileListConcepts: " + fileListConcepts);
        voltmx.print("### Utility_openJSONCaseData fileListConcepts: " + JSON.stringify(fileListConcepts));
        var fileDirectoryOutbox = voltmx.io.FileSystem.getFile(path + folderForCaseOutbox);
        var fileListOutbox = null;
        if (fileDirectoryOutbox !== undefined && fileDirectoryOutbox != null) {
            fileListOutbox = fileDirectoryOutbox.getFilesList();
        }
        if (fileListOutbox === null) {
            fileListOutbox = {};
        }
        voltmx.print("### Utility_openJSONCaseData fileListOutbox: " + fileListOutbox);
        voltmx.print("### Utility_openJSONCaseData fileListOutbox: " + JSON.stringify(fileListOutbox));
        var getfile = "";
        var name = "";
        //check if case is in concepts
        for (var i = 0; i < fileListConcepts.length; i++) {
            voltmx.print("### Utility_openJSONCaseData concepts fullpath i: " + fileListConcepts.item(i).fullPath);
            voltmx.print("### Utility_openJSONCaseData concepts readable i: " + fileListConcepts.item(i).readable);
            voltmx.print("### Utility_openJSONCaseData concepts name i: " + fileListConcepts.item(i).name);
            name = fileListConcepts.item(i).name;
            if (name == filename) {
                voltmx.print("### Utility_openJSONCaseData case found in list concepts: " + fileListConcepts.item(i).name);
                getfile = voltmx.io.FileSystem.getFile(fileListConcepts.item(i).fullPath + "/case.json");
                break;
            }
        }
        //check if case is in outbox
        for (var j = 0; j < fileListOutbox.length; j++) {
            voltmx.print("### Utility_openJSONCaseData outbox fullpath j: " + fileListOutbox.item(j).fullPath);
            voltmx.print("### Utility_openJSONCaseData outbox readable j: " + fileListOutbox.item(j).readable);
            voltmx.print("### Utility_openJSONCaseData outbox name j: " + fileListOutbox.item(j).name);
            name = fileListOutbox.item(j).name;
            if (name == filename) {
                voltmx.print("### Utility_openJSONCaseData case found in list outbox: " + fileListOutbox.item(j).name);
                getfile = voltmx.io.FileSystem.getFile(fileListOutbox.item(j).fullPath + "/case.json");
                break;
            }
        }
        //open the file
        if (getfile !== "" && getfile.exists()) {
            voltmx.print("### Utility_openJSONCaseData getfile exists");
            voltmx.print("### Utility_openJSONCaseData getfile done");
            voltmx.print("### Utility_openJSONCaseData IS READABLE >>>>>>>" + getfile.readable);
            var time = new Date(voltmx.os.toNumber(getfile.modificationTime) * 1000); //modification time is unix timestamp
            var formatted = time.getDate().toString().lpad("0", 2) + "-" + (time.getMonth() + 1).toString().lpad("0", 2) + "-" + time.getFullYear() + " " + time.getHours().toString().lpad("0", 2) + ":" + time.getMinutes().toString().lpad("0", 2);
            voltmx.print("### Utility_openJSONCaseData formatted date: " + formatted);
            var readJSONFromFile = getfile.read();
            voltmx.print("### Utility_openJSONCaseData readJSONFromFile: " + JSON.stringify(readJSONFromFile));
            var stringifiedData = JSON.stringify(readJSONFromFile);
            var parsedData = JSON.parse(stringifiedData);
            voltmx.print("### Utility_openJSONCaseData parsedData: " + JSON.stringify(parsedData));
            voltmx.print("### Utility_openJSONCaseData parsedData text: " + JSON.stringify(parsedData.text));
            var parsedCaseData = JSON.parse(parsedData.text);
            voltmx.print("### Utility_openJSONCaseData parsedData parsedCaseData: " + JSON.stringify(parsedCaseData));
            voltmx.print("### Utility_openJSONCaseData parsedData parsedCaseData processinfo: " + JSON.stringify(parsedCaseData.processinfo));
            if (parsedCaseData.person !== undefined) {
                voltmx.print("### Utility_openJSONCaseData parsedData parsedCaseData person: " + JSON.stringify(parsedCaseData.person));
            }
            return parsedCaseData;
        } else {
            voltmx.print("### Utility_openJSONCaseData getfile does not exist");
            return false;
        }
    } catch (error) {
        voltmx.print("### Utility_openJSONCaseData error: " + JSON.stringify(error));
        return false;
    }
}

function Utility_showCaseFileList(outbox) {
    //show the files in concepts or outbox
    //outbox is a boolean
    voltmx.print("### Utility_showCaseFileList outbox boolean: " + outbox);
    var showFileList = {};
    try {
        if (outbox === undefined || outbox === null || outbox === "") {
            outbox = false;
        }
        var path = voltmx.io.FileSystem.getDataDirectoryPath();
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
            if (appgrouppath != null) {
                voltmx.print("### grouppath: " + appgrouppath);
                path = appgrouppath;
            }
        }
        voltmx.print("### Utility_openJSONCaseData path: " + JSON.stringify(path));
        var folderForCaseConcepts = "/concepts/";
        var folderForCaseOutbox = "/outbox/";
        if (outbox) {
            var fileDirectoryOutbox = voltmx.io.FileSystem.getFile(path + folderForCaseOutbox);
            var fileListOutbox = null;
            if (fileDirectoryOutbox !== undefined && fileDirectoryOutbox != null) {
                fileListOutbox = fileDirectoryOutbox.getFilesList();
            }
            if (fileListOutbox === null) {
                fileListOutbox = {};
            }
            voltmx.print("### Utility_openJSONCaseData fileListOutbox: " + fileListOutbox);
            voltmx.print("### Utility_openJSONCaseData fileListOutbox: " + JSON.stringify(fileListOutbox));
            showFileList = fileListOutbox;
        } else {
            var fileDirectoryConcepts = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts);
            var fileListConcepts = fileDirectoryConcepts.getFilesList();
            if (fileListConcepts === null) {
                fileListConcepts = {};
            }
            voltmx.print("### Utility_openJSONCaseData fileListConcepts: " + fileListConcepts);
            voltmx.print("### Utility_openJSONCaseData fileListConcepts: " + JSON.stringify(fileListConcepts));
            showFileList = fileListConcepts;
        }
        return showFileList;
    } catch (error) {
        voltmx.print("### Utility_openJSONCaseData error: " + JSON.stringify(error));
        return showFileList;
    }
}

function Utility_caseInFilesystemOutbox(caseName) {
    //check if case is in outbox
    var path = voltmx.io.FileSystem.getDataDirectoryPath();
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
        if (appgrouppath != null) {
            voltmx.print("### grouppath: " + appgrouppath);
            path = appgrouppath;
        }
    }
    var fileDirectoryOutbox = voltmx.io.FileSystem.getFile(path + "/outbox/");
    var fileListOutbox = null;
    if (fileDirectoryOutbox !== undefined && fileDirectoryOutbox != null) {
        fileListOutbox = fileDirectoryOutbox.getFilesList();
    }
    var fileInOutBox = false;
    var getfile = "";
    if (fileListOutbox === null) {
        fileListOutbox = {};
    }
    for (var j = 0; j < fileListOutbox.length; j++) {
        voltmx.print("### Utility_caseInFilesystemOutbox outbox fullpath j: " + fileListOutbox.item(j).fullPath);
        voltmx.print("### Utility_caseInFilesystemOutbox outbox readable j: " + fileListOutbox.item(j).readable);
        voltmx.print("### Utility_caseInFilesystemOutbox outbox name j: " + fileListOutbox.item(j).name);
        var name = fileListOutbox.item(j).name;
        if (name == caseName) {
            voltmx.print("### Utility_caseInFilesystemOutbox case found in list outbox: " + fileListOutbox.item(j).name);
            getfile = voltmx.io.FileSystem.getFile(fileListOutbox.item(j).fullPath);
            break;
        }
    }
    //open the file
    if (getfile !== "" && getfile.exists()) {
        voltmx.print("### Utility_caseInFilesystemOutbox getfile exists");
        voltmx.print("### Utility_caseInFilesystemOutbox getfile done");
        voltmx.print("### Utility_caseInFilesystemOutbox IS READABLE >>>>>>>" + getfile.readable);
        fileInOutBox = true;
    }
    return fileInOutBox;
}

function Utility_FinishAfterSendUpload() {
    voltmx.print("### Utility_FinishAfterSendUpload");
    try {
        voltmx.timer.cancel("FinishUploadAfter");
    } catch (err) {}
    Global_resetGlobals();
    CaseData_init();
    voltmx.application.dismissLoadingScreen();
}
//get vehiclecountries
function Utility_executeQueryVehicleCountries() {
    voltmx.print("### Utility_executeQueryVehicleCountries");
    Global.vars.gCountries = {
        showCount: 150,
        startRec: 0,
        endRec: 150,
        orderBy: "ORDER BY Description ASC",
        whereClause: "select * from mle_v_country_vehicle_reg_msv",
        subClause: ""
    };
    var wcs = Utility_addTimelineToWhereClauseObjectSync(Global.vars.gCountries.whereClause, null, true);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    wcs = wcs.replace("language_code", "cty_language_code");
    voltmx.print("### Utility_executeQueryVehicleCountries Countries wcs: " + wcs);
    //com.redora.Vehicle.CountryVehicleReg.find(wcs, Utility_executeQueryVehicleCountries_successCallback, Utility_executeQueryVehicleCountries_errorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_executeQueryVehicleCountries_successCallback, Utility_executeQueryVehicleCountries_errorCallback);
}

function Utility_executeQueryVehicleCountries_successCallback(result) {
    voltmx.print("### Utility_executeQueryVehicleCountries_successCallback result length: " + JSON.stringify(result.length));
    // only add records if there were new database records
    var unknown = {};
    unknown.category = "country";
    unknown.country_code = Global.vars.unknownVehicleCountryLicense.code;
    unknown.cty_licenseCode = Global.vars.unknownVehicleCountryLicense.module;
    unknown.cty_description = Global.vars.unknownVehicleCountryLicense.description;
    var lblone = Global.vars.unknownVehicleCountryLicense.description;
    unknown.lblOne = lblone;
    unknown.lblCountryCode = Global.vars.unknownVehicleCountryLicense.module;
    unknown.orderSequence = Utility_getSequenceOrder(lblone, frmVehicleCountries.search.txtSearch.text);
    unknown.Scanable = false;
    unknown.VehicleReg = false;
    unknown.scanOrder = 4;
    unknown.orderSequence = "5" + unknown.orderSequence;
    var automaticProcessCountries = [];
    if (result.length >= 1) {
        var p = 0;
        var licenseCode = false;
        for (var i = 0; result != null && i < result.length; i++) {
            var v = result[i];
            result[i].category = "country";
            if (result[i].ctydescription == Global.vars.unknownVehicleCountryLicense.description) {
                licenseCode = true;
                if (result[i].cty_licensecode === undefined || result[i].cty_licensecode === null) {
                    result[i].cty_licensecode = Global.vars.unknownVehicleCountryLicense.module;
                    result[i].lblCountryCode = Global.vars.unknownVehicleCountryLicense.module;
                }
            } else if (result[i].cty_licensecode === undefined || result[i].cty_licensecode === null) {
                result[i].cty_licensecode = Global.vars.unknownVehicleCountryLicense.module;
                result[i].lblCountryCode = "-";
            } else {
                result[i].lblCountryCode = result[i].cty_licensecode;
            }
            result[i].lblOne = result[i].cty_description;
            result[i].orderSequence = Utility_getSequenceOrder(result[i].cty_licensecode + " - " + result[i].cty_description, frmVehicleCountries.search.txtSearch.text);
            if (Utility_stringToBoolean(result[i].ind_scanable) === true && Utility_stringToBoolean(result[i].ind_vehicle_reg_sys) === true) {
                result[i].Scanable = true;
                result[i].VehicleReg = true;
                result[i].scanOrder = 1;
                result[i].orderSequence = "1" + result[i].orderSequence;
            } else if (Utility_stringToBoolean(result[i].ind_scanable) === true && Utility_stringToBoolean(result[i].ind_vehicle_reg_sys) === false) {
                result[i].Scanable = true;
                result[i].VehicleReg = false;
                result[i].scanOrder = 2;
                result[i].orderSequence = "2" + result[i].orderSequence;
            } else if (Utility_stringToBoolean(result[i].ind_scanable) === false && Utility_stringToBoolean(result[i].ind_vehicle_reg_sys) === true) {
                result[i].Scanable = false;
                result[i].VehicleReg = true;
                result[i].scanOrder = 3;
                result[i].orderSequence = "3" + result[i].orderSequence;
            } else {
                result[i].Scanable = false;
                result[i].VehicleReg = false;
                result[i].scanOrder = 4;
                result[i].orderSequence = "4" + result[i].orderSequence;
            }
            if (Global.vars.carChecked === true && Utility_stringToBoolean(result[i].ind_vehicle_reg_sys) === true) {
                // if plate is checked and country vehicleregsys is on it cannot be choosen
            } else {
                automaticProcessCountries.push(result[i]);
            }
        }
        if (licenseCode === false) {
            automaticProcessCountries.push(unknown);
        }
        voltmx.print("### Utility_executeQueryVehicleCountries_successCallback unknown automaticProcessCountries: " + JSON.stringify(automaticProcessCountries));
        // Sort
        voltmx.print("### Utility_executeQueryVehicleCountries_successCallback automaticProcessCountries: " + JSON.stringify(automaticProcessCountries)); //alle landen gesorteerd
        Global.vars.allCountries = automaticProcessCountries;
    }
}

function Utility_executeQueryVehicleCountries_errorCallback(error) {
    voltmx.print("### Utility_executeQueryVehicleCountries_errorCallback: " + error);
}

function Utility_getVehicleRegIndication(countryLicense) {
    voltmx.print("### Utility_getVehicleRegIndication");
    var vehicleReg = false;
    for (var i in Global.vars.allCountries) {
        var v = Global.vars.allCountries[i];
        voltmx.print("### Utility_getVehicleRegIndication v: " + JSON.stringify(v));
        if (v.cty_description !== undefined && v.cty_licensecode !== undefined) {
            var _countryLicense = v.cty_licensecode.toLowerCase();
            if (_countryLicense === countryLicense.toLowerCase()) {
                vehicleReg = v.VehicleReg;
                voltmx.print("### Utility_getVehicleRegIndication for loop hit: " + v.cty_licensecode + " - " + v.VehicleReg);
                break;
            }
        }
    }
    voltmx.print("### Utility_getVehicleRegIndication vehicleReg: " + vehicleReg);
    return vehicleReg;
}

function Utility_deleteJSONCaseData(deleteCaseName) {
    voltmx.print("### Utility_deleteJSONCaseData deleteCaseName: " + deleteCaseName);
    try {
        var d = new Date();
        var n = d.getTime();
        if (deleteCaseName != null && deleteCaseName != null && deleteCaseName !== "") {
            var path = voltmx.io.FileSystem.getDataDirectoryPath();
            if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
                var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
                if (appgrouppath != null) {
                    voltmx.print("### grouppath: " + appgrouppath);
                    path = appgrouppath;
                }
            }
            voltmx.print("### Utility_openJSONCaseData path: " + JSON.stringify(path));
            var folderForCaseConcepts = "/concepts/";
            var folderForCaseOutbox = "/outbox/";
            var fileDirectoryConcepts = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts);
            var fileListConcepts = fileDirectoryConcepts.getFilesList();
            if (fileListConcepts === null) {
                fileListConcepts = {};
            }
            voltmx.print("### Utility_openJSONCaseData fileListConcepts: " + fileListConcepts);
            voltmx.print("### Utility_openJSONCaseData fileListConcepts: " + JSON.stringify(fileListConcepts));
            var fileDirectoryOutbox = voltmx.io.FileSystem.getFile(path + folderForCaseOutbox);
            var fileListOutbox = null;
            if (fileDirectoryOutbox !== undefined && fileDirectoryOutbox != null) {
                fileListOutbox = fileDirectoryOutbox.getFilesList();
            }
            if (fileListOutbox === null) {
                fileListOutbox = {};
            }
            voltmx.print("### Utility_openJSONCaseData fileListOutbox: " + fileListOutbox);
            voltmx.print("### Utility_openJSONCaseData fileListOutbox: " + JSON.stringify(fileListOutbox));
            var getfileForDeletionOutBox = "";
            var getfileForDeletionConcepts = "";
            var name = "";
            //check if case is in concepts
            for (var i = 0; i < fileListConcepts.length; i++) {
                voltmx.print("### Utility_openJSONCaseData concepts fullpath i: " + fileListConcepts.item(i).fullPath);
                voltmx.print("### Utility_openJSONCaseData concepts readable i: " + fileListConcepts.item(i).readable);
                voltmx.print("### Utility_openJSONCaseData concepts name i: " + fileListConcepts.item(i).name);
                name = fileListConcepts.item(i).name;
                if (name == deleteCaseName) {
                    voltmx.print("### Utility_openJSONCaseData case found in list concepts: " + fileListConcepts.item(i).name);
                    getfileForDeletionConcepts = voltmx.io.FileSystem.getFile(fileListConcepts.item(i).fullPath);
                    break;
                }
            }
            //check if case is in outbox
            for (var j = 0; j < fileListOutbox.length; j++) {
                voltmx.print("### Utility_openJSONCaseData outbox fullpath j: " + fileListOutbox.item(j).fullPath);
                voltmx.print("### Utility_openJSONCaseData outbox readable j: " + fileListOutbox.item(j).readable);
                voltmx.print("### Utility_openJSONCaseData outbox name j: " + fileListOutbox.item(j).name);
                name = fileListOutbox.item(j).name;
                if (name == deleteCaseName) {
                    voltmx.print("### Utility_openJSONCaseData case found in list outbox: " + fileListOutbox.item(j).name);
                    getfileForDeletionOutBox = voltmx.io.FileSystem.getFile(fileListOutbox.item(j).fullPath);
                    break;
                }
            }
            if (getfileForDeletionOutBox !== "" && getfileForDeletionOutBox.exists()) {
                voltmx.print("### Utility_deleteJSONCaseData file to delete exists in outbox");
                getfileForDeletionOutBox.remove(true);
            }
            if (getfileForDeletionOutBox !== "" && !getfileForDeletionOutBox.exists()) {
                voltmx.print("### Utility_deleteJSONCaseData file deleted from outBox");
            }
            if (getfileForDeletionConcepts !== "" && getfileForDeletionConcepts.exists()) {
                voltmx.print("### Utility_deleteJSONCaseData file to delete exists in concepts");
                getfileForDeletionConcepts.remove(true);
            }
            if (getfileForDeletionConcepts !== "" && !getfileForDeletionConcepts.exists()) {
                voltmx.print("### Utility_deleteJSONCaseData file deleted from concepts");
            }
            voltmx.print("### Utility_deleteJSONCaseData done");
            Utility_countFilelists();
        } else {
            voltmx.print("### Utility_deleteJSONCaseData no case to delete");
        }
    } catch (error) {
        voltmx.print("### Utility_deleteJSONCaseData error: " + JSON.stringify(error));
    }
}

function Utility_deleteConceptsAfterDays(numberdays) {
    try {
        var filelist = Utility_showCaseFileList(false);
        voltmx.print("#### Utility_deleteConceptsAfterDays filelist: " + JSON.stringify(filelist));
        //check if case is in concepts
        voltmx.print("#### Utility_deleteConceptsAfterDays filelist length: " + filelist.length);
        for (var i = 0; i < filelist.length; i++) {
            voltmx.print("### Utility_deleteConceptsAfterDays filelist fullpath i: " + filelist.item(i).fullPath);
            voltmx.print("### Utility_deleteConceptsAfterDays filelist readable i: " + filelist.item(i).readable);
            voltmx.print("### Utility_deleteConceptsAfterDays filelist name i: " + filelist.item(i).name);
            voltmx.print("### Utility_deleteConceptsAfterDays case modification time: " + filelist.item(i).modificationTime);
            var name = filelist.item(i).name;
            var date = filelist.item(i).modificationTime;
            if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
                date = new Date(date * 1000); //modification time is unix timestamp
            }
            var javascriptDateFile = new Date(date);
            var d = new Date();
            var deleteFromDate = d.setDate(d.getDate() - Number(numberdays));
            deleteFromDate = new Date(deleteFromDate);
            voltmx.print("### Utility_deleteConceptsAfterDays deleteFromDate: " + deleteFromDate);
            voltmx.print("### Utility_deleteConceptsAfterDays javascriptDateFile: " + javascriptDateFile);
            if (javascriptDateFile < deleteFromDate) {
                voltmx.print("### Utility_deleteConceptsAfterDays  file must be deleted name: " + name);
                var getfileForDeletion = voltmx.io.FileSystem.getFile(filelist.item(i).fullPath);
                getfileForDeletion.remove(true);
                if (new voltmx.io.File(filelist.item(i).fullPath).exists()) {
                    voltmx.print("### Utility_deleteConceptsAfterDays removing file failed");
                } else {
                    voltmx.print("### Utility_deleteConceptsAfterDays removing file was successful");
                }
            }
        }
    } catch (e) {
        voltmx.print("#### Utility_deleteConceptsAfterDays error: " + JSON.stringify(e));
    }
}

function Utility_deleteConceptsThatAreOnline() {
    try {
        var filelist = Utility_showCaseFileList(false);
        voltmx.print("#### Utility_deleteConceptsThatAreOnline filelist: " + JSON.stringify(filelist));
        //check if case is in concepts
        voltmx.print("#### Utility_deleteConceptsThatAreOnline filelist length: " + filelist.length);
        for (var i = 0; i < filelist.length; i++) {
            voltmx.print("### Utility_deleteConceptsThatAreOnline filelist fullpath i: " + filelist.item(i).fullPath);
            voltmx.print("### Utility_deleteConceptsThatAreOnline filelist readable i: " + filelist.item(i).readable);
            voltmx.print("### Utility_deleteConceptsThatAreOnline filelist name i: " + filelist.item(i).name);
            voltmx.print("### Utility_deleteConceptsThatAreOnline case modification time: " + filelist.item(i).modificationTime);
            var name = filelist.item(i).name;
            if (name.startsWith("noCouchCase") === false) {
                voltmx.print("### Utility_deleteConceptsThatAreOnline  file must be deleted name: " + name);
                var getfileForDeletion = voltmx.io.FileSystem.getFile(filelist.item(i).fullPath);
                getfileForDeletion.remove(true);
                if (new voltmx.io.File(filelist.item(i).fullPath).exists()) {
                    voltmx.print("### Utility_deleteConceptsThatAreOnline removing file failed");
                } else {
                    voltmx.print("### Utility_deleteConceptsThatAreOnline removing file was successful");
                }
            }
        }
    } catch (e) {
        voltmx.print("#### Utility_deleteConceptsThatAreOnline error: " + JSON.stringify(e));
    }
}

function Utility_returnCaseTypeDesc(caseType) {
    function Utility_returnCaseTypeDesc_successCallback(result) {
        voltmx.print("### Utility_returnCaseTypeDesc_successCallback: " + JSON.stringify(result));
        if (result.length > 0) {
            return result[0].description;
        } else {
            return "";
        }
    }

    function Utility_returnCaseTypeDesc_errorCallback(error) {
        voltmx.print("### Utility_returnCaseTypeDesc_errorCallback: " + JSON.stringify(error));
        return "";
    }
    var wcs = "select * from mle_v_case_type_m where identification = '" + caseType + "'";
    var caseTypesWcs = Utility_addTimelineToWhereClauseObjectSync(wcs);
    caseTypesWcs = Utility_addLanguageToWhereClauseObjectSync(caseTypesWcs);
    //   com.redora.CaseManagementData.CaseType.find(caseTypesWcs,Utility_returnCaseTypeDesc_successCallback,Utility_returnCaseTypeDesc_errorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(caseTypesWcs, Utility_returnCaseTypeDesc_successCallback, Utility_returnCaseTypeDesc_errorCallback);
}

function Utility_deleteEmptyOutboxFiles() {
    try {
        var filelist = Utility_showCaseFileList(true);
        voltmx.print("#### Utility_deleteEmptyOutboxFiles filelist: " + JSON.stringify(filelist));
        //check if case is in concepts
        voltmx.print("#### Utility_deleteEmptyOutboxFiles filelist length: " + filelist.length);
        for (var i = 0; i < filelist.length; i++) {
            var parsedData = null;
            voltmx.print("### Utility_deleteEmptyOutboxFiles filelist fullpath i: " + filelist.item(i).fullPath);
            voltmx.print("### Utility_deleteEmptyOutboxFiles filelist readable i: " + filelist.item(i).readable);
            voltmx.print("### Utility_deleteEmptyOutboxFiles filelist name i: " + filelist.item(i).name);
            voltmx.print("### Utility_deleteEmptyOutboxFiles case modification time: " + filelist.item(i).modificationTime);
            var name = filelist.item(i).name;
            if (name.startsWith("noCouchCase") === true) {
                voltmx.print("### Utility_deleteEmptyOutboxFiles file name: " + name);
                var getfileForDeletion = voltmx.io.FileSystem.getFile(filelist.item(i).fullPath);
                var getfile = voltmx.io.FileSystem.getFile(filelist.item(i).fullPath + "/case.json");
                var readJSONFromFile = getfile.read();
                var stringifiedData = JSON.stringify(readJSONFromFile);
                parsedData = JSON.parse(stringifiedData);
                if (parsedData != null) {
                    //voltmx.print("### Utility_deleteEmptyOutboxFiles parsedData text: " + JSON.stringify(parsedData.text));
                    var parsedCaseData = JSON.parse(parsedData.text);
                    voltmx.print("### Utility_deleteEmptyOutboxFiles parsedCaseData time: " + JSON.stringify(parsedCaseData.time));
                    if (parsedCaseData.time.localeShortDate === null) {
                        getfileForDeletion.remove(true);
                        if (new voltmx.io.File(filelist.item(i).fullPath).exists()) {
                            voltmx.print("### Utility_deleteEmptyOutboxFiles removing file failed");
                        } else {
                            voltmx.print("### Utility_deleteEmptyOutboxFiles removing file was successful");
                        }
                    }
                }
            }
        }
    } catch (e) {
        voltmx.print("#### Utility_deleteEmptyOutboxFiles error: " + JSON.stringify(e));
    }
}

function Utility_addPhotoToCase(photoinfo) {
    voltmx.print("#### Utility_addPhotoToCase CaseData multimedia length before: " + CaseData.multimedia.length);
    voltmx.print("#### Utility_addPhotoToCase photoinfo: " + JSON.stringify(photoinfo));
    if (photoinfo != null) {
        // add record to CaseData.text
        var loctextindex = null;
        for (var p = 0; CaseData.multimedia != null && p < CaseData.multimedia.length; p++) {
            var v = CaseData.multimedia[p];
            if (v.fileName == photoinfo.fileName) {
                voltmx.print("#### Utility_addPhotoToCase photo exists: " + v + " index: " + p);
                loctextindex = p;
                break;
            }
        }
        if (loctextindex === null) {
            voltmx.print("### Utility_addPhotoToCase addrecord: " + JSON.stringify(photoinfo));
            CaseData.multimedia.splice(0, 0, photoinfo);
        } else {
            voltmx.print("### Utility_addPhotoToCase replace record: " + JSON.stringify(photoinfo));
            if (CaseData.multimedia[loctextindex].photoStatus == "done") {
                photoinfo.photoStatus = "done";
            }
            CaseData.multimedia.splice(loctextindex, 1, photoinfo);
        }
        voltmx.print("### Utility_addPhotoToCase CaseData multimedia length after: " + CaseData.multimedia.length);
    }
}

function Utility_showPhotos(base64FromImage, name, callback) {
    voltmx.print("### Utility_showPhotos");
    var time = new Date();
    voltmx.print("### file modification time 1b: " + time);
    var formatted = Utility_getLocalizedDateTimeString(time, false);
    var fileExtension = ".jpg";
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        fileExtension = ".png";
    }
    var filename = "";
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
        fileExtension = ".png";
    }
    if (name.includes(".jpg") === false && name.includes(".png") === false) {
        filename = name + fileExtension;
    } else {
        filename = name;
    }
    voltmx.print("### Utility_showPhotos name: " + filename);
    voltmx.print("### Utility_showPhotos file modification time formatted: " + formatted);
    //push photo into gphoto
    var lLocationText = "";
    var lblLocationHeader = voltmx.i18n.getLocalizedString("l_location");
    if (Global.vars.previousForm == "frmResume") {
        lLocationText = frmResume.location.lblText.text;
    } else if (Global.vars.previousForm == "frmNHA") {
        lLocationText = frmNHA.location.lblText.text;
    } else if (Global.vars.previousForm == "frmClamp") {
        lLocationText = frmClamp.lblLocation.text;
    } else if (Global.vars.previousForm == "frmActiveCaseResume") {
        lLocationText = frmActiveCaseResume.lblLocation.text;
    } else if (Global.vars.previousForm == "frmCheckLabel") {
        lLocationText = frmCheckLabel.location.lblText.text;
    } else if (Global.vars.previousForm == "frmRegisterLabel") {
        lLocationText = frmRegisterLabel.location.lblText.text;
    } else {
        lblLocationHeader = voltmx.i18n.getLocalizedString("l_photo");
        lLocationText = filename;
    }
    if (lLocationText == voltmx.i18n.getLocalizedString("l_location")) {
        lLocationText = "";
    }
    voltmx.print("### photo size: " + base64FromImage.length);
    var newPhoto = {
        lblLocationHeader: lblLocationHeader,
        lblLocation: lLocationText,
        imgPhoto: {
            base64: base64FromImage
        },
        lblDate: formatted,
        imgReceipt: "empty.png",
        id: filename,
        fileName: filename,
        nr: Global.vars.mediaSequence,
        imgSelect: "unchecked.png"
    };
    Global.vars.newPhotos.push(newPhoto);
    var photoData = [];
    for (var j = 0; j < Global.vars.newPhotos.length; j++) {
        voltmx.print("### Utility_showPhotos Global.vars.newPhotos signature check length: " + Global.vars.newPhotos.length);
        var w = Global.vars.newPhotos[j];
        if (
            (w.id !== undefined && w.id != null && w.id.startsWith("signature") === false) || w.id === undefined || w.id === null) {
            photoData.push(w);
        }
    }

    function segPhotos_setData() {
        voltmx.print("### Utility_showPhotos inner function segPhotos_setData");
        frmPhotos.segPhotos.setData(photoData);
    }
    //functie die de UI update
    voltmx.runOnMainThread(segPhotos_setData, []); //werkt alleen op iOS
    //frmPhotos.segPhotos.setData(photoData);
    voltmx.print("### Global.vars.newPhotos 1: " + JSON.stringify(Global.vars.newPhotos));
    if (Global.vars.newPhotos.length > 0) {
        Global.vars.newPhotos.sort(function(a, b) {
            return b.nr - a.nr;
        });
    }
    //functie die de UI update
    //voltmx.runOnMainThread(frmPhotos_checkPhotoNumber, []);
    if (callback !== undefined && callback != null) {
        callback();
    } else {
        voltmx.application.dismissLoadingScreen();
    }
}

function Utility_finishedUpdateMultimedia() {
    voltmx.application.dismissLoadingScreen();
    if (Global.vars.couchCaseUpdateMultimediaCallback !== undefined && Global.vars.couchCaseUpdateMultimediaCallback != null) {
        Global.vars.couchCaseUpdateMultimediaCallback();
        Global.vars.couchCaseUpdateMultimediaCallback = null;
    } else {
        Global.vars.couchCaseUpdateMultimediaCallback = null;
    }
}
// function Utility_savePhotoToFileSystem(caseName,filename,imgData,base64boolean,description, documentType,callback){
//   voltmx.print("### Utility_savePhotoToFileSystem savedFileName: " +caseName);
//   if(base64boolean === true){
//     imgData = voltmx.convertToRawBytes(imgData);
//   }
//   try {
//     if(caseName === undefined || caseName === null || caseName === ""){
//       caseName = Global.vars.writtenCaseName;
//     }
//     voltmx.print("### Utility_savePhotoToFileSystem savedFileName: " +caseName);
//     var path = voltmx.io.FileSystem.getDataDirectoryPath();
//     voltmx.print("### Utility_savePhotoToFileSystem path: " + JSON.stringify(path));
//     if(voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true){
//       var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
//       if(appgrouppath != null){
//         voltmx.print("### grouppath: " + appgrouppath);
//         path = appgrouppath;
//       }
//     }
//     var folderForCaseConcepts = "/concepts/";
//     var folderForCaseOutbox = "/outbox/";
//     var fileDirectoryConcepts = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts);
//     var fileListConcepts = fileDirectoryConcepts.getFilesList();
//     if(fileListConcepts === null){
//       fileListConcepts ={};
//     }
//     voltmx.print("### Utility_savePhotoToFileSystem fileListConcepts: " + fileListConcepts);
//     voltmx.print("### Utility_savePhotoToFileSystem fileListConcepts: " + JSON.stringify(fileListConcepts));
//     var fileDirectoryOutbox = voltmx.io.FileSystem.getFile(path +folderForCaseOutbox);
//     var fileListOutbox = null;
//     if (fileDirectoryOutbox !== undefined && fileDirectoryOutbox != null){
//       fileListOutbox = fileDirectoryOutbox.getFilesList();
//     }
//   	if(fileListOutbox === null){
//       fileListOutbox ={};
//     }
//     voltmx.print("### Utility_savePhotoToFileSystem fileListOutbox: " + fileListOutbox);
//     voltmx.print("### Utility_savePhotoToFileSystem fileListOutbox: " + JSON.stringify(fileListOutbox));
//     var getfileDirectoryConcepts = "";
//     var getfileDirectoryOutBox = "";
//     var name = "";
//     var saveCasePath = "";
//     //check if case is in concepts
//     for(var i = 0; i < fileListConcepts.length; i++){
//       voltmx.print("### Utility_savePhotoToFileSystem concepts fullpath i: " + fileListConcepts.item(i).fullPath);
//       voltmx.print("### Utility_savePhotoToFileSystem concepts readable i: " + fileListConcepts.item(i).readable);
//       voltmx.print("### Utility_savePhotoToFileSystem concepts name i: " + fileListConcepts.item(i).name);
//       name = fileListConcepts.item(i).name;
//       if(name == caseName){
//         voltmx.print("### Utility_savePhotoToFileSystem case found in list concepts: " + fileListConcepts.item(i).name);
//         getfileDirectoryConcepts = voltmx.io.FileSystem.getFile(fileListConcepts.item(i).fullPath);
//         saveCasePath = fileListConcepts.item(i).fullPath;
//         break;
//       }
//     }
//     //check if case is in outbox
//     for(var j = 0; j < fileListOutbox.length; j++){
//       voltmx.print("### Utility_savePhotoToFileSystem outbox fullpath j: " + fileListOutbox.item(j).fullPath);
//       voltmx.print("### Utility_savePhotoToFileSystem outbox readable j: " + fileListOutbox.item(j).readable);
//       voltmx.print("### Utility_savePhotoToFileSystem outbox name j: " + fileListOutbox.item(j).name);
//       name = fileListOutbox.item(j).name;
//       if(name == caseName){
//         voltmx.print("### Utility_savePhotoToFileSystem case found in list outbox: " + fileListOutbox.item(j).name);
//         getfileDirectoryOutBox = voltmx.io.FileSystem.getFile(fileListOutbox.item(j).fullPath);
//         saveCasePath = fileListOutbox.item(j).fullPath;
//         break;
//       }
//     }
//     var directoryexists = false;
//     if (getfileDirectoryConcepts !=="" && getfileDirectoryConcepts.exists()){
//       voltmx.print("### Utility_savePhotoToFileSystem file directory exists in concepts");
//       directoryexists = true;
//     }
//     if (getfileDirectoryOutBox !=="" && getfileDirectoryOutBox.exists()){
//       voltmx.print("### Utility_savePhotoToFileSystem file  directory exists in outbox");
//       directoryexists = true;
//     }
//     if(directoryexists === false){
//       var d = new Date();
// 	  var n = d.getTime();
//       if(caseName !== undefined && caseName != null && caseName !== ""){
//         caseName = caseName;
//       }else if(CaseData.caseinfo.id === null || CaseData.caseinfo.id === ""){
//         caseName = "noCouchCase" + n;
//       }else{
//         caseName = CaseData.caseinfo.id;
//       }
//       //first save the case to concepts
//       var myDirectoryCase = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts + caseName + "/");
//       var directorycreatedCase = myDirectoryCase.createDirectory();
//       voltmx.print("### Utility_savePhotoToFileSystem directorycreatedCase: " + JSON.stringify(directorycreatedCase));//returns false if already exists
//       //create the file in directory jsonfiles
//       var myfileCase = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts + caseName + "/" + "case.json");
//       if (myfileCase.exists()){
//           voltmx.print("### Utility_savePhotoToFileSystem myfile exists");
//           myfileCase.remove();
//       }
//       var filecreatedCase = myfileCase.createFile();
//       voltmx.print("### Utility_savePhotoToFileSystem Case IS READABLE >>>>>>>"+myfileCase.readable);
//       voltmx.print("### Utility_savePhotoToFileSystem Case IS WRITABLE >>>>>>>"+myfileCase.writable);
//       var dataToWrite = JSON.stringify(CaseData);
//       var dataAddedCase = myfileCase.write(dataToWrite,false);
//       voltmx.print("### Utility_savePhotoToFileSystem Case IS DATA ADDED >>>>>>>"+dataAddedCase);
//       Global.vars.writtenCaseName = caseName;
//     }
//     //create directory photos
//     var myDirectoryPhotos = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts + caseName + "/photos/");
//     var directorycreatedPhotos = myDirectoryPhotos.createDirectory();
//     voltmx.print("### Utility_savePhotoToFileSystem directorycreatedPhotos: " + JSON.stringify(directorycreatedPhotos));//returns false if already exists
//     //create the file in directory jsonfiles
//     var fileExtension = ".jpg";
//     var imageMimeType = "image/jpeg";
// //     if(voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true){
// //       fileExtension = ".png";
// //       imageMimeType = "image/png";
// //     }
//     if(filename.includes(".jpg") === false && filename.includes(".png") === false){
//       filename = filename + fileExtension;
//     }
//     var myfile = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts + caseName + "/photos/" + filename);
//     if (myfile.exists()){
//       voltmx.print("### Utility_savePhotoToFileSystem myfile exists: " + filename);
//       myfile.remove();
//     }
//     var filecreated = myfile.createFile();
//     voltmx.print("### Utility_savePhotoToFileSystem photo IS READABLE >>>>>>>"+myfile.readable);
//     voltmx.print("### Utility_savePhotoToFileSystem photo IS WRITABLE >>>>>>>"+myfile.writable);
//     var dataAdded = myfile.write(imgData,false);
//     voltmx.print("### Utility_savePhotoToFileSystem photo IS DATA ADDED >>>>>>>"+dataAdded);
//     var photoinfo = {
//       fileDate:	Utility_getUTCJavascriptDate(null),
//       //datetime stamp of the file
//       fileName:	filename,
//       //name of media file
//       description:	description,
//       //description of media
//       documentType:	documentType,
//       //type of media document for example "photo" or "licenseplatePhoto"
//       contentType : imageMimeType,
//       attachmentId: filename,
//       //Couch attachment id
//       uploaded: false,
//       photoStatus: "draft"
//     };
//     voltmx.print("### Utility_savePhotoToFileSystem update multimedia in CaseData, photoinfo: " + JSON.stringify(photoinfo));
//     Utility_addPhotoToCase(photoinfo);
//     callback();
//   } catch (error){
//   		voltmx.print("### Utility_savePhotoToFileSystem error: " +  JSON.stringify(error));
//   }
// }
function Utility_openPhotoFromFileSystem(openCaseName, filename) {
    //returns the base64 from file
    voltmx.print("### Utility_openPhotoFromFileSystem openCaseName: " + openCaseName);
    try {
        if (openCaseName != null && openCaseName != null && openCaseName !== "") {
            openCaseName = openCaseName;
        } else {
            openCaseName = CaseData.caseinfo.id;
        }
        var fileExtension = ".jpg";
        //     if(voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true){
        //       fileExtension = ".png";
        //     }
        if (filename.includes(".jpg") === false && filename.includes(".png") === false) {
            filename = filename + fileExtension;
        }
        var path = voltmx.io.FileSystem.getDataDirectoryPath();
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
            if (appgrouppath != null) {
                voltmx.print("### grouppath: " + appgrouppath);
                path = appgrouppath;
            }
        }
        voltmx.print("### Utility_openPhotoFromFileSystem path: " + JSON.stringify(path));
        var folderForCaseConcepts = "/concepts/";
        var folderForCaseOutbox = "/outbox/";
        var fileDirectoryConcepts = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts + openCaseName + "/photos/");
        var fileListConcepts = fileDirectoryConcepts.getFilesList();
        if (fileListConcepts === null) {
            fileListConcepts = {};
        }
        voltmx.print("### Utility_openPhotoFromFileSystem fileListConcepts: " + fileListConcepts);
        voltmx.print("### Utility_openPhotoFromFileSystem fileListConcepts: " + JSON.stringify(fileListConcepts));
        var fileDirectoryOutbox = voltmx.io.FileSystem.getFile(path + folderForCaseOutbox + openCaseName + "/photos/");
        var fileListOutbox = null;
        if (fileDirectoryOutbox !== undefined && fileDirectoryOutbox != null) {
            fileListOutbox = fileDirectoryOutbox.getFilesList();
        }
        if (fileListOutbox === null) {
            fileListOutbox = {};
        }
        voltmx.print("### Utility_openPhotoFromFileSystem fileListOutbox: " + fileListOutbox);
        voltmx.print("### Utility_openPhotoFromFileSystem fileListOutbox: " + JSON.stringify(fileListOutbox));
        var getfile = "";
        var name = "";
        //check if case is in concepts
        for (var i = 0; i < fileListConcepts.length; i++) {
            voltmx.print("### Utility_openPhotoFromFileSystem concepts fullpath i: " + fileListConcepts.item(i).fullPath);
            voltmx.print("### Utility_openPhotoFromFileSystem concepts readable i: " + fileListConcepts.item(i).readable);
            voltmx.print("### Utility_openPhotoFromFileSystem concepts name i: " + fileListConcepts.item(i).name);
            name = fileListConcepts.item(i).name;
            if (name == filename) {
                voltmx.print("### Utility_openPhotoFromFileSystem case found in list concepts: " + fileListConcepts.item(i).name);
                getfile = voltmx.io.FileSystem.getFile(fileListConcepts.item(i).fullPath);
                break;
            }
        }
        //check if case is in outbox
        for (var j = 0; j < fileListOutbox.length; j++) {
            voltmx.print("### Utility_openPhotoFromFileSystem outbox fullpath j: " + fileListOutbox.item(j).fullPath);
            voltmx.print("### Utility_openPhotoFromFileSystem outbox readable j: " + fileListOutbox.item(j).readable);
            voltmx.print("### Utility_openPhotoFromFileSystem outbox name j: " + fileListOutbox.item(j).name);
            name = fileListOutbox.item(j).name;
            if (name == filename) {
                voltmx.print("### Utility_openPhotoFromFileSystem case found in list outbox: " + fileListOutbox.item(j).name);
                getfile = voltmx.io.FileSystem.getFile(fileListOutbox.item(j).fullPath);
                break;
            }
        }
        //open the file
        if (getfile !== "" && getfile.exists()) {
            voltmx.print("### Utility_openPhotoFromFileSystem getfile exists");
            voltmx.print("### Utility_openPhotoFromFileSystem getfile done");
            voltmx.print("### Utility_openPhotoFromFileSystem IS READABLE >>>>>>>" + getfile.readable);
            var readPhotoFromFile = getfile.read();
            var base64FromImage = voltmx.convertToBase64(readPhotoFromFile);
            return base64FromImage;
        } else {
            voltmx.print("### Utility_openPhotoFromFileSystem getfile does not exist");
            return false;
        }
    } catch (error) {
        voltmx.print("### Utility_openPhotoFromFileSystem error: " + JSON.stringify(error));
        return false;
    }
}

function Utility_deletePhotoFromFileSystem(CaseName, filename) {
    voltmx.print("### Utility_deletePhotoFromFileSystem openCaseName: " + CaseName);
    voltmx.print("### Utility_deletePhotoFromFileSystem filename: " + filename);
    try {
        if (CaseName != null && CaseName != null && CaseName !== "") {
            CaseName = CaseName;
        } else {
            CaseName = CaseData.caseinfo.id;
        }
        var fileExtension = ".jpg";
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            fileExtension = ".png";
        }
        if (filename.includes(".jpg") === false && filename.includes(".png") === false) {
            filename = filename + fileExtension;
        }
        var path = voltmx.io.FileSystem.getDataDirectoryPath();
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
            if (appgrouppath != null) {
                voltmx.print("### grouppath: " + appgrouppath);
                path = appgrouppath;
            }
        }
        voltmx.print("### Utility_deletePhotoFromFileSystem path: " + JSON.stringify(path));
        var folderForCaseConcepts = "/concepts/";
        var folderForCaseOutbox = "/outbox/";
        var fileDirectoryConcepts = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts + CaseName + "/photos/");
        var fileListConcepts = fileDirectoryConcepts.getFilesList();
        if (fileListConcepts === null) {
            fileListConcepts = {};
        }
        voltmx.print("### Utility_deletePhotoFromFileSystem fileListConcepts: " + fileListConcepts);
        voltmx.print("### Utility_deletePhotoFromFileSystem fileListConcepts: " + JSON.stringify(fileListConcepts));
        var fileDirectoryOutbox = voltmx.io.FileSystem.getFile(path + folderForCaseOutbox + CaseName + "/photos/");
        var fileListOutbox = null;
        if (fileDirectoryOutbox !== undefined && fileDirectoryOutbox != null) {
            fileListOutbox = fileDirectoryOutbox.getFilesList();
        }
        if (fileListOutbox === null) {
            fileListOutbox = {};
        }
        voltmx.print("### Utility_deletePhotoFromFileSystem fileListOutbox: " + fileListOutbox);
        voltmx.print("### Utility_deletePhotoFromFileSystem fileListOutbox: " + JSON.stringify(fileListOutbox));
        var deletefile = "";
        var name = "";
        //check if case is in concepts
        for (var i = 0; i < fileListConcepts.length; i++) {
            voltmx.print("### Utility_deletePhotoFromFileSystem concepts fullpath i: " + fileListConcepts.item(i).fullPath);
            voltmx.print("### Utility_deletePhotoFromFileSystem concepts readable i: " + fileListConcepts.item(i).readable);
            voltmx.print("### Utility_deletePhotoFromFileSystem concepts name i: " + fileListConcepts.item(i).name);
            name = fileListConcepts.item(i).name;
            if (name == filename) {
                voltmx.print("### Utility_deletePhotoFromFileSystem case found in list concepts: " + fileListConcepts.item(i).name);
                deletefile = voltmx.io.FileSystem.getFile(fileListConcepts.item(i).fullPath);
                break;
            }
        }
        //check if case is in outbox
        for (var j = 0; j < fileListOutbox.length; j++) {
            voltmx.print("### Utility_deletePhotoFromFileSystem outbox fullpath j: " + fileListOutbox.item(j).fullPath);
            voltmx.print("### Utility_deletePhotoFromFileSystem outbox readable j: " + fileListOutbox.item(j).readable);
            voltmx.print("### Utility_deletePhotoFromFileSystem outbox name j: " + fileListOutbox.item(j).name);
            name = fileListOutbox.item(j).name;
            if (name == filename) {
                voltmx.print("### Utility_deletePhotoFromFileSystem case found in list outbox: " + fileListOutbox.item(j).name);
                deletefile = voltmx.io.FileSystem.getFile(fileListOutbox.item(j).fullPath);
                break;
            }
        }
        //open the file
        if (deletefile !== "" && deletefile.exists()) {
            voltmx.print("### Utility_deletePhotoFromFileSystem deletefile exists");
            deletefile.remove();
            voltmx.print("### Utility_deletePhotoFromFileSystem deletefile done");
        } else {
            voltmx.print("### Utility_deletePhotoFromFileSystem deletefile does not exist");
        }
    } catch (error) {
        voltmx.print("### Utility_deletePhotoFromFileSystem error: " + JSON.stringify(error));
    }
}
// function Utility_countFilelists(){
//   voltmx.print("### Utility_countFilelists");
//   try {
//     var path = voltmx.io.FileSystem.getDataDirectoryPath();
//     if(voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true){
//       var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
//       if(appgrouppath != null){
//         voltmx.print("### Utility_countFilelists grouppath: " + appgrouppath);
//         path = appgrouppath;
//       }
//     }
//     voltmx.print("### Utility_countFilelists path: " + JSON.stringify(path));
//     var folderForCaseConcepts = "/concepts/";
//     var folderForCaseOutbox = "/outbox/";
//     var fileDirectoryConcepts = voltmx.io.FileSystem.getFile(path + folderForCaseConcepts);
//     var fileListConcepts = fileDirectoryConcepts.getFilesList();
//   	if(fileListConcepts === null  || fileListConcepts === undefined){
//       fileListConcepts ={};
//     }
//     voltmx.print("### Utility_countFilelists fileListConcepts: " + fileListConcepts);
//   	voltmx.print("### Utility_countFilelists fileListConcepts: " + JSON.stringify(fileListConcepts));
//     voltmx.print("### Utility_countFilelists fileListConcepts count: " + fileListConcepts.length);
//     var fileDirectoryOutbox = voltmx.io.FileSystem.getFile(path +folderForCaseOutbox);
// 		var fileListOutbox = null;
//         if (fileDirectoryOutbox !== undefined && fileDirectoryOutbox != null){
//           fileListOutbox = fileDirectoryOutbox.getFilesList();
//         }
//   	if(fileListOutbox === null || fileListOutbox === undefined){
//       fileListOutbox ={};
//     }
//     voltmx.print("### Utility_countFilelists fileListOutbox: " + fileListOutbox);
//   	voltmx.print("### Utility_countFilelists fileListOutbox: " + JSON.stringify(fileListOutbox));
//     voltmx.print("### Utility_countFilelists fileListOutbox count: " + fileListOutbox.length);
//     Global.vars.numberOfConceptfiles = fileListConcepts.length;
//     Global.vars.numberOfOutboxfiles = fileListOutbox.length;
//     if(Global.vars.numberOfConceptfiles === undefined){
//       Global.vars.numberOfConceptfiles = 0;
//     }
//     if(Global.vars.numberOfOutboxfiles === undefined){
//       Global.vars.numberOfOutboxfiles = 0;
//     }
//     Global_updateMenuCounters();
//   } catch (error){
//   		voltmx.print("### Utility_openJSONCaseData error: " +  JSON.stringify(error));
//     	return false;
//   }
// }
function Utility_countFilelists() {
    voltmx.print("### Utility_countFilelists");
    try {
        //     //count concepts
        //     var countedConcepts = 0;
        //     Global.vars.numberOfConceptfilesOnDevice = 0;
        //     var filelist = Utility_showCaseFileList(false);
        //   	voltmx.print("#### Utility_countFilelists filelist: " + JSON.stringify(filelist));
        //     //check if case is in concepts
        //   	voltmx.print("#### Utility_countFilelists filelist length: " + filelist.length);
        //     for(var i = 0; i < filelist.length; i++){
        //       	var getfile = "";
        //         var parsedData = null;
        //       	voltmx.print("### Utility_countFilelists filelist fullpath i: " + filelist.item(i).fullPath);
        //       	voltmx.print("### Utility_countFilelists filelist readable i: " + filelist.item(i).readable);
        //       	getfile = voltmx.io.FileSystem.getFile(filelist.item(i).fullPath + "/case.json");
        //         var readJSONFromFile = getfile.read();
        //         var stringifiedData = JSON.stringify(readJSONFromFile);
        //       	var name = filelist.item(i).name;
        //       	if(voltmx.string.startsWith(name, "noCouchCase") === true){
        //           parsedData = JSON.parse(stringifiedData);
        //           if(parsedData != null){
        //             var parsedCaseData = JSON.parse(parsedData.text);
        //             voltmx.print("### Utility_countFilelists parsedCaseData: " + JSON.stringify(parsedCaseData.caseinfo.instanceID));
        //             if(parsedCaseData.caseinfo.instanceID == Global.vars.gInstanceId){
        //               countedConcepts = countedConcepts + 1;
        //             }
        //           }
        //         }
        //     }
        //     Global.vars.numberOfConceptfilesOnDevice = countedConcepts;
        //count offline concepts
        var countedConcepts = 0;
        Global.vars.numberOfConceptfilesOnDevice = 0;
        var filelist = Utility_showCaseFileListOfflineCases(); //getofflineFileList
        voltmx.print("#### Utility_countFilelists filelist: " + JSON.stringify(filelist));
        //check if case is in offline
        voltmx.print("#### Utility_countFilelists filelist length: " + filelist.length);
        for (var i = 0; i < filelist.length; i++) {
            var getfile = "";
            var parsedData = null;
            voltmx.print("### Utility_countFilelists filelist fullpath i: " + filelist.item(i).fullPath);
            voltmx.print("### Utility_countFilelists filelist readable i: " + filelist.item(i).readable);
            getfile = voltmx.io.FileSystem.getFile(filelist.item(i).fullPath + "/case.json");
            var readJSONFromFile = getfile.read();
            var stringifiedData = JSON.stringify(readJSONFromFile);
            var name = filelist.item(i).name;
            if (voltmx.string.startsWith(name, "noCouchCase") === true) {
                parsedData = JSON.parse(stringifiedData);
                if (parsedData != null) {
                    var parsedCaseData = JSON.parse(parsedData.text);
                    voltmx.print("### Utility_countFilelists parsedCaseData: " + JSON.stringify(parsedCaseData.caseinfo.instanceID));
                    if (parsedCaseData.caseinfo.instanceID == Global.vars.gInstanceId) {
                        countedConcepts = countedConcepts + 1;
                    }
                }
            } else if (voltmx.string.startsWith(name, "noCouchCase") === false) {
                parsedData = JSON.parse(stringifiedData);
                if (parsedData != null) {
                    var parsedCaseDataHalfOnline = JSON.parse(parsedData.text);
                    voltmx.print("### Utility_countFilelists parsedCaseDataHalfOnline: " + JSON.stringify(parsedCaseDataHalfOnline.caseinfo.instanceID));
                    if (parsedCaseDataHalfOnline.caseinfo.instanceID == Global.vars.gInstanceId) {
                        countedConcepts = countedConcepts + 1;
                    }
                }
            }
        }
        Global.vars.numberOfConceptfilesOnDevice = countedConcepts;
        //count outbox
        var countedOutbox = 0;
        var filelistOutbox = Utility_showCaseFileList(true);
        voltmx.print("#### Utility_countFilelists filelistOutbox: " + JSON.stringify(filelistOutbox));
        //check if case is in concepts
        voltmx.print("#### Utility_countFilelists filelistOutbox length: " + filelistOutbox.length);
        for (var j = 0; j < filelistOutbox.length; j++) {
            var getfileOutbox = "";
            var parsedDataOutbox = null;
            voltmx.print("### Utility_countFilelists filelistOutbox fullpath j: " + filelistOutbox.item(j).fullPath);
            voltmx.print("### Utility_countFilelists filelistOutbox readable j: " + filelistOutbox.item(j).readable);
            getfileOutbox = voltmx.io.FileSystem.getFile(filelistOutbox.item(j).fullPath + "/case.json");
            var readJSONFromFileOutbox = getfileOutbox.read();
            var stringifiedDataOutbox = JSON.stringify(readJSONFromFileOutbox);
            parsedDataOutbox = JSON.parse(stringifiedDataOutbox);
            if (parsedDataOutbox != null) {
                var parsedCaseDataOutbox = JSON.parse(parsedDataOutbox.text);
                voltmx.print("### Utility_countFilelists parsedCaseDataOutbox: " + JSON.stringify(parsedCaseDataOutbox.caseinfo.instanceID));
                if (parsedCaseDataOutbox.caseinfo.instanceID == Global.vars.gInstanceId) {
                    countedOutbox = countedOutbox + 1;
                }
            }
        }
        Global.vars.numberOfOutboxfiles = countedOutbox;
        Global_updateMenuCounters();
    } catch (error) {
        voltmx.print("### Utility_openJSONCaseData error: " + JSON.stringify(error));
        return false;
    }
}

function Utility_unEscapeJavaScript(input) {
    return input.replace(/\\r/g, "\r").replace(/\\n/g, "\n").replace(/\\'/g, "'").replace(/\\\"/g, '"').replace(/\\&/g, "&").replace(/\\\\/g, "\\").replace(/\\t/g, "\t").replace(/\\b/g, "\b").replace(/\\f/g, "\f").replace(/\\x2F/g, "/").replace(/\\x3C/g, "<").replace(/\\x3E/g, ">");
}

function Utility_getTaskType(taskType, callback) {
    voltmx.print("#### Utility_getTaskType");
    var lTaskTypeClause = "select * from mle_v_task_type_msv where identification = '" + taskType + "'";
    voltmx.print("#### Utility_getTaskType lTaskTypeClause: " + lTaskTypeClause);
    lTaskTypeClause = Utility_addTimelineToWhereClauseObjectSync(lTaskTypeClause, CaseData.time.dateComponents);
    lTaskTypeClause = Utility_addLanguageToWhereClauseObjectSync(lTaskTypeClause);
    lTaskTypeClause = lTaskTypeClause + " order by valid_from desc";
    voltmx.print("### Utility_getTaskType ocwcs: " + lTaskTypeClause);
    //  com.redora.CaseManagementData.TaskType.find(lTaskTypeClause, callback, Utility_getTaskTypeErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lTaskTypeClause, callback, Utility_getTaskTypeErrorCallback);
}

function Utility_getTaskTypeErrorCallback(error) {
    voltmx.print("#### Utility_getTaskTypeErrorCallback: " + JSON.stringify(error));
}

function Utility_fomatDate(javascriptdate, format) {
    var date = new Date(javascriptdate);
    voltmx.print("### Utility_fomatDate date: " + date);
    if (!format) {
        format = "MM/dd/yyyy";
    }
    var month = date.getMonth() + 1;
    var year = date.getFullYear();
    format = format.replace("MM", month.toString().lpad("0", 2));
    if (format.indexOf("yyyy") > -1) {
        format = format.replace("yyyy", year.toString());
    } else if (format.indexOf("yy") > -1) {
        format = format.replace("yy", year.toString().substr(2, 2));
    }
    format = format.replace("dd", date.getDate().toString().lpad("0", 2));
    var hours = date.getHours();
    if (format.indexOf("t") > -1) {
        if (hours > 11) format = format.replace("t", "pm");
        else format = format.replace("t", "am");
    }
    if (format.indexOf("HH") > -1) {
        format = format.replace("HH", hours.toString().lpad("0", 2));
    }
    if (format.indexOf("hh") > -1) {
        if (hours > 12) {
            hours = hours - 12;
        }
        if (hours === 0) {
            hours = 12;
        }
        format = format.replace("hh", hours.toString().lpad("0", 2));
    }
    if (format.indexOf("mm") > -1) {
        format = format.replace("mm", date.getMinutes().toString().lpad("0", 2));
    }
    if (format.indexOf("ss") > -1) {
        format = format.replace("ss", date.getSeconds().toString().lpad("0", 2));
    }
    voltmx.print("### Utility_fomatDate format: " + format);
    return format;
}

function Utility_getCaseTypeDescriptionfrmTrackDown(caseType) {
    voltmx.print("### Utility_getCaseTypeDescriptionfrmTrackDown");
    var description = "";

    function Utility_getCaseTypeDescriptions_successCallback(result) {
        voltmx.print("### Utility_getCaseTypeDescription_successCallback: " + JSON.stringify(result));
        if (result.length > 0) {
            description = result[0].description;
            voltmx.print("### Utility_getCaseTypeDescription_successCallback description: " + description);
            frmTrackDown.lblOffenceType.text = description;
        }
    }

    function Utility_getCaseTypeDescriptions_errorCallback(error) {
        voltmx.print("### Utility_getCaseTypeDescription_errorCallback: " + JSON.stringify(error));
    }
    var wcs = "select * from mle_v_case_type_m where identification = '" + caseType + "'";
    var caseTypesWcs = Utility_addTimelineToWhereClauseObjectSync(wcs);
    caseTypesWcs = Utility_addLanguageToWhereClauseObjectSync(caseTypesWcs);
    // 	com.redora.CaseManagementData.CaseType.find(caseTypesWcs,Utility_getCaseTypeDescriptions_successCallback,Utility_getCaseTypeDescriptions_errorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(caseTypesWcs, Utility_getCaseTypeDescriptions_successCallback, Utility_getCaseTypeDescriptions_errorCallback);
}

function Utility_setMapImage() {
    for (var i in CaseData.multimedia) {
        var v = CaseData.multimedia[i];
        if (v.base64 !== undefined && v.documentType == "SmallMap") {
            Global.vars.base64MapImage = v.base64;
            voltmx.print("### Utility_setMapImage map file is set");
        }
    }
}

function Utility_isDateInArray(needle, haystack) {
    for (var i = 0; i < haystack.length; i++) {
        if (new Date(needle).getTime() === new Date(haystack[i]).getTime()) {
            return true;
        }
    }
    return false;
}

function Utility_isDate(input) {
    try {
        var timestamp = Date.parse(input);
        if (isNaN(timestamp) === false) {
            voltmx.print("### Utility_isDate isDate true: " + input);
            return true;
        } else {
            voltmx.print("### Utility_isDate isDate false: " + input);
            return false;
        }
    } catch (err) {
        voltmx.print("### Utility_isDate err: " + input);
        return false;
    }
}

function Utility_isEmptyObject(obj) {
    for (var name in obj) {
        voltmx.print("### Utility_isEmptyObject: false");
        return false;
    }
    voltmx.print("### Utility_isEmptyObject: true");
    return true;
}

function Utility_isEmptyEnforcementObject(obj) {
    try {
        voltmx.print("### Utility_isEmptyEnforcementObject obj: " + JSON.stringify(obj));
        if (obj !== undefined && obj.name !== undefined && (obj.name === null || obj.name === "")) {
            voltmx.print("### Utility_isEmptyEnforcementObject true");
            return true;
        } else {
            voltmx.print("### Utility_isEmptyEnforcementObject false");
            return false;
        }
    } catch (e) {
        voltmx.print("### Utility_isEmptyEnforcementObject error true");
        return true;
    }
}

function Utility_saveGlobalsForFiles() {
    var globals = {
        //frmTrackDown
        newLocationSet: Global.vars.newLocationSet,
        newTimeSet: Global.vars.newTimeSet,
        appMode: Global.vars.appMode,
        previousForm: Global.vars.previousForm,
        btnCountryVehicleCode: Global.vars.btnCountryVehicleCode,
        gLatitudeLast: Global.vars.gLatitudeLast,
        gLatitude: Global.vars.gLatitude,
        gLongitudeLast: Global.vars.gLongitudeLast,
        gLongitude: Global.vars.gLongitude,
        licenseplateCountryEnableScan: Global.vars.licenseplateCountryEnableScan,
        lastTaskProcessedPrevious: Global.vars.lastTaskProcessedPrevious,
        handleCharacteristicType: Global.vars.handleCharacteristicType,
        gCaseVehiclesIndex: Global.vars.gCaseVehiclesIndex,
        licenseplateCountryCode: Global.vars.licenseplateCountryCode,
        licenseplateCountryModule: Global.vars.licenseplateCountryModule,
        licenseplateCountryDesc: Global.vars.licenseplateCountryDesc,
        vehicleTypeGroupOriginal: Global.vars.vehicleTypeGroupOriginal,
        gCaseVehicles: Global.vars.gCaseVehicles,
        noViolation: Global.vars.noViolation,
        licensplateHistoryRadius: Global.vars.licensplateHistoryRadius,
        resetAppTrackdown: Global.vars.resetAppTrackdown,
        defaultTaskOutcomeFrmTrackDownCancel: Global.vars.defaultTaskOutcomeFrmTrackDownCancel,
        anprCalled: Global.vars.anprCalled,
        lastChosenLicensePlateCountry: Global.vars.lastChosenLicensePlateCountry,
        licenseplateCountryRegisterCheck: Global.vars.licenseplateCountryRegisterCheck,
        frmTrackDownANPRresult: Global.vars.frmTrackDownANPRresult,
        frmCheckVehicleANPRresult: Global.vars.frmCheckVehicleANPRresult,
        photosLoaded: Global.vars.photosLoaded,
        base64MapImage: Global.vars.base64MapImage,
        mediaSequence: Global.vars.mediaSequence,
        addPhotos: Global.vars.addPhotos,
        vehicleRegistrationCalled: Global.vars.vehicleRegistrationCalled,
        createCaseRetry: Global.vars.createCaseRetry,
        frmTrackDefaultCaseTypeTaskType: Global.vars.frmTrackDefaultCaseTypeTaskType,
        frmCheckVehicleDefaultCaseTypeTaskType: Global.vars.frmCheckVehicleDefaultCaseTypeTaskType,
        frmCheckCardDefaultCaseTypeTaskType: Global.vars.frmCheckCardDefaultCaseTypeTaskType,
        frmCheckLocationDefaultCaseTypeTaskType: Global.vars.frmCheckLocationDefaultCaseTypeTaskType,
        checkLocationObject: Global.vars.checkLocationObject,
        //handlecharacteristic
        directOffenceSelect: Global.vars.directOffenceSelect,
        taskTypeId: Global.vars.taskTypeId,
        taskType: Global.vars.taskType,
        //handlingTypes: Global.vars.handlingTypes,
        gCasePersonsIndex: Global.vars.gCasePersonsIndex,
        officerFunctions: Global.vars.officerFunctions,
        handleCharacteristicsBack: Global.vars.handleCharacteristicsBack,
        chosenTaskOutcome: Global.vars.chosenTaskOutcome,
        outcomeTypes: Global.vars.outcomeTypes,
        gOffenceSearchModus: Global.vars.gOffenceSearchModus,
        indHasOffenceTypes: Global.vars.indHasOffenceTypes,
        //caseTypes: Global.vars.caseTypes,//nodig????
        offenceTypeCodes: Global.vars.offenceTypeCodes,
        codeMasterThemes: Global.vars.codeMasterThemes,
        serviceTaskValues: Global.vars.serviceTaskValues,
        //offence
        questionTypesUsage: Global.vars.questionTypesUsage,
        QuestionsSet: Global.vars.QuestionsSet,
        gOffenceSearchText: Global.vars.gOffenceSearchText,
        gOffenceCatsSearchText: Global.vars.gOffenceCatsSearchText,
        gSearchtypeOffenceSelect: Global.vars.gSearchtypeOffenceSelect,
        previousCasetypes: Global.vars.previousCasetypes,
        chosenThemeCode: Global.vars.chosenThemeCode,
        themeModus: Global.vars.themeModus,
        OffenceSelectPrevious: Global.vars.OffenceSelectPrevious,
        selectedFocusedTheme: Global.vars.selectedFocusedTheme,
        getAllOffences: Global.vars.getAllOffences,
        VteCode: Global.vars.VteCode,
        chosenTickettypeManual: Global.vars.chosenTickettypeManual,
        themeBack: Global.vars.themeBack,
        lastThemeHeader: Global.vars.lastThemeHeader,
        gOptionUsage: Global.vars.gOptionUsage,
        tariffNHA: Global.vars.tariffNHA,
        administrationCosts: Global.vars.administrationCosts,
        //vehicle select: //vehicle select,
        VehicleTypeModus: Global.vars.VehicleTypeModus,
        //resume
        optionvariablesSet: Global.vars.optionvariablesSet,
        RegionDuplicatePopupSet: Global.vars.RegionDuplicatePopupSet,
        needToUploadPhotos: Global.vars.needToUploadPhotos,
        defaultTaskOutcomeFrmResume: Global.vars.defaultTaskOutcomeFrmResume,
        optionvariablesText: Global.vars.optionvariablesText,
        offlinePhotosToUpload: Global.vars.offlinePhotosToUpload,
        gCasePersons: Global.vars.gCasePersons,
        personDeleteButtons: Global.vars.personDeleteButtons,
        variableTimeField: Global.vars.variableTimeField,
        selectedOption: Global.vars.selectedOption,
        //person
        checkdocument: Global.vars.checkdocument,
        indDcocumentChecked: Global.vars.indDcocumentChecked,
        indDcocumentValidated: Global.vars.indDcocumentValidated,
        documentTypeCheckable: Global.vars.documentTypeCheckable,
        personInputMethod: Global.vars.personInputMethod,
        //originalCaseInfo
        originalCaseInfo: Global.vars.originalCaseInfo,
        //manual location
        manualLocation: Global.vars.savedLocationManual,
        // LASTTASKPROCESSEd
        processinfo_lastTaskProcessed: Global.vars.processinfo_lastTaskProcessed,
        // themeSelection
        themeSelection: Global.vars.themeSelection,
        // cameToCaseTypeSelectFromForm
        cameToCaseTypeSelectFromForm: Global.vars.cameToCaseTypeSelectFromForm
    };
    return JSON.stringify(globals);
}

function Utility_MergeRecursive(obj1, obj2) {
    //Waardes uit object 2 vervangen de waarden van object 1 of worden toegevoegd aan object 1
    for (var p in obj2) {
        try {
            // Property in destination object set; update its value.
            if (obj2[p].constructor == Object) {
                obj1[p] = Utility_MergeRecursive(obj1[p], obj2[p]);
            } else {
                obj1[p] = obj2[p];
            }
        } catch (e) {
            // Property in destination object not set; create it and set its value.
            obj1[p] = obj2[p];
        }
    }
    return obj1;
}

function Utility_loadGlobalsFromFiles(globals) {
    voltmx.print("### Utility_loadGlobalsFromFiles globals: " + globals);
    var savedGlobals = {};
    if (typeof globals == "string") {
        savedGlobals = JSON.parse(globals);
    } else {
        savedGlobals = globals;
    }
    var mergedGlobals = Utility_MergeRecursive(Global.vars, savedGlobals);
    Global.vars = mergedGlobals;
}

function Utility_transformKeys(obj) {
    return Object.keys(obj).reduce(function(o, prop) {
        var value = obj[prop];
        var newProp = prop.replace("_", "");
        o[newProp] = value;
        return o;
    }, {});
}

function Utility_doServerCallsMT() {
    Utility_doServerCalls(false);
}

function Utility_doServerCalls(setTimer) {
    var _setTimer = setTimer === undefined ? true : setTimer;
    voltmx.print("#### Utility_doServerCalls _setTimer: " + _setTimer);
    Utility_checkOpenTasks();
    Utility_deleteEmptyOutboxFiles();
    Utility_deleteConceptsThatAreOnline();
    Utility_checkConceptTasks();
    Utility_sendOfflineCases();
    // na 30 seconden nogmaals taken ophalen
    if (_setTimer === true) {
        try {
            voltmx.timer.schedule("checkTasks", Utility_checkTasks, 30, false);
        } catch (err) {}
    }
}

function Utility_checkTasks() {
    voltmx.print("#### Utility_checkTasks");
    try {
        voltmx.timer.cancel("checkTasks");
    } catch (err) {}
    Utility_checkOpenTasks();
    Utility_checkConceptTasks();
}

function Utility_checkOpenTasks() {
    voltmx.print("#### Utility_checkOpenTasks");
    if (Global.vars.checkOpenTaskRunning === false) {
        Global.vars.checkOpenTaskRunning = true;
        voltmx.print("#### Utility_checkOpenTasks start: " + new Date());
        Global.vars.numberOfopenTasks = 0;
        Utility_getNumberOfOpenTasks();
        voltmx.print("### Utility_checkOpenTasks LET OP!!!! - nu zijn de opentaken checks gedaisychained voor OV en NS - moet op termijn aangepast worden");
        //set running to false for next run if no network
        if (voltmx.net.isNetworkAvailable(constants.NETWORK_TYPE_ANY) === false) {
            Global.vars.checkOpenTaskRunning = false;
        }
    }
}

function Utility_getNumberOfOpenTasks() {
    voltmx.print("### Utility_getNumberOfOpenTasks");
    //haal de opentaken op die nodig zijn
    Global.vars.openTasks = [];
    var fromRow = "";
    var size = "";
    var taskTypes = "";
    var caseTypes = "";
    var ssn = "";
    var licensePlateNumber = "";
    var externalId = "";
    var couchId = "";
    var caseDateFrom = "";
    var caseDateTo = "";
    var municipaltyCode = "";
    var city = "";
    var taskClaimedBy = "";
    var registerlabel = "";
    var secondLabel = "";
    var operationalAreaId = "";
    var includeGroupTasks = '"true"';
    var scanUnit = "";
    if (Global.vars.registerConceptEnabled === true) {
        //
        var taskTypesToQuery = "";
        Global.vars.taskTypesToQueryInitiatorOnly = "";
        for (var j = 0; j < Global.vars.openTasksToQuery.length; j++) {
            var w = Global.vars.openTasksToQuery[j];
            if (w.initiatorOnly !== undefined && w.initiatorOnly != null && w.initiatorOnly === true) {
                if (Global.vars.taskTypesToQueryInitiatorOnly === "") {
                    Global.vars.taskTypesToQueryInitiatorOnly = '"' + w.taskType + '"';
                } else {
                    Global.vars.taskTypesToQueryInitiatorOnly = Global.vars.taskTypesToQueryInitiatorOnly + ',"' + w.taskType + '"';
                }
            } else {
                if (taskTypesToQuery === "") {
                    taskTypesToQuery = '"' + w.taskType + '"';
                } else {
                    taskTypesToQuery = taskTypesToQuery + ',"' + w.taskType + '"';
                }
            }
        }
        voltmx.print("### Utility_getNumberOfOpenTasks taskTypesToQueryInitiatorOnly: " + Global.vars.taskTypesToQueryInitiatorOnly);
        voltmx.print("### Utility_getNumberOfOpenTasks taskTypesToQuery group: " + taskTypesToQuery);
        if (taskTypesToQuery !== "") {
            taskTypes = taskTypesToQuery;
        }
        voltmx.print("### Utility_getNumberOfOpenTasks taskTypes for group tasks: " + taskTypes);
        voltmx.print("### Utility_getNumberOfOpenTasks filtered");
        if (taskTypesToQuery === "") {
            voltmx.print("### Utility_getNumberOfOpenTasks NO taskTypesToQuery for group");
            Utility_opentasksSuccesCallback();
        } else {
            service_GetOpenTasksFiltered(fromRow, size, taskTypes, caseTypes, ssn, licensePlateNumber, externalId, couchId, caseDateFrom, caseDateTo, municipaltyCode, city, taskClaimedBy, registerlabel, secondLabel, operationalAreaId, scanUnit, Utility_opentasksSuccesCallback, Utility_opentasksErrorCallback, includeGroupTasks);
        }
    } else {
        //service_GetOpenTasks("EditTicket", Utility_opentasksSuccesCallback, Utility_opentasksErrorCallback);
        voltmx.print("### service_GetOpenTasksFiltered Global.vars.openTaskTypesToQuery: " + Global.vars.openTaskTypesToQuery);
        taskTypes = Global.vars.openTaskTypesToQuery;
        //  service_GetOpenTasks("EditTicket", frmOpenTasks_opentasksSuccesCallbackEditTicket, frmOpenTasks_opentasksErrorCallback);
        service_GetOpenTasksFiltered(fromRow, size, taskTypes, caseTypes, ssn, licensePlateNumber, externalId, couchId, caseDateFrom, caseDateTo, municipaltyCode, city, taskClaimedBy, registerlabel, secondLabel, operationalAreaId, scanUnit, Utility_opentasksSuccesCallback, Utility_opentasksErrorCallback, includeGroupTasks);
    }
}

function Utility_opentasksSuccesCallback(result) {
    voltmx.print("#### Utility_opentasksSuccesCallback result: " + JSON.stringify(result));
    if (typeof Global.vars.numberOfopenTasks != "number") {
        Global.vars.numberOfopenTasks = 0;
    }
    if (result !== undefined && result.rawResponse !== undefined && result.rawResponse[0].openTasks !== undefined) {
        voltmx.print("#### Utility_opentasksSuccesCallback count: " + result.rawResponse[0].openTasks.length);
        Global.vars.numberOfopenTasks = Number(result.rawResponse[0].openTasks.length);
    } else {
        Global.vars.numberOfopenTasks = 0;
    }
    if (Global.vars.numberOfopenTasks === undefined) {
        Global.vars.numberOfopenTasks = 0;
    }
    Global_updateMenuCounters();
    voltmx.print("#### Utility_checkOpenTasks end: " + new Date());
    if (Global.vars.registerConceptEnabled === true) {
        var fromRow = "";
        var size = "";
        var taskTypes = "";
        var caseTypes = "";
        var ssn = "";
        var licensePlateNumber = "";
        var externalId = "";
        var couchId = "";
        var caseDateFrom = "";
        var caseDateTo = "";
        var municipaltyCode = "";
        var city = "";
        var taskClaimedBy = "";
        var registerlabel = "";
        var secondLabel = "";
        var operationalAreaId = "";
        var includeGroupTasks = '"false"';
        var scanUnit = "";
        //
        var taskTypesToQuery = "";
        if (Global.vars.taskTypesToQueryInitiatorOnly !== "") {
            taskTypes = Global.vars.taskTypesToQueryInitiatorOnly;
        }
        voltmx.print("### Utility_opentasksSuccesCallback taskTypesToQuery: " + taskTypesToQuery);
        if (taskTypesToQuery !== "") {
            taskTypes = taskTypesToQuery;
        }
        voltmx.print("### Utility_opentasksSuccesCallback taskTypes: " + taskTypes);
        voltmx.print("### Utility_opentasksSuccesCallback filtered for individual tasks");
        if (taskTypes === "") {
            voltmx.print("### Utility_opentasksSuccesCallback NO taskTypesToQuery for individual");
            Utility_opentasksSuccesCallbackIndividualTasks();
        } else {
            service_GetOpenTasksFiltered(fromRow, size, taskTypes, caseTypes, ssn, licensePlateNumber, externalId, couchId, caseDateFrom, caseDateTo, municipaltyCode, city, taskClaimedBy, registerlabel, secondLabel, operationalAreaId, scanUnit, Utility_opentasksSuccesCallbackIndividualTasks, Utility_opentasksErrorCallback, includeGroupTasks);
        }
    } else {
        if (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV") {
            service_GetOpenTasks("ValidateAlibi", Utility_opentasksSuccesCallbackAlibi, Utility_opentasksErrorCallback);
        } else {
            Global.vars.checkOpenTaskRunning = false;
        }
    }
}

function Utility_opentasksSuccesCallbackIndividualTasks(result) {
    voltmx.print("#### Utility_opentasksSuccesCallbackIndividualTasks result: " + JSON.stringify(result));
    if (typeof Global.vars.numberOfopenTasks != "number") {
        Global.vars.numberOfopenTasks = 0;
    }
    if (result !== undefined && result.rawResponse[0].openTasks !== undefined) {
        voltmx.print("#### Utility_opentasksSuccesCallbackIndividualTasks count: " + result.rawResponse[0].openTasks.length);
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + Number(result.rawResponse[0].openTasks.length);
    } else {
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + 0;
    }
    if (Global.vars.numberOfopenTasks === undefined) {
        Global.vars.numberOfopenTasks = 0;
    }
    Global_updateMenuCounters();
    voltmx.print("#### Utility_opentasksSuccesCallbackIndividualTasks end: " + new Date());
    if (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV") {
        service_GetOpenTasks("ValidateAlibi", Utility_opentasksSuccesCallbackAlibi, Utility_opentasksErrorCallback);
    } else {
        Global.vars.checkOpenTaskRunning = false;
    }
}

function Utility_opentasksSuccesCallbackAlibi(result) {
    voltmx.print("#### Utility_opentasksSuccesCallbackAlibi result: " + JSON.stringify(result));
    if (typeof Global.vars.numberOfopenTasks != "number") {
        Global.vars.numberOfopenTasks = 0;
    }
    if (result.rawResponse[0].openTasks !== undefined) {
        voltmx.print("#### Utility_opentasksSuccesCallbackAlibi count: " + result.rawResponse[0].openTasks.length);
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + Number(result.rawResponse[0].openTasks.length);
    } else {
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + 0;
    }
    if (Global.vars.numberOfopenTasks === undefined) {
        Global.vars.numberOfopenTasks = 0;
    }
    Global_updateMenuCounters();
    voltmx.print("#### Utility_opentasksSuccesCallbackAlibi end: " + new Date());
    service_GetOpenTasks("ValidateDocument", Utility_opentasksSuccesCallbackAanvullenDirectVerbod, Utility_opentasksErrorCallback);
}

function Utility_opentasksSuccesCallbackAanvullenDirectVerbod(result) {
    voltmx.print("#### Utility_opentasksSuccesCallbackAanvullenDirectVerbod result: " + JSON.stringify(result));
    if (typeof Global.vars.numberOfopenTasks != "number") {
        Global.vars.numberOfopenTasks = 0;
    }
    if (result.rawResponse[0].openTasks !== undefined) {
        voltmx.print("#### Utility_opentasksSuccesCallbackAanvullenDirectVerbod count: " + result.rawResponse[0].openTasks.length);
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + Number(result.rawResponse[0].openTasks.length);
    } else {
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + 0;
    }
    if (Global.vars.numberOfopenTasks === undefined) {
        Global.vars.numberOfopenTasks = 0;
    }
    Global_updateMenuCounters();
    voltmx.print("#### Utility_opentasksSuccesCallbackAanvullenDirectVerbod end: " + new Date());
    service_GetOpenTasks("AanvullenDirectVerbod", Utility_opentasksSuccesCallbackOverdrachtPolitie, Utility_opentasksErrorCallback);
}

function Utility_opentasksSuccesCallbackOverdrachtPolitie(result) {
    voltmx.print("#### Utility_opentasksSuccesCallbackOverdrachtPolitie result: " + JSON.stringify(result));
    if (typeof Global.vars.numberOfopenTasks != "number") {
        Global.vars.numberOfopenTasks = 0;
    }
    if (result.rawResponse[0].openTasks !== undefined) {
        voltmx.print("#### Utility_opentasksSuccesCallbackOverdrachtPolitie count: " + result.rawResponse[0].openTasks.length);
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + Number(result.rawResponse[0].openTasks.length);
    } else {
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + 0;
    }
    if (Global.vars.numberOfopenTasks === undefined) {
        Global.vars.numberOfopenTasks = 0;
    }
    Global_updateMenuCounters();
    voltmx.print("#### Utility_opentasksSuccesCallbackOverdrachtPolitie end: " + new Date());
    service_GetOpenTasks("OverdrachtPolitie", Utility_opentasksSuccesCallbackVersturenVerbod, Utility_opentasksErrorCallback);
}

function Utility_opentasksSuccesCallbackVersturenVerbod(result) {
    voltmx.print("#### Utility_opentasksSuccesCallbackVersturenVerbod result: " + JSON.stringify(result));
    if (typeof Global.vars.numberOfopenTasks != "number") {
        Global.vars.numberOfopenTasks = 0;
    }
    if (result.rawResponse[0].openTasks !== undefined) {
        voltmx.print("#### Utility_opentasksSuccesCallbackVersturenVerbod count: " + result.rawResponse[0].openTasks.length);
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + Number(result.rawResponse[0].openTasks.length);
    } else {
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + 0;
    }
    if (Global.vars.numberOfopenTasks === undefined) {
        Global.vars.numberOfopenTasks = 0;
    }
    Global_updateMenuCounters();
    voltmx.print("#### Utility_opentasksSuccesCallbackVersturenVerbod end: " + new Date());
    service_GetOpenTasks("VersturenVerbod", Utility_opentasksSuccesCallbackDocument, Utility_opentasksErrorCallback);
}

function Utility_opentasksSuccesCallbackDocument(result) {
    voltmx.print("#### Utility_opentasksSuccesCallbackDocument result: " + JSON.stringify(result));
    if (typeof Global.vars.numberOfopenTasks != "number") {
        Global.vars.numberOfopenTasks = 0;
    }
    if (result.rawResponse[0].openTasks !== undefined) {
        voltmx.print("#### Utility_opentasksSuccesCallbackDocument count: " + result.rawResponse[0].openTasks.length);
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + Number(result.rawResponse[0].openTasks.length);
    } else {
        Global.vars.numberOfopenTasks = Global.vars.numberOfopenTasks + 0;
    }
    if (Global.vars.numberOfopenTasks === undefined) {
        Global.vars.numberOfopenTasks = 0;
    }
    Global.vars.checkOpenTaskRunning = false;
    Global_updateMenuCounters();
    voltmx.print("#### Utility_opentasksSuccesCallbackDocument end: " + new Date());
}

function Utility_opentasksErrorCallback(error) {
    voltmx.print("#### Utility_opentasksErrorCallback error: " + JSON.stringify(error));
    Global.vars.checkOpenTaskRunning = false;
}

function Utility_checkConceptTasks() {
    voltmx.print("#### Utility_checkConceptTasks");
    voltmx.print("#### Utility_checkConceptTasks start: " + new Date());
    Global.vars.numberOfConceptfiles = 0;
    if (Global.vars.registerConceptEnabled === true) {
        var fromRow = "";
        var size = "";
        var taskTypes = '"P98.07.T02"'; //[T02] Aanvullen concept in P98.07 BPMN
        var caseTypes = "";
        var ssn = "";
        var licensePlateNumber = "";
        var externalId = "";
        var couchId = "";
        var caseDateFrom = "";
        var caseDateTo = "";
        var municipaltyCode = "";
        var city = "";
        var taskClaimedBy = "";
        var registerlabel = "";
        var secondLabel = "";
        var operationalAreaId = "";
        var includeGroupTasks = '"false"';
        var scanUnit = "";
        voltmx.print("### Utility_checkConceptTasks taskTypes for group tasks: " + taskTypes);
        voltmx.print("### Utility_checkConceptTasks filtered");
        service_GetOpenTasksFiltered(fromRow, size, taskTypes, caseTypes, ssn, licensePlateNumber, externalId, couchId, caseDateFrom, caseDateTo, municipaltyCode, city, taskClaimedBy, registerlabel, secondLabel, operationalAreaId, scanUnit, Utility_concepttasksSuccesCallback, Utility_concepttasksErrorCallback, includeGroupTasks);
    } else {
        service_GetConceptTasks(Utility_concepttasksSuccesCallback, Utility_concepttasksErrorCallback);
    }
}

function Utility_concepttasksSuccesCallback(result) {
    voltmx.print("#### Utility_concepttasksSuccesCallback result: " + JSON.stringify(result));
    if (result.rawResponse[0].openTasks !== undefined) {
        voltmx.print("#### Utility_concepttasksSuccesCallback count: " + result.rawResponse[0].openTasks.length);
        //apply filter until service is correct
        var concepts = [];
        Global.vars.numberOfConceptfiles = 0;
        for (var i in result.rawResponse[0].openTasks) {
            var v = result.rawResponse[0].openTasks[i];
            //if(v.statusSetBy == Global.vars.gUsername){
            concepts.push(v);
            //}
        }
        voltmx.print("#### Utility_concepttasksSuccesCallback filtered concepts: " + JSON.stringify(concepts));
        voltmx.print("#### Utility_concepttasksSuccesCallback count filterde concepts: " + concepts.length);
        Global.vars.numberOfConceptfiles = Number(concepts.length);
        //End of filter
        //Global.vars.numberOfConceptfiles = Number(result.rawResponse[0].openTasks.length);
        if (Global.vars.numberOfConceptfiles === undefined) {
            Global.vars.numberOfConceptfiles = 0;
        }
        Global_updateMenuCounters();
        voltmx.print("#### Utility_checkConceptTasks end: " + new Date());
    }
}

function Utility_concepttasksErrorCallback(error) {
    voltmx.print("#### Utility_concepttasksErrorCallback error: " + JSON.stringify(error));
}

function Utility_60secondSendTimerCancelLoading() {
    try {
        //logout identity
        voltmx.timer.schedule("CancelLoadingAfter60s", Utility_60secondSendCancelLoading, 60, false);
    } catch (error) {
        voltmx.print("### Utility_60secondSendTimerCancelLoading - error: " + JSON.stringify(error));
    }
}

function Utility_60secondSendCancelLoading() {
    try {
        voltmx.application.dismissLoadingScreen();
        voltmx.timer.cancel("CancelLoadingAfter60s");
    } catch (error) {
        voltmx.print("### Utility_60secondSendCancelLoading - error: " + JSON.stringify(error));
    }
}

function Utility_determinLocationSpecification() {
    voltmx.print("#### Utility_determinLocationSpecification CaseData.location.chosenFurtherIndication: " + CaseData.location.chosenFurtherIndication);
    var location = "";
    if (CaseData.location.chosenFurtherIndication != null && CaseData.location.chosenFurtherIndication !== "") {
        if (CaseData.location.chosenFurtherIndication == "In de trein" && Global.vars.buildFor == "NS") {
            location = '"value":"in de trein"';
        } else if (CaseData.location.chosenFurtherIndication == "Langs het spoor") {
            location = '"value":"langs het spoor"';
        } else if (CaseData.location.chosenFurtherIndication == "Op het station" && Global.vars.buildFor == "NS") {
            location = '"value":"op het station"';
        } else if (CaseData.location.chosenFurtherIndication == "Op de bushalte" && Global.vars.buildFor == "OV") {
            location = '"value":"op de halte"';
        } else if (CaseData.location.chosenFurtherIndication == "Op het station" && Global.vars.buildFor == "OV") {
            location = '"value":"op de halte"';
        } else if (CaseData.location.chosenFurtherIndication == "In de bus" && Global.vars.buildFor == "OV") {
            location = '"value":"in de bus"';
        } else if (CaseData.location.chosenFurtherIndication == "In de trein" && Global.vars.buildFor == "OV") {
            location = '"value":"in de bus"';
        } else if (CaseData.location.chosenFurtherIndication == "Op het water") {
            location = '"value":"op het water"';
        } else if (CaseData.location.chosenFurtherIndication == "Langs de weg") {
            location = '"value":"langs de weg"';
        } else if (CaseData.location.chosenFurtherIndication == "Overig") {
            location = '"value":"overig"';
        }
    }
    return location;
}

function Utility_determinKindofRoadFilter() {
    var location = "";
    if (CaseData.location.roadTypeDescription == "autosnelweg") {
        location = '"value":"autosnelweg"';
    } else if (CaseData.location.roadTypeDescription == "autoweg") {
        location = '"value":"autoweg"';
    } else if (CaseData.location.roadTypeDescription == "een weg") {
        location = '"value":"een weg"';
    }
    return location;
}

function Utility_setDefaultStatusFromInstanceParamFrmTrackDefaultCaseTypeTaskType() {
    voltmx.print("### Utility_setDefaultStatusFromInstanceParam Global.vars.frmTrackDefaultCaseTypeTaskType: " + Global.vars.frmTrackDefaultCaseTypeTaskType);
    if (Global.vars.frmTrackDefaultCaseTypeTaskType.StatusType !== undefined && Global.vars.frmTrackDefaultCaseTypeTaskType.StatusType != null) {
        var addstatus = {
            status: Global.vars.frmTrackDefaultCaseTypeTaskType.StatusType,
            //name of the taskType
            statuscode: null,
            //Id of the task outcome
            statusSetOn: Utility_getUTCJavascriptDate(null),
            //date in UTC when status was set
            statusSetBy: Global.vars.gUsername,
            //username (email) that set the status
            statusSetByName: CaseData.caseinfo.officerName
        };
        var statusAdded = false;
        for (var i in CaseData.processinfo.statuses) {
            var v = CaseData.processinfo.statuses[i];
            if (v.status === null) {
                CaseData.processinfo.statuses.splice(i, 1, addstatus);
                statusAdded = true;
                break;
            }
        }
        if (statusAdded === false) {
            CaseData.processinfo.statuses.splice(0, 0, addstatus);
        }
        CaseData.status = Global.vars.frmTrackDefaultCaseTypeTaskType.StatusType;
        CaseData.statuscode = null;
    }
}

function Utility_setDefaultStatusFromInstanceParamFrmCheckVehicleDefaultCaseTypeTaskType() {
    voltmx.print("### Utility_setDefaultStatusFromInstanceParam Global.vars.frmCheckVehicleDefaultCaseTypeTaskType: " + JSON.stringify(Global.vars.frmCheckVehicleDefaultCaseTypeTaskType));
    if (Global.vars.frmCheckVehicleDefaultCaseTypeTaskType.StatusType !== undefined && Global.vars.frmCheckVehicleDefaultCaseTypeTaskType.StatusType != null) {
        var addstatus = {
            status: Global.vars.frmCheckVehicleDefaultCaseTypeTaskType.StatusType,
            //name of the taskType
            statuscode: null,
            //Id of the task outcome
            statusSetOn: Utility_getUTCJavascriptDate(null),
            //date in UTC when status was set
            statusSetBy: Global.vars.gUsername,
            //username (email) that set the status
            statusSetByName: CaseData.caseinfo.officerName
        };
        var statusAdded = false;
        for (var i in CaseData.processinfo.statuses) {
            var v = CaseData.processinfo.statuses[i];
            if (v.status === null) {
                CaseData.processinfo.statuses.splice(i, 1, addstatus);
                statusAdded = true;
                break;
            }
        }
        if (statusAdded === false) {
            CaseData.processinfo.statuses.splice(0, 0, addstatus);
        }
        CaseData.status = Global.vars.frmCheckVehicleDefaultCaseTypeTaskType.StatusType;
        CaseData.statuscode = null;
    }
}
//
function Utility_setDefaultStatusFromInstanceParamfrmCheckCardDefaultCaseTypeTaskType() {
    voltmx.print("### Utility_setDefaultStatusFromInstanceParam Global.vars.frmCheckCardDefaultCaseTypeTaskType: " + JSON.stringify(Global.vars.frmCheckCardDefaultCaseTypeTaskType));
    if (Global.vars.frmCheckCardDefaultCaseTypeTaskType.StatusType !== undefined && Global.vars.frmCheckCardDefaultCaseTypeTaskType.StatusType != null) {
        var addstatus = {
            status: Global.vars.frmCheckCardDefaultCaseTypeTaskType.StatusType,
            //name of the taskType
            statuscode: null,
            //Id of the task outcome
            statusSetOn: Utility_getUTCJavascriptDate(null),
            //date in UTC when status was set
            statusSetBy: Global.vars.gUsername,
            //username (email) that set the status
            statusSetByName: CaseData.caseinfo.officerName
        };
        var statusAdded = false;
        for (var i in CaseData.processinfo.statuses) {
            var v = CaseData.processinfo.statuses[i];
            if (v.status === null) {
                CaseData.processinfo.statuses.splice(i, 1, addstatus);
                statusAdded = true;
                break;
            }
        }
        if (statusAdded === false) {
            CaseData.processinfo.statuses.splice(0, 0, addstatus);
        }
        CaseData.status = Global.vars.frmCheckCardDefaultCaseTypeTaskType.StatusType;
        CaseData.statuscode = null;
    }
}

function Utility_setDefaultStatusFromInstanceParamfrmCheckLocationDefaultCaseTypeTaskType() {
    voltmx.print("### Utility_setDefaultStatusFromInstanceParam Global.vars.frmCheckLocationDefaultCaseTypeTaskType: " + JSON.stringify(Global.vars.frmCheckLocationDefaultCaseTypeTaskType));
    if (Global.vars.frmCheckLocationDefaultCaseTypeTaskType.StatusType !== undefined && Global.vars.frmCheckLocationDefaultCaseTypeTaskType.StatusType != null) {
        var addstatus = {
            status: Global.vars.frmCheckLocationDefaultCaseTypeTaskType.StatusType,
            //name of the taskType
            statuscode: null,
            //Id of the task outcome
            statusSetOn: Utility_getUTCJavascriptDate(null),
            //date in UTC when status was set
            statusSetBy: Global.vars.gUsername,
            //username (email) that set the status
            statusSetByName: CaseData.caseinfo.officerName
        };
        var statusAdded = false;
        for (var i in CaseData.processinfo.statuses) {
            var v = CaseData.processinfo.statuses[i];
            if (v.status === null) {
                CaseData.processinfo.statuses.splice(i, 1, addstatus);
                statusAdded = true;
                break;
            }
        }
        if (statusAdded === false) {
            CaseData.processinfo.statuses.splice(0, 0, addstatus);
        }
        CaseData.status = Global.vars.frmCheckLocationDefaultCaseTypeTaskType.StatusType;
        CaseData.statuscode = null;
    }
}

function Utility_getTaskOutcomeForActiveTaskType() {
    voltmx.print("### Utility_getTaskOutcomeForActiveTaskType");
    var outcomeIdentification = null;
    var TasktypeSearch = "select id from mle_v_task_type_msv where identification = '" + CaseData.processinfo.activeTaskType + "'";
    TasktypeSearch = Utility_addTimelineToWhereClauseObjectSync(TasktypeSearch, CaseData.time.dateComponents);
    TasktypeSearch = Utility_addLanguageToWhereClauseObjectSync(TasktypeSearch);
    var wcs = "select * from mle_v_outcome_type_m where tte_id = (" + TasktypeSearch + ") and not ind_system";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### Utility_getTaskOutcomeForActiveTaskType wcs: " + wcs);

    function Utility_getTaskOutcomeForActiveTaskTypeSuccessCallback(result) {
        voltmx.print("### Utility_getTaskOutcomeForActiveTaskType result: " + JSON.stringify(result));
        if (result.length > 0) {
            outcomeIdentification = result[0].identification;
        }
        Global.vars.defaultTaskOutcomeEditTicket = outcomeIdentification;
    }

    function Utility_getTaskOutcomeForActiveTaskTypeErrorCallback(error) {
        voltmx.print("### Utility_getTaskOutcomeForActiveTaskType error: " + JSON.stringify(error));
        Global.vars.defaultTaskOutcomeEditTicket = null;
    }
    //  com.redora.CaseManagementData.OutcomeType.find(wcs, Utility_getTaskOutcomeForActiveTaskTypeSuccessCallback, Utility_getTaskOutcomeForActiveTaskTypeErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getTaskOutcomeForActiveTaskTypeSuccessCallback, Utility_getTaskOutcomeForActiveTaskTypeErrorCallback);
}

function Utility_getTicketType(ticketTypeId) {
    voltmx.print("### Utility_getTicketType for ticketTypeId: " + ticketTypeId);
    if (ticketTypeId != null) {
        var wcs = "select * from mle_v_ticket_type_m where id = '" + ticketTypeId + "'";
        wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
        wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
        voltmx.print("### Utility_getTicketType wcs" + wcs);
        //com.redora.Offence.TicketType.find(wcs, Utility_getTicketTypeSuccessCallback, Utility_getTicketTypeErrorCallback);
        KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getTicketTypeSuccessCallback, Utility_getTicketTypeErrorCallback);
    }
}

function Utility_getTicketTypeSuccessCallback(result) {
    voltmx.print("### Utility_getTicketTypeSuccessCallback" + JSON.stringify(result));
    if (result.length > 0) {
        CaseData.caseinfo.ticketType = result[0].id;
        CaseData.caseinfo.ticketTypeDescription = result[0].description;
        CaseData.caseinfo.ticketText = result[0].ticket_text;
        CaseData.caseinfo.enforcementType = result[0].enforcement_type;
        CaseData.caseinfo.enforcementLevel = result[0].enforcement_level;
        if (Global.vars.enforcementLevel != CaseData.caseinfo.enforcementLevel && Global.vars.gInstanceId != "RL0001" && Global.vars.gInstanceId != "NL0036") {
            Utility_getDefaultTicketType(Global.vars.enforcementLevel, CaseData.caseinfo.enforcementType);
        }
    } else {
        CaseData.caseinfo.ticketType = null;
        CaseData.caseinfo.ticketTypeDescription = null;
        CaseData.caseinfo.ticketText = null;
        CaseData.caseinfo.enforcementType = null;
        CaseData.caseinfo.enforcementLevel = null;
    }
    voltmx.print("### Utility_getTicketTypeSuccessCallback Global.vars.offenceCities" + JSON.stringify(CaseData.caseinfo));
}

function Utility_getTicketTypeErrorCallback(error) {
    voltmx.print("### Utility_getTicketTypeErrorCallback" + JSON.stringify(error));
}

function Utility_getDefaultTicketType(enforcementLevel, enforcementType) {
    voltmx.print("### Utility_getDefaultTicketType: " + enforcementLevel + " / " + enforcementType);
    if (enforcementLevel != null && enforcementType != null) {
        var wcs = "select * from mle_v_ticket_type_m where enforcement_level = '" + enforcementLevel + "' and enforcement_type = '" + enforcementType + "'";
        wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
        wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
        voltmx.print("### Utility_getDefaultTicketType wcs" + wcs);
        //		com.redora.Offence.TicketType.find(wcs, Utility_getDefaultTicketTypeSuccessCallback, Utility_getDefaultTicketTypeErrorCallback);
        KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getDefaultTicketTypeSuccessCallback, Utility_getDefaultTicketTypeErrorCallback);
    }
}

function Utility_getDefaultTicketTypeSuccessCallback(result) {
    voltmx.print("### Utility_getDefaultTicketTypeSuccessCallback" + JSON.stringify(result));
    if (result.length > 0) {
        CaseData.caseinfo.ticketType = result[0].id;
        CaseData.caseinfo.ticketTypeDescription = result[0].description;
        CaseData.caseinfo.ticketText = result[0].ticket_text;
        CaseData.caseinfo.enforcementType = result[0].enforcement_type;
        CaseData.caseinfo.enforcementLevel = result[0].enforcement_level;
    } else {
        CaseData.caseinfo.ticketType = null;
        CaseData.caseinfo.ticketTypeDescription = null;
        CaseData.caseinfo.ticketText = null;
        CaseData.caseinfo.enforcementType = null;
        CaseData.caseinfo.enforcementLevel = null;
    }
    voltmx.print("### Utility_getDefaultTicketTypeSuccessCallback " + JSON.stringify(CaseData.caseinfo));
}

function Utility_getDefaultTicketTypeErrorCallback(error) {
    voltmx.print("### Utility_getDefaultTicketTypeErrorCallback" + JSON.stringify(error));
}

function Utility_getTicketTypes() {
    voltmx.print("### Utility_getTicketTypes");

    function Utility_getTicketTypesSuccessCallback(result) {
        voltmx.print("### Utility_getTicketTypeSuccessCallback" + JSON.stringify(result));
        if (result.length > 0) {
            Global.vars.ticketTypes = result;
        }
    }

    function Utility_getTicketTypesErrorCallback(error) {
        voltmx.print("### Utility_getTicketTypesErrorCallback" + JSON.stringify(error));
    }
    var wcs = "select * from mle_v_ticket_type_m";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents, true);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### Utility_getTicketType wcs" + wcs);
    //    com.redora.Offence.TicketType.find(wcs, Utility_getTicketTypesSuccessCallback, Utility_getTicketTypesErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getTicketTypesSuccessCallback, Utility_getTicketTypesErrorCallback);
}

function Utility_returnTicketTypeDesc(enforcementtype, enforcementlevel) {
    var tickettypedesc = voltmx.i18n.getLocalizedString("l_unknown");
    for (var i = 0; i < Global.vars.ticketTypes.length; i++) {
        voltmx.print("### Utility_returnTicketTypeDesc tickettypesList: " + JSON.stringify(Global.vars.ticketTypes[i]));
        if (Global.vars.ticketTypes[i].enforcement_type == enforcementtype && Global.vars.ticketTypes[i].enforcement_level == enforcementlevel) {
            tickettypedesc = Global.vars.ticketTypes[i].description;
            break;
        }
    }
    return tickettypedesc;
}

function Utility_checkIfCityExists(city, succescallback) {
    function checkIfCityExists_errorcallback(error) {
        voltmx.print("### Utility_checkIfCityExists error: " + JSON.stringify(error));
    }
    var wcs = 'select * from mle_v_city_m WHERE upper(name) = "' + city.toUpperCase() + '"';
    voltmx.print("### Utility_checkIfCityExists wcs: " + wcs);
    //com.redora.Location.City.find(wcs, succescallback, checkIfCityExists_errorcallback);
    // objectQuery
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, succescallback, checkIfCityExists_errorcallback);
}

function Utility_setPersonAsVehicleType() {
    voltmx.print("### Utility_setPersonAsVehicleType");
    if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex] === undefined) {
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex] = CaseData_setNewvehicle();
    }
    if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc === null) {
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc = "Persoon";
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType = "PS";
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType = vehicleIdentType.other;
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber = null;
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense = null;
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode = null;
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseDesc = null;
    }
    voltmx.print("### Utility_setPersonAsVehicleType CaseData.vehicle: " + JSON.stringify(CaseData.vehicle));
}

function Utility_findCountryByIso31661alpha3(iso31661alpha3, dateComponents, callback) {
    voltmx.print("### Utility_findCountryByIso31661alpha3");
    var wcs = "select * from mle_v_country_m where iso_3166_1_alpha3 = '" + iso31661alpha3 + "'";
    if (dateComponents == null) {
        voltmx.print("### Utility_findCountryByIso31661alpha3 Empty datecomponents: " + dateComponents);
        var dateTime = voltmx.os.date("*t");
        dateComponents = [
            dateTime.day,
            dateTime.month,
            dateTime.year,
            dateTime.hour,
            dateTime.min,
            dateTime.sec
        ];
    }
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, dateComponents, false);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### Utility_findCountryByIso31661alpha3 Countries wcs: " + wcs);

    function Utility_findCountryByIso31661alpha3_errorCallback(error) {
        voltmx.print("### Utility_findCountryByIso31661alpha3_errorCallback: " + JSON.stringify(error));
        return null;
    }
    //com.redora.Location.Country.find(wcs, callback, Utility_findCountryByIso31661alpha3_errorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, callback, Utility_findCountryByIso31661alpha3_errorCallback);
}

function Utility_findNationalityByCode(nnycode, dateComponents, callback) {
    voltmx.print("### Utility_findNationalityByCode");
    var wcs = "select * from mle_v_nationality_m where code = '" + nnycode + "'";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, dateComponents);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### Nationality wcs: " + wcs);

    function Utility_findNationalityByCode_errorCallback(error) {
        voltmx.print("### Utility_findNationalityByCode_errorCallback: " + JSON.stringify(error));
        return null;
    }
    //  com.redora.Person.Nationality.find(wcs, callback, Utility_findNationalityByCode_errorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, callback, Utility_findNationalityByCode_errorCallback);
}

function Utility_defineLocationAddress(addressLine1) {
    var _addressLine1 = addressLine1 === undefined ? false : addressLine1;
    //adress
    var labelAddress = "";
    var housenumber = "";
    var houseletter = "";
    var housenumberaddition = "";
    if (CaseData.location.houseNumber != null && CaseData.location.houseNumber !== "null") {
        voltmx.print("### frmHandle_btnDoneSetHousenumber set housenumber");
        housenumber = CaseData.location.houseNumber;
    }
    if (CaseData.location.houseLetter != null && CaseData.location.houseLetter !== "null") {
        voltmx.print("### frmHandle_btnDoneSetHousenumber set houseletter");
        houseletter = CaseData.location.houseLetter;
    }
    if (CaseData.location.houseNumberAddition != null && CaseData.location.houseNumberAddition !== "null") {
        voltmx.print("### frmHandle_btnDoneSetHousenumber set housenumberaddition");
        housenumberaddition = CaseData.location.houseNumberAddition;
    }
    var streetaddition = "";
    var addMunicipality = "";
    var addCity = "";
    var street = "";
    voltmx.print("### Utility_defineLocationAddress " + JSON.stringify(CaseData.location));
    if (CaseData.location.street != null && Global.vars.specifyFurtherIndication === true && (CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.others || CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.onTheWater || CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.alongTheRoad)) {
        street = CaseData.location.street;
    } else if (CaseData.location.street != null && Global.vars.specifyFurtherIndication === false) {
        street = CaseData.location.street;
    } else {
        street = "";
    }
    if (_addressLine1 === false) {
        if (CaseData.location.city != null && street !== "") {
            addCity = ", " + CaseData.location.city;
        } else if (CaseData.location.city != null) {
            addCity = CaseData.location.city;
        }
        if (CaseData.location.municipality != null && CaseData.location.municipality != CaseData.location.city) {
            addMunicipality = ", " + CaseData.location.municipality;
        } else {
            addMunicipality = "";
        }
    }
    voltmx.print("### Utility_defineLocationAddress " + JSON.stringify(CaseData.enforcementObject));
    if (Global.vars.keepLocationHousenumber === false && Utility_isEmptyEnforcementObject(CaseData.enforcementObject) === true && Global.vars.buildFor == "GEN") {
        voltmx.print("### Utility_defineLocationAddress keephousenumber show false");
        if (Global.vars.enforcementObjectSearch === false) {
            voltmx.print("### Utility_defineLocationAddress keephousenumber empty and delete");
            CaseData.location.houseNumber = null;
            CaseData.location.houseLetter = null;
            CaseData.location.houseNumberAddition = null;
            housenumber = "";
        } else {
            // don't remove housenumber yet, enforcementobject can be add later.
            housenumber = "";
            voltmx.print("### Utility_defineLocationAddress keephousenumber empty");
        }
        streetaddition = "";
    } else {
        voltmx.print("### Utility_defineLocationAddress keephousenumber show true");
        if (CaseData.location.houseNumber != null && CaseData.location.houseNumber !== "null" && Global.vars.specifyFurtherIndication === true && (CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.others || CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.onTheWater || CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.alongTheRoad)) {
            housenumber = CaseData.location.houseNumber;
            streetaddition = (housenumber + " " + (houseletter + " " + housenumberaddition).trim()).trim();
        } else if (CaseData.location.houseNumber != null && CaseData.location.houseNumber !== "null" && Global.vars.specifyFurtherIndication === false) {
            housenumber = CaseData.location.houseNumber;
            streetaddition = (housenumber + " " + (houseletter + " " + housenumberaddition).trim()).trim();
        } else {
            housenumber = "";
            streetaddition = "";
        }
    }
    //   if (Global.vars.buildFor == "RWS"){
    //     labelAddress = street + addMunicipality;
    //   } else {
    if ((street === "" || street === null) && Global.vars.buildFor == "GEN") {
        labelAddress = "";
    } else {
        labelAddress = (street + " " + streetaddition).trim() + addCity + addMunicipality;
    }
    //   }
    voltmx.print("### Utility_defineLocationAddress labelAddress: " + labelAddress);
    return labelAddress;
}

function Utility_getTaskOutcomesForTask(taskIdentification) {
    Global.vars.outcomeTypes = [];
    voltmx.print("#### Utility_getTaskOutcomesForTask");
    var activeTaskTypeId = null;
    for (var j in CaseData.processinfo.tasks) {
        var x = CaseData.processinfo.tasks[j];
        if (x.taskType == CaseData.processinfo.activeTaskType) {
            activeTaskTypeId = x.taskTypeId;
            voltmx.print("#### Utility_getTaskOutcomesForTask activeTaskTypeId: " + activeTaskTypeId);
            break;
        }
    }
    var lOutcomeTypeClause = "select * from mle_v_outcome_type_m where tte_id = (" + activeTaskTypeId + ")";
    voltmx.print("#### Utility_getTaskOutcomesForTask lOutcomeTypeClause: " + lOutcomeTypeClause);
    lOutcomeTypeClause = Utility_addTimelineToWhereClauseObjectSync(lOutcomeTypeClause); //, CaseData.time.dateComponents);
    lOutcomeTypeClause = lOutcomeTypeClause + " and not ind_system";
    lOutcomeTypeClause = Utility_addLanguageToWhereClauseObjectSync(lOutcomeTypeClause);
    lOutcomeTypeClause = lOutcomeTypeClause + " ORDER BY description ASC";
    voltmx.print("### Utility_getTaskOutcomesForTask ocwcs: " + lOutcomeTypeClause);
    //  com.redora.CaseManagementData.OutcomeType.find(lOutcomeTypeClause, Utility_getTaskOutcomesForTaskSuccessCallback, Utility_getTaskOutcomesForTaskErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lOutcomeTypeClause, Utility_getTaskOutcomesForTaskSuccessCallback, Utility_getTaskOutcomesForTaskErrorCallback);
}

function Utility_getTaskOutcomesForTasktypeId(taskTypeId) {
    Global.vars.outcomeTypes = [];
    voltmx.print("#### Utility_getTaskOutcomesForTasktypeId: " + taskTypeId);
    var lOutcomeTypeClause = "select * from mle_v_outcome_type_m where tte_id = " + taskTypeId + " and ind_system = 0";
    voltmx.print("#### Utility_getTaskOutcomesForTasktypeId lOutcomeTypeClause: " + lOutcomeTypeClause);
    lOutcomeTypeClause = Utility_addTimelineToWhereClauseObjectSync(lOutcomeTypeClause); //, CaseData.time.dateComponents);
    lOutcomeTypeClause = Utility_addLanguageToWhereClauseObjectSync(lOutcomeTypeClause);
    lOutcomeTypeClause = lOutcomeTypeClause + " ORDER BY description ASC";
    voltmx.print("### Utility_getTaskOutcomesForTasktypeId ocwcs: " + lOutcomeTypeClause);
    //  com.redora.CaseManagementData.OutcomeType.find(lOutcomeTypeClause, Utility_getTaskOutcomesForTaskSuccessCallback, Utility_getTaskOutcomesForTaskErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lOutcomeTypeClause, Utility_getTaskOutcomesForTaskSuccessCallback, Utility_getTaskOutcomesForTaskErrorCallback);
}

function Utility_getTaskOutcomesForTaskErrorCallback(error) {
    voltmx.print("#### Utility_getTaskOutcomesForTaskErrorCallback: " + JSON.stringify(error));
    Global.vars.outcomeTypes = [];
}

function Utility_getTaskOutcomesForTaskSuccessCallback(result) {
    voltmx.print("#### Utility_getTaskOutcomesForTaskSuccessCallback: " + JSON.stringify(result));
    var outcomeIdentification = null;
    for (var i in result) {
        var v = result[i];
        v.category = "OutcomeType";
        v.name = v.description;
        v.lbl1 = v.description;
        v.imgRight = "arrowrightmini.png";
        v.template = flcSegItemHandle;
        Global.vars.outcomeTypes.push(v);
    }
    if (result.length > 0) {
        Utility_getTaskTypeOutcomeCategories();
    }
}

function Utility_getTaskTypeOutcomeCategories() {
    Global.vars.outcomeTypeCategories = [];
    voltmx.print("#### Utility_getTaskTypeOutcomeCategories");
    var activeTaskTypeId = null;
    for (var j in CaseData.processinfo.tasks) {
        var x = CaseData.processinfo.tasks[j];
        if (x.taskType == CaseData.processinfo.activeTaskType) {
            activeTaskTypeId = x.taskTypeId;
            voltmx.print("#### Utility_getTaskOutcomesForTask activeTaskTypeId: " + activeTaskTypeId);
            break;
        }
    }
    var lTaskTypeOutcomeCategorieClause = "select * from mle_v_task_type_outcome_category_msv where tte_id = '" + activeTaskTypeId + "'";
    voltmx.print("#### Utility_getTaskTypeOutcomeCategories lOffenceCatClause: " + lTaskTypeOutcomeCategorieClause);
    lTaskTypeOutcomeCategorieClause = Utility_addTimelineToWhereClauseObjectSync(lTaskTypeOutcomeCategorieClause, CaseData.time.dateComponents);
    lTaskTypeOutcomeCategorieClause = Utility_addLanguageToWhereClauseObjectSync(lTaskTypeOutcomeCategorieClause);
    voltmx.print("### Utility_getTaskTypeOutcomeCategories ocwcs: " + lTaskTypeOutcomeCategorieClause);
    //com.redora.Offence.TaskTypeOutcomeCategory.find(lTaskTypeOutcomeCategorieClause, Utility_getTaskTypeOutcomeCategoriesSuccessCallback, Utility_getTaskTypeOutcomeCategoriesErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lTaskTypeOutcomeCategorieClause, Utility_getTaskTypeOutcomeCategoriesSuccessCallback, Utility_getTaskTypeOutcomeCategoriesErrorCallback);
}

function Utility_getTaskTypeOutcomeCategoriesErrorCallback(error) {
    voltmx.print("#### Utility_getTaskTypeOutcomeCategoriesErrorCallback: " + JSON.stringify(error));
    Global.vars.outcomeTypeCategories = [];
}

function Utility_getTaskTypeOutcomeCategoriesSuccessCallback(result) {
    voltmx.print("#### Utility_getTaskTypeOutcomeCategories: " + JSON.stringify(result));
    Global.vars.outcomeTypeCategories = [];
    if (result.length > 0) {
        //var lAddHeaderRecord = {lblSecHdr1:voltmx.i18n.getLocalizedString("l_wanted"), imgHeader:"empty.png", template:flcHeaderRed};
        for (var i in result) {
            var v = result[i];
            v.category = "OutcomeTypeCategory";
            v.name = v.ouy_description;
            v.lbl1 = v.ouy_description;
            v.imgRight = "arrowrightmini.png";
            v.order = "999";
            Global.vars.outcomeTypeCategories.push(v);
        }
    }
    voltmx.print("#### Utility_getTaskTypeOutcomeCategories Global.vars.outcomeTypeCategories: " + JSON.stringify(Global.vars.outcomeTypeCategories));
}

function Utility_clearGeoDataCaseLocation() {
    voltmx.print("#### Utility_clearGeoDataCaseLocation");
    CaseData.location.latitude = 0.0;
    CaseData.location.longitude = 0.0;
    CaseData.location.street = null;
    CaseData.location.streetCode = null;
    CaseData.location.houseNumber = null;
    CaseData.location.houseLetter = null;
    CaseData.location.houseNumberAddition = null;
    CaseData.location.city = null;
    CaseData.location.cityCode = null;
    CaseData.location.municipality = null;
    CaseData.location.municipalityCode = null;
    //other values
    CaseData.location.selectedProposition = null;
    CaseData.location.propositions = [];
    CaseData.location.zipcode = null;
    CaseData.location.checkTime = null;
    CaseData.location.roadType = null;
    CaseData.location.roadTypeDescription = null;
    CaseData.location.edited = false;
    CaseData.location.inserted = false;
    voltmx.print("#### Utility_clearGeoDataCaseLocation CaseData.location: " + JSON.stringify(CaseData.location));
}

function Utility_lockLocation() {
    voltmx.print("### Utility_lockLocation: " + JSON.stringify(CaseData.location));
    Global.vars.savedLocation = CaseData.location;
    Global.vars.newLocationSet = true;
}

function Utility_clearlockLocation() {
    voltmx.print("### Utility_clearlockLocation");
    Global.vars.savedLocation = null;
    Global.vars.newLocationSet = false;
    //clear manual location
    Global.vars.savedLocationManual = null;
}

function Utility_getAndroidCameraResolutions() {
    voltmx.print("### Utility_getAndroidCameraResolutions");
}

function Utility_getfilledOptionVariablesForOffence(offencecode) {
    voltmx.print("### Utility_getfilledOptionVariablesForOffence offencecode: " + offencecode);
    var filledOptions = null;
    if (Global.vars.filledOptionVariables != null) {
        filledOptions = [];
        voltmx.print("### Utility_getfilledOptionVariablesForOffence Global.vars.filledOptionVariables: " + JSON.stringify(Global.vars.filledOptionVariables));
        for (var l in Global.vars.filledOptionVariables) {
            var y = Global.vars.filledOptionVariables[l];
            y = Utility_transformKeys(y);
            if (y.filledDate !== undefined && new Date(y.filledDate).toDateString() == new Date().toDateString()) {
                voltmx.print("### Utility_getfilledOptionVariablesForOffence Global.vars.filledOptionVariables y: " + JSON.stringify(y));
                if (y != null && y.offencecode == offencecode) {
                    var addoption = {
                        optiondescription: y.optiondescription,
                        index: l
                    };
                    filledOptions.push(addoption);
                }
            } else {
                Global.vars.filledOptionVariables.splice(l, 1);
            }
        }
        voltmx.print("### Utility_getfilledOptionVariablesForOffence filledOptions: " + JSON.stringify(filledOptions));
    }
    if (filledOptions != null && filledOptions.length === 0) {
        filledOptions = null;
    }
    return filledOptions;
}

function Utility_geterrorMessage(_provider, errorCode, errorMessage) {
    var _errorMessage = "";
    var provider = _provider;
    if (provider != null && provider != "" && provider.length > 3 && voltmx.string.startsWith(provider, "NPR")) {
        provider = "NPR";
    }
    var _i18nError = "e_serverError" + provider + "_" + errorCode.toString() + "";
    voltmx.print("### Utility_geterrorMessage _i18nError: " + _i18nError);
    try {
        _errorMessage = voltmx.i18n.getLocalizedString(_i18nError);
    } catch (err) {
        _errorMessage = errorMessage;
    }
    voltmx.print("### Utility_geterrorMessage _errorMessage 1: " + _errorMessage);
    if (_errorMessage === "" || _errorMessage === null || _errorMessage === undefined) {
        _errorMessage = errorMessage;
    }
    voltmx.print("### Utility_geterrorMessage _errorMessage 2: " + _errorMessage);
    return _errorMessage;
}

function Utility_ANPR_OutOfMemory() {
    voltmx.print("### Utility_ANPR_OutOfMemory memory: " + voltmx.os.freeMemory());
    voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_outOfMemory"), Global_exitApplication, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
}

function Utility_findLicenseplateCountryByIso31661alpha3(iso31661alpha3, dateComponents, callback) {
    voltmx.print("### Utility_findCountryByIso31661alpha3");
    var wcs = "select * from mle_v_country_vehicle_reg_msv where iso_3166_1_alpha3 = '" + iso31661alpha3 + "'";
    if (dateComponents == null) {
        voltmx.print("### Utility_findCountryByIso31661alpha3 Empty datecomponents: " + dateComponents);
        var dateTime = voltmx.os.date("*t");
        dateComponents = [
            dateTime.day,
            dateTime.month,
            dateTime.year,
            dateTime.hour,
            dateTime.min,
            dateTime.sec
        ];
    }
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, dateComponents);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### Utility_findCountryByIso31661alpha3 Countries wcs: " + wcs);

    function Utility_findCountryByIso31661alpha3_errorCallback(error) {
        voltmx.print("### Utility_findCountryByIso31661alpha3_errorCallback: " + JSON.stringify(error));
        return null;
    }
    wcs = wcs.replace("language_code", "cty_language_code");
    voltmx.print("### Utility_findLicenseplateCountryByIso31661alpha3 Countries wcs: " + wcs);
    //  com.redora.Vehicle.CountryVehicleReg.find(wcs, callback, Utility_findCountryByIso31661alpha3_errorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, callback, Utility_findCountryByIso31661alpha3_errorCallback);
}

function Utility_showPhotosANPR(anprpictures, callback) {
    voltmx.print("### Utility_showPhotosANPR: " + JSON.stringify(anprpictures));
    Global.vars.mediaSequence = Global.vars.mediaSequence + 1;
    var time = new Date();
    var formatted = Utility_getLocalizedDateTimeString(time, false);
    var fileExtension = ".jpg";
    var name = "";
    var filename = "";
    var lLocationText = "";
    var lblLocationHeader = voltmx.i18n.getLocalizedString("l_location");
    if (Global.vars.previousForm == "frmResume") {
        lLocationText = frmResume.location.lblText.text;
    } else if (Global.vars.previousForm == "frmNHA") {
        lLocationText = frmNHA.location.lblText.text;
    } else if (Global.vars.previousForm == "frmClamp") {
        lLocationText = frmClamp.lblLocation.text;
    } else if (Global.vars.previousForm == "frmActiveCaseResume") {
        lLocationText = frmActiveCaseResume.lblLocation.text;
    } else if (Global.vars.previousForm == "frmCheckLabel") {
        lLocationText = frmCheckLabel.location.lblText.text;
    } else if (Global.vars.previousForm == "frmRegisterLabel") {
        lLocationText = frmRegisterLabel.location.lblText.text;
    } else {
        lblLocationHeader = voltmx.i18n.getLocalizedString("l_photo");
        lLocationText = filename;
    }
    if (lLocationText == voltmx.i18n.getLocalizedString("l_location")) {
        lLocationText = "";
    }
    for (var i in anprpictures) {
        var v = anprpictures[i];
        name = v.type + "_" + v.name;
        filename = name + fileExtension;
        voltmx.print("### Utility_showPhotosANPR name: " + filename);
        //       if (v.imgPhoto.base64 != null){
        //       	voltmx.print("### Utility_showPhotosANPR photo size: " + v.imgPhoto.base64.length);
        //       }
        var newPhoto = {
            lblLocationHeader: lblLocationHeader,
            lblLocation: lLocationText,
            imgPhoto: {
                base64: v.imgPhoto.base64
            },
            lblDate: formatted,
            imgReceipt: "empty.png",
            id: filename,
            fileName: filename,
            nr: Global.vars.mediaSequence,
            imgSelect: "unchecked.png"
        };
        Global.vars.newPhotos.push(newPhoto);
    }
    var photoData = [];
    for (var j = 0; j < Global.vars.newPhotos.length; j++) {
        voltmx.print("### Utility_showPhotosANPR Global.vars.newPhotos signature check length: " + Global.vars.newPhotos.length);
        var w = Global.vars.newPhotos[j];
        if (
            (w.id !== undefined && w.id != null && w.id.startsWith("signature") === false) || w.id === undefined || w.id === null) {
            photoData.push(w);
        }
    }

    function segPhotos_setData() {
        voltmx.print("### Utility_showPhotosANPR inner function segPhotos_setData");
        frmPhotos.segPhotos.setData(photoData);
    }
    //functie die de UI update
    voltmx.runOnMainThread(segPhotos_setData, []);
    if (Global.vars.newPhotos.length > 0) {
        Global.vars.newPhotos.sort(function(a, b) {
            return b.nr - a.nr;
        });
    }
    //functie die de UI update
    //voltmx.runOnMainThread(frmPhotos_checkPhotoNumber, []);
    //frmPhotos_setPhotosOnForm();
    Global.vars.showPhotosANPRLoaded = true;
}

function Utility_removePhotosANPR() {
    voltmx.print("### Utility_removePhotosANPR");
    var retry = false;
    if (Global.vars.newPhotos.length > 0) {
        for (var b in Global.vars.newPhotos) {
            retry = false;
            var c = Global.vars.newPhotos[b];
            voltmx.print("### Utility_removePhotosANPR id: " + c.id);
            if (c.id.toLowerCase().startsWith("anprphoto") === true) {
                voltmx.print("### Utility_removePhotosANPR anprphoto");
                Global.vars.newPhotos.splice(b, 1);
                retry = true;
                break;
            } else if (c.id.toLowerCase().startsWith("anproverviewphoto") === true) {
                voltmx.print("### Utility_removePhotosANPR anproverviewphoto");
                Global.vars.newPhotos.splice(b, 1);
                retry = true;
                break;
            }
        }
        if (retry === true) {
            Utility_removePhotosANPR();
        }
    } else {
        voltmx.print("### Utility_removePhotosANPR ANPR photos removed");
    }
}

function Utility_addStatusOnLocation() {
    voltmx.print("#### Utility_addStatusOnLocation");

    function getStatusCallback(result) {
        voltmx.print("### Utility_addStatusOnLocation getStatusCallback: " + JSON.stringify(result));
        var _statusDescription = "Gebruiker op locatie";
        var _statuscode = null;
        if (result.length > 0) {
            _statusDescription = result[0].description;
            _statuscode = result[0].id;
        }
        var addstatus = {
            status: Global.vars.officerOnLocationStatus,
            statusDescription: _statusDescription,
            //name of the taskType
            statuscode: _statuscode,
            //Id of the task outcome
            statusSetOn: Utility_getUTCJavascriptDate(null),
            //date in UTC when status was set
            statusSetBy: Global.vars.gUsername,
            //username (email) that set the status
            statusSetByName: CaseData.caseinfo.officerName
        };
        //CaseData.processinfo.statuses.splice(0,0,addstatus);
        CaseData.processinfo.statuses.push(addstatus);
        voltmx.print("#### Utility_addStatusOnLocation added status 1: " + JSON.stringify(addstatus));
        voltmx.print("#### Utility_addStatusOnLocation CaseData.processinfo.statuses 1: " + JSON.stringify(CaseData.processinfo.statuses));
    }

    function getStatusCallbackError(error) {
        voltmx.print("### Utility_getStatusCode getStatusCallbackError: " + JSON.stringify(error));
        var addstatus = {
            status: Global.vars.officerOnLocationStatus,
            statusDescription: "Gebruiker op locatie",
            //name of the taskType
            statuscode: null,
            //Id of the task outcome
            statusSetOn: Utility_getUTCJavascriptDate(null),
            //date in UTC when status was set
            statusSetBy: Global.vars.gUsername,
            //username (email) that set the status
            statusSetByName: CaseData.caseinfo.officerName
        };
        CaseData.processinfo.statuses.splice(0, 0, addstatus);
        voltmx.print("#### Utility_addStatusOnLocation added status 2: " + JSON.stringify(addstatus));
        voltmx.print("#### Utility_addStatusOnLocation CaseData.processinfo.statuses 2: " + JSON.stringify(CaseData.processinfo.statuses));
    }
    var lStatusClause = "select * from mle_v_status_type_m where identification = '" + Global.vars.officerOnLocationStatus + "'";
    lStatusClause = Utility_addTimelineToWhereClauseObjectSync(lStatusClause);
    lStatusClause = Utility_addLanguageToWhereClauseObjectSync(lStatusClause);
    //  	com.redora.CaseManagementData.StatusType.find(lStatusClause, getStatusCallback,getStatusCallbackError);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lStatusClause, getStatusCallback, getStatusCallbackError);
}

function Utility_encryptDecrypt(passphrase, inputstring) {
    //als passphrase gebuik ik: "Red0r@976"
    //inputstring is dan bijvoorbeeld: "Red0r@2019-07-10T09:28:04.447Z"
    //create key
    var encryptDecryptKey = voltmx.crypto.newKey("passphrase", 128, {
        passphrasetext: [passphrase],
        subalgo: "aes",
        passphrasehashlogo: "md5"
    });
    //encrypt and create base64
    var inputstr = inputstring;
    var prptobj = {
        padding: "pkcs5",
        mode: "cbc",
        initializationvector: "1234567890123456"
    };
    var algo = "aes";
    var myEncryptedText = voltmx.crypto.encrypt(algo, encryptDecryptKey, inputstr, prptobj);
    var myEncryptedTextBase64 = voltmx.convertToBase64(myEncryptedText);
    voltmx.print("### Utility_encryptDecrypt myEncryptedTextBase64: " + myEncryptedTextBase64);
    //decrypt
    var mydecryptRawBytes = voltmx.convertToRawBytes(myEncryptedTextBase64);
    var myClearText = voltmx.crypto.decrypt(algo, encryptDecryptKey, mydecryptRawBytes, prptobj);
    voltmx.print("### Utility_encryptDecrypt decrypted myClearText: " + JSON.stringify(myClearText));
    return myEncryptedTextBase64;
}

function Utility_setFlashModeCameraANPROverlay() {
    //hiermee cycle je door de mogelijke instellingen
    voltmx.print("### Utility_setFlashModeCamera Global.vars.gScannerLight: " + Global.vars.gScannerLight);
    //   if(Global.vars.gScannerLight == "3"){
    //     voltmx.print("### Utility_setFlashModeCamera set autoflash");
    //     Global.vars.gScannerLight = "1";
    //     frmANPROverlay.imgFlash.src = "but_flash_auto.png";
    //     frmANPROverlay.lblFlash.text = voltmx.i18n.getLocalizedString("l_flash_auto");
    //   }else if(Global.vars.gScannerLight == "0" || Global.vars.gScannerLight == "1"){
    //     voltmx.print("### Utility_setFlashModeCamera set flash on");
    //     Global.vars.gScannerLight = "2";
    //     frmANPROverlay.imgFlash.src = "but_flash_on.png";
    //     frmANPROverlay.lblFlash.text = voltmx.i18n.getLocalizedString("l_flash_on");
    //   }else if(Global.vars.gScannerLight == "2"){
    //     voltmx.print("### Utility_setFlashModeCamera set flash off");
    //     Global.vars.gScannerLight = "3";
    //     frmANPROverlay.imgFlash.src = "but_flash_off.png";
    //     frmANPROverlay.lblFlash.text = voltmx.i18n.getLocalizedString("l_flash_off");
    //   }
    if (Global.vars.gScannerLight == "0" || Global.vars.gScannerLight == "1") {
        voltmx.print("### Utility_setFlashModeCamera flash auto");
        frmANPROverlay.lblFlashAuto.skin = lblFlashYellow;
        frmANPROverlay.lblFlashOn.skin = lblFlash;
        frmANPROverlay.lblFlashOff.skin = lblFlash;
    } else if (Global.vars.gScannerLight == "2") {
        voltmx.print("### Utility_setFlashModeCamera flash on");
        frmANPROverlay.lblFlashAuto.skin = lblFlash;
        frmANPROverlay.lblFlashOn.skin = lblFlashYellow;
        frmANPROverlay.lblFlashOff.skin = lblFlash;
    } else if (Global.vars.gScannerLight == "3") {
        voltmx.print("### Utility_setFlashModeCamera flash off");
        frmANPROverlay.lblFlashAuto.skin = lblFlash;
        frmANPROverlay.lblFlashOn.skin = lblFlash;
        frmANPROverlay.lblFlashOff.skin = lblFlashYellow;
    }
    frmANPROverlay.flcChooseFlashMode.setVisibility(true);
}

function Utility_setFlashMode(eventObject) {
    //hiermee cycle je door de mogelijke instellingen
    voltmx.print("### Utility_setFlashModeAuto Global.vars.gScannerLight before: " + Global.vars.gScannerLight);
    var flashButtonID = null;
    if (eventObject !== undefined && eventObject != null) {
        flashButtonID = eventObject.id;
    }
    voltmx.print("### Utility_setFlashModeAuto flashButtonID: " + flashButtonID);
    if (flashButtonID == "btnFlashAuto") {
        voltmx.print("### Utility_setFlashModeAuto set autoflash");
        Global.vars.gScannerLight = "1";
        frmANPROverlay.imgFlash.src = "but_flash_auto.png";
        frmANPROverlay.lblFlash.text = voltmx.i18n.getLocalizedString("l_flash_auto");
        frmANPROverlay.lblFlashAuto.skin = lblFlashYellow;
        frmANPROverlay.lblFlashOn.skin = lblFlash;
        frmANPROverlay.lblFlashOff.skin = lblFlash;
        frmANPROverlay.flcChooseFlashMode.setVisibility(false);
    } else if (flashButtonID == "btnFlashOn") {
        voltmx.print("### Utility_setFlashModeCamera set flash on");
        Global.vars.gScannerLight = "2";
        frmANPROverlay.imgFlash.src = "but_flash_on.png";
        frmANPROverlay.lblFlash.text = voltmx.i18n.getLocalizedString("l_flash_on");
        frmANPROverlay.lblFlashAuto.skin = lblFlash;
        frmANPROverlay.lblFlashOn.skin = lblFlashYellow;
        frmANPROverlay.lblFlashOff.skin = lblFlash;
        frmANPROverlay.flcChooseFlashMode.setVisibility(false);
    } else if (flashButtonID == "btnFlashOff") {
        voltmx.print("### Utility_setFlashModeCamera set flash off");
        Global.vars.gScannerLight = "3";
        frmANPROverlay.imgFlash.src = "but_flash_off.png";
        frmANPROverlay.lblFlash.text = voltmx.i18n.getLocalizedString("l_flash_off");
        frmANPROverlay.lblFlashAuto.skin = lblFlash;
        frmANPROverlay.lblFlashOn.skin = lblFlash;
        frmANPROverlay.lblFlashOff.skin = lblFlashYellow;
        frmANPROverlay.flcChooseFlashMode.setVisibility(false);
    }
    voltmx.print("### Utility_setFlashModeAuto Global.vars.gScannerLight after: " + Global.vars.gScannerLight);
}

function Utility_checkGPS() {
    voltmx.print("### Utility_checkGPS");
}

function Utility_setStatementDataToCase(statemendata, StatementNoPledge, OffenceCommunicatedNoStatement, statementText, enableDefaultRepealText) {
    voltmx.print("#### Utility_setStatementDataToCase");
    //CaseData
    CaseData.offence.execByPartner = statemendata.execByPartner;
    CaseData.offence.rightsRead = statemendata.rightsRead;
    CaseData.offence.offenceCommunicated = statemendata.offenceCommunicated;
    CaseData.offence.legalAssistCommunicated = statemendata.legalAssistCommunicated;
    CaseData.offence.usesLegalAssistance = statemendata.usesLegalAssistance;
    CaseData.offence.declinedLegalAssistance = statemendata.declinedLegalAssistance;
    voltmx.print("#### Utility_setStatementDataToCase interpreterCommunicated: " + statemendata.interpreterCommunicated);
    voltmx.print("#### Utility_setStatementDataToCase usesInterpreter: " + statemendata.usesInterpreter);
    voltmx.print("#### Utility_setStatementDataToCase translationLanguageDesc: " + statemendata.translationLanguageDesc);
    CaseData.offence.interpreterCommunicated = statemendata.interpreterCommunicated;
    CaseData.offence.usesInterpreter = statemendata.usesInterpreter;
    CaseData.offence.interpreter = statemendata.interpreter;
    CaseData.offence.translationLanguage = statemendata.translationLanguage;
    CaseData.offence.translationLanguageDesc = statemendata.translationLanguageDesc;
    CaseData.offence.statementEdited = statemendata.statementEdited;
    CaseData.offence.statementKey = statemendata.statementKey;
    //Globals to fill
    Global.vars.StatementNoPledge = StatementNoPledge;
    Global.vars.OffenceCommunicatedNoStatement = OffenceCommunicatedNoStatement;
    Global.vars.statementText = statementText;
    Global.vars.enableDefaultRepealText = enableDefaultRepealText;
    //
    voltmx.print("#### Utility_setStatementDataToCase CaseData text before: " + JSON.stringify(CaseData.text));
    voltmx.print("#### Utility_setStatementDataToCase StatementNoPledge Global.vars.StatementNoPledge: " + Global.vars.StatementNoPledge);
    if (Global.vars.StatementNoPledge != null && Global.vars.StatementNoPledge !== "") {
        // add record to CaseData.text
        var loctextindex = null;
        for (var p = 0; CaseData.text != null && p < CaseData.text.length; p++) {
            var v = CaseData.text[p];
            if (v.type == 20 && voltmx.string.startsWith(v.value, "Cautie: ")) {
                //Reden voor het niet geven van mededeling
                voltmx.print("#### Utility_setStatementDataToCase: " + v + " index: " + p);
                loctextindex = p;
                break;
            }
        }
        var laddrecord = CaseData_setNewtext();
        laddrecord.inserted = true;
        laddrecord.edited = true;
        laddrecord.type = 20; //Reden voor het niet geven van mededeling
        laddrecord.value = Global.vars.StatementNoPledge;
        if (loctextindex === null) {
            laddrecord.value = "Cautie: " + Global.vars.StatementNoPledge;
            CaseData.text.splice(0, 0, laddrecord);
        } else {
            CaseData.text.splice(loctextindex, 1, laddrecord);
        }
        voltmx.print("#### Utility_setStatementDataToCase StatementNoPledge CaseData.text after: " + JSON.stringify(CaseData.text));
    } else {
        for (var q = 0; CaseData.text != null && q < CaseData.text.length; q++) {
            var vv = CaseData.text[q];
            if (vv.type == 20 && voltmx.string.startsWith(vv.value, "Cautie: ")) {
                //Reden voor het niet geven van mededeling
                voltmx.print("#### Utility_setStatementDataToCase: " + vv + " index: " + q);
                CaseData.text.splice(q, 1);
                break;
            }
        }
        voltmx.print("#### Utility_setStatementDataToCase NO StatementNoPledge CaseData.text after: " + JSON.stringify(CaseData.text));
    }
    voltmx.print("#### Utility_setStatementDataToCase OffenceCommunicatedNoStatement Global.vars.OffenceCommunicatedNoStatement: " + Global.vars.OffenceCommunicatedNoStatement);
    if (Global.vars.OffenceCommunicatedNoStatement != null && Global.vars.OffenceCommunicatedNoStatement !== "") {
        // add record to CaseData.text
        var loctextindex2 = null;
        for (var k = 0; CaseData.text != null && k < CaseData.text.length; k++) {
            var w = CaseData.text[k];
            if (w.type == 20 && voltmx.string.startsWith(w.value, "Cautie: ") === false) {
                //Reden voor het niet geven van mededeling
                //if(w.type == 20 && voltmx.string.startsWith(w.value, "Strafbaar feit niet medegedeeld: ")){ //Reden voor het niet geven van mededeling
                voltmx.print("#### frmStatement_setGlobalsToCaseData lblOffenceCommunicatedNoStatement Text: " + w + " index: " + k);
                loctextindex2 = k;
                break;
            }
        }
        var laddrecord2 = CaseData_setNewtext();
        laddrecord2.inserted = true;
        laddrecord2.edited = true;
        laddrecord2.type = 20; //Reden voor het niet geven van mededeling
        laddrecord2.value = Global.vars.OffenceCommunicatedNoStatement;
        if (loctextindex2 === null) {
            laddrecord2.value = Global.vars.OffenceCommunicatedNoStatement;
            //laddrecord2.value = "Strafbaar feit niet medegedeeld: " + Global.vars.OffenceCommunicatedNoStatement;
            CaseData.text.splice(0, 0, laddrecord2);
        } else {
            CaseData.text.splice(loctextindex2, 1, laddrecord2);
        }
        voltmx.print("#### Utility_setStatementDataToCase OffenceCommunicatedNoStatement CaseData.text after: " + JSON.stringify(CaseData.text));
    } else {
        for (var l = 0; CaseData.text != null && l < CaseData.text.length; l++) {
            var ww = CaseData.text[l];
            if (ww.type == 20 && voltmx.string.startsWith(ww.value, "Cautie: ") === false) {
                //Reden voor het niet geven van mededeling
                voltmx.print("#### Utility_setStatementDataToCase: " + ww + " index: " + l);
                CaseData.text.splice(l, 1);
                break;
            }
        }
        voltmx.print("#### Utility_setStatementDataToCase NO OffenceCommunicatedNoStatement CaseData.text after: " + JSON.stringify(CaseData.text));
    }
    for (var m = 0; CaseData.text != null && m < CaseData.text.length; m++) {
        var x = CaseData.text[m];
        if (x.type == 20 && x.value === null) {
            //Reden voor het niet geven van mededeling
            voltmx.print("#### Utility_setStatementDataToCase: " + x + " index: " + m);
            CaseData.text.splice(m, 1);
        }
    }
    voltmx.print("#### Utility_setStatementDataToCase CaseData.text : " + JSON.stringify(CaseData.text));
    voltmx.print("#### Utility_setStatementDataToCase statementText Global.vars.statementText: " + Global.vars.statementText);
    if (Global.vars.statementText != null && Global.vars.statementText !== "") {
        // add record to CaseData.text
        var loctextindex3 = null;
        for (var l = 0; CaseData.text != null && l < CaseData.text.length; l++) {
            var x = CaseData.text[l];
            if (x.type == 2) {
                //Verklaring verdachte
                voltmx.print("#### frmStatement_setGlobalsToCaseData lblStatementText Text:" + x + " index: " + l);
                loctextindex3 = l;
                break;
            }
        }
        var laddrecord3 = CaseData_setNewtext();
        laddrecord3.inserted = true;
        laddrecord3.edited = true;
        laddrecord3.type = 2; //Verklaring verdachte
        laddrecord3.value = Global.vars.statementText;
        if (loctextindex3 === null) {
            CaseData.text.splice(0, 0, laddrecord3);
        } else {
            CaseData.text.splice(loctextindex3, 1, laddrecord3);
        }
        voltmx.print("#### Utility_setStatementDataToCase StatementText CaseData.text after: " + JSON.stringify(CaseData.text));
    }
}

function Utility_cloneObject(obj) {
    voltmx.print("#### Utility_cloneObject");
    var clone = {};
    for (var i in obj) {
        if (obj[i] != null && typeof obj[i] == "object") clone[i] = Utility_cloneObject(obj[i]);
        else clone[i] = obj[i];
    }
    return clone;
}

function Utility_preloadMaps(form) {
    // android only
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === false) {
        voltmx.print("#### Utility_preloadMaps form: " + form);
        var gDescription = voltmx.i18n.getLocalizedString("l_currentPosition");
        var zoomOut = false;
        var zoomlevel = 4;
        var latitude = 52.071807;
        var longitude = 5.483147;
        voltmx.print("#### Utility_preloadMaps Latitude: " + latitude + " Longitude: " + longitude);
        voltmx.print("#### frmLocationMap_buildMap_GPS 2a");
        if (Global.vars.gLongitude === 0.0 || Global.vars.gLongitude === 0 || Global.vars.gLatitude === 0.0 || Global.vars.gLatitude === 0) {
            voltmx.print("#### frmLocationMap_buildMap_GPS 3");
            zoomOut = true;
            latitude = Number(latitude); //52.502848;
            longitude = Number(longitude); //3.887329;
            Global.vars.gGpsFix = false;
            gDescription = voltmx.i18n.getLocalizedString("l_noCoordinates");
        } else {
            longitude = Global.vars.gLongitude;
            latitude = Global.vars.gLatitude;
        }
        voltmx.print("#### Utility_preloadMaps gLatitude: " + Global.vars.gLatitude + " gLongitude: " + Global.vars.gLongitude);
        voltmx.print("#### Utility_preloadMaps 5");
        voltmx.print("#### Utility_preloadMaps Latitude: " + latitude + " Longitude: " + longitude);
        var lmaptable = [];
        lmaptable[0] = {};
        lmaptable[0].lat = latitude + "";
        lmaptable[0].lon = longitude + "";
        lmaptable[0].name = " ";
        lmaptable[0].desc = " ";
        lmaptable[0].image = "empty.png";
        voltmx.print("#### frmLocationMap_buildMap_GPS lmaptable: " + JSON.stringify(lmaptable));
        if (form == "frmClamp") {
            frmClamp.mapLocation.locationData = lmaptable;
            //frmClamp.mapLocation.retainMapPositionOnRestore = true;
            //}else if(form == "frmFollow"){
            //frmFollow.mapCases.locationData = lmaptable;
            //frmFollow.mapCases.retainMapPositionOnRestore = true;
        } else if (form == "frmHandle") {
            frmHandle.mapLocationHandle.locationData = lmaptable;
            //frmHandle.mapLocationHandle.retainMapPositionOnRestore = true;
        } else if (form == "frmLocation") {
            frmLocation.mapLocation.locationData = lmaptable;
            frmLocation.mapLocation.retainMapPositionOnRestore = true;
        } else if (form == "frmLogOn") {
            frmLogOn.mapCloud.locationData = lmaptable;
            //frmLogOn.mapCloud.retainMapPositionOnRestore = true;
        } else if (form == "frmNHA") {
            //frmNHA.mapdisplay.mapLocation.retainMapPositionOnRestore = true;
        } else if (form == "frmRegister") {
            frmRegister.mapRegister.locationData = lmaptable;
            frmRegister.mapRegister.retainMapPositionOnRestore = true;
        } else if (form == "frmResume") {
            //frmResume.mapdisplay.mapLocation.retainMapPositionOnRestore = true;
        } else if (form == "frmTrackDown") {
            frmTrackDown.mapTrackDown.locationData = lmaptable;
            frmTrackDown.mapTrackDown.retainMapPositionOnRestore = true;
        } else if (form == "frmCheckVehicle") {
            frmCheckVehicle.mapCheckVehicle.locationData = lmaptable;
            frmCheckVehicle.mapCheckVehicle.retainMapPositionOnRestore = true;
        } else if (form == "frmOverviewLabel") {
            frmOverviewLabel.mapCases.locationData = lmaptable;
            //frmOverviewLabel.mapCases.retainMapPositionOnRestore = true;
        }
    }
}

function Utility_resetEnforcementObject() {
    voltmx.print("### Utility_resetEnforcementObject");
    if (CaseData.enforcementObject !== undefined && CaseData.enforcementObject != null) {
        voltmx.print("### Utility_resetEnforcementObject CaseData enforcementObject pre: " + JSON.stringify(CaseData.enforcementObject));
        CaseData.enforcementObject = CaseData_setNewEnforcementObject();
        Global.vars.gCaseEnforcementObject = null;
        voltmx.print("### Utility_resetEnforcementObject CaseData enforcementObject after: " + JSON.stringify(CaseData.enforcementObject));
    }
}

function Utility_clearEnforcementObjectQuestions() {
    voltmx.print("### Utility_clearEnforcementObjectQuestions");
    if (CaseData.questions !== undefined && CaseData.questions != null) {
        voltmx.print("### Utility_clearEnforcementObjectQuestions CaseData questions pre: " + JSON.stringify(CaseData.questions));
        CaseData.questions = [];
        Global.vars.questionTypes = [];
        Global.vars.questions = [];
        Global.vars.QuestionsSet = false;
        voltmx.print("### Utility_clearEnforcementObjectQuestions CaseData questions after: " + JSON.stringify(CaseData.questions));
    }
}

function Utility_getFuelDescription(fuels) {
    var fuelDescription = "";
    if (fuels != null && fuels.length > 0) {
        for (var j = 0; j < fuels.length; j++) {
            var fuel = fuels[j];
            if (fuelDescription.length > 0) {
                fuelDescription = fuelDescription + ", " + fuel.description;
            } else {
                fuelDescription = fuel.description;
            }
        }
    }
    return fuelDescription;
}

function Utility_back_to_Menu() {
    Global.vars.openedFromMenu = false;
    if (Global.vars.previousMenuForm == "frmLogOn") {
        frmLogOn.show();
    } else if (Global.vars.previousMenuForm == "frmFollow") {
        frmFollow.show();
    } else if (Global.vars.previousMenuForm == "frmTrackDown") {
        frmTrackDown.show();
    } else if (Global.vars.previousMenuForm == "frmCheckVehicle") {
        frmCheckVehicle.show();
    } else if (Global.vars.previousMenuForm == "frmCheckCard") {
        frmCheckCard.show();
    } else if (Global.vars.previousMenuForm == "frmCheckLocation") {
        frmCheckLocation.show();
    } else if (Global.vars.previousMenuForm == "frmActiveCases") {
        frmActiveCases.show();
    } else if (Global.vars.previousMenuForm == "frmStatistics") {
        frmStatistics.show();
    } else if (Global.vars.previousMenuForm == "frmRegister") {
        frmRegister.show();
    } else if (Global.vars.previousMenuForm == "frmRegisterConcept") {
        frmRegisterConcept.show();
    } else if (Global.vars.previousMenuForm == "frmConcepts") {
        frmConcepts.show();
    } else if (Global.vars.previousMenuForm == "frmOutbox") {
        frmOutbox.show();
    } else if (Global.vars.previousMenuForm == "frmOpenTasks") {
        frmOpenTasks.show();
    } else if (Global.vars.previousMenuForm == "frmOverviewTask") {
        frmOverviewTask.show();
    } else if (Global.vars.previousMenuForm == "frmHistory") {
        frmHistory.show();
    }
    Global.vars.previousMenuForm = null;
}

function Utility_clearOverlayANPRImage() {
    voltmx.print("### Utility_clearOverlayANPRImage");
    voltmx.runOnMainThread(Utility_clearOverlayANPRImage_MT, []);
}

function Utility_clearOverlayANPRImage_MT() {
    voltmx.print("### Utility_clearOverlayANPRImage_MT");
    frmANPROverlay.imgPlate.src = "empty.png";
    frmANPROverlay.flcLicenseplateImage.setVisibility(false);
    frmANPROverlay.flcVehicleInfo.setVisibility(false);
    frmANPROverlay.flcResult.setVisibility(false);
    frmANPROverlay.licenseplate.setVisibility(false);
    frmANPROverlay.flcSpacer.setVisibility(false);
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        frmANPROverlay.flcLoader.setVisibility(false);
        try {
            voltmx.timer.cancel("ANPRLoading");
        } catch (err) {}
    }
}

function Utility_showANPRLoading() {
    voltmx.print("### Utility_showANPRLoading");
    if (frmANPROverlay.flcLicenseplateInfo.top != "0%") {
        voltmx.print("### Utility_showANPRLoading Licenseplate info niet te zien!");
        frmANPROverlay.flcLicenseplateInfo.top = "0%";
    }
    if (frmANPROverlay.flcLoader.lblLoader.text == voltmx.i18n.getLocalizedString("l_loadLicenseplate")) {
        frmANPROverlay.flcLoader.lblLoader.text = voltmx.i18n.getLocalizedString("l_loadLicenseplate") + ".";
    } else if (frmANPROverlay.flcLoader.lblLoader.text == voltmx.i18n.getLocalizedString("l_loadLicenseplate") + ".") {
        frmANPROverlay.flcLoader.lblLoader.text = voltmx.i18n.getLocalizedString("l_loadLicenseplate") + "..";
    } else if (frmANPROverlay.flcLoader.lblLoader.text == voltmx.i18n.getLocalizedString("l_loadLicenseplate") + "..") {
        frmANPROverlay.flcLoader.lblLoader.text = voltmx.i18n.getLocalizedString("l_loadLicenseplate") + "...";
    } else if (frmANPROverlay.flcLoader.lblLoader.text == voltmx.i18n.getLocalizedString("l_loadLicenseplate") + "...") {
        frmANPROverlay.flcLoader.lblLoader.text = voltmx.i18n.getLocalizedString("l_loadLicenseplate");
    }
}

function Utility_setCaseTypeAndTaskToCase(casedata, callback, errorcallback) {
    voltmx.print("### Utility_setCaseTypeAndTaskToCase");

    function Utility_setCaseTypeAndTaskToCase_getTaskTypeSuccessCallback(result) {
        voltmx.print("#### Utility_setCaseTypeAndTaskToCase_getTaskTypeSuccessCallback: " + JSON.stringify(result));
        voltmx.print("#### Utility_setCaseTypeAndTaskToCase_getTaskTypeSuccessCallback casedata.processinfo before: " + JSON.stringify(casedata.processinfo));
        //now set taskdata to case
        if (result.length > 0) {
            casedata.processinfo.activeTaskType = result[0].identification;
            var task = CaseData_setNewTask();
            task.taskType = result[0].identification;
            task.taskTypeId = result[0].id;
            task.taskTypeDescription = result[0].description;
            task.taskClaimedBy = Global.vars.gUsername;
            task.taskClaimedByName = casedata.caseinfo.officerName;
            task.processName = result[0].process;
            for (var i in casedata.processinfo.tasks) {
                var v = casedata.processinfo.tasks[i];
                if (v.taskType == result[0].identification) {
                    voltmx.print("#### Utility_setCaseTypeAndTaskToCase_getTaskTypeSuccessCallback 1");
                    v.taskTypeDescription = result[0].description;
                    v.taskTypeId = result[0].id;
                    v.taskClaimedBy = Global.vars.gUsername;
                    v.taskClaimedByName = casedata.caseinfo.officerName;
                    v.processName = result[0].process;
                } else if (v.taskType === null) {
                    voltmx.print("#### Utility_setCaseTypeAndTaskToCase_getTaskTypeSuccessCallback 2");
                    v.taskType = result[0].identification;
                    v.taskTypeDescription = result[0].description;
                    v.taskTypeId = result[0].id;
                    v.taskClaimedBy = Global.vars.gUsername;
                    v.taskClaimedByName = casedata.caseinfo.officerName;
                    v.processName = result[0].process;
                } else {
                    voltmx.print("#### Utility_setCaseTypeAndTaskToCase_getTaskTypeSuccessCallback 3");
                    casedata.processinfo.tasks.push(task);
                }
            }
            casedata.processinfo.lastTaskProcessed = task;
            callback();
            voltmx.print("#### Utility_setCaseTypeAndTaskToCase_getTaskTypeSuccessCallback casedata.processinfo after: " + JSON.stringify(casedata.processinfo));
        } else {
            errorcallback();
        }
    }

    function Utility_setCaseTypeAndTaskToCase_getCaseTypeCategoryErrorCallback(error) {
        voltmx.print("### Utility_setCaseTypeAndTaskToCase_getCaseTypeCategoryErrorCallback: " + JSON.stringify(error));
        errorcallback();
    }

    function Utility_setCaseTypeAndTaskToCase_getCaseTypeCategorySuccessCallback(result) {
        voltmx.print("### Utility_setCaseTypeAndTaskToCase_getCaseTypeCategorySuccessCallback: " + JSON.stringify(result));
        if (result.length > 0) {
            casedata.caseinfo.caseTypeCategory = result[0].code;
            //now get and set the default TaskType
            Utility_setDefaultStatusFromInstanceParamFrmTrackDefaultCaseTypeTaskType();
            Utility_getTaskType(Global.vars.frmTrackDefaultCaseTypeTaskType.TaskType, Utility_setCaseTypeAndTaskToCase_getTaskTypeSuccessCallback);
        } else {
            errorcallback();
        }
    }

    function Utility_setCaseTypeAndTaskToCase_SetCaseType(result) {
        voltmx.print("### Utility_setCaseTypeAndTaskToCase_SetCaseType: " + JSON.stringify(result));
        if (result.length > 0) {
            casedata.caseinfo.caseType = result[0].identification;
            casedata.caseinfo.caseTypeId = result[0].id;
            casedata.caseinfo.caseTypeCategoryId = result[0].ctc_id;
            var lCaseTypeCategoryClause = "select * from mle_v_case_type_category_m where id ='" + result[0].ctc_id + "'";
            voltmx.print("#### Utility_setCaseTypeAndTaskToCase_SetCaseType lCaseTypeCategoryClause: " + lCaseTypeCategoryClause);
            lCaseTypeCategoryClause = Utility_addTimelineToWhereClauseObjectSync(lCaseTypeCategoryClause, casedata.time.dateComponents);
            lCaseTypeCategoryClause = Utility_addLanguageToWhereClauseObjectSync(lCaseTypeCategoryClause);
            voltmx.print("### Utility_setCaseTypeAndTaskToCase_SetCaseType ocwcs: " + lCaseTypeCategoryClause);
            //try and find the casetypeCategory
            //      com.redora.CaseManagementData.CaseTypeCategory.find(lCaseTypeCategoryClause, Utility_setCaseTypeAndTaskToCase_getCaseTypeCategorySuccessCallback, Utility_setCaseTypeAndTaskToCase_getCaseTypeCategoryErrorCallback);
            KNYMobileFabric.OfflineObjects.executeSelectQuery(lCaseTypeCategoryClause, Utility_setCaseTypeAndTaskToCase_getCaseTypeCategorySuccessCallback, Utility_setCaseTypeAndTaskToCase_getCaseTypeCategoryErrorCallback);
        } else {
            errorcallback();
        }
    }
    //first determin there is no CaseType
    voltmx.print("### Utility_setCaseTypeAndTaskToCase current casedata: " + JSON.stringify(casedata));
    if (casedata !== undefined && casedata.caseinfo !== undefined && (casedata.caseinfo.caseType === undefined || casedata.caseinfo.caseType === null || casedata.caseinfo.caseType === "")) {
        voltmx.print("### Utility_setCaseTypeAndTaskToCase current caseType: " + casedata.caseinfo.caseType);
        voltmx.print("### Utility_setCaseTypeAndTaskToCase current casedata Global.vars.gCaseVehicles: " + JSON.stringify(Global.vars.gCaseVehicles));
        voltmx.print("### Utility_setCaseTypeAndTaskToCase Global.vars.frmTrackDefaultCaseTypeTaskType: " + JSON.stringify(Global.vars.frmTrackDefaultCaseTypeTaskType));
        //now get the casetype
        Utility_getCaseTypeDescription(Global.vars.frmTrackDefaultCaseTypeTaskType.CaseType, Utility_setCaseTypeAndTaskToCase_SetCaseType);
    } else {
        voltmx.print("### Utility_setCaseTypeAndTaskToCase case already has a casetype");
        callback();
    }
}

function Utility_daysBetweenDates(date1, date2) {
    voltmx.print("### Utility_daysBetweenDates date1: " + new Date(date1));
    voltmx.print("### Utility_daysBetweenDates date2: " + new Date(date2));
    var oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
    var firstDate = new Date(date1);
    var secondDate = new Date(date2);
    var diffDays = Math.round(Math.abs((firstDate - secondDate) / oneDay));
    return diffDays;
}

function Utility_getMonthsBetweenDates(date1, date2) {
    voltmx.print("### Utility_getMonthsBetweenDates date1: " + new Date(date1));
    voltmx.print("### Utility_getMonthsBetweenDates date2: " + new Date(date2));
    date1 = new Date(date1);
    date2 = new Date(date2);
    var months;
    months = (date2.getFullYear() - date1.getFullYear()) * 12;
    months -= date1.getMonth();
    months += date2.getMonth();
    return months <= 0 ? 0 : months;
}

function Utility_translateDuration(iso8601Duration) {
    //P1Y2M10DT2H30M
    //var iso8601DurationRegex = /(-)?P(?:([.,\d]+)Y)?(?:([.,\d]+)M)?(?:([.,\d]+)W)?(?:([.,\d]+)D)?T(?:([.,\d]+)H)?(?:([.,\d]+)M)?(?:([.,\d]+)S)?/;
    var iso8601DurationRegex = /(-)?P(?:([.,\d]+)Y)?(?:([.,\d]+)M)?(?:([.,\d]+)W)?(?:([.,\d]+)D)?(?:T(?:([.,\d]+)H)?(?:([.,\d]+)M)?(?:([.,\d]+)S)?)?/;
    var matches = iso8601Duration.match(iso8601DurationRegex);
    return {
        sign: matches[1] === undefined ? "+" : "-",
        years: matches[2] === undefined ? 0 : matches[2],
        months: matches[3] === undefined ? 0 : matches[3],
        weeks: matches[4] === undefined ? 0 : matches[4],
        days: matches[5] === undefined ? 0 : matches[5],
        hours: matches[6] === undefined ? 0 : matches[6],
        minutes: matches[7] === undefined ? 0 : matches[7],
        seconds: matches[8] === undefined ? 0 : matches[8]
    };
}

function Utility_startPDFViewer(fileLocation) {
    voltmx.print("### Utility_startPDFViewer StartPDFViewer");

    function callback(result) {
        voltmx.print("### Utility_startPDFViewer StartPDFViewer callback: " + result);
    }
    REDLINE.startPDFviewer( /**String*/ fileLocation, /**Function*/ callback);
}

function Utility_checkObjectEqual_RecusiveDeepEqual(objA, objB) {
    // Multiple comparision check
    //--------------------------------------------
    var args = Array.prototype.slice.call(arguments);
    if (args.length > 2) {
        for (var a = 1; a < args.length; ++a) {
            if (!Utility_checkObjectEqual_RecusiveDeepEqual(args[a - 1], args[a])) {
                return false;
            }
        }
        return true;
    } else if (args.length < 2) {
        voltmx.print("Utility_checkObjectEqual_RecusiveDeepEqual, requires atleast 2 arguments");
    }
    // basic equality check,
    //--------------------------------------------
    // if this succed the 2 basic values is equal,
    // such as numbers and string.
    //
    // or its actually the same object pointer. Bam
    //
    // Note that if string and number strictly equal is required
    // change the equality from ==, to ===
    //
    if (objA == objB) {
        return true;
    }
    // If a value is a bsic type, and failed above. This fails
    var basicTypes = ["boolean", "number", "string"];
    if (basicTypes.indexOf(typeof objA) >= 0 || basicTypes.indexOf(typeof objB) >= 0) {
        return false;
    }
    // JSON equality check,
    //--------------------------------------------
    // this can fail, if the JSON stringify the objects in the wrong order
    // for example the following may fail, due to different string order:
    //
    // JSON.stringify( {a:1, b:2} ) == JSON.stringify( {b:2, a:1} )
    //
    if (JSON.stringify(objA) == JSON.stringify(objB)) {
        return true;
    }
    // Array equality check
    //--------------------------------------------
    // This is performed prior to iteration check,
    // Without this check the following would have been considered valid
    //
    // simpleRecusiveDeepEqual( { 0:1963 }, [1963] );
    //
    // Note that u may remove this segment if this is what is intended
    //
    if (Array.isArray(objA)) {
        //objA is array, objB is not an array
        if (!Array.isArray(objB)) {
            return false;
        }
    } else if (Array.isArray(objB)) {
        //objA is not array, objB is an array
        return false;
    }
    // Nested values iteration
    //--------------------------------------------
    // Scan and iterate all the nested values, and check for non equal values recusively
    //
    // Note that this does not check against null equality, remove the various "!= null"
    // if this is required
    var i; //reuse var to iterate
    // Check objA values against objB
    for (i in objA) {
        //Protect against inherited properties
        if (objA.hasOwnProperty(i)) {
            if (objB.hasOwnProperty(i)) {
                // Check if deep equal is valid
                if (!Utility_checkObjectEqual_RecusiveDeepEqual(objA[i], objB[i])) {
                    return false;
                }
            } else if (objA[i] != null) {
                //ignore null values in objA, that objB does not have
                //else fails
                return false;
            }
        }
    }
    // Check if objB has additional values, that objA do not, fail if so
    for (i in objB) {
        if (objB.hasOwnProperty(i)) {
            if (objB[i] != null && !objA.hasOwnProperty(i)) {
                //ignore null values in objB, that objA does not have
                //else fails
                return false;
            }
        }
    }
    // End of all checks
    //--------------------------------------------
    // By reaching here, all iteration scans have been done.
    // and should have returned false if it failed
    return true;
}

function Utility_determinKindOfProhibition(caseType) {
    var splitCaseType = caseType.split("_");
    var caseTypePrefix = splitCaseType[0];
    var prohibitionType = "";
    if (caseTypePrefix.endsWith("VV")) {
        prohibitionType = "Verblijfsverbod";
    } else if (caseTypePrefix.endsWith("RV")) {
        prohibitionType = "Reisverbod";
    }
    return prohibitionType;
}

function Utility_setOptionVariablesGlobals() {
    voltmx.print("### Utility_setOptionVariablesGlobals");
    if (CaseData.option != null && CaseData.option.length > 0) {
        voltmx.print("### Utility_setOptionVariablesGlobals CaseData.option:" + JSON.stringify(CaseData.option));
        for (var a in CaseData.option) {
            var b = CaseData.option[a];
            b = Utility_transformKeys(b);
            voltmx.print("### Utility_setOptionVariablesGlobals CaseData option b: " + JSON.stringify(b));
            if (b.offencecode == CaseData.offence.offenceCode) {
                Global.vars.selectedOption = b;
                Global.vars.optionvariablesSet = true;
                voltmx.print("### Utility_setOptionVariablesGlobals fill Global.vars.optionvariablesText 1");
                Global.vars.optionvariablesText = b.filledText;
            }
        }
    } else {
        voltmx.print("### Utility_setOptionVariablesGlobals CaseData.option empty");
        Utility_clearOptions();
    }
}

function Utility_RegisterCase(caseData, callback, errorcallback) {
    voltmx.print("### Utility_RegisterCase");
    try {
        //set Ticket number
        Utility_setTicketNumberToRegisterCaseData(caseData);
        //set update time
        caseData.caseinfo.lastUpdateTime = Utility_getUTCJavascriptDate(null);
    } catch (e) {
        voltmx.print("### Utility_RegisterCase NO caseData.caseinfo / TypeError: Cannot read property 'xxx' of undefined");
    }
    //RegisterCaseService
    function RegisterCaseCallback(result) {
        voltmx.print("### Utility_RegisterCase RegisterCaseCallback result: " + JSON.stringify(result));
        //    01-30 12:01:27.788: D/StandardLib(3619): ### RegisterCaseCallback result: {"response":[{"externalCaseId":null,"code":"RegisterCaseError","checkCaseOutcome":null,"caseId":null,"description":"Exception occurred during execution on the exchange: Exchange[c9548208-a7b1-4cb8-b735-db4156852f4c]","ok":false,"caseType":null}],"opstatus":0,"httpStatusCode":500,"httpresponse":{"headers":{"X-Android-Received-Millis":"1580382087755","Cache-Control":"no-store, no-cache, must-revalidate","Access-Control-Allow-Methods":"GET, HEAD, POST, TRACE, OPTIONS, PUT, DELETE, PATCH","X-Kony-Service-Message":"","X-Android-Selected-Protocol":"http/1.1","Server":"Kony","X-Android-Sent-Millis":"1580382087495","X-Kony-RequestId":"609754b5-0409-4d9e-8689-339e0ae2db1b","Connection":"Keep-Alive","Date":"Thu, 30 Jan 2020 11:01:27 GMT","Access-Control-Allow-Origin":"*","X-Kony-Service-Opstatus":"0","Content-Type":"text/plain;charset=UTF-8","X-Android-Response-Source":"NETWORK 200","Keep-Alive":"timeout=5, max=100","Pragma":"no-cache","Content-Length":"280"},"url":"https://dev8.redora.com/services/CaseServiceActions/RegisterCase","responsecode":200}}
        if (result.opstatus === 0 && result.httpStatusCode == 200) {
            if (result.response[0].ok === false) {
                voltmx.print("### Utility_RegisterCase RegisterCaseCallback Error: RegisterCaseError");
                errorcallback();
            } else {
                callback(result);
            }
        } else {
            //alert(voltmx.i18n.getLocalizedString("e_ser0001"));
            voltmx.application.dismissLoadingScreen();
            if (result.httpStatusCode == 409) {
                //zaak bestaat al in all cases (en kan daardoor niet in Active aangemaakt worden) of zaak heeft een ongeldige status
                voltmx.print("### RegisterCaseCallback 409");
                if (result.response[0].code === "DuplicateCaseException") {
                    voltmx.print("### RegisterCaseCallback  DuplicateCaseException: " + result.response[0].description);
                } else if (result.response[0].code === "InvalidStateException") {
                    voltmx.print("### RegisterCaseCallback  InvalidStateException: " + result.response[0].description);
                } else {
                    voltmx.print("### RegisterCaseCallback some other error: " + result.response[0].description);
                }
                voltmx.print("### RegisterCaseCallback execute error callback");
                errorcallback();
            } else {
                voltmx.print("### RegisterCaseCallback execute error callback2");
                errorcallback();
            }
        }
    }

    function RegisterCaseErrorCallback(error) {
        voltmx.print("### RegisterCaseErrorCallback error: " + JSON.stringify(error));
        if (error !== undefined && error.httpStatusCode == 409) {
            //zaak bestaat al in all cases (en kan daardoor niet in Active aangemaakt worden) of zaak heeft een ongeldige status
            voltmx.print("### RegisterCaseCallback Error 409");
            if (error.response[0].code === "DuplicateCaseException") {
                voltmx.print("### RegisterCaseCallback Error DuplicateCaseException: " + error.response[0].description);
            } else if (error.response[0].code === "InvalidStateException") {
                voltmx.print("### RegisterCaseCallback Error InvalidStateException: " + error.response[0].description);
            } else {
                voltmx.print("### RegisterCaseCallback some other Error: " + error.response[0].description);
            }
            voltmx.print("### RegisterCaseCallback execute Error error callback");
            errorcallback(error);
        } else {
            voltmx.print("### RegisterCaseCallback execute Error error callback2");
            errorcallback();
        }
    }
    service_RegisterCase(caseData, RegisterCaseCallback, RegisterCaseErrorCallback);
}

function Utility_saveUploadCaseData(caseData, callback, formToSave) {
    if (caseData !== undefined && caseData != null) {
        var d = new Date();
        var n = d.getTime();
        var time = CaseData.time.utcDateTime;
        if (time !== undefined && time != null && time !== "") {
            n = time;
        }
        var caseName = "noCouchCase" + n;
        try {
            var name = caseData.caseinfo.id;
            if (name !== undefined && name != null && typeof name === "string" && name !== "") {
                caseName = name;
            }
            if (formToSave !== undefined && formToSave != null && typeof formToSave === "string") {
                caseData.caseinfo.formSaved = formToSave;
            }
            caseData.caseinfo.appModus = Global.vars.appMode;
            voltmx.print("### Utility_saveUploadCaseData caseData.caseinfo.formSaved: " + caseData.caseinfo.formSaved);
        } catch (e) {
            voltmx.print("### Utility_saveUploadCaseData NO caseData.caseinfo / TypeError: Cannot read property 'id' of undefined");
        }
        if (caseData.person !== undefined) {
            voltmx.print("### Utility_saveUploadCaseData caseData.person: " + JSON.stringify(caseData.person));
        }
        caseData.globals = Utility_saveGlobalsForFiles();

        function registerSuccescallback(result) {
            //succes
            voltmx.print("### Utility_saveUploadCaseData registerSuccescallback case uploaded: " + JSON.stringify(result));
            if (callback !== undefined && callback != null) {
                voltmx.print("### Utility_saveUploadCaseData execute callback");
                callback(result);
            } else {
                voltmx.print("### Utility_saveUploadCaseData NO callback specified");
            }
        }

        function registerErrorcallback(error) {
            voltmx.print("### Utility_saveUploadCaseData registerErrorcallback case NOT uploaded: " + error);
            if (error !== undefined && error.httpStatusCode == 409) {
                //alert(voltmx.i18n.getLocalizedString("l_documentAlreadyProcessedException"));
                if (callback !== undefined && callback != null) {
                    voltmx.print("### Utility_saveUploadCaseData registerErrorcallback execute callback");
                    callback();
                } else {
                    voltmx.print("### Utility_saveUploadCaseData registerErrorcallback NO callback specified");
                }
            } else {
                //case could not be uploaded for some reason so write to filesystem
                voltmx.print("### Utility_saveUploadCaseData registerErrorcallback casename: " + caseName);
                var path = voltmx.io.FileSystem.getDataDirectoryPath();
                if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
                    var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
                    if (appgrouppath != null) {
                        voltmx.print("### Utility_saveUploadCaseData registerErrorcallback grouppath: " + appgrouppath);
                        path = appgrouppath;
                    }
                }
                voltmx.print("### Utility_saveUploadCaseData registerErrorcallback path: " + JSON.stringify(path));
                //create directory outbox
                var myDirectoryOutbox = voltmx.io.FileSystem.getFile(path + "/offline/");
                var directorycreatedOffline = myDirectoryOutbox.createDirectory();
                voltmx.print("### Utility_saveUploadCaseData registerErrorcallback directorycreatedOffline: " + JSON.stringify(directorycreatedOffline)); //returns false if already exists
                var folderForCase = "/offline/";
                //
                var myDirectoryCase = voltmx.io.FileSystem.getFile(path + folderForCase + caseName + "/");
                var directorycreatedCase = myDirectoryCase.createDirectory();
                voltmx.print("### Utility_saveUploadCaseData registerErrorcallback directorycreatedCase: " + JSON.stringify(directorycreatedCase)); //returns false if already exists
                //create the file in directory jsonfiles
                var myfile = voltmx.io.FileSystem.getFile(path + folderForCase + caseName + "/" + "case.json");
                if (myfile.exists()) {
                    voltmx.print("### Utility_saveUploadCaseData registerErrorcallback myfile exists");
                    myfile.remove();
                }
                if (caseData.caseinfo !== undefined && caseData.caseinfo != null) {
                    var filecreated = myfile.createFile();
                    voltmx.print("### Utility_saveUploadCaseData registerErrorcallback File IS READABLE >>>>>>>" + myfile.readable);
                    voltmx.print("### Utility_saveUploadCaseData registerErrorcallback File IS WRITABLE >>>>>>>" + myfile.writable);
                    var dataToWrite = JSON.stringify(caseData);
                    var dataAdded = myfile.write(dataToWrite, false);
                    voltmx.print("### Utility_saveUploadCaseData registerErrorcallback File IS DATA ADDED >>>>>>>" + dataAdded);
                }
                if (callback !== undefined && callback != null) {
                    voltmx.print("### Utility_saveUploadCaseData registerErrorcallback execute callback");
                    callback();
                } else {
                    voltmx.print("### Utility_saveUploadCaseData registerErrorcallback NO callback specified");
                }
                //Global.vars.writtenCaseName = caseName;
                //Global.vars.claimedDocID = caseName;
            }
        }
        //Delete case if it exists offline
        Utility_deleteOfflineJSONCaseData(caseName);
        //Try to upload case
        Utility_RegisterCase(caseData, registerSuccescallback, registerErrorcallback);
    }
}

function Utility_setTicketNumberToRegisterCaseData(caseData) {
    caseData.time.dateComponents = Utility_normalizeDateObject(caseData.time.dateComponents);
    voltmx.print("### Utility_setTicketNumberToRegisterCaseData: " + JSON.stringify(caseData.time.dateComponents));
    if (caseData.time.dateComponents == null) {
        voltmx.print("### Utility_setTicketNumberToRegisterCaseData Empty datecomponents: " + caseData.time.dateComponents);
        if (caseData.time.utcDateTime !== undefined && caseData.time.utcDateTime != null && caseData.time.utcDateTime !== "") {
            var caseDate = new Date(caseData.time.utcDateTime);
            voltmx.print("### Utility_setTicketNumberToRegisterCaseData CaseTime UTC: " + caseDate);
            var case_date = caseDate.getDate();
            var case_month = caseDate.getMonth();
            case_month++;
            var case_year = caseDate.getFullYear();
            var case_hour = caseDate.getHours();
            var case_min = caseDate.getMinutes();
            var case_sec = caseDate.getSeconds();
            caseData.time.dateComponents = [
                case_date,
                case_month,
                case_year,
                case_hour,
                case_min,
                case_sec
            ];
        } else {
            var dateTime = voltmx.os.date("*t");
            var dateComponents = [
                dateTime.day,
                dateTime.month,
                dateTime.year,
                dateTime.hour,
                dateTime.min,
                dateTime.sec
            ];
            caseData.time.dateComponents = dateComponents;
        }
    }
    var dateTimeComponent = Utility_getIdentificationString(caseData.time.dateComponents, "ticket");
    voltmx.print("### Utility_setTicketNumberToRegisterCaseData dateTimeComponent: " + JSON.stringify(dateTimeComponent));
    var lpadLength = 20 - dateTimeComponent.length;
    if (typeof lpadLength == "number" && lpadLength > 0 && lpadLength < 21) {
        voltmx.print("### Utility_setTicketNumberToRegisterCaseData Valid length");
    } else {
        lpadLength = 20;
    }
    voltmx.print("### Utility_setTicketNumberToRegisterCaseData lpadLength: " + lpadLength);
    if (caseData.caseinfo.officerNumber != null) {
        caseData.caseinfo.ticketNumber = dateTimeComponent + (caseData.caseinfo.officerNumber + "").lpad("0", lpadLength);
    } else if (Global.vars.gOfficerNumber != null) {
        caseData.caseinfo.ticketNumber = dateTimeComponent + (Global.vars.gOfficerNumber + "").lpad("0", lpadLength);
    }
    voltmx.print("### Utility_setTicketNumberToCaseData ticketNumber: " + JSON.stringify(caseData.caseinfo.ticketNumber));
}

function Utility_normalizeDateObject(input) {
    // Check if input is an array with 6 numerical elements
    voltmx.print("### Utility_normalizeDateObject input: " + JSON.stringify(input));
    if (Array.isArray(input) && input.length === 6 && input.every(num => typeof num === "number")) {
        voltmx.print("### Utility_normalizeDateObject input Already in the desired format");
        return input; // Already in the desired format
    }
    // Check if input is an object with the required properties
    // required output [day, month, year, hour, min, sec]
    if (typeof input === "object" && input !== null) {
        const {
            year,
            month,
            day,
            hour,
            min,
            sec
        } = input;
        if ([day, month, year, hour, min, sec].every(prop => typeof prop === "number")) {
            voltmx.print("### Utility_normalizeDateObject input changed to the desired format");
            return [day, month, year, hour, min, sec];
        }
    }
    // If input doesn't match any expected format, return null
    voltmx.print("### Utility_normalizeDateObject input doesn't match any expected format");
    return null;
}

function Utility_openOfflineJSONCaseData(openCaseName) {
    voltmx.print("### Utility_openOfflineJSONCaseData openCaseName: " + openCaseName);
    try {
        var d = new Date();
        var n = d.getTime();
        var filename = "";
        if (openCaseName !== undefined && openCaseName != null && openCaseName !== "") {
            filename = openCaseName;
        } else {
            filename = CaseData.caseinfo.id;
        }
        var path = voltmx.io.FileSystem.getDataDirectoryPath();
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
            if (appgrouppath != null) {
                voltmx.print("### grouppath: " + appgrouppath);
                path = appgrouppath;
            }
        }
        voltmx.print("### Utility_openOfflineJSONCaseData path: " + JSON.stringify(path));
        var folderForOfflineCases = "/offline/";
        var fileDirectoryOfflineCases = voltmx.io.FileSystem.getFile(path + folderForOfflineCases);
        var fileListOfflineCases = fileDirectoryOfflineCases.getFilesList();
        if (fileListOfflineCases === null) {
            fileListOfflineCases = {};
        }
        voltmx.print("### Utility_openOfflineJSONCaseData fileListOfflineCases1: " + fileListOfflineCases);
        voltmx.print("### Utility_openOfflineJSONCaseData fileListOfflineCases2: " + JSON.stringify(fileListOfflineCases));
        var getfile = "";
        var name = "";
        //check if case is in concepts
        for (var i = 0; i < fileListOfflineCases.length; i++) {
            voltmx.print("### Utility_openOfflineJSONCaseData offlineCase fullpath i: " + fileListOfflineCases.item(i).fullPath);
            voltmx.print("### Utility_openOfflineJSONCaseData offlineCase readable i: " + fileListOfflineCases.item(i).readable);
            voltmx.print("### Utility_openOfflineJSONCaseData offlineCase name i: " + fileListOfflineCases.item(i).name);
            name = fileListOfflineCases.item(i).name;
            if (name == filename) {
                voltmx.print("### Utility_openOfflineJSONCaseData case found in list offlineCase: " + fileListOfflineCases.item(i).name);
                getfile = voltmx.io.FileSystem.getFile(fileListOfflineCases.item(i).fullPath + "/case.json");
                break;
            }
        }
        //open the file
        if (getfile !== "" && getfile.exists()) {
            voltmx.print("### Utility_openOfflineJSONCaseData getfile exists");
            voltmx.print("### Utility_openOfflineJSONCaseData getfile done");
            voltmx.print("### Utility_openOfflineJSONCaseData IS READABLE >>>>>>>" + getfile.readable);
            var time = new Date(voltmx.os.toNumber(getfile.modificationTime) * 1000); //modification time is unix timestamp
            var formatted = time.getDate().toString().lpad("0", 2) + "-" + (time.getMonth() + 1).toString().lpad("0", 2) + "-" + time.getFullYear() + " " + time.getHours().toString().lpad("0", 2) + ":" + time.getMinutes().toString().lpad("0", 2);
            voltmx.print("### Utility_openOfflineJSONCaseData formatted date: " + formatted);
            var readJSONFromFile = getfile.read();
            voltmx.print("### Utility_openOfflineJSONCaseData readJSONFromFile: " + JSON.stringify(readJSONFromFile));
            var stringifiedData = JSON.stringify(readJSONFromFile);
            var parsedData = JSON.parse(stringifiedData);
            voltmx.print("### Utility_openOfflineJSONCaseData parsedData: " + JSON.stringify(parsedData));
            voltmx.print("### Utility_openOfflineJSONCaseData parsedData text: " + JSON.stringify(parsedData.text));
            var parsedCaseData = JSON.parse(parsedData.text);
            voltmx.print("### Utility_openOfflineJSONCaseData parsedData parsedCaseData: " + JSON.stringify(parsedCaseData));
            voltmx.print("### Utility_openOfflineJSONCaseData parsedData parsedCaseData processinfo: " + JSON.stringify(parsedCaseData.processinfo));
            if (parsedCaseData.person !== undefined) {
                voltmx.print("### Utility_openOfflineJSONCaseData parsedData parsedCaseData person: " + JSON.stringify(parsedCaseData.person));
            }
            return parsedCaseData;
        } else {
            voltmx.print("### Utility_openOfflineJSONCaseData getfile does not exist");
            return false;
        }
    } catch (error) {
        voltmx.print("### Utility_openOfflineJSONCaseData error: " + JSON.stringify(error));
        return false;
    }
}

function Utility_deleteOfflineJSONCaseData(deleteCaseName) {
    voltmx.print("### Utility_deleteOfflineJSONCaseData deleteCaseName: " + deleteCaseName);
    try {
        if (deleteCaseName != null && deleteCaseName != null && deleteCaseName !== "") {
            var path = voltmx.io.FileSystem.getDataDirectoryPath();
            if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
                var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
                if (appgrouppath != null) {
                    voltmx.print("### Utility_deleteOfflineJSONCaseData grouppath: " + appgrouppath);
                    path = appgrouppath;
                }
            }
            voltmx.print("### Utility_deleteOfflineJSONCaseData path: " + JSON.stringify(path));
            var folderForOfflineCases = "/offline/";
            var fileDirectoryOfflineCases = voltmx.io.FileSystem.getFile(path + folderForOfflineCases);
            var fileListOfflineCases = fileDirectoryOfflineCases.getFilesList();
            if (fileListOfflineCases === null) {
                fileListOfflineCases = {};
            }
            voltmx.print("### Utility_deleteOfflineJSONCaseData fileListOfflineCases1: " + fileListOfflineCases);
            voltmx.print("### Utility_deleteOfflineJSONCaseData fileListOfflineCases2: " + JSON.stringify(fileListOfflineCases));
            var getfileForDeletion = "";
            var name = "";
            //check if case is in offline
            for (var i = 0; i < fileListOfflineCases.length; i++) {
                voltmx.print("### Utility_deleteOfflineJSONCaseData offline fullpath i: " + fileListOfflineCases.item(i).fullPath);
                voltmx.print("### Utility_deleteOfflineJSONCaseData offline readable i: " + fileListOfflineCases.item(i).readable);
                voltmx.print("### Utility_deleteOfflineJSONCaseData offline name i: " + fileListOfflineCases.item(i).name);
                name = fileListOfflineCases.item(i).name;
                if (name == deleteCaseName) {
                    voltmx.print("### Utility_deleteOfflineJSONCaseData case found in list offline: " + fileListOfflineCases.item(i).name);
                    getfileForDeletion = voltmx.io.FileSystem.getFile(fileListOfflineCases.item(i).fullPath);
                    break;
                }
            }
            if (getfileForDeletion !== "" && getfileForDeletion.exists()) {
                voltmx.print("### Utility_deleteOfflineJSONCaseData file to delete exists in offline, now remove");
                getfileForDeletion.remove(true);
            }
            if (getfileForDeletion !== "" && !getfileForDeletion.exists()) {}
            voltmx.print("### Utility_deleteOfflineJSONCaseData done");
            //Utility_countFilelists();
        } else {
            voltmx.print("### Utility_deleteOfflineJSONCaseData no case to delete");
        }
    } catch (error) {
        voltmx.print("### Utility_deleteOfflineJSONCaseData error: " + JSON.stringify(error));
    }
}

function Utility_sendOfflineCases(callback) {
    voltmx.print("### Utility_sendOfflineCases");
    if (voltmx.net.isNetworkAvailable(constants.NETWORK_TYPE_ANY)) {
        //only try if network is available
        //first check for offline cases
        var path = voltmx.io.FileSystem.getDataDirectoryPath();
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
            if (appgrouppath != null) {
                voltmx.print("### Utility_sendOfflineCases grouppath: " + appgrouppath);
                path = appgrouppath;
            }
        }
        voltmx.print("### Utility_sendOfflineCases path: " + JSON.stringify(path));
        var folderForOfflineCases = "/offline/";
        var fileDirectoryOfflineCases = voltmx.io.FileSystem.getFile(path + folderForOfflineCases);
        var fileListOfflineCases = fileDirectoryOfflineCases.getFilesList();
        if (fileListOfflineCases === null) {
            fileListOfflineCases = {};
        }
        voltmx.print("### Utility_sendOfflineCases fileListOfflineCases1: " + fileListOfflineCases);
        voltmx.print("### Utility_sendOfflineCases fileListOfflineCases2: " + JSON.stringify(fileListOfflineCases));
        var getfile = "";
        var name = "";
        if (fileListOfflineCases.length > 0) {
            var offlineCasesToUpload = [];
            for (var i = 0; i < fileListOfflineCases.length; i++) {
                voltmx.print("### Utility_sendOfflineCases offlineCase name i: " + fileListOfflineCases.item(i).name);
                name = fileListOfflineCases.item(i).name;
                offlineCasesToUpload.push(name);
            }

            function tryAndUploadCases() {
                if (offlineCasesToUpload.length > 0) {
                    var caseToUpload = offlineCasesToUpload.pop();
                    voltmx.print("### Utility_sendOfflineCases tryAndUploadCases caseToUpload: " + caseToUpload);
                    var caseData = Utility_openOfflineJSONCaseData(caseToUpload);
                    if (caseData !== undefined && caseData != null && caseData !== false) {
                        //found the caseData
                        //delete the case from filesystem
                        Utility_deleteOfflineJSONCaseData(caseToUpload);
                        //send the case
                        var _continue = true;
                        if (caseData.caseinfo !== undefined && caseData.caseinfo != null && caseData.caseinfo.ticketNumber != null) {
                            if (voltmx.string.startsWith(caseData.caseinfo.ticketNumber, "unundefined") === true) {
                                voltmx.print("### Utility_sendOfflineCases tryAndUploadCases do not send case: " + caseData.caseinfo.ticketNumber);
                                _continue = false;
                            }
                        }
                        if (_continue === true) {
                            if (caseData.caseinfo !== undefined && caseData.caseinfo != null) {
                                voltmx.print("### Utility_sendOfflineCases tryAndUploadCases trying to send case");
                                if (caseToUpload.includes("Message")) {
                                    var messageType = caseToUpload.substring(caseToUpload.lastIndexOf("_") + 1, caseToUpload.lastIndexOf("#"));
                                    voltmx.print("### Utility_sendOfflineCases tryAndUploadCases trying to send case messageType: " + messageType);
                                    if (messageType == "Startproces") {
                                        Utility_saveUploadMessage(caseData, messageType);
                                    } else {
                                        Utility_saveUploadMessage(caseData, messageType);
                                    }
                                } else {
                                    Utility_saveUploadCaseData(caseData, tryAndUploadCases);
                                }
                            } else {
                                voltmx.print("### Utility_sendOfflineCases No caseData found try the next one");
                                tryAndUploadCases();
                            }
                        } else {
                            voltmx.print("### Utility_sendOfflineCases No caseData found try the next one");
                            tryAndUploadCases();
                        }
                    } else {
                        voltmx.print("### Utility_sendOfflineCases No caseData found try the next one");
                        tryAndUploadCases();
                    }
                } else {
                    voltmx.print("### Utility_sendOfflineCases No more offlineCases in array to upload");
                    if (callback !== undefined && callback != null) {
                        callback();
                    }
                }
            }
            tryAndUploadCases();
        } else {
            voltmx.print("### Utility_sendOfflineCases no more offline cases in filelist");
            //finish
            if (callback !== undefined && callback != null) {
                callback();
            }
        }
    } else {
        voltmx.print("### Utility_sendOfflineCases no network so do nothing");
        if (callback !== undefined && callback != null) {
            callback();
        }
    }
}

function Utility_showCaseFileListOfflineCases() {
    //show the files in offline folder
    voltmx.print("### Utility_showCaseFileListOfflineCases");
    var showFileList = {};
    try {
        var path = voltmx.io.FileSystem.getDataDirectoryPath();
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
            if (appgrouppath != null) {
                voltmx.print("### grouppath: " + appgrouppath);
                path = appgrouppath;
            }
        }
        voltmx.print("### Utility_showCaseFileListOfflineCases path: " + JSON.stringify(path));
        var folderForOfflineCases = "/offline/";
        var fileDirectoryOffline = voltmx.io.FileSystem.getFile(path + folderForOfflineCases);
        var fileListOffline = fileDirectoryOffline.getFilesList();
        if (fileListOffline === null) {
            fileListOffline = {};
        }
        voltmx.print("### Utility_showCaseFileListOfflineCases fileListConcepts: " + fileListOffline);
        voltmx.print("### Utility_showCaseFileListOfflineCases fileListConcepts: " + JSON.stringify(fileListOffline));
        showFileList = fileListOffline;
        return showFileList;
    } catch (error) {
        voltmx.print("### Utility_showCaseFileListOfflineCases error: " + JSON.stringify(error));
        return showFileList;
    }
}

function Utility_addANPRAttachmentInline() {
    voltmx.print("### Utility_addANPRAttachmentInline: " + JSON.stringify(Global.vars.addANPRPhotos));
    if (Global.vars.addANPRPhotos != null && Global.vars.addANPRPhotos.length > 0) {
        var lMediaName = "";
        var d = new Date();
        var base64FromImage = "";
        var w = Global.vars.addANPRPhotos.pop();
        voltmx.print("### Utility_addANPRAttachmentInline w: " + JSON.stringify(w));
        if (w.imgPhoto.base64 != null && w.imgPhoto.base64.length > 0) {
            var description = "ANPR photo: " + w.name;
            var name = w.name;
            lMediaName = "anprphoto_" + d.getTime(); //+ imageExtension;
            var documentType = w.type;
            voltmx.print("### Utility_addANPRAttachmentInline lMediaName: " + lMediaName);
            base64FromImage = w.imgPhoto.base64;
            //voltmx.print("### Utility_addANPRAttachmentInline photo size: " + base64FromImage.length);
            var fileExtension = ".jpg";
            var imageMimeType = "image/jpeg";
            var filename = "";
            if (lMediaName.includes(".jpg") === false && lMediaName.includes(".png") === false) {
                filename = lMediaName + fileExtension;
                voltmx.print("### Utility_addANPRAttachmentInline filename 1: " + filename);
            } else {
                filename = lMediaName;
                voltmx.print("### Utility_addANPRAttachmentInline filename 2: " + filename);
            }
            var photoinfo = {
                fileDate: Utility_getUTCJavascriptDate(null),
                //datetime stamp of the file
                fileName: filename,
                //name of media file
                description: description,
                //description of media
                documentType: documentType,
                //type of media document for example "photo" or "licenseplatePhoto"
                contentType: imageMimeType,
                //attachmentId: filename, deze weglaten anders wordt ie niet verwerkt naar Minio
                //Couch attachment id
                uploaded: true,
                photoStatus: "draft",
                base64: base64FromImage
            };
            voltmx.print("### Utility_addANPRAttachmentInline photoinfo: " + JSON.stringify(photoinfo));
            Utility_addPhotoToCase(photoinfo);
            Utility_addANPRAttachmentInline();
        } else {
            Utility_addANPRAttachmentInline();
        }
    } else {
        voltmx.print("### Utility_addANPRAttachmentInline finished ");
        voltmx.application.dismissLoadingScreen();
    }
}

function Utility_sepotCase(casedata, callback) {
    voltmx.print("### Utility_sepotCase");

    function Utility_sepotCase_getDefaultCancelOutcomeForTaskScreenSuccessCallback(result) {
        voltmx.print("### Utility_sepotCase_getDefaultCancelOutcomeForTaskScreenSuccessCallback result: " + JSON.stringify(result));
        if (result.length > 0) {
            for (var i in casedata.processinfo.tasks) {
                var v = casedata.processinfo.tasks[i];
                if (v.taskType == casedata.processinfo.activeTaskType && (v.taskCompletedOn === "" || v.taskCompletedOn === null)) {
                    v.taskOutcome = result[0].identification;
                    v.taskOutcomeId = result[0].id;
                    v.taskCompletedOn = Utility_getUTCJavascriptDate(null);
                    v.taskOutcomeDescription = result[0].description;
                    casedata.caseinfo.indComplete = true;
                    casedata.caseinfo.timeComplete = v.taskCompletedOn;
                    casedata.processinfo.lastTaskProcessed = v;
                    break;
                }
            }
            voltmx.print("#### Utility_sepotCase_getDefaultCancelOutcomeForTaskScreenSuccessCallback casedata: " + JSON.stringify(casedata));
            callback();
        } else {
            voltmx.print("#### Utility_sepotCase_getDefaultCancelOutcomeForTaskScreenSuccessCallback geen uitkomsten");
            voltmx.print("### dismiss 11");
            voltmx.application.dismissLoadingScreen();
            alert(voltmx.i18n.getLocalizedString("l_noOutcome"));
        }
    }

    function Utility_sepotCase_getDefaultCancelOutcomeForTaskScreenErrorCallback(error) {
        voltmx.print("### Utility_sepotCase_getDefaultCancelOutcomeForTaskScreenErrorCallback error: " + JSON.stringify(error));
        voltmx.print("### dismiss 10");
        voltmx.application.dismissLoadingScreen();
        alert(voltmx.i18n.getLocalizedString("l_removalFailed") + error);
    }

    function Utility_sepotCase_setCaseTypeAndTaskToCaseerrorcallback(error) {
        voltmx.print("### Utility_sepotCase_setCaseTypeAndTaskToCaseerrorcallback error: " + JSON.stringify(error));
        voltmx.print("### dismiss 10");
        voltmx.application.dismissLoadingScreen();
        alert(voltmx.i18n.getLocalizedString("l_removalFailed") + error);
    }

    function Utility_sepotCase_getDefaultCancelOutcomeForTaskScreen() {
        voltmx.print("### Utility_sepotCase_getDefaultCancelOutcomeForTaskScreen defaultTaskOutcomeUtility_sepotCaseCancel: " + Global.vars.defaultTaskOutcomeFrmTrackDownCancel);
        voltmx.print("### Utility_sepotCase_getDefaultCancelOutcomeForTaskScreen casedata.processinfo.activeTaskType: " + casedata.processinfo.activeTaskType);
        var lTaskTypeClause = Utility_addTimelineToWhereClauseObjectSync("select id from mle_v_task_type_msv where identification = '" + casedata.processinfo.activeTaskType + "'", casedata.time.dateComponents);
        var wcs = "select * from mle_v_outcome_type_m where identification = '" + Global.vars.defaultTaskOutcomeFrmTrackDownCancel + "' and tte_id in (" + lTaskTypeClause + ")";
        wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, casedata.time.dateComponents);
        wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
        voltmx.print("### Utility_sepotCase_getDefaultCancelOutcomeForTaskScreen wcs: " + wcs);
        //    com.redora.CaseManagementData.OutcomeType.find(wcs, Utility_sepotCase_getDefaultCancelOutcomeForTaskScreenSuccessCallback, Utility_sepotCase_getDefaultCancelOutcomeForTaskScreenErrorCallback);
        KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_sepotCase_getDefaultCancelOutcomeForTaskScreenSuccessCallback, Utility_sepotCase_getDefaultCancelOutcomeForTaskScreenErrorCallback);
    }
    voltmx.print("### Utility_sepotCase check appmode: " + Global.vars.appMode);
    //  if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer" || Global.vars.appMode === voltmx.i18n.getLocalizedString("l_trackDown")){
    if (casedata.caseinfo.caseType === undefined || casedata.caseinfo.caseType === null || casedata.caseinfo.caseType === "") {
        voltmx.print("### Utility_sepotCase NO casetype so try to set casetype");
        Utility_setCaseTypeAndTaskToCase(casedata, Utility_sepotCase_getDefaultCancelOutcomeForTaskScreen, Utility_sepotCase_setCaseTypeAndTaskToCaseerrorcallback);
    } else {
        voltmx.print("### Utility_sepotCase casetype so try to set default taskoutcome");
        Utility_sepotCase_getDefaultCancelOutcomeForTaskScreen();
    }
    //   } else {
    //     voltmx.application.dismissLoadingScreen();
    //     voltmx.print("### Utility_sepotCase " + voltmx.i18n.getLocalizedString("l_removalFailed"));
    //     //alert(voltmx.i18n.getLocalizedString("l_removalFailed"));
    //   }
}

function Utility_set_favorite(offencecode) {
    // {offenceCode: “R400A”, numberOfTimesChosen: 6, lastDateChosen: “15-06-2020"}
    voltmx.print("#### favorite offences before insert: " + JSON.stringify(Global.vars.favoriteOffences));
    // if empty inserts offenceCode otherwise remove last one if more then 24 occurrences
    var index = -1;
    if (Global.vars.favoriteOffences != null && Global.vars.favoriteOffences.length > 0) {
        index = Utility_getIndexIfObjWithAttr(Global.vars.favoriteOffences, "offenceCode", offencecode);
        voltmx.print("#### favorite offences index: " + index);
    } else {
        Global.vars.favoriteOffences = [];
    }
    var dateTime = voltmx.os.date("*t");
    var currentDateTime = dateTime.year + dateTime.month.toString().lpad("0", 2) + dateTime.day.toString().lpad("0", 2) + dateTime.hour.toString().lpad("0", 2) + dateTime.min.toString().lpad("0", 2) + dateTime.sec.toString().lpad("0", 2);
    var laddrecord = {
        offenceCode: offencecode,
        numberOfTimesChosen: 1,
        lastDateChosen: currentDateTime
    };
    if (index !== -1) {
        laddrecord.numberOfTimesChosen = Global.vars.favoriteOffences[index].numberOfTimesChosen + 1;
        Global.vars.favoriteOffences.splice(index, 1);
    }
    Global.vars.favoriteOffences.splice(0, 0, laddrecord);
    if (Global.vars.favoriteOffences.length > 24) {
        Global.vars.favoriteOffences.pop();
    }
    voltmx.store.setItem("favorites_" + Global.vars.gUsername, Global.vars.favoriteOffences);
    voltmx.store.setItem("previousOffenceSearchModus_" + Global.vars.gUsername, Global.vars.previousOffenceSearchModus);
    voltmx.print("#### favorite offences Global.vars.previousOffenceSearchModus after insert: " + Global.vars.previousOffenceSearchModus);
    voltmx.print("#### favorite offences after insert: " + JSON.stringify(Global.vars.favoriteOffences));
}

function Utility_getFavoriteOffences() {
    Global.vars.favoriteOffences = [];
    var favoriteOffences = voltmx.store.getItem("favorites_" + Global.vars.gUsername);
    voltmx.print("#### Utility_getFavoriteOffences favoriteOffences: " + favoriteOffences);
    if (favoriteOffences !== undefined && favoriteOffences != null && favoriteOffences !== "null") {
        Global.vars.favoriteOffences = JSON.parse(JSON.stringify(favoriteOffences));
    }
    voltmx.print("#### Utility_getFavoriteOffences Global.vars.favoriteOffences: " + JSON.stringify(Global.vars.favoriteOffences));
    Global.vars.previousOffenceSearchModus = voltmx.store.getItem("previousOffenceSearchModus_" + Global.vars.gUsername);
    voltmx.print("#### Utility_getFavoriteOffences previousOffenceSearchModus: " + Global.vars.previousOffenceSearchModus);
    if (Global.vars.previousOffenceSearchModus === undefined || Global.vars.previousOffenceSearchModus === null || Global.vars.previousOffenceSearchModus === "null") {
        Global.vars.previousOffenceSearchModus = Global.vars.gOffenceSearchModus;
    }
    voltmx.print("#### Utility_getFavoriteOffences Global.vars.previousOffenceSearchModus: " + Global.vars.previousOffenceSearchModus);
}

function Utility_set_favoritePolicy(policycode, policydescription) {
    // {policyCode: “B100”, policyDescription: "Fietswrak", numberOfTimesChosen: 6, lastDateChosen: “19-02-2021"}
    var _policydescription = policydescription === undefined ? null : policydescription;
    voltmx.print("#### favorite policies before insert: " + JSON.stringify(Global.vars.favoritePolicies));
    // if empty inserts offenceCode otherwise remove last one if more then 24 occurrences
    var index = -1;
    if (Global.vars.favoritePolicies != null && Global.vars.favoritePolicies.length > 0) {
        index = Utility_getIndexIfObjWithAttr(Global.vars.favoritePolicies, "policyCode", policycode);
        voltmx.print("#### favorite policies index: " + index);
    } else {
        Global.vars.favoritePolicies = [];
    }
    var dateTime = voltmx.os.date("*t");
    var currentDateTime = dateTime.year + dateTime.month.toString().lpad("0", 2) + dateTime.day.toString().lpad("0", 2) + dateTime.hour.toString().lpad("0", 2) + dateTime.min.toString().lpad("0", 2) + dateTime.sec.toString().lpad("0", 2);
    var laddrecord = {
        policyCode: policycode,
        policyDescription: _policydescription,
        numberOfTimesChosen: 1,
        lastDateChosen: currentDateTime
    };
    if (index !== -1) {
        laddrecord.numberOfTimesChosen = Global.vars.favoritePolicies[index].numberOfTimesChosen + 1;
        Global.vars.favoritePolicies.splice(index, 1);
    }
    Global.vars.favoritePolicies.splice(0, 0, laddrecord);
    if (Global.vars.favoritePolicies.length > 24) {
        Global.vars.favoritePolicies.pop();
    }
    voltmx.store.setItem("favoritePolicies_" + Global.vars.gUsername, Global.vars.favoritePolicies);
    voltmx.store.setItem("previousPoliciySearchModus_" + Global.vars.gUsername, Global.vars.previousPoliciySearchModus);
    voltmx.print("#### favorite policies Global.vars.previousPoliciySearchModus after insert: " + Global.vars.previousPoliciySearchModus);
    voltmx.print("#### favorite plolicies after insert: " + JSON.stringify(Global.vars.favoritePolicies));
}

function Utility_getFavoritePolicies() {
    Global.vars.favoritePolicies = [];
    var favoritePolicies = voltmx.store.getItem("favoritePolicies_" + Global.vars.gUsername);
    voltmx.print("#### Utility_getFavoritePolicies favoritePolicies: " + favoritePolicies);
    if (favoritePolicies !== undefined && favoritePolicies != null && favoritePolicies !== "null") {
        Global.vars.favoritePolicies = JSON.parse(JSON.stringify(favoritePolicies));
    }
    voltmx.print("#### Utility_getFavoritePolicies Global.vars.favoritePolicies: " + JSON.stringify(Global.vars.favoritePolicies));
    Global.vars.previousPoliciySearchModus = voltmx.store.getItem("previousPoliciySearchModus_" + Global.vars.gUsername);
    voltmx.print("#### Utility_getFavoritePolicies previousPoliciySearchModus: " + Global.vars.previousPoliciySearchModus);
    if (Global.vars.previousPoliciySearchModus === undefined || Global.vars.previousPoliciySearchModus === null || Global.vars.previousPoliciySearchModus === "null") {
        Global.vars.previousPoliciySearchModus = Global.vars.gPolicySearchModus;
    }
    voltmx.print("#### Utility_getFavoritePolicies Global.vars.previousOffenceSearchModus: " + Global.vars.previousOffenceSearchModus);
}

function Utility_noNetworkCall() {
    voltmx.print("### Utility_noNetworkCall");
    voltmx.print("### Utility_noNetworkCall Global.vars.noNetworkCallRaised: " + Global.vars.noNetworkCallRaised);
    if (Global.vars.noNetworkCallRaised === false) {
        voltmx.print("### Utility_noNetworkCall raise alert");
        Global.vars.noNetworkCallRaised = true;
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("i_fsc0007"), Utility_cancelNoNetworkCall, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
    }
}

function Utility_cancelNoNetworkCall() {
    voltmx.print("### Utility_noNetworkCall alert canceled");
    Global.vars.noNetworkCallRaised = false;
}

function Utility_cancelGeneralAlert() {
    voltmx.print("### Utility_cancelGeneralAlert alert canceled");
    Global.vars.generalAlertRaised = false;
}

function Utility_questionsMandatory(optionUsage) {
    voltmx.print("### Utility_questionsMandatory");
    var min = 0;
    if (optionUsage !== "") {
        voltmx.print("#### Utility_questionsMandatory Global.vars.questionTypesUsage: " + Global.vars.questionTypesUsage);
        var range = optionUsage.split(",");
        try {
            if (range[0] == "N") {
                min = 0;
            } else {
                min = voltmx.os.toNumber(range[0]);
                if (min === null) {
                    min = 0;
                }
            }
        } catch (e) {
            voltmx.print("#### Utility_questionsMandatory Global.vars.questionTypesUsage error min: " + range[0]);
            min = 0;
        }
    }
    voltmx.print("#### Utility_questionsMandatory Global.vars.questionTypesUsage min: " + min);
    if (min !== 0) {
        return true;
    } else {
        return false;
    }
}

function Utility_questionsRequired(optionid, callback) {
    voltmx.print("### Utility_questionsRequired");
    var wcs = "select * from mle_v_option_variable_msv where option_id = '" + optionid + "'";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    wcs = wcs + " order by ove_order asc";
    voltmx.print("#### Utility_questionsRequired wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, function(result) {
        voltmx.print("#### questionsRequired_succescallback result: " + JSON.stringify(result));
        for (var i = 0; i < result.length; i++) {
            if (result[i].ind_required === 1) {
                voltmx.print("#### questionsRequired_succescallback indRequired = 1");
                callback(true);
                return;
            }
        }
        callback(false);
    }, function(error) {
        voltmx.print("#### questionsRequired_errorcallback result" + JSON.stringify(error));
        callback(false);
    });
}

function Utility_getGUID() {
    voltmx.print("#### Utility_getGUID");
    let u = Date.now().toString(16) + Math.random().toString(16) + "0".repeat(16);
    voltmx.print("#### Utility_getGUID u: " + u);
    let guid = [u.substr(0, 8), u.substr(8, 4), "4000-8" + u.substr(13, 3), u.substr(16, 12)].join("-");
    voltmx.print("#### Utility_getGUID guid: " + guid);
    return guid;
}

function Utility_isUtcDate(input) {
    voltmx.print("### Utility_isUtcDate input: " + input);
    var pattern = /\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[1-2]\d|3[0-1])T(?:[0-1]\d|2[0-3]):[0-5]\d:[0-5]\d(?:\.\d+|)(?:Z|(?:\+|\-)(?:\d{2}):?(?:\d{2}))/;
    var result = pattern.test(input);
    voltmx.print("### Utility_isUtcDate result: " + result);
    return result;
}

function Utility_getLabelInfo(labelIdentification) {
    voltmx.print("#### Utility_getLabelInfo labelIdentification: " + labelIdentification);
    /*  	Global.vars.caseTypeLabelColor = [{"caseType":"EVE","labelColor":"yellow"}, //Rotterdam kleuren
                                      {"caseType":"FWR","labelColor":"red"},
                                      {"caseType":"WEF","labelColor":"blue"},
                                      {"caseType":"GVV","labelColor":"purple"},
                                      {"caseType":"WZH","labelColor":"orange"},
                                      {"caseType":"CSF","labelColor":"green"},
                                      {"caseType":"HGF","labelColor":"black"},
                                      {"caseType":"VGF","labelColor":"pink"},
                                      {"caseType":"WAA","labelColor":"lightblue"}
                                     ];

  	Global.vars.caseTypeLabelColor = [{"caseType":"FWR","labelColor":"yellow"}, //Zaanstad kleuren
                                      {"caseType":"WEF","labelColor":"blue"},
                                      {"caseType":"ONB","labelColor":"purple"},
                                      {"caseType":"HGF","labelColor":"green"},
                                      {"caseType":"WAA","labelColor":"pink"},
                                      {"caseType":"VGS","labelColor":"lightblue"},
                                      {"caseType":"GGF","labelColor":"red"},
                                      {"caseType":"VGA","labelColor":"orange"},
                                      {"caseType":"VGN","labelColor":"darkgrey"},
                                      {"caseType":"4UZ","labelColor":"white"}
                                      ];
     */
    var caseType = labelIdentification.substring(7, 10);
    var labelColor = "";
    for (var i in Global.vars.caseTypeLabelColor) {
        var v = Global.vars.caseTypeLabelColor[i];
        if (v.caseType === caseType) {
            labelColor = v.labelColor;
            break;
        }
    }
    var labelInfo = {
        labelDescription: "",
        labelImage: flcLabelColorGrey,
        pinImage: "marker.png"
    };
    if (labelColor == "yellow") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(labelColor),
            labelImage: flcLabelColorYellow,
            pinImage: "pinyellow.png"
        };
    } else if (labelColor == "red") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(caseType),
            labelImage: flcLabelColorRed,
            pinImage: "pinred.png"
        };
    } else if (labelColor == "blue") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(caseType),
            labelImage: flcLabelColorBlue,
            pinImage: "pinblue.png"
        };
    } else if (labelColor == "purple") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(caseType),
            labelImage: flcLabelColorPurple,
            pinImage: "pinpurple.png"
        };
    } else if (labelColor == "orange") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(caseType),
            labelImage: flcLabelColorOrange,
            pinImage: "pinorange.png"
        };
    } else if (labelColor == "green") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(caseType),
            labelImage: flcLabelColorGreen,
            pinImage: "pingreen.png"
        };
    } else if (labelColor == "black") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(caseType),
            labelImage: flcLabelColorBlack,
            pinImage: "pinblack.png"
        };
    } else if (labelColor == "pink") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(caseType),
            labelImage: flcLabelColorPink,
            pinImage: "pinpink.png"
        };
    } else if (labelColor == "lightblue") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(caseType),
            labelImage: flcLabelColorLightBlue,
            pinImage: "pinlightblue.png"
        };
    } else if (labelColor == "darkgrey") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(caseType),
            labelImage: flcLabelColorDarkGrey,
            pinImage: "pindarkgrey.png"
        };
    } else if (labelColor == "white") {
        labelInfo = {
            labelDescription: Utiltity_getCaseTypeDescription(caseType),
            labelImage: flcLabelColorWhite,
            pinImage: "pinwhite.png"
        };
    }
    voltmx.print("### Utility_getLabelInfo labelInfo: " + JSON.stringify(labelInfo));
    return labelInfo;
}

function Utiltity_getCaseTypeDescription(caseType) {
    voltmx.print("### Utiltity_getCaseTypeDescription: " + caseType);
    var description = "Geen omschrijving gevonden";
    var casetype = null;
    for (var i in Global.vars.caseTypes) {
        var v = Global.vars.caseTypes[i];
        if (voltmx.string.startsWith(v.identification, "FH_" + caseType)) {
            description = v.description;
            casetype = v;
        }
    }
    if (casetype != null) {
        Utility_setRegisterLabelCaseType(casetype);
    }
    return description;
}

function Utility_setRegisterLabelCaseType(casetype) {
    voltmx.print("### Utility_setRegisterLabelCaseType: " + JSON.stringify(casetype));
    if (casetype !== undefined && casetype != null) {
        CaseData.caseinfo.caseType = casetype.identification;
        CaseData.caseinfo.caseTypeId = casetype.id;
        CaseData.caseinfo.caseTypeCategoryId = casetype.ctc_id;
        CaseData.caseinfo.caseTypeDescription = casetype.description;
    }
}

function Utility_getCaseTypes(caseCategoryId) {
    voltmx.print("#### Utility_getCaseTypes caseCategoryId: " + caseCategoryId);
    //var lCaseTypeClause = "where ctcid = '" + caseCategoryId + "'";
    var lCaseTypeClause = "select * from mle_v_case_type_m";
    lCaseTypeClause = Utility_addTimelineToWhereClauseObjectSync(lCaseTypeClause, null, true);
    lCaseTypeClause = Utility_addLanguageToWhereClauseObjectSync(lCaseTypeClause);
    voltmx.print("### Utility_getCaseTypes ocwcs: " + lCaseTypeClause);
    //  com.redora.CaseManagementData.CaseType.find(lCaseTypeClause, Utility_getCaseTypeSuccessCallback, Utility_getCaseTypeErrorCallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lCaseTypeClause, Utility_getCaseTypeSuccessCallback, Utility_getCaseTypeErrorCallback);
}

function Utility_getCaseTypeErrorCallback(error) {
    voltmx.print("#### Utility_getCaseTypeErrorCallback: " + JSON.stringify(error));
}

function Utility_getCaseTypeSuccessCallback(result) {
    voltmx.print("#### Utility_getCaseTypeSuccessCallback: " + JSON.stringify(result));
    if (result.length > 0) {
        Global.vars.caseTypes = [];
        for (var i in result) {
            var v = result[i];
            v.category = "CaseType";
            v.name = v.description;
            v.imgRight = "arrowrightmini.png";
            v.template = flcSegItemHandle;
            Global.vars.caseTypes.push(v);
        }
        voltmx.print("#### Utility_getCaseTypeSuccessCallback Global.vars.caseTypes: " + JSON.stringify(Global.vars.caseTypes));
    }
}

function Utility_setCaseTypeAndTaskToRegisterLabelCase(casedata, callback, errorcallback) {
    voltmx.print("### Utility_setCaseTypeAndTaskToRegisterLabelCase");

    function Utility_setCaseTypeAndTaskToRegisterLabelCase_getTaskTypeSuccessCallback(result) {
        voltmx.print("#### Utility_setCaseTypeAndTaskToRegisterLabelCase_getTaskTypeSuccessCallback: " + JSON.stringify(result));
        voltmx.print("#### Utility_setCaseTypeAndTaskToRegisterLabelCase_getTaskTypeSuccessCallback casedata.processinfo before: " + JSON.stringify(casedata.processinfo));
        //now set taskdata to case
        if (result.length > 0) {
            casedata.processinfo.activeTaskType = result[0].identification;
            var task = CaseData_setNewTask();
            task.taskType = result[0].identification;
            task.taskTypeId = result[0].id;
            task.taskClaimedBy = Global.vars.gUsername;
            task.taskClaimedByName = casedata.caseinfo.officerName;
            for (var i in casedata.processinfo.tasks) {
                var v = casedata.processinfo.tasks[i];
                if (v.taskType == result[0].identification) {
                    voltmx.print("#### Utility_setCaseTypeAndTaskToRegisterLabelCase_getTaskTypeSuccessCallback 1");
                    v.taskTypeId = result[0].id;
                    v.taskClaimedBy = Global.vars.gUsername;
                    v.taskClaimedByName = casedata.caseinfo.officerName;
                } else if (v.taskType === null) {
                    voltmx.print("#### Utility_setCaseTypeAndTaskToRegisterLabelCase_getTaskTypeSuccessCallback 2");
                    v.taskType = result[0].identification;
                    v.taskTypeId = result[0].id;
                    v.taskClaimedBy = Global.vars.gUsername;
                    v.taskClaimedByName = casedata.caseinfo.officerName;
                } else {
                    voltmx.print("#### Utility_setCaseTypeAndTaskToRegisterLabelCase_getTaskTypeSuccessCallback 3");
                    casedata.processinfo.tasks.push(task);
                }
            }
            casedata.processinfo.lastTaskProcessed = task;
            callback();
            voltmx.print("#### Utility_setCaseTypeAndTaskToRegisterLabelCase_getTaskTypeSuccessCallback casedata.processinfo after: " + JSON.stringify(casedata.processinfo));
        } else {
            errorcallback();
        }
    }

    function Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategoryErrorCallback(error) {
        voltmx.print("### Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategoryErrorCallback: " + JSON.stringify(error));
        errorcallback();
    }

    function Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback(result) {
        voltmx.print("### Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback: " + JSON.stringify(result));
        if (result.length > 0) {
            casedata.caseinfo.caseTypeCategory = result[0].code;
            //now get and set the default TaskType
            Utility_setDefaultStatusFromInstanceParamFrmTrackDefaultCaseTypeTaskType();
            Utility_getTaskType(Global.vars.frmTrackDefaultCaseTypeTaskType.TaskType, Utility_setCaseTypeAndTaskToRegisterLabelCase_getTaskTypeSuccessCallback);
        } else {
            errorcallback();
        }
    }
    //first determin there is no CaseType
    voltmx.print("### Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback current casedata: " + JSON.stringify(casedata));
    if (casedata !== undefined && casedata.caseinfo !== undefined && casedata.caseinfo.caseType !== undefined && casedata.caseinfo.caseType != null && casedata.caseinfo.caseType !== "") {
        voltmx.print("### Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback current caseType: " + casedata.caseinfo.caseType);
        voltmx.print("### Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback current casedata Global.vars.gCaseVehicles: " + JSON.stringify(Global.vars.gCaseVehicles));
        voltmx.print("### Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback Global.vars.frmTrackDefaultCaseTypeTaskType: " + JSON.stringify(Global.vars.frmTrackDefaultCaseTypeTaskType));
        //now set the casetype
        if (casedata.caseinfo.caseTypeCategoryId !== undefined && casedata.caseinfo.caseTypeCategoryId != null && casedata.caseinfo.caseTypeCategoryId !== "") {
            var lCaseTypeCategoryClause = "select * from mle_v_case_type_category_m where id ='" + casedata.caseinfo.caseTypeCategoryId + "'";
            voltmx.print("#### Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback lCaseTypeCategoryClause: " + lCaseTypeCategoryClause);
            lCaseTypeCategoryClause = Utility_addTimelineToWhereClauseObjectSync(lCaseTypeCategoryClause, casedata.time.dateComponents);
            lCaseTypeCategoryClause = Utility_addLanguageToWhereClauseObjectSync(lCaseTypeCategoryClause);
            voltmx.print("### Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback ocwcs: " + lCaseTypeCategoryClause);
            //try and find the casetypeCategory
            //      com.redora.CaseManagementData.CaseTypeCategory.find(lCaseTypeCategoryClause, Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback, Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategoryErrorCallback);
            KNYMobileFabric.OfflineObjects.executeSelectQuery(lCaseTypeCategoryClause, Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback, Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategoryErrorCallback);
        } else {
            errorcallback();
        }
    } else {
        voltmx.print("### Utility_setCaseTypeAndTaskToCase case already has a casetype");
        callback();
    }
}

function Utility_timeBetweenDates(date_current, date_old) {
    voltmx.print("### Utility_timeBetweenDates date_current input: " + date_current);
    voltmx.print("### Utility_timeBetweenDates date_old input: " + date_old);
    var hoursordaysbetween = 0;
    // get total seconds between the times
    var delta = Math.abs(date_current - date_old) / 1000;
    // calculate (and subtract) whole days
    var days = Math.floor(delta / 86400);
    delta -= days * 86400;
    // calculate (and subtract) whole hours
    var hours = Math.floor(delta / 3600) % 24;
    delta -= hours * 3600;
    // calculate (and subtract) whole minutes
    var minutes = Math.floor(delta / 60) % 60;
    delta -= minutes * 60;
    // what's left is seconds
    var seconds = delta % 60;
    voltmx.print("### Utility_timeBetweenDates days: " + days);
    voltmx.print("### Utility_timeBetweenDates hours: " + hours);
    voltmx.print("### Utility_timeBetweenDates minutes: " + minutes);
    voltmx.print("### Utility_timeBetweenDates seconds: " + seconds);
    if (days > 0) {
        hoursordaysbetween = days + " " + voltmx.i18n.getLocalizedString("i_daysago");
    } else if (hours > 0) {
        hoursordaysbetween = hours + " " + voltmx.i18n.getLocalizedString("i_hoursago");
    } else {
        hoursordaysbetween = 0 + " " + voltmx.i18n.getLocalizedString("i_hoursago");
    }
    return hoursordaysbetween;
}

function Utility_saveUploadMessage(caseData, message, callback, formToSave, duplicatecallback) {
    if (caseData !== undefined && caseData != null) {
        var d = new Date();
        var n = d.getTime();
        var time = CaseData.time.utcDateTime;
        if (time !== undefined && time != null && time !== "") {
            n = time;
        }
        var caseName = "noCouchCase" + "Message_" + message + "#" + n;
        var name = caseData.caseinfo.id + "Message_" + message + "#";
        if (name !== undefined && name != null && typeof name === "string" && name !== "") {
            caseName = name;
        }
        if (formToSave !== undefined && formToSave != null && typeof formToSave === "string") {
            caseData.caseinfo.formSaved = formToSave;
        }
        voltmx.print("### Utility_saveUploadMessage caseData.caseinfo.formSaved: " + caseData.caseinfo.formSaved);
        voltmx.print("#### Utility_saveUploadMessage caseData.caseinfo: " + JSON.stringify(caseData.caseinfo));
        // remove country items if they do not apply
        //  Voorlopig alleen voor fietshandhaving
        if (Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_checklabel") || Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_registerlabel")) {
            if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType != vehicleIdentType.vehicle && (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType != vehicleIdentType.vessel || (Global.vars.getBoatInfoEnabled === false && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vessel))) {
                //geen kenteken
                CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense = null;
                CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode = null;
                CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseDesc = null;
            }
        }
        if (caseData.person !== undefined) {
            voltmx.print("### Utility_saveUploadMessage caseData.person: " + JSON.stringify(caseData.person));
        }
        caseData.caseinfo.appModus = Global.vars.appMode;
        caseData.globals = Utility_saveGlobalsForFiles();

        function registerSuccescallback(result) {
            //succes
            voltmx.print("### Utility_saveUploadMessage registerSuccescallback case uploaded: " + JSON.stringify(result));
            if (callback !== undefined && callback != null) {
                voltmx.print("### Utility_saveUploadMessage execute callback");
                callback(result);
            } else {
                voltmx.print("### Utility_saveUploadMessage NO callback specified");
            }
        }

        function registerErrorcallback(error) {
            voltmx.print("### Utility_saveUploadMessage registerErrorcallback case NOT uploaded: " + error);
            //case could not be uploaded for some reason so write to filesystem
            voltmx.print("### Utility_saveUploadMessage registerErrorcallback casename: " + caseName);
            var path = voltmx.io.FileSystem.getDataDirectoryPath();
            if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
                var appgrouppath = path; //voltmx.io.FileSystem.getAppGroupDirectoryPath("group.com.redora.redline");
                if (appgrouppath != null) {
                    voltmx.print("### Utility_saveUploadMessage registerErrorcallback grouppath: " + appgrouppath);
                    path = appgrouppath;
                }
            }
            voltmx.print("### Utility_saveUploadMessage registerErrorcallback path: " + JSON.stringify(path));
            //create directory outbox
            var myDirectoryOutbox = voltmx.io.FileSystem.getFile(path + "/offline/");
            var directorycreatedOffline = myDirectoryOutbox.createDirectory();
            voltmx.print("### Utility_saveUploadMessage registerErrorcallback directorycreatedOffline: " + JSON.stringify(directorycreatedOffline)); //returns false if already exists
            var folderForCase = "/offline/";
            //
            var myDirectoryCase = voltmx.io.FileSystem.getFile(path + folderForCase + caseName + "/");
            var directorycreatedCase = myDirectoryCase.createDirectory();
            voltmx.print("### Utility_saveUploadMessage registerErrorcallback directorycreatedCase: " + JSON.stringify(directorycreatedCase)); //returns false if already exists
            //create the file in directory jsonfiles
            var myfile = voltmx.io.FileSystem.getFile(path + folderForCase + caseName + "/" + "case.json");
            if (myfile.exists()) {
                voltmx.print("### Utility_saveUploadMessage registerErrorcallback myfile exists");
                myfile.remove();
            }
            var filecreated = myfile.createFile();
            voltmx.print("### Utility_saveUploadMessage registerErrorcallback File IS READABLE >>>>>>>" + myfile.readable);
            voltmx.print("### Utility_saveUploadMessage registerErrorcallback File IS WRITABLE >>>>>>>" + myfile.writable);
            var dataToWrite = JSON.stringify(caseData);
            var dataAdded = myfile.write(dataToWrite, false);
            voltmx.print("### Utility_saveUploadMessage registerErrorcallback File IS DATA ADDED >>>>>>>" + dataAdded);
            if (callback !== undefined && callback != null) {
                voltmx.print("### Utility_saveUploadMessage registerErrorcallback execute callback");
                callback();
            } else {
                voltmx.print("### Utility_saveUploadMessage registerErrorcallback NO callback specified");
            }
            //Global.vars.writtenCaseName = caseName;
            //Global.vars.claimedDocID = caseName;
        }

        function duplicateCaseCallback() {
            //succes
            voltmx.print("### Utility_saveUploadMessage duplicatecallback");
            if (duplicatecallback !== undefined && duplicatecallback != null) {
                voltmx.print("### Utility_saveUploadMessage execute duplicatecallback");
                duplicatecallback();
            } else {
                voltmx.print("### Utility_saveUploadMessage NO duplicatecallback specified");
            }
        }
        //Delete case if it exists offline
        Utility_deleteOfflineJSONCaseData(caseName);
        //Try to upload case
        if (message == "startProces") {
            voltmx.print("Utility_saveUploadMessage startProces sendMessageStartProces");
            Utility_sendMessageStartProces(caseData, registerSuccescallback, registerErrorcallback);
        } else {
            voltmx.print("Utility_saveUploadMessage sendMessage");
            Utility_sendMessage(caseData, message, registerSuccescallback, registerErrorcallback, duplicateCaseCallback);
        }
    }
}

function Utility_sendMessage(caseData, message, callback, errorcallback, duplicatecallback) {
    voltmx.print("### Utility_sendMessage");
    //set Ticket number
    Utility_setTicketNumberToRegisterCaseData(caseData);
    //set update time
    caseData.caseinfo.lastUpdateTime = Utility_getUTCJavascriptDate(null);
    //RegisterCaseService
    function SendMessageCallback(result) {
        voltmx.print("### Utility_sendMessage SendMessageCallback result: " + JSON.stringify(result));
        //    01-30 12:01:27.788: D/StandardLib(3619): ### RegisterCaseCallback result: {"response":[{"externalCaseId":null,"code":"RegisterCaseError","checkCaseOutcome":null,"caseId":null,"description":"Exception occurred during execution on the exchange: Exchange[c9548208-a7b1-4cb8-b735-db4156852f4c]","ok":false,"caseType":null}],"opstatus":0,"httpStatusCode":500,"httpresponse":{"headers":{"X-Android-Received-Millis":"1580382087755","Cache-Control":"no-store, no-cache, must-revalidate","Access-Control-Allow-Methods":"GET, HEAD, POST, TRACE, OPTIONS, PUT, DELETE, PATCH","X-Kony-Service-Message":"","X-Android-Selected-Protocol":"http/1.1","Server":"Kony","X-Android-Sent-Millis":"1580382087495","X-Kony-RequestId":"609754b5-0409-4d9e-8689-339e0ae2db1b","Connection":"Keep-Alive","Date":"Thu, 30 Jan 2020 11:01:27 GMT","Access-Control-Allow-Origin":"*","X-Kony-Service-Opstatus":"0","Content-Type":"text/plain;charset=UTF-8","X-Android-Response-Source":"NETWORK 200","Keep-Alive":"timeout=5, max=100","Pragma":"no-cache","Content-Length":"280"},"url":"https://dev8.redora.com/services/CaseServiceActions/RegisterCase","responsecode":200}}
        if (result.opstatus === 0 && result.httpStatusCode == 200) {
            if (result.response[0].ok === false) {
                voltmx.print("### Utility_sendMessage SendMessageCallback Error: RegisterCaseError");
                errorcallback();
            } else {
                callback(result);
            }
        } else {
            //alert(voltmx.i18n.getLocalizedString("e_ser0001"));
            voltmx.application.dismissLoadingScreen();
            if (result.httpStatusCode == 409) {
                //zaak bestaat al in all cases (en kan daardoor niet in Active aangemaakt worden) of zaak heeft een ongeldige status
                if (result.response[0].code === "DuplicateCaseException") {
                    voltmx.print("### SendMessageCallback Error DuplicateCaseException: " + result.response[0].description);
                    if (duplicatecallback !== undefined && duplicatecallback != null) {
                        voltmx.print("### SendMessageCallback Error DuplicateCaseException execute callback");
                        duplicatecallback();
                    }
                } else if (result.response[0].code === "InvalidStateException") {
                    voltmx.print("### SendMessageCallback Error InvalidStateException: " + result.response[0].description);
                } else {
                    voltmx.print("### SendMessageCallback some other Error: " + result.response[0].description);
                }
            } else {
                errorcallback();
            }
        }
    }

    function SendMessageErrorCallback(error) {
        voltmx.print("### SendMessageErrorCallback error: " + JSON.stringify(error));
        if (error !== undefined && error.httpStatusCode == 409) {
            //zaak bestaat al in all cases (en kan daardoor niet in Active aangemaakt worden) of zaak heeft een ongeldige status
            if (error.response[0].code === "DuplicateCaseException") {
                voltmx.print("### SendMessageCallback Error DuplicateCaseException: " + error.response[0].description);
                if (duplicatecallback !== undefined && duplicatecallback != null) {
                    voltmx.print("### SendMessageErrorCallback Error DuplicateCaseException execute callback");
                    duplicatecallback();
                }
            } else if (error.response[0].code === "InvalidStateException") {
                voltmx.print("### SendMessageErrorCallback Error InvalidStateException: " + error.response[0].description);
            } else {
                voltmx.print("### SendMessageErrorCallback some other Error: " + error.response[0].description);
            }
        } else {
            errorcallback();
        }
    }
    service_SendMessage(caseData, message, SendMessageCallback, SendMessageErrorCallback);
}

function Utility_sendMessageStartProces(caseData, callback, errorcallback, duplicatecallback) {
    voltmx.print("### Utility_sendMessageStartProces");
    //set Ticket number
    Utility_setTicketNumberToRegisterCaseData(caseData);
    //set update time
    caseData.caseinfo.lastUpdateTime = Utility_getUTCJavascriptDate(null);
    //RegisterCaseService
    function SendMessageCallback(result) {
        voltmx.print("### Utility_sendMessageStartProces SendMessageCallback result: " + JSON.stringify(result));
        if (result.opstatus === 0 && result.httpStatusCode == 200) {
            if (result.response[0].ok === false) {
                voltmx.print("### Utility_sendMessageStartProces SendMessageCallback Error: RegisterCaseError");
                errorcallback();
            } else {
                callback(result);
            }
        } else {
            //alert(voltmx.i18n.getLocalizedString("e_ser0001"));
            voltmx.application.dismissLoadingScreen();
            if (result.httpStatusCode == 409) {
                //zaak bestaat al in all cases (en kan daardoor niet in Active aangemaakt worden) of zaak heeft een ongeldige status
                if (result.response[0].code === "DuplicateCaseException") {
                    voltmx.print("### SendMessageCallback Error DuplicateCaseException: " + result.response[0].description);
                    if (duplicatecallback !== undefined && duplicatecallback != null) {
                        voltmx.print("### SendMessageCallback Error DuplicateCaseException execute callback");
                        duplicatecallback();
                    } else {
                        errorcallback();
                    }
                } else if (result.response[0].code === "InvalidStateException") {
                    voltmx.print("### SendMessageCallback Error InvalidStateException: " + result.response[0].description);
                } else {
                    voltmx.print("### SendMessageCallback some other Error: " + result.response[0].description);
                }
            } else {
                errorcallback();
            }
        }
    }

    function SendMessageErrorCallback(error) {
        voltmx.print("### SendMessageErrorCallback error: " + JSON.stringify(error));
        if (error !== undefined && error.httpStatusCode == 409) {
            //zaak bestaat al in all cases (en kan daardoor niet in Active aangemaakt worden) of zaak heeft een ongeldige status
            if (error.response[0].code === "DuplicateCaseException") {
                voltmx.print("### SendMessageCallback Error DuplicateCaseException: " + error.response[0].description);
                if (duplicatecallback !== undefined && duplicatecallback != null) {
                    voltmx.print("### SendMessageErrorCallback Error DuplicateCaseException execute callback");
                    duplicatecallback();
                } else {
                    errorcallback();
                }
            } else if (error.response[0].code === "InvalidStateException") {
                voltmx.print("### SendMessageErrorCallback Error InvalidStateException: " + error.response[0].description);
            } else {
                voltmx.print("### SendMessageErrorCallback some other Error: " + error.response[0].description);
            }
        } else {
            errorcallback();
        }
    }
    //  Voorlopig alleen voor fietshandhaving
    if (Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_checklabel") || Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_registerlabel")) {
        if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType != vehicleIdentType.vehicle && (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType != vehicleIdentType.vessel || (Global.vars.getBoatInfoEnabled === false && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vessel))) {
            //geen kenteken
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense = null;
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode = null;
            CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseDesc = null;
        }
    }
    service_SendMessageStartProces(caseData, SendMessageCallback, SendMessageErrorCallback);
}

function Utility_setCaseTypeToRegisterLabelCase(casedata, callback, errorcallback) {
    voltmx.print("### Utility_setCaseTypeToRegisterLabelCase");

    function Utility_setCaseTypeToRegisterLabelCase_getCaseTypeCategorySuccessCallback(result) {
        voltmx.print("### Utility_setCaseTypeAndTaskToRegisterLabelCase_getCaseTypeCategorySuccessCallback: " + JSON.stringify(result));
        if (result.length > 0) {
            casedata.caseinfo.caseTypeCategory = result[0].code;
            //now get and set the default TaskType
            Utility_setDefaultStatusFromInstanceParamFrmTrackDefaultCaseTypeTaskType();
            callback();
        } else {
            errorcallback();
        }
    }

    function Utility_setCaseTypeToRegisterLabelCase_getCaseTypeCategoryErrorCallback(error) {
        voltmx.print("### Utility_setCaseTypeToRegisterLabelCase_getCaseTypeCategoryErrorCallback: " + JSON.stringify(error));
        errorcallback();
    }
    //first determin there is no CaseType
    voltmx.print("### Utility_setCaseTypeToRegisterLabelCase current casedata: " + JSON.stringify(casedata));
    if (casedata !== undefined && casedata.caseinfo !== undefined && casedata.caseinfo.caseType !== undefined && casedata.caseinfo.caseType != null && casedata.caseinfo.caseType !== "") {
        voltmx.print("### Utility_setCaseTypeToRegisterLabelCase current caseType: " + casedata.caseinfo.caseType);
        voltmx.print("### Utility_setCaseTypeToRegisterLabelCase current casedata Global.vars.gCaseVehicles: " + JSON.stringify(Global.vars.gCaseVehicles));
        voltmx.print("### Utility_setCaseTypeToRegisterLabelCase Global.vars.frmTrackDefaultCaseTypeTaskType: " + JSON.stringify(Global.vars.frmTrackDefaultCaseTypeTaskType));
        //now set the casetype
        if (casedata.caseinfo.caseTypeCategoryId !== undefined && casedata.caseinfo.caseTypeCategoryId != null && casedata.caseinfo.caseTypeCategoryId !== "") {
            var lCaseTypeCategoryClause = "select * from mle_v_case_type_category_m where id ='" + casedata.caseinfo.caseTypeCategoryId + "'";
            voltmx.print("#### Utility_setCaseTypeToRegisterLabelCase lCaseTypeCategoryClause: " + lCaseTypeCategoryClause);
            lCaseTypeCategoryClause = Utility_addTimelineToWhereClauseObjectSync(lCaseTypeCategoryClause, casedata.time.dateComponents);
            lCaseTypeCategoryClause = Utility_addLanguageToWhereClauseObjectSync(lCaseTypeCategoryClause);
            voltmx.print("### Utility_setCaseTypeToRegisterLabelCase ocwcs: " + lCaseTypeCategoryClause);
            //try and find the casetypeCategory
            //com.redora.CaseManagementData.CaseTypeCategory.find(lCaseTypeCategoryClause, Utility_setCaseTypeToRegisterLabelCase_getCaseTypeCategorySuccessCallback, Utility_setCaseTypeToRegisterLabelCase_getCaseTypeCategoryErrorCallback);
            KNYMobileFabric.OfflineObjects.executeSelectQuery(lCaseTypeCategoryClause, Utility_setCaseTypeToRegisterLabelCase_getCaseTypeCategorySuccessCallback, Utility_setCaseTypeToRegisterLabelCase_getCaseTypeCategoryErrorCallback);
        } else {
            errorcallback();
        }
    } else {
        voltmx.print("### Utility_setCaseTypeAndTaskToCase case already has a casetype");
        callback();
    }
}

function Utility_Setlocation(street, housenumber, city) {
    var locationText = "";
    if (street !== undefined && street != null && street !== "") {
        locationText = street;
    }
    if (housenumber !== undefined && housenumber != null && housenumber !== "" && locationText !== "") {
        locationText = locationText + " " + housenumber;
    }
    if (city !== undefined && city != null && city !== "" && locationText !== "") {
        locationText = locationText + ", " + city;
    } else {
        locationText = city;
    }
    return locationText;
}

function Utility_getOperationalAreaIdForTeamCode(teamCode, callback, errorcallback) {
    voltmx.print("### Utility_getOperationalAreaIdForTeamCode teamCode: " + teamCode);
    var options = {};
    var lTeamClause = "Select * from mle_v_operational_area_team_m where team_code ='" + teamCode + "'";
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lTeamClause, callback, errorcallback);
}

function Utility_setCaseTypeAndCategory(caseType, callback, errorcallback) {
    voltmx.print("#### Utility_setCaseTypeAndCategory caseType: " + caseType);
    var lCaseTypeClause = "select * from mle_v_case_type_m where identification = '" + caseType + "'";
    lCaseTypeClause = Utility_addTimelineToWhereClauseObjectSync(lCaseTypeClause);
    lCaseTypeClause = Utility_addLanguageToWhereClauseObjectSync(lCaseTypeClause);

    function getCaseTypeSuccessCallback(result) {
        voltmx.print("### getCaseTypeSuccessCallback result: " + JSON.stringify(result));
        if (result.length == 1) {
            CaseData.caseinfo.caseType = result[0].identification;
            CaseData.caseinfo.caseTypeId = result[0].id;
            CaseData.caseinfo.caseTypeDescription = result[0].description;
            CaseData.caseinfo.caseTypeCategoryId = result[0].ctc_id;
            voltmx.print("### getCaseTypeSuccessCallback caseType set, now find Category");
            var lCaseTypeCategoryClause = "select * from mle_v_case_type_category_m where id ='" + result[0].ctc_id + "'";
            voltmx.print("#### getCaseTypeSuccessCallback lCaseTypeCategoryClause: " + lCaseTypeCategoryClause);
            lCaseTypeCategoryClause = Utility_addTimelineToWhereClauseObjectSync(lCaseTypeCategoryClause, CaseData.time.dateComponents);
            lCaseTypeCategoryClause = Utility_addLanguageToWhereClauseObjectSync(lCaseTypeCategoryClause);
            voltmx.print("### getCaseTypeSuccessCallback ocwcs: " + lCaseTypeCategoryClause);

            function getCaseTypeCategorySuccessCallback(result) {
                voltmx.print("#### getCaseTypeCategorySuccessCallback: " + JSON.stringify(result));
                if (result.length > 0) {
                    CaseData.caseinfo.caseTypeCategory = result[0].code;
                    CaseData.caseinfo.caseTypeCategoryDescription = result[0].description;
                    voltmx.print("#### getCaseTypeCategorySuccessCallback category set");
                    if (callback !== undefined && callback != null) {
                        voltmx.print("### getCaseTypeCategorySuccessCallback execute callback");
                        callback();
                    }
                }
            }

            function getCaseTypeCategoryErrorCallback(error) {
                voltmx.print("#### getCaseTypeCategoryErrorCallback: " + JSON.stringify(error));
                if (errorcallback !== undefined && errorcallback != null) {
                    voltmx.print("### getCaseTypeCategoryErrorCallback execute errorcallback");
                    errorcallback();
                }
            }
            KNYMobileFabric.OfflineObjects.executeSelectQuery(lCaseTypeCategoryClause, getCaseTypeCategorySuccessCallback, getCaseTypeCategoryErrorCallback);
        }
    }

    function getCaseTypeErrorCallback(error) {
        voltmx.print("#### getCaseTypeErrorCallback: " + JSON.stringify(error));
        if (errorcallback !== undefined && errorcallback != null) {
            voltmx.print("### getCaseTypeErrorCallback execute errorcallback");
            errorcallback();
        }
    }
    voltmx.print("### Utility_setCaseTypeAndCategory ocwcs: " + lCaseTypeClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lCaseTypeClause, getCaseTypeSuccessCallback, getCaseTypeErrorCallback);
}

function Utility_usageMandatory(objectUsage) {
    voltmx.print("### Utility_usageMandatory");
    var min = 0;
    if (objectUsage !== "") {
        voltmx.print("#### Utility_usageMandatory objectUsage: " + objectUsage);
        var range = objectUsage.split(",");
        try {
            if (range[0] == "N") {
                min = 0;
            } else {
                min = voltmx.os.toNumber(range[0]);
                if (min === null) {
                    min = 0;
                }
            }
        } catch (e) {
            voltmx.print("#### Utility_questionsMandatory Global.vars.questionTypesUsage error min: " + range[0]);
            min = 0;
        }
    }
    voltmx.print("#### Utility_questionsMandatory Global.vars.questionTypesUsage min: " + min);
    if (min !== 0) {
        return true;
    } else {
        return false;
    }
}

function Utility_usagePossible(objectUsage) {
    voltmx.print("### Utility_usagePossible");
    var pos = 0;
    if (objectUsage !== "") {
        voltmx.print("#### Utility_usagePossible objectUsage: " + objectUsage);
        var range = objectUsage.split(",");
        try {
            if (range[1] == "N") {
                pos = 1;
            } else {
                pos = voltmx.os.toNumber(range[1]);
                if (pos === null) {
                    pos = 0;
                }
            }
        } catch (e) {
            voltmx.print("#### Utility_usagePossible error pos: " + range[1]);
            pos = 1;
        }
    }
    voltmx.print("#### Utility_usagePossible pos: " + pos);
    if (pos !== 0) {
        return true;
    } else {
        return false;
    }
}

function Utility_checkAppModus() {
    //callback for backgroundmodus
    function functionCallbackOninactive() {
        voltmx.print("### functionCallbackOninactive");
        //alert("====appInActive callback executed====");
        Global.vars.appState = "inactive";
        try {
            voltmx.application.getCurrentForm().add(frmOverlayImage.flcBackImage);
            voltmx.print("### flexContainer1 added to form");
        } catch (e) {}
    }

    function functionCallbackOnactive() {
        voltmx.print("### functionCallbackOnactive");
        //alert("====appActive callback executed====");
        Global.vars.appState = "active";
        try {
            voltmx.application.getCurrentForm().remove(frmOverlayImage.flcBackImage);
            voltmx.print("### flexContainer1 removed from form");
        } catch (e) {}
    }

    function functionCallbackOnBackground() {
        voltmx.print("### functionCallbackOnBackground");
        Global.vars.appState = "background";
    }

    function functionCallbackOnForeground() {
        voltmx.print("### functionCallbackOnForeground");
        Global.vars.appState = "foreground";
    }

    function functionAddApplicationCallbacks() {
        var callbacksMapObject = {
            oninactive: {
                functionID: functionCallbackOninactive
            },
            onactive: {
                functionID: functionCallbackOnactive
            },
            onbackground: {
                functionID: functionCallbackOnBackground
            },
            onforeground: {
                functionID: functionCallbackOnForeground
            }
        };
        voltmx.application.addApplicationCallbacks(callbacksMapObject);
    }
    voltmx.runOnMainThread(functionAddApplicationCallbacks, []);
}

function Utility_authorizedTicketType(tttid) {
    var functions = "";
    var maxIndex = Global.vars.officerFunctions.length - 1;
    voltmx.print("### Utility_authorizedTicketType maxIndex: " + maxIndex);
    for (var b in Global.vars.officerFunctions) {
        var w = Global.vars.officerFunctions[b];
        if (b != maxIndex) {
            functions = functions + "'" + w + "'" + ",";
        } else {
            functions = functions + "'" + w + "'";
        }
    }
    //check employeefunctions
    voltmx.print("### Utility_authorizedTicketType functions: " + functions);
    var wcs = "select distinct function_code from mle_v_ttt_authorization_m where ttt_id = " + tttid + " and function_code in (" + functions + ")";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
    voltmx.print("### Utility_authorizedTicketType wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_authorizedTicketTypecalllback, Utility_authorizedTicketTypeerrorcalllback);

    function Utility_authorizedTicketTypecalllback(result) {
        voltmx.print("### Utility_authorizedTicketTypecalllback result: " + JSON.stringify(result));
        if (result.length > 0) {
            Global.vars.authorizedTicketType = true;
        } else {
            Global.vars.authorizedTicketType = false;
        }
        voltmx.print("### Utility_authorizedTicketTypecalllback Global.vars.authorizedTicketType: " + Global.vars.authorizedTicketType);
    }

    function Utility_authorizedTicketTypeerrorcalllback(error) {
        voltmx.print("### Utility_authorizedTicketTypeerrorcalllback error: " + JSON.stringify(error));
        Global.vars.authorizedTicketType = false;
    }
}

function Utility_getAuthorizedApplicationItems() {
    Global.vars.authorizedFunctions = [];
    var wcs = "select distinct function_code from mle_v_application_item_authorization where application_name = 'Redline' and ite_description = 'MenuItemApp'";
    voltmx.print("### Utility_getAuthorizedApplicationItems wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_getAuthorizedApplicationItemscallback, Utility_getAuthorizedApplicationItemserrorcallback);

    function Utility_getAuthorizedApplicationItemscallback(result) {
        voltmx.print("### Utility_getAuthorizedApplicationItemscalllback result: " + JSON.stringify(result));
        for (var b in result) {
            var w = result[b];
            Global.vars.authorizedFunctions.push(w.function_code);
        }
        voltmx.print("### Utility_getAuthorizedApplicationItemscallback Global.vars.authorizedFunctions: " + Global.vars.authorizedFunctions);
    }

    function Utility_getAuthorizedApplicationItemserrorcallback(error) {
        voltmx.print("### Utility_getAuthorizedApplicationItemserrorcallback error: " + JSON.stringify(error));
        Global.vars.authorizedFunctions = ["NHA", "BOA"];
        voltmx.print("### Utility_getAuthorizedApplicationItemserrorcallback Global.vars.authorizedFunctions: " + Global.vars.authorizedFunctions);
    }
}

function Utility_regexpRestrictedInputPersonName(text) {
    var _text = text === undefined ? "" : text;
    voltmx.print("### Utility_regexpRestrictedInputPersonName start text: " + _text);
    if (_text.length > 0) {
        _text = _text.replace(/[^a-zA-ZàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð '-]/gi, "");
    }
    voltmx.print("### Utility_regexpRestrictedInputPersonName return _text: " + _text);
    return _text;
}

function Utility_regexpRestrictedInputAddressLine1(text) {
    var _text = text === undefined ? "" : text;
    voltmx.print("### Utility_regexpRestrictedInputAddressLine1 start text: " + _text);
    if (_text.length > 0) {
        _text = _text.replace(/[^a-zA-Z0-9àáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð '-.,]/gi, "");
    }
    voltmx.print("### Utility_regexpRestrictedInputAddressLine1 return _text: " + _text);
    return _text;
}

function Utility_regexpRestrictedInput(text) {
    var _text = text === undefined ? "" : text;
    var _changed = false;
    voltmx.print("### Utility_regexpRestrictedInput start text: " + _text);
    if (_text.length > 0) {
        _text = _text.replace(/[^\d\s\.,:;?!&@-A-Za-z\u00C0-\u00ff0-9\u0600-\u06FF\u08A0-\u08FF\u4E00-\u9FCC\u3400-\u4DB5\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29\ud840-\ud868\udc00-\udfff\ud869\udc00-\uded6\udf00-\udfff\ud86a-\ud86c\udc00-\udfff\ud86d\udc00-\udf34\udf40-\udfff\ud86e\udc00-\udc1d\u0401\u0451\u0410-\u044f\u0370-\u03FF\"]/gi, "");
        if (text !== _text) {
            _changed = true;
        }
        _text = _text.replace(/[(\r\n|\r|\n)]/gi, "\\n");
    }
    voltmx.print("### Utility_regexpRestrictedInput return _text: " + _text);
    return {
        text: _text,
        changed: _changed
    };
}

function Utility_restrictedInput(eventObject) {
    voltmx.print("### Utility_restrictedInput eventObject: " + eventObject);
    voltmx.print("### Utility_restrictedInput eventObject.text: " + JSON.stringify(eventObject.text));
    var _text = eventObject.text == null ? "" : eventObject.text;
    voltmx.print("### Utility_restrictedInput _text: " + _text);
    var output = {};
    output = Utility_regexpRestrictedInput(_text);
    if (output.changed === true) {
        voltmx.print("### Utility_restrictedInput regExp changed input");
        var _formItem = voltmx.application.getCurrentForm().id + "." + eventObject.id + ".text";
        voltmx.print("### Utility_restrictedInput eventObject.parent: " + JSON.stringify(eventObject.parent.id));
        if (eventObject.parent.id === "textarea") {
            _formItem = voltmx.application.getCurrentForm().id + ".settext.textarea." + eventObject.id + ".text";
        } else if (eventObject.parent.id === "flcEditLicensePlateText") {
            _formItem = voltmx.application.getCurrentForm().id + ".editplate." + eventObject.id + ".text";
        } else if (eventObject.parent.id === "streetmanual") {
            _formItem = voltmx.application.getCurrentForm().id + ".streetmanual." + eventObject.id + ".text";
        }
        voltmx.print("### Utility_restrictedInput formItem: " + _formItem);
        eval(_formItem + " = '" + output.text + "';");
    }
}

function Utility_restrictedInputSettext2(eventObject) {
    voltmx.print("### Utility_restrictedInputSettext2 eventObject: " + eventObject);
    voltmx.print("### Utility_restrictedInputSettext2 eventObject.text: " + JSON.stringify(eventObject.text));
    var _text = eventObject.text === undefined ? "" : eventObject.text;
    var output = {};
    output = Utility_regexpRestrictedInput(_text);
    if (output.changed === true) {
        voltmx.print("### Utility_restrictedInputSettext2 _text: " + _text);
        _text = Utility_regexpRestrictedInput(_text);
        var _formItem = voltmx.application.getCurrentForm().id + "." + eventObject.id + ".text";
        voltmx.print("### Utility_restrictedInputSettext2 eventObject.parent: " + JSON.stringify(eventObject.parent.id));
        if (eventObject.parent.id === "textarea") {
            _formItem = voltmx.application.getCurrentForm().id + ".settext2.textarea." + eventObject.id + ".text";
        }
        voltmx.print("### Utility_restrictedInputSettext2 formItem: " + _formItem);
        eval(_formItem + " = '" + output.text + "';");
    }
}

function Utility_isThisNumeric(string) {
    return /^[0-9]*$/.test(string);
}

function Utility_restrictedInputSettext3(eventObject) {
    voltmx.print("### Utility_restrictedInputSettext3 eventObject: " + eventObject);
    voltmx.print("### Utility_restrictedInputSettext3 eventObject.text: " + JSON.stringify(eventObject.text));
    var _text = eventObject.text === undefined ? "" : eventObject.text;
    voltmx.print("### Utility_restrictedInputSettext3 _text: " + _text);
    _text = Utility_regexpRestrictedInput(_text);
    var output = {};
    output = Utility_regexpRestrictedInput(_text);
    if (output.changed === true) {
        var _formItem = voltmx.application.getCurrentForm().id + "." + eventObject.id + ".text";
        if (eventObject.parent.id === "textarea") {
            _formItem = voltmx.application.getCurrentForm().id + ".settext3.textarea." + eventObject.id + ".text";
        }
        voltmx.print("### Utility_restrictedInputSettext3 formItem: " + _formItem);
        eval(_formItem + " = '" + output.text + "';");
    }
}

function Utility_checkCoCNumber(cocNumber) {
    var regex = /^[0-9]{8}([0-9]{3}H?)?$/;
    var verify = regex.test(cocNumber);
    if (verify === false) {
        alert("Dit is geen geldig KVK-nummer");
    }
    return verify;
}

function Utility_checkVariableisEmpty(value) {
    //returns true when empty
    return value == null || value.length === 0;
}

function Utility_getAreasForMap() {
    //check if areas not empty and load them onto the map
    //clear selectedAreas
    voltmx.print("### Utility_getAreasForMap clear selectedAreas");
    Global.vars.selectedAreas = null;
    //get areas
    var wcs = "select * from mle_v_area_msv";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents, true);
    voltmx.print("### Utility_getAreasForMap wcs: " + wcs);
    Global.vars.areasLoaded = false;
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, Utility_objectSync_onGetAllSuccess, Utility_objectSync_onGetAllFail);
}

function Utility_objectSync_onGetAllSuccess(records) {
    voltmx.print("### Utility_objectSync_onGetAllSuccess records: " + JSON.stringify(records));
    //Update synced data to the segment.
    Utility_objectSync_populateGlobal(records);
}

function Utility_objectSync_onGetAllFail(error) {
    voltmx.print("Utility_objectSync_onGetAllSuccess unable to objectSync_onGetAllFail retrieve records from db" + error.code);
    Global.vars.areasLoaded = false;
}

function Utility_objectSync_populateGlobal(records) {
    var objRecords = records;
    Global.vars.parkingZonesMap = [];
    var noOfRecords = objRecords.length;
    for (var i = 0; i < noOfRecords; i++) {
        var objRecord = objRecords[i];
        voltmx.print("### Utility_objectSync_populateGlobal objRecord 1"); //: " + JSON.stringify(objRecord));
        //     var locate = objRecord.locations.replace(/)/g,'');
        //     voltmx.print("### Utility_objectSync_populateGlobal locate: " + JSON.stringify(locate));
        var locations = JSON.parse(objRecord.locations);
        for (var key in locations) {
            var setRecord = {};
            if (locations.hasOwnProperty(key)) {
                voltmx.print("### Utility_objectSync_populateGlobal location key " + locations[key] + " is #" + key);
                objRecord.id = objRecord.identification + "_" + key;
                objRecord.navigateAndZoom = false;
                objRecord.polygonConfig = {
                    lineColor: objRecord.line_color,
                    fillColor: objRecord.fill_color,
                    lineWidth: objRecord.line_width.toString()
                };
                if (key.toString() == "innerpoly") {
                    objRecord.polygonConfig["innerPolygons"] = [locations[key]];
                } else {
                    objRecord.locations = locations[key];
                }
                voltmx.print("### Utility_objectSync_populateGlobal objRecord 2"); //: " + JSON.stringify(objRecord));
                setRecord = JSON.parse(JSON.stringify(objRecord));
                Global.vars.parkingZonesMap.push(setRecord);
            }
        }
    }
    Global.vars.areasLoaded = true;
}

function Utility_checkLocationWithinParkingZoneMap(areas, callback) {
    voltmx.print("### Utility_checkLocationWithinParkingZoneMap areas: " + JSON.stringify(areas));
    var zonesfound = [];
    var location = {
        lat: CaseData.location.latitude,
        lon: CaseData.location.longitude
    };
    if (areas !== undefined && areas != null) {
        voltmx.print("### Utility_checkLocationWithinParkingZoneMap 1");
        //var locationwithin = false;
        for (var a in areas) {
            var locationwithin = false;
            var c = areas[a];
            voltmx.print("### Utility_checkLocationWithinParkingZoneMap 2: first loop");
            for (var i in Global.vars.parkingZonesMap) {
                voltmx.print("### Utility_checkLocationWithinParkingZoneMap 3: second loop");
                var v = Global.vars.parkingZonesMap[i];
                locationwithin = voltmx.map.containsLocation(voltmx.map.SHAPE_TYPE_POLYGON, location, {
                    locations: v.locations,
                    tolerance: 0
                });
                if (v.code.toString() === c.areaCode && c.displayInApp === true) {
                    voltmx.print("### Utility_checkLocationWithinParkingZoneMap v.code: " + v.code + ", c.areaCode: " + c.areaCode + ", c.areaDescription: " + c.areaDescription);
                    v.areadescription = c.areaDescription;
                    if (c.areaDescription.length > 23) {
                        v.areadescription = c.areaDescription.substring(0, 23);
                        v.areadescription = v.areadescription + "...";
                    }
                    //locationwithin = true;
                    voltmx.print("### Utility_checkLocationWithinParkingZoneMap v.areadescription: " + v.areadescription);
                }
                if (locationwithin) {
                    //voltmx.print("### Utility_checkLocationWithinParkingZoneMap zonesfound: " + JSON.stringify(v));
                    voltmx.print("### Utility_checkLocationWithinParkingZoneMap 4 v: " + JSON.stringify(v));
                    if (v.fill_color == "FF000026") {
                        v.flcColorBox = {
                            skin: FF000026
                        };
                    } else if (v.fill_color == "00336626") {
                        v.flcColorBox = {
                            skin: S00336626
                        };
                    } else if (v.fill_color == "312CD126") {
                        v.flcColorBox = {
                            skin: S312CD126
                        }; //diep Blauw
                    } else if (v.fill_color == "04871826") {
                        v.flcColorBox = {
                            skin: S04871826
                        }; //groen
                    } else if (v.fill_color == "FF00E126") {
                        v.flcColorBox = {
                            skin: FF00E126
                        }; //roze
                    } else {
                        v.flcColorBox = {
                            skin: FF000026
                        }; //uitbreiding nog nodig
                    }
                    v.imgLeft = "empty.png";
                    var recordExists = false;
                    for (var j in zonesfound) {
                        var w = zonesfound[j];
                        if (v.id === w.id) {
                            recordExists = true;
                            break;
                        }
                    }
                    if (recordExists === false) {
                        zonesfound.push(v);
                    }
                    //break;
                }
            }
        }
        voltmx.print("### Utility_checkLocationWithinParkingZoneMap zonesfound: " + JSON.stringify(zonesfound));
        voltmx.print("### Utility_checkLocationWithinParkingZoneMap zonesfound callback timestamp: " + Utility_getUTCJavascriptDate(null));
        CaseData.location.overlayAreas = zonesfound;
        if (callback !== undefined && callback != null) {
            callback();
        }
    }
}

function Utility_resetOnlineAttachments() {
    voltmx.print("### Utility_resetOnlineAttachments");
    Global.vars.photosLoaded = false;
    Global.vars.attachmentsLoaded = false;
    Global.vars.onlineAttachments = [];
    Global.vars.getPhotos = [];
}

function Utility_getOnlineAttachments(docid, callback) {
    voltmx.print("#### Utility_getOnlineAttachments");
    voltmx.print("### Utility_getOnlineAttachments Global.vars.attachmentsLoaded: " + Global.vars.attachmentsLoaded);

    function service_getAttachmentListCallback(result) {
        voltmx.print("#### Utility_getOnlineAttachments service_getAttachmentListCallback");
        voltmx.print("### Utility_getOnlineAttachments service_getAttachmentListCallback result: " + JSON.stringify(result));
        if (result.opstatus === 0 && result.httpStatusCode == 200) {
            if (result.response !== undefined && result.response.length > 0) {
                Global.vars.onlineAttachments = result.response;
            }
            Global.vars.attachmentsLoaded = true;
            callback();
        }
    }

    function service_getAttachmentListErrorCallback(error) {
        voltmx.print("#### Utility_getOnlineAttachments service_getAttachmentListErrorCallback");
        voltmx.print("### Utility_getOnlineAttachments service_getAttachmentListErrorCallback error: " + JSON.stringify(error));
        Global.vars.onlineAttachments = [];
        Global.vars.attachmentsLoaded = true;
        callback();
    }
    //
    if (Global.vars.attachmentsLoaded === false) {
        Global.vars.onlineAttachments = [];
        voltmx.print("### Utility_getOnlineAttachments service_getAttachmentList");
        service_getAttachmentList(docid, service_getAttachmentListCallback, service_getAttachmentListErrorCallback);
    }
}

function Utility_encrypt(inputstr) {
    voltmx.print("#### Utility_encrypt");
    try {
        var algo = "aes";
        var myEncryptedTextRaw = "";
        var passPhraseText = Global_getEncryptionParameter("passphrase");
        var encryptDecryptKey = voltmx.crypto.newKey("passphrase", 128, {
            passphrasetext: [passPhraseText],
            subalgo: "aes",
            passphrasehashalgo: "md5"
        });
        var initializationVector = Global_getEncryptionParameter("vector");
        var prptobj = {
            padding: "pkcs5",
            mode: "cbc",
            initializationvector: initializationVector
        };
        myEncryptedTextRaw = voltmx.crypto.encrypt(algo, encryptDecryptKey, inputstr, prptobj);
        var myEncryptedText = voltmx.convertToBase64(myEncryptedTextRaw);
        voltmx.print("### Utility_encrypt finished");
        return myEncryptedText.toString();
    } catch (err) {
        voltmx.print("Error in callbackEncryptAes : " + err);
    }
}

function Utility_decrypt(inputstr) {
    voltmx.print("#### Utility_decrypt");
    try {
        var algo = "aes";
        var myEncryptedTextRaw = "";
        var passPhraseText = Global_getEncryptionParameter("passphrase");
        var encryptDecryptKey = voltmx.crypto.newKey("passphrase", 128, {
            passphrasetext: [passPhraseText],
            subalgo: "aes",
            passphrasehashalgo: "md5"
        });
        var initializationVector = Global_getEncryptionParameter("vector");
        var prptobj = {
            padding: "pkcs5",
            mode: "cbc",
            initializationvector: initializationVector
        };
        myEncryptedTextRaw = voltmx.convertToRawBytes(inputstr);
        var myClearText = voltmx.crypto.decrypt(algo, encryptDecryptKey, myEncryptedTextRaw, prptobj);
        voltmx.print("### Utility_decrypt finished");
        return myClearText.toString();
    } catch (err) {
        voltmx.print("Error in callbackDecryptAes : " + err);
    }
}

function Utility_getOpenTaskTypesToQuery() {
    var functions = "";
    Global.vars.openTaskTypesToQuery = '""';
    for (var b in Global.vars.officerFunctions) {
        var officerFunction = Global.vars.officerFunctions[b];
        for (var j = 0; j < Global.vars.authorizedFunctions.length; j++) {
            var authorizedFunction = Global.vars.authorizedFunctions[j];
            if (authorizedFunction === officerFunction) {
                if (functions === "") {
                    functions = "'" + officerFunction + "'";
                } else {
                    functions = functions + ",'" + officerFunction + "'";
                }
                break;
            }
        }
    }
    voltmx.print("### Utility_getOpenTaskTypesToQuery functions: " + functions);
    var lTteAuthorisationClause = "select tte_id from mle_v_tte_authorization_m where function_code in (" + functions + ")";
    lTteAuthorisationClause = Utility_addTimelineToWhereClauseObjectSync(lTteAuthorisationClause, CaseData.time.dateComponents);
    voltmx.print("### Utility_getOpenTaskTypesToQuery lTteAuthorisationClause: " + lTteAuthorisationClause);
    var lTaskTypeClause = "select distinct identification, description from mle_v_task_type_msv where id in (" + lTteAuthorisationClause + ") and ind_mobile = 1";
    lTaskTypeClause = Utility_addTimelineToWhereClauseObjectSync(lTaskTypeClause, CaseData.time.dateComponents);
    lTaskTypeClause = Utility_addLanguageToWhereClauseObjectSync(lTaskTypeClause);
    voltmx.print("### Utility_getOpenTaskTypesToQuery lTaskTypeClause: " + lTaskTypeClause);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(lTaskTypeClause, Utility_getOpenTaskTypesToQuerysuccessCallback, Utility_getOpenTaskTypesToQueryErrorCallback);
}

function Utility_getOpenTaskTypesToQueryErrorCallback(error) {
    voltmx.print("#### Utility_getOpenTaskTypesToQueryErrorCallback: " + JSON.stringify(error));
}

function Utility_getOpenTaskTypesToQuerysuccessCallback(result) {
    voltmx.print("#### Utility_getOpenTaskTypesToQuerysuccessCallback: " + JSON.stringify(result));
    if (result.length > 0) {
        for (var i = 0; i < result.length; i++) {
            var v = result[i];
            voltmx.print("#### Utility_getOpenTaskTypesToQuerysuccessCallback v : " + JSON.stringify(v));
            voltmx.print("#### Utility_getOpenTaskTypesToQuerysuccessCallback v.identification : " + v.identification);
            var _addOpenTask = -1;
            if (Global.vars.openTaskTypes != null) {
                _addOpenTask = Utility_getIndexIfObjWithAttr(Global.vars.openTaskTypes, "taskType", v.identification);
            }
            voltmx.print("#### Utility_getOpenTaskTypesToQuerysuccessCallback _addOpenTask : " + _addOpenTask);
            if (_addOpenTask != -1) {
                if (Global.vars.openTaskTypesToQuery == '""') {
                    Global.vars.openTaskTypesToQuery = '"' + v.identification + '"';
                } else if (Global.vars.openTaskTypesToQuery.indexOf(v.identification, 0) == -1) {
                    Global.vars.openTaskTypesToQuery = Global.vars.openTaskTypesToQuery + ',"' + v.identification + '"';
                } else {
                    voltmx.print("#### Utility_getOpenTaskTypesToQuerysuccessCallback v.identification : " + v.identification + " already in " + Global.vars.openTaskTypesToQuery);
                }
            }
        }
    }
    voltmx.print("#### Utility_getOpenTaskTypesToQuerysuccessCallback Global.vars.openTaskTypesToQuery: " + Global.vars.openTaskTypesToQuery);
}

function Utility_appPackageName() {
    const packageInfo = context1.getPackageManager().getPackageInfo(context1.getPackageName(), PackageManager.GET_ACTIVITIES);
    const packageName = packageInfo.packageName;
    return packageName;
}

function Utility_isPackageInstalled(packagename) {
    var result = false;
    try {
        // is the application installed?
        const packageInfo = context1.getPackageManager().getPackageInfo(packagename, 0);
        voltmx.print("#### Utility_isPackageInstalled success: " + packageInfo);
        result = true;
    } catch {
        //Not installed
        voltmx.print("#### Utility_isPackageInstalled try_catch");
    }
    return result;
}

function Utility_getExtendedReportCaseTypeCategories() {
    voltmx.print("#### Utility_getExtendedReportCaseTypeCategories");
    Global.vars.extendedReportCaseTypeCategories = [];

    function getCtcAuthorization() {
        voltmx.print("#### Utility_getExtendedReportCaseTypeCategories getCtcAuthorization");
        var functions = "";
        for (var b in Global.vars.officerFunctions) {
            var officerFunction = Global.vars.officerFunctions[b];
            for (var j = 0; j < Global.vars.authorizedFunctions.length; j++) {
                var authorizedFunction = Global.vars.authorizedFunctions[j];
                if (authorizedFunction === officerFunction) {
                    if (functions === "") {
                        functions = "'" + officerFunction + "'";
                    } else {
                        functions = functions + ",'" + officerFunction + "'";
                    }
                    break;
                }
            }
        }
        //check employeefunctions
        voltmx.print("### Utility_getExtendedReportCaseTypeCategories getCtcAuthorization functions: " + functions);
        var lCtcAuthorizationClause = "select * from mle_v_ctc_authorization_m where function_code in (" + functions + ")";
        voltmx.print("#### Utility_getExtendedReportCaseTypeCategories getCtcAuthorization lCtcAuthorizationClause: " + lCtcAuthorizationClause);
        lCtcAuthorizationClause = Utility_addTimelineToWhereClauseObjectSync(lCtcAuthorizationClause);
        voltmx.print("### Utility_getExtendedReportCaseTypeCategories getCtcAuthorization ocwcs: " + lCtcAuthorizationClause);
        KNYMobileFabric.OfflineObjects.executeSelectQuery(lCtcAuthorizationClause, getCtcAuthorizationSuccessCallback, getCtcAuthorizationErrorCallback);
    }

    function getCtcAuthorizationErrorCallback(error) {
        voltmx.print("#### Utility_getExtendedReportCaseTypeCategories getCtcAuthorizationErrorCallback: " + JSON.stringify(error));
    }

    function getCtcAuthorizationSuccessCallback(result) {
        voltmx.print("#### Utility_getExtendedReportCaseTypeCategories getCtcAuthorizationSuccessCallback: " + JSON.stringify(result));
        //ctcid
        var caseCategoryIds = "";
        for (var i in result) {
            var v = result[i];
            if (caseCategoryIds.indexOf("'" + v.ctc_id + "'") == -1) {
                caseCategoryIds = caseCategoryIds + "'" + v.ctc_id + "'" + ",";
            }
        }
        caseCategoryIds = caseCategoryIds.replace(/,\s*$/, "");
        voltmx.print("### Utility_getExtendedReportCaseTypeCategories getCtcAuthorizationSuccessCallback caseCategoryIds: " + caseCategoryIds);
        //   Utility_getExtendedReportCaseTypeCategories getCaseTypeCategries(caseCategoryIds);
        getCaseTypeCategriesObject(caseCategoryIds);
    }

    function getCaseTypeCategriesObject(caseCategoryIds) {
        voltmx.print("#### Utility_getExtendedReportCaseTypeCategories getCaseTypeCategriesObject");
        var wcs = "select * from mle_v_case_type_category_m where id in (" + caseCategoryIds + ")";
        wcs = Utility_addTimelineToWhereClauseObjectSync(wcs);
        wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
        //   var options = {};
        //   options["whereConditionAsAString"] = wcs;
        voltmx.print("### Utility_getExtendedReportCaseTypeCategories getCaseTypeCategriesObject wcs: " + wcs);
        //   Global.vars.syncObjects.caseTypeCategoryObj.get(options, Utility_getExtendedReportCaseTypeCategories getCaseTypeCategriesSuccessCallback, Utility_getExtendedReportCaseTypeCategories getCaseTypeCategriesErrorCallback);
        KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, getCaseTypeCategriesSuccessCallback, getCaseTypeCategriesErrorCallback);
    }

    function getCaseTypeCategriesErrorCallback(error) {
        voltmx.print("#### Utility_getExtendedReportCaseTypeCategories getCaseTypeCategriesErrorCallback: " + JSON.stringify(error));
    }

    function getCaseTypeCategriesSuccessCallback(result) {
        voltmx.print("### Utility_getExtendedReportCaseTypeCategories getCaseTypeCategriesSuccessCallback result: " + JSON.stringify(result));
        // result code find = pv?
        // var parentId = result.id
        // if result.id_master_category = parentId
        // array.push {caseTypeCategory: result.code}
        if (result != null && result.length > 0) {
            var masterIdSet = result.filter(item => item.code === "pv");
            voltmx.print("### Utility_getExtendedReportCaseTypeCategories getCaseTypeCategriesSuccessCallback masterIdSet: " + JSON.stringify(masterIdSet));
            if (masterIdSet.length > 0) {
                var masterId = masterIdSet[0].id;
                var caseTypeCategorySet = result.filter(item => item.id_master_category === masterId);
                voltmx.print("### Utility_getExtendedReportCaseTypeCategories getCaseTypeCategriesSuccessCallback caseTypeCategorySet: " + JSON.stringify(caseTypeCategorySet));
                caseTypeCategorySet.forEach(item => {
                    voltmx.print("### Utility_getExtendedReportCaseTypeCategories getCaseTypeCategriesSuccessCallback item: " + JSON.stringify(item));
                    if (item.code !== "dpv") {
                        var record = {
                            caseTypeCategory: item.code
                        };
                        Global.vars.extendedReportCaseTypeCategories.push(record);
                    }
                });
            }
        }
        voltmx.print("##### Utility_getExtendedReportCaseTypeCategories getCaseTypeCategriesSuccessCallback " + JSON.stringify(Global.vars.extendedReportCaseTypeCategories));
    }
    getCtcAuthorization();
}

function Utility_isExtendReportCaseTypeCategory(caseTypeCategory) {
    voltmx.print("### Utility_isExtendReportCaseTypeCategory caseTypeCategory: " + caseTypeCategory);
    if (caseTypeCategory == null) {
        return false;
    }
    var foundCaseTypeCategory = Global.vars.extendedReportCaseTypeCategories.find(item => item.caseTypeCategory === caseTypeCategory);
    if (foundCaseTypeCategory) {
        voltmx.print("### Utility_isExtendReportCaseTypeCategory found!");
        return true;
    } else {
        voltmx.print("### Utility_isExtendReportCaseTypeCategory not found!");
        return false;
    }
}

function Utility_getDomainFromEmail(email) {
    voltmx.print("### Utility_getDomainFromEmail email: " + email);
    if (email != null && email.length > 0) {
        var atIndex = email.indexOf("@");
        voltmx.print("### Utility_getDomainFromEmail atIndex: " + atIndex);
        if (atIndex > -1 && atIndex < email.length - 1) {
            // Get the domain part of the email
            var domain = email.slice(atIndex + 1);
            voltmx.print("### Utility_getDomainFromEmail domain: " + domain);
            // Split by '.' and remove the last part (TLD)
            var domainParts = domain.split(".");
            // Remove the TLD
            domainParts.pop();
            //
            var domain = domainParts.join("").toUpperCase();
            var replaceDomain = "@" + domain.toLowerCase() + ".";
            voltmx.print("### Utility_getDomainFromEmail replaceDomain: " + replaceDomain);
            voltmx.print("### Utility_getDomainFromEmail domain server: " + domain);
            // If NS then NSLOGIN
            if (domain === "NS") {
                Global.vars.gEntraUsername = email.replace(replaceDomain, "@nslogin.");
                voltmx.print("### Utility_getDomainFromEmail gEntraUsername: " + Global.vars.gEntraUsername);
                Utility_storeSetItem("entrausername", Global.vars.gEntraUsername);
                domain = "NSLOGIN";
            } else if (domain === "WITTEKRUIS" || domain === "HERMES" || domain === "HERMESGROEP" || domain === "CONNEXXION") {
                // Global.vars.gEntraUsername = email.replace(replaceDomain, "@transdev.");
                Utility_storeSetItem("entrausername", Global.vars.gEntraUsername);
                domain = "TRANSDEV";
            }
            voltmx.print("### Utility_getDomainFromEmail email server: " + domain);
            voltmx.print("### Utility_getDomainFromEmail email gEntraUsername: " + Global.vars.gEntraUsername);
            // Rejoin the remaining parts without dots
            return domain;
        }
    }
    return ""; // Return "" if no "@" is found or if it's the last character
}

function Utility_validateDomain(domain) {
    voltmx.print("### Utility_validateDomain domain: " + domain);
    voltmx.print("### Utility_validateDomain availableIDP: " + JSON.stringify(Global.vars.availableIDP));
    if (Global.vars.availableIDP.length === 1) {
        Global.vars.gDomain = Global.vars.availableIDP[0].domain;
        voltmx.print("### Utility_validateDomain Global.vars.gDomain A: " + Global.vars.gDomain);
        return Global.vars.availableIDP[0].identity;
    } else if (domain != null && domain != "" && Global.vars.availableIDP.length > 0) {
        var result = Global.vars.availableIDP.find(item => item.domain === domain);
        if (result) {
            Global.vars.gDomain = result.domain;
            voltmx.print("### Utility_validateDomain Global.vars.gDomain B: " + Global.vars.gDomain);
            voltmx.print("### Utility_validateDomain domain: " + domain);
            return result.identity;
        } else {
            var result1 = Global.vars.availableIDP.find(item => item.domain === Global.vars.gDefaultDomain);
            if (result1) {
                Global.vars.gDomain = result1.domain;
                voltmx.print("### Utility_validateDomain Global.vars.gDomain C: " + Global.vars.gDomain);
                return result1.identity;
            } else {
                Global.vars.gDomain = "";
                voltmx.print("### Utility_validateDomain Global.vars.gDomain D: " + Global.vars.gDomain);
                return Global.vars.selectedOAuthServiceProvider;
            }
        }
    } else {
        Global.vars.gDomain = "";
        voltmx.print("### Utility_validateDomain Global.vars.gDomain E: " + Global.vars.gDomain);
        return Global.vars.selectedOAuthServiceProvider;
    }
}

function Utility_dismissLoadingScreen() {
    voltmx.runOnMainThread(voltmx.application.dismissLoadingScreen, []);
}

function Utility_transform_encrypted_keys() {
    // old keys needs to be transformed or existing keys needs to be removed
    var keysTransformed = voltmx.store.getItem("keysTransformed");
    var array = [{
        key: "username"
    }, {
        key: "entrausername"
    }, {
        key: "email"
    }, {
        key: "pinHash"
    }, {
        key: "fullname"
    }, {
        key: "officernumber"
    }, {
        key: "officerIdentification"
    }, {
        key: "identityprovider"
    }];
    voltmx.print("### Utility_transform_encrypted_keys keysTransformed: " + keysTransformed);
    voltmx.print("### Utility_transform_encrypted_keys Global.vars.firstLogin: " + Global.vars.firstLogin);
    if (keysTransformed == null || (keysTransformed != null && keysTransformed !== "yes")) {
        var username = voltmx.store.getItem("username");
        if (username != null && username !== "" && Global.vars.firstLogin != null && Global.vars.firstLogin == "no") {
            // Loop through the array and transform key-value pairs
            array.forEach(item => {
                var value = Utility_storeGetEncryptedItem(item.key);
                if (value != null && value !== "") {
                    Utility_keychain_set(item.key, value);
                }
                voltmx.store.removeItem(item.key);
            });
            username = Utility_storeGetItem("username");
            var userArray = [{
                key: username + "_furtherIndicationKey"
            }, {
                key: username + "_usequeuelengthtimer"
            }, {
                key: username + "_scanUnitQueueLength"
            }];
            // Loop through the array and transform key-value pairs
            userArray.forEach(item => {
                var value = Utility_storeGetEncryptedItem(item.key);
                if (value != null && value !== "") {
                    Utility_keychain_set(item.key, value);
                }
                voltmx.store.removeItem(item.key);
            });
            // Loop through the array and remove keyss
            var removeArray = [{
                key: "token"
            }, {
                key: "tokenCouch"
            }, {
                key: "password"
            }];
            // Loop through the array and remove item
            removeArray.forEach(item => {
                voltmx.print("### Utility_transform_encrypted_keys remove key: " + item.key);
                voltmx.store.removeItem(item.key);
            });
            voltmx.store.setItem("keysTransformed", "yes");
        } else if (Global.vars.firstLogin != null && Global.vars.firstLogin == "yes") {
            array.forEach(item => {
                Utility_keychain_remove(item.key);
            });
            voltmx.store.setItem("keysTransformed", "yes");
        }
    } else {
        if (Global.vars.firstLogin == null || (Global.vars.firstLogin != null && Global.vars.firstLogin == "yes")) {
            array.forEach(item => {
                Utility_keychain_remove(item.key);
            });
            voltmx.store.setItem("keysTransformed", "yes");
        }
    }
}

function Utility_keychain_set(key, value) {
    var appName = ("" + appConfig.appName).replace(" ", "");
    var cred = {
        securedata: value,
        secureaccount: appName,
        accessiblity: constants.VOLTMX_KEYCHAIN_ITEM_ACCESSIBLE_WHEN_UNLOCKED_THIS_DEVICE_ONLY,
        identifier: key
    };
    var result = voltmx.keychain.save(cred);
}

function Utility_keychain_get(key) {
    var appName = ("" + appConfig.appName).replace(" ", "");
    var cred = {
        secureaccount: appName,
        identifier: key
    };
    var result = voltmx.keychain.retrieve(cred);
    if (result.securedata != null && result.securedata !== "") {
        voltmx.print("#### Utility_keychain_get result.securedata: " + result.securedata);
        return result.securedata;
    } else {
        return null;
    }
}

function Utility_keychain_remove(key) {
    var appName = ("" + appConfig.appName).replace(" ", "");
    var cred = {
        secureaccount: appName,
        identifier: key
    };
    var result = voltmx.keychain.remove(cred);
    return result;
}

function Utility_storeSetItem(key, value) {
    voltmx.print("#### Utility_storeSetItem");
    Utility_keychain_set(key, value);
}

function Utility_storeGetItem(key) {
    voltmx.print("#### Utility_storeGetItem");
    //var value = null;
    return Utility_keychain_get(key);
}

function Utility_storeGetEncryptedItem(key) {
    // will be deprecated after this version is in use
    voltmx.print("#### Utility_storeGetItem");
    var value = null;
    if (Global.vars.encryptionInUse != null && Global.vars.encryptionInUse === true) {
        var decryptValue = voltmx.store.getItem(key);
        if (decryptValue !== null) {
            value = Utility_decrypt(decryptValue);
        }
    } else {
        value = voltmx.store.getItem(key);
    }
    return value;
}