function frmPhotoView_init() {
    voltmx.print("### frmPhotoView init");
    //Utility_registerForIdleTimeout();
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        frmPhotoView.skin = frmBlack;
    }
}

function frmPhotoView_preShow() {
    Analytics_logScreenView("photo-view");
    voltmx.print("### frmPhotoView preshow");
}

function frmPhotoView_postshow() {
    voltmx.print("### frmPhotoView_postshow");
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        //frmTrackDown.CameraANPR1.closeCamera();
        //frmTrackDown.CameraANPR1.takePicture();
        if (frmANPROverlay.flcLicenseplateInfo.top !== "0%") {
            voltmx.print("### frmPhotoView_postshow flcLicenseplateImage isVisible");
            voltmx.runOnMainThread(frmTrackDown_showLicenseplateinfoANPROverlay, []);
        } else {
            voltmx.print("### frmPhotoView_postshow flcLicenseplateImage NOT visible");
            voltmx.runOnMainThread(frmTrackDown_HideLicenseplateinfoANPROverlay, []);
        }
        voltmx.runOnMainThread(frmPhotoView_openCameraiOS, []);
    } else {
        frmTrackDown.CameraANPR1.openCamera();
        frmPhotoView.imgPhoto.src = "empty.png";
    }
}

function frmPhotoView_openCameraiOS() {
    voltmx.print("### frmPhotoView_openCameraiOS");
    // frmTrackDown.CameraANPR1.takePicture();
    frmTrackDown.CameraANPR1.openCamera();
    frmPhotoView.imgPhoto.src = "empty.png";
    //  voltmx.timer.schedule("takePicture",frmPhotoOverview_takePictureTest,2,false);
}

function frmPhotoOverview_takePictureTest() {
    voltmx.print("### frmPhotoOverview_takePictureTest");
    // frmTrackDown.CameraANPR1.takePicture();
    frmTrackDown.CameraANPR1.openCamera();
}

function frmPhotoView_openCameraANPR1() {
    voltmx.print("### frmPhotoView_openCameraANPR1");
    try {
        voltmx.timer.cancel("openCameraANPR1");
    } catch (err) {}
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        //frmTrackDown.CameraANPR1.takePicture();
        frmTrackDown.CameraANPR1.openCamera();
        voltmx.runOnMainThread(frmTrackDown_showLicenseplateinfoANPROverlay, []);
    }
}

function frmPhotoView_back() {
    voltmx.print("### frmPhotoView_back");
    frmTrackDown.show();
}