function frmPersonNationalities_flcSearchHolder_setVisibility(boolean) {
    voltmx.print("### frmPersonNationalities_flcSearchHolder_setVisibility");

    function flcSearchHolder_setVisibility() {
        voltmx.print("### frmPersonNationalities_flcSearchHolder_setVisibility flcSearchHolder_setVisibility: " + boolean);
        frmPersonNationalities.flcSearchHolder.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcSearchHolder_setVisibility, []);
}

function frmPersonNationalities_btnBack_setVisibility(boolean) {
    voltmx.print("### frmPersonNationalities_btnBack_setVisibility");

    function btnBack_setVisibility() {
        voltmx.print("### frmPersonNationalities_btnBack_setVisibility btnBack_setVisibility: " + boolean);
        frmPersonNationalities.btnBack.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnBack_setVisibility, []);
}

function frmPersonNationalities_init() {
    voltmx.print("#### frmPersonNationalities_init");
    //Utility_registerForIdleTimeout();
    frmPersonNationalities.onDeviceBack = Global_onDeviceBack;
    frmPersonNationalities.txtSearch.text = "";
    frmPersonNationalities.txtSearch.placeholder = frmPersonNationalities.txtSearch.placeholder + " (" + voltmx.i18n.getLocalizedString("l_3Characterinput") + ")";
    frmPersonNationalities.segSearch.widgetDataMap = {
        lbl1: "lbl1"
    };
}

function frmPersonNationalities_preshow() {
    Analytics_logScreenView("person-nationalities");
    voltmx.print("### frmPersonNationalities_preShow Global.vars.nationalitypreset: " + Global.vars.nationalitypreset);
    frmPersonNationalities.segSearch.removeAll();
    voltmx.print("#### Search text: " + frmPersonNationalities.txtSearch.text);
    frmPersonNationalities_flcSearchHolder_setVisibility(true);
    frmPersonNationalities_btnBack_setVisibility(false);
    frmPersonNationalities.btnSearch.text = voltmx.i18n.getLocalizedString("bt_cancel");
    if (frmPersonNationalities.txtSearch.text !== "" && frmPersonNationalities.txtSearch.text != null) {
        frmPersonNationalities_TextSearch();
    } else {
        frmPersonNationalities_getNationalities();
    }
}

function frmPersonNationalities_toggleSearch() {
    if (frmPersonNationalities.btnSearch.text == voltmx.i18n.getLocalizedString("l_search")) {
        frmPersonNationalities_flcSearchHolder_setVisibility(true);
        frmPersonNationalities_btnBack_setVisibility(false);
        frmPersonNationalities.btnSearch.text = voltmx.i18n.getLocalizedString("bt_cancel");
    } else {
        frmPersonNationalities_flcSearchHolder_setVisibility(false);
        frmPersonNationalities_btnBack_setVisibility(true);
        frmPersonNationalities.btnSearch.text = voltmx.i18n.getLocalizedString("l_search");
        frmPersonNationalities.txtSearch.text = "";
        frmPersonNationalities_TextSearch();
    }
}

function frmPersonNationalities_clearSearchText() {
    frmPersonNationalities.txtSearch.text = "";
    frmPersonNationalities.txtSearch.setFocus(true);
    frmPersonNationalities_TextSearch();
}

function frmPersonNationalities_getNationalities() {
    var findNationalities = {
        showCount: 100,
        startRec: 0,
        endRec: 100,
        orderBy: "ORDER BY description ASC",
        whereClause: "select * from mle_v_nationality_m where (UPPER(description) like '%" + frmPersonNationalities.txtSearch.text.toUpperCase() + "%')"
    };
    voltmx.print("### frmPersonNationalities_getNationalities get: " + findNationalities.showCount + " Nationalities");
    var wcs = Utility_addTimelineToWhereClauseObjectSync(findNationalities.whereClause, CaseData.time.dateComponents);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    wcs = wcs + " " + findNationalities.orderBy; // + " LIMIT " + findNationalities.showCount + " OFFSET " + findNationalities.startRec;
    voltmx.print("### findNationalities wcs: " + wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmPersonNationalities_successCallback, frmPersonNationalities_errorCallback);
}

function frmPersonNationalities_successCallback(result) {
    voltmx.print("### Nationalities result: " + JSON.stringify(result));
    if (result.length >= 1) {
        for (var i = 0;
            ((result) != null) && i < result.length; i++) {
            var v = result[i];
            result[i].lbl1 = result[i].description;
            result[i].orderSequence = Utility_getSequenceOrder(result[i].description, frmPersonNationalities.txtSearch.text);
        }
        // Sort
        result.sort((a, b) => a.orderSequence.localeCompare(b.orderSequence, undefined, {
            numeric: true,
            sensitivity: 'base'
        }));
        frmPersonNationalities.segSearch.setData(result);
    } else {
        var value = [{
            lbl1: voltmx.i18n.getLocalizedString("l_noCountriesFound")
        }];
        frmPersonNationalities.segSearch.setData(value);
    }
}

function frmPersonNationalities_errorCallback(error) {
    voltmx.print("### Countries error: " + error);
}

function frmPersonNationalities_onClick_segment() {
    var focusedCharacteristic = frmPersonNationalities.segSearch.selectedItems;
    //fill globals
    Global.vars.gCasePersons.nationality = focusedCharacteristic[0].code;
    Global.vars.gCasePersons.nationalityDesc = focusedCharacteristic[0].description;
    voltmx.print("#### frmPersonNationality_onClick_segment Global.vars.gCasePersons.nationality: " + Global.vars.gCasePersons.nationality);
    voltmx.print("#### frmPersonNationality_onClick_segment Global.vars.gCasePersons.nationalityDesc: " + Global.vars.gCasePersons.nationalityDesc);
    if (focusedCharacteristic[0].code == Global.vars.nationalitypreset) {
        Global.vars.gCasePersons.indicationDutch = true;
    } else {
        Global.vars.gCasePersons.indicationDutch = false;
    }
    voltmx.print("#### frmPersonNationality_onClick_segment Global.vars.gCasePersons.IndicationDutch: " + Global.vars.gCasePersons.IndicationDutch);
    frmPersonNationalities_btnback();
}
// Search Country function
function frmPersonNationalities_TextSearch() {
    if (frmPersonNationalities.txtSearch.text.length > 2 || frmPersonNationalities.txtSearch.text.length === 0) {
        frmPersonNationalities_getNationalities();
    }
}

function frmPersonNationalities_btnback() {
    frmPersonNationalities.txtSearch.text = "";
    if (Global.vars.previousForm == "frmPersonSSN") {
        frmPersonSSN.show();
    } else if (Global.vars.previousForm == "frmPersonManualPerson") {
        frmPersonManualPerson.show();
    } else if (Global.vars.previousForm == "frmPersonAlternativeSearch") {
        frmPersonAlternativeSearch.show();
    }
}