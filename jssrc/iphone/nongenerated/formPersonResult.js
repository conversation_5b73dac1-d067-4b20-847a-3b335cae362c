function frmPersonResult_btnDone_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_btnDone_setVisibility");

    function btnDone_setVisibility() {
        voltmx.print("### frmPersonResult_btnDone_setVisibility btnDone_setVisibility: " + boolean);
        frmPersonResult.btnDone.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnDone_setVisibility, []);
}

function frmPersonResult_btnEdit_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_btnEdit_setVisibility");

    function btnEdit_setVisibility() {
        voltmx.print("### frmPersonResult_btnEdit_setVisibility btnEdit_setVisibility: " + boolean);
        frmPersonResult.btnEdit.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnEdit_setVisibility, []);
}

function frmPersonResult_flcFooterMain_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcFooterMain_setVisibility");

    function flcFooterMain_setVisibility() {
        voltmx.print("### frmPersonResult_flcFooterMain_setVisibility flcFooterMain_setVisibility: " + boolean);
        frmPersonResult.flcFooterMain.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcFooterMain_setVisibility, []);
}

function frmPersonResult_frmResume_flcFooterMain_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_frmResume_flcFooterMain_setVisibility");

    function frmResume_flcFooterMain_setVisibility() {
        voltmx.print("### frmPersonResult_frmResume_flcFooterMain_setVisibility frmResume_flcFooterMain_setVisibility: " + boolean);
        frmResume.flcFooterMain.setVisibility(boolean);
    }
    voltmx.runOnMainThread(frmResume_flcFooterMain_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcButChangeDocument_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcButChangeDocument_setVisibility");

    function flcButChangeDocument_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcButChangeDocument_setVisibility flcButChangeDocument_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcButChangeDocument.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcButChangeDocument_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcDocumentCountry_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcDocumentCountry_setVisibility");

    function flcDocumentCountry_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcDocumentCountry_setVisibility flcDocumentCountry_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcDocumentCountry.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcDocumentCountry_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcDocumentNumber_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcDocumentNumber_setVisibility");

    function flcDocumentNumber_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcDocumentNumber_setVisibility flcDocumentNumber_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcDocumentNumber.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcDocumentNumber_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcDocumentDescription_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcDocumentDescription_setVisibility");

    function flcDocumentDescription_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcDocumentDescription_setVisibility flcDocumentDescription_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcDocumentDescription.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcDocumentDescription_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcZipCode_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcZipCode_setVisibility");

    function flcZipCode_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcZipCode_setVisibility flcZipCode_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcZipCode.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcZipCode_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcPlaceOfResidency_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcPlaceOfResidency_setVisibility");

    function flcPlaceOfResidency_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcPlaceOfResidency_setVisibility flcPlaceOfResidency_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcPlaceOfResidency.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcPlaceOfResidency_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcStreetNumberAddition_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcStreetNumberAddition_setVisibility");

    function flcStreetNumberAddition_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcStreetNumberAddition_setVisibility flcStreetNumberAddition_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcStreetNumberAddition.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcStreetNumberAddition_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcMailbox_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcMailbox_setVisibility");

    function flcMailbox_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcMailbox_setVisibility flcMailbox_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcMailbox.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcMailbox_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcStreet_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcStreet_setVisibility");

    function flcStreet_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcStreet_setVisibility flcStreet_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcStreet.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcStreet_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcReplyNumber_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcReplyNumber_setVisibility");

    function flcReplyNumber_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcReplyNumber_setVisibility flcReplyNumber_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcReplyNumber.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcReplyNumber_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcAddressline1_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcAddressline1_setVisibility");

    function flcAddressline1_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcAddressline1_setVisibility flcAddressline1_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcAddressline1.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcAddressline1_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcAddressline2_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcAddressline2_setVisibility");

    function flcAddressline2_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcAddressline2_setVisibility flcAddressline2_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcAddressline2.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcAddressline2_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcAddressline3_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcAddressline3_setVisibility");

    function flcAddressline3_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcAddressline3_setVisibility flcAddressline3_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcAddressline3.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcAddressline3_setVisibility, []);
}

function frmPersonResult_flcPersonDetails_flcAdress_setVisibility(boolean) {
    voltmx.print("### frmPersonResult_flcPersonDetails_flcAdress_setVisibility");

    function flcAdress_setVisibility() {
        voltmx.print("### frmPersonResult_flcPersonDetails_flcAdress_setVisibility flcAdress_setVisibility: " + boolean);
        frmPersonResult.flcPersonDetails.flcAdress.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcAdress_setVisibility, []);
}

function frmPersonResult_init() {
    voltmx.print("### frmPersonResult_init");
    //Utility_registerForIdleTimeout();
    frmPersonResult.onDeviceBack = Global_onDeviceBack;
}

function frmPersonResult_preshow() {
    Analytics_logScreenView("person-result");
    voltmx.print("### frmPersonResult_preshow");
    frmPersonResult.flcPersonDetails.flcButChangeDocument.btnButChangeDocument.onClick = frmPersonResult_onclick_btnChangeDocument;
    if (Global.vars.readIDScanned === true) {
        voltmx.print("### frmPersonResult_preshow Global.vars.gCasePersons.idenDocType: " + Global.vars.gCasePersons.idenDocType);
        Global.vars.readIDScanned = false;
    }
    if (Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_registerconcept")) {
        if (Global.vars.previousForm == "frmRegisterResume" || Global.vars.previousForm == "frmRegisterConcept") {
            Global.vars.cameToPersonFromForm = Global.vars.previousForm;
        }
    } else {
        Global.vars.cameToPersonFromForm = "";
    }
    // show edit document button
    if (Global.vars.previousForm !== "frmResume" && Global.vars.previousForm !== "frmRegister" && Global.vars.previousForm !== "frmRegisterResume" && Global.vars.previousForm !== "frmRegisterConcept") {
        frmPersonResult_flcPersonDetails_flcButChangeDocument_setVisibility(true);
    } else {
        frmPersonResult_flcPersonDetails_flcButChangeDocument_setVisibility(false);
    }
    frmPersonResult_resetFields();
    frmPersonResult_setGlobalsToFields();
    if (Global.vars.previousForm == "frmResume") {
        frmPersonResult_btnDone_setVisibility(false);
        if (Global.vars.gCasePersons.indRegCalled === false) {
            frmPersonResult_btnDone_setVisibility(true);
        } else {
            frmPersonResult_btnEdit_setVisibility(false);
        }
    } else if (Global.vars.previousForm == "frmPersonManualPerson") {
        frmPersonResult_btnDone_setVisibility(false);
        if (Global.vars.gCasePersons.indRegCalled === false) {
            frmPersonResult_btnEdit_setVisibility(true);
        } else {
            frmPersonResult_btnEdit_setVisibility(false);
        }
    } else if (Global.vars.previousForm == "frmRegister" || Global.vars.previousForm == "frmRegisterResume" || Global.vars.previousForm == "frmRegisterConcept") {
        frmPersonResult_btnDone_setVisibility(false);
        frmPersonResult_btnEdit_setVisibility(true);
    } else {
        frmPersonResult_btnDone_setVisibility(true);
        frmPersonResult_btnEdit_setVisibility(false);
    }
    if (Global.vars.disablePrintButtons === true) {
        frmPersonResult_flcFooterMain_setVisibility(false);
        frmPersonResult.flcLayout.bottom = "12dp";
    } else {
        frmPersonResult_flcFooterMain_setVisibility(true);
        frmPersonResult.flcLayout.bottom = "44dp";
    }
}

function frmPersonResult_posthow() {
    voltmx.print("### frmPersonResult_posthow");
    voltmx.application.dismissLoadingScreen();
}
// function frmPersonResult_confirmSetDocument(response){
//   if(response){
//     Global.vars.previousForm = "frmPersonResult";
//     frmPersonDocument.show();
//   }
// }
function frmPersonResult_onclick_btnChangeDocument() {
    var personinfo = JSON.parse(JSON.stringify(Global.vars.gCasePersons));
    Global.vars.originalPersonDocumentInfo.idenDocType = personinfo.idenDocType;
    Global.vars.originalPersonDocumentInfo.idenDocTypeDesc = personinfo.idenDocTypeDesc;
    Global.vars.originalPersonDocumentInfo.countryIdenDoc = personinfo.countryIdenDoc;
    Global.vars.originalPersonDocumentInfo.countryIdenDocDesc = personinfo.countryIdenDocDesc;
    Global.vars.originalPersonDocumentInfo.documentNumber = personinfo.documentNumber;
    Global.vars.originalPersonDocumentInfo.documentTypeCheckable = personinfo.documentTypeCheckable;
    Global.vars.originalPersonDocumentInfo.documentNumberChecked = personinfo.documentNumberChecked;
    Global.vars.originalPersonDocumentInfo.documentNumberValid = personinfo.documentNumberValid;
    var documentDescription = "";
    for (var p = 0;
        ((CaseData.text) != null) && p < CaseData.text.length; p++) {
        var v = CaseData.text[p];
        if ((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))) { //beschrijving documenten
            voltmx.print("#### frmPersonResult_onclick_btnChangeDocument: description document: " + JSON.stringify(v) + " index: " + p);
            documentDescription = v.value;
            documentDescription = documentDescription.replace("Beschrijving document(en): ", "");
            break;
        }
    }
    voltmx.print("#### frmPersonResult_onclick_btnChangeDocument: documentDescription: " + documentDescription);
    Global.vars.originalPersonDocumentInfo.documentAdditionalDescription = documentDescription;
    Global.vars.originalPersonDocumentInfo.formToGoBackTo = "frmPersonResult";
    frmPersonDocument.show();
}

function frmPersonResult_setGlobalsToFields() {
    voltmx.print("### frmPersonResult_setGlobalsToFields");
    var docMandatory = false;
    var index = Global.vars.additionalDocumentTypes.map(function(e) {
        return e.numbervalue;
    }).indexOf(Global.vars.gCasePersons.idenDocType);
    if (index > -1) {
        docMandatory = Global.vars.additionalDocumentTypes[index].country_number_mandatory;
    }
    voltmx.print("### frmPersonResult_setGlobalsToFields docMandatory: " + docMandatory);
    voltmx.print("### frmPersonResult_setGlobalsToFields countryIdenDocDesc: " + Global.vars.gCasePersons.countryIdenDocDesc);
    voltmx.print("### frmPersonResult_setGlobalsToFields documentNumber: " + Global.vars.gCasePersons.documentNumber);
    //Document info
    frmPersonResult.flcPersonDetails.lblDocumentType.text = Global.vars.gCasePersons.idenDocTypeDesc;
    if (Global.vars.gCasePersons.idenDocType.toString().startsWith("99") !== true && Global.vars.gCasePersons.idenDocType !== 0 && Global.vars.gCasePersons.idenDocType != 20) {
        voltmx.print("### frmPersonResult_setGlobalsToFields IF");
        frmPersonResult.flcPersonDetails.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
        frmPersonResult.flcPersonDetails.lblDocumentNumber.text = Global.vars.gCasePersons.documentNumber;
        frmPersonResult_flcPersonDetails_flcDocumentCountry_setVisibility(true);
        frmPersonResult_flcPersonDetails_flcDocumentNumber_setVisibility(true);
        frmPersonResult_flcPersonDetails_flcDocumentDescription_setVisibility(false);
    } else if (Global.vars.gCasePersons.idenDocType.toString().startsWith("99") === true && docMandatory === true) {
        voltmx.print("### frmPersonResult_setGlobalsToFields ELSE IF");
        frmPersonResult.flcPersonDetails.lblDocumentCountry.text = Global.vars.gCasePersons.countryIdenDocDesc;
        frmPersonResult.flcPersonDetails.lblDocumentNumber.text = Global.vars.gCasePersons.documentNumber;
        frmPersonResult_flcPersonDetails_flcDocumentCountry_setVisibility(true);
        frmPersonResult_flcPersonDetails_flcDocumentNumber_setVisibility(true);
        frmPersonResult_flcPersonDetails_flcDocumentDescription_setVisibility(true);
    } else {
        voltmx.print("### frmPersonResult_setGlobalsToFields ELSE");
        frmPersonResult_flcPersonDetails_flcDocumentCountry_setVisibility(false);
        frmPersonResult_flcPersonDetails_flcDocumentNumber_setVisibility(false);
        frmPersonResult_flcPersonDetails_flcDocumentDescription_setVisibility(true);
    }
    var documentDescription = "";
    for (var p = 0;
        ((CaseData.text) != null) && p < CaseData.text.length; p++) {
        var v = CaseData.text[p];
        if ((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))) { //beschrijving documenten
            voltmx.print("#### frmPersonResult_setGlobalsToFields: description document: " + JSON.stringify(v) + " index: " + p);
            documentDescription = v.value;
            documentDescription = documentDescription.replace("Beschrijving document(en): ", "");
            break;
        }
    }
    frmPersonResult.flcPersonDetails.lblDocumentDescription.text = documentDescription;
    //Person info
    frmPersonResult.flcPersonDetails.lblSSN.text = Global.vars.gCasePersons.ssn;
    var fullname = "";
    if (Global.vars.gCasePersons.middlename === null || Global.vars.gCasePersons.middlename === undefined || Global.vars.gCasePersons.middlename.length === 0) {
        fullname = Global.vars.gCasePersons.givenNames + " " + Global.vars.gCasePersons.surname;
    } else {
        fullname = (Global.vars.gCasePersons.givenNames + " " + Global.vars.gCasePersons.middlename).trim(" ") + " " + Global.vars.gCasePersons.surname;
    }
    frmPersonResult.flcPersonDetails.lblFullName.text = fullname;
    Global.vars.gCasePersons.fullName = fullname.trim();
    frmPersonResult.flcPersonDetails.lblInitails.text = Global.vars.gCasePersons.initials;
    if (Global.vars.gCasePersons.birthDateSet === false) {
        frmPersonResult.flcPersonDetails.lblDateOfBirth.text = Global.vars.gCasePersons.yearOfBirth + "";
    } else {
        frmPersonResult.flcPersonDetails.lblDateOfBirth.text = Global.vars.gCasePersons.birthdateDesc;
    }
    frmPersonResult.flcPersonDetails.lblPersonNationality.text = Global.vars.gCasePersons.nationalityDesc;
    frmPersonResult.flcPersonDetails.lblCountryOfOrigin.text = Global.vars.gCasePersons.countryOfBirthDesc;
    voltmx.print("### frmPersonResult_setGlobalsToFields Global.vars.CountryCode: " + Global.vars.CountryCode);
    voltmx.print("### frmPersonResult_setGlobalsToFields Global.vars.gCasePersons.countryOfBirth: " + Global.vars.gCasePersons.countryOfBirth);
    voltmx.print("### frmPersonResult_setGlobalsToFields Global.vars.gCasePersons.birthMunicipNLDesc: " + Global.vars.gCasePersons.birthMunicipNLDesc);
    voltmx.print("### frmPersonResult_setGlobalsToFields Global.vars.gCasePersons.birthplace: " + Global.vars.gCasePersons.birthplace);
    if (Global.vars.gCasePersons.countryOfBirth == Global.vars.CountryCode) {
        frmPersonResult.flcPersonDetails.lblMunicipalityOfBirth.text = Global.vars.gCasePersons.birthMunicipNLDesc;
    } else {
        frmPersonResult.flcPersonDetails.lblMunicipalityOfBirth.text = Global.vars.gCasePersons.birthplace;
    }
    frmPersonResult.flcPersonDetails.lblGender.text = Global.vars.gCasePersons.genderDesc;
    //set Address info
    if (Global.vars.gCasePersons.addresses[0].addressType == addressType.emptyDataGBA.value) {
        frmPersonResult.flcPersonDetails.lblAdressType.text = "Geheim adres";
        frmPersonResult_flcPersonDetails_flcAdress_setVisibility(false);
    } else if (Global.vars.gCasePersons.addresses[0].addressType == addressType.withoutFixedStay.value || Global.vars.gCasePersons.addresses[0].addressType == addressType.departedDestinationUnknown.value) {
        frmPersonResult.flcPersonDetails.lblAdressType.text = Global.vars.gCasePersons.addresses[0].addressTypeDesc;
        frmPersonResult_flcPersonDetails_flcAdress_setVisibility(false);
    } else {
        frmPersonResult.flcPersonDetails.lblAdressType.text = Global.vars.gCasePersons.addresses[0].addressTypeDesc;
        frmPersonResult_flcPersonDetails_flcAdress_setVisibility(true);
        frmPersonResult.flcPersonDetails.lblCountryAdress.text = Global.vars.gCasePersons.addresses[0].country;
        if (Global.vars.gCasePersons.addresses[0].countryCode == Global.vars.CountryCode) {
            frmPersonResult_flcPersonDetails_flcZipCode_setVisibility(true);
            frmPersonResult_flcPersonDetails_flcStreet_setVisibility(true);
            frmPersonResult_flcPersonDetails_flcStreetNumberAddition_setVisibility(true);
            frmPersonResult_flcPersonDetails_flcPlaceOfResidency_setVisibility(true);
            frmPersonResult_flcPersonDetails_flcAddressline1_setVisibility(false);
            frmPersonResult_flcPersonDetails_flcAddressline2_setVisibility(false);
            frmPersonResult_flcPersonDetails_flcAddressline3_setVisibility(false);
            //set data
            frmPersonResult.flcPersonDetails.lblZipCode.text = Global.vars.gCasePersons.addresses[0].zipcode;
            frmPersonResult.flcPersonDetails.lblStreet.text = Global.vars.gCasePersons.addresses[0].street;
            frmPersonResult.flcPersonDetails.lblStreetNumber.text = Global.vars.gCasePersons.addresses[0].streetNumber;
            frmPersonResult.flcPersonDetails.lblHouseNumberAddition.text = Global.vars.gCasePersons.addresses[0].streetNumAdditn;
            frmPersonResult.flcPersonDetails.lblPlaceOfResidency.text = Global.vars.gCasePersons.addresses[0].city;
            if (Global.vars.gCasePersons.addresses[0].postOfficeBox != null) {
                frmPersonResult.flcPersonDetails.lblMailbox.text = Global.vars.gCasePersons.addresses[0].postOfficeBox;
                frmPersonResult_flcPersonDetails_flcMailbox_setVisibility(true);
                frmPersonResult_flcPersonDetails_flcStreet_setVisibility(false);
                frmPersonResult_flcPersonDetails_flcStreetNumberAddition_setVisibility(false);
            } else {
                frmPersonResult_flcPersonDetails_flcMailbox_setVisibility(false);
                frmPersonResult_flcPersonDetails_flcStreet_setVisibility(true);
                frmPersonResult_flcPersonDetails_flcStreetNumberAddition_setVisibility(true);
            }
            if (Global.vars.gCasePersons.addresses[0].replyNumber === null) {
                voltmx.print("#### hide Reply");
                frmPersonResult_flcPersonDetails_flcReplyNumber_setVisibility(false);
            } else {
                frmPersonResult_flcPersonDetails_flcReplyNumber_setVisibility(true);
                frmPersonResult.flcPersonDetails.lblReplyNumber.text = Global.vars.gCasePersons.addresses[0].replyNumber;
            }
        } else {
            if (Global.vars.gCasePersons.addresses[0].zipcode === null || Global.vars.gCasePersons.addresses[0].zipcode === "") {
                frmPersonResult_flcPersonDetails_flcZipCode_setVisibility(false);
            } else {
                frmPersonResult.flcPersonDetails.lblZipCode.text = Global.vars.gCasePersons.addresses[0].zipcode;
                frmPersonResult_flcPersonDetails_flcZipCode_setVisibility(true);
            }
            if (Global.vars.gCasePersons.addresses[0].street === null || Global.vars.gCasePersons.addresses[0].street === "") {
                frmPersonResult_flcPersonDetails_flcStreet_setVisibility(false);
            } else {
                frmPersonResult.flcPersonDetails.lblStreet.text = Global.vars.gCasePersons.addresses[0].street;
                frmPersonResult_flcPersonDetails_flcStreet_setVisibility(true);
            }
            if ((Global.vars.gCasePersons.addresses[0].streetNumber === null || Global.vars.gCasePersons.addresses[0].streetNumber === "") && (Global.vars.gCasePersons.addresses[0].streetNumAdditn === null || Global.vars.gCasePersons.addresses[0].streetNumAdditn === "")) {
                frmPersonResult_flcPersonDetails_flcStreetNumberAddition_setVisibility(false);
            } else {
                frmPersonResult.flcPersonDetails.lblStreetNumber.text = Global.vars.gCasePersons.addresses[0].streetNumber;
                frmPersonResult.flcPersonDetails.lblHouseNumberAddition.text = Global.vars.gCasePersons.addresses[0].streetNumAdditn;
                frmPersonResult_flcPersonDetails_flcStreetNumberAddition_setVisibility(true);
            }
            if (Global.vars.gCasePersons.addresses[0].addressLine1 === null || Global.vars.gCasePersons.addresses[0].addressLine1 === "") {
                frmPersonResult_flcPersonDetails_flcAddressline1_setVisibility(false);
            } else {
                frmPersonResult_flcPersonDetails_flcAddressline1_setVisibility(true);
                frmPersonResult.flcPersonDetails.lblAddressline1.text = Global.vars.gCasePersons.addresses[0].addressLine1;
            }
            if (Global.vars.gCasePersons.addresses[0].addressLine2 === null || Global.vars.gCasePersons.addresses[0].addressLine2 === "") {
                frmPersonResult_flcPersonDetails_flcAddressline2_setVisibility(false);
            } else {
                frmPersonResult_flcPersonDetails_flcAddressline2_setVisibility(true);
                frmPersonResult.flcPersonDetails.lblAddressline2.text = Global.vars.gCasePersons.addresses[0].addressLine2;
            }
            if (Global.vars.gCasePersons.addresses[0].addressLine3 === null || Global.vars.gCasePersons.addresses[0].addressLine3 === "") {
                frmPersonResult_flcPersonDetails_flcPlaceOfResidency_setVisibility(false);
                frmPersonResult_flcPersonDetails_flcAddressline3_setVisibility(false);
            } else {
                frmPersonResult.flcPersonDetails.lblPlaceOfResidency.text = Global.vars.gCasePersons.addresses[0].addressLine3;
                frmPersonResult_flcPersonDetails_flcPlaceOfResidency_setVisibility(true);
                frmPersonResult_flcPersonDetails_flcAddressline3_setVisibility(false);
            }
            frmPersonResult_flcPersonDetails_flcMailbox_setVisibility(false);
            frmPersonResult_flcPersonDetails_flcReplyNumber_setVisibility(false);
        }
    }
    if (Global.vars.disablePrintButtons === false) {
        frmPersonResult_frmResume_flcFooterMain_setVisibility(true);
    }
    //voltmx.application.dismissLoadingScreen();
}

function frmPersonResult_resetFields() {
    frmPersonResult.flcPersonDetails.lblDocumentType.text = "";
    frmPersonResult.flcPersonDetails.lblDocumentCountry.text = "";
    frmPersonResult.flcPersonDetails.lblDocumentNumber.text = "";
    frmPersonResult.flcPersonDetails.lblSSN.text = "";
    frmPersonResult.flcPersonDetails.lblFullName.text = "";
    frmPersonResult.flcPersonDetails.lblInitails.text = "";
    frmPersonResult.flcPersonDetails.lblDateOfBirth.text = "";
    frmPersonResult.flcPersonDetails.lblPersonNationality.text = "";
    frmPersonResult.flcPersonDetails.lblCountryOfOrigin.text = "";
    frmPersonResult.flcPersonDetails.lblMunicipalityOfBirth.text = "";
    frmPersonResult.flcPersonDetails.lblGender.text = "";
    frmPersonResult.flcPersonDetails.lblAdressType.text = "";
    frmPersonResult.flcPersonDetails.lblCountryAdress.text = "";
    frmPersonResult.flcPersonDetails.lblZipCode.text = "";
    frmPersonResult.flcPersonDetails.lblStreet.text = "";
    frmPersonResult.flcPersonDetails.lblStreetNumber.text = "";
    frmPersonResult.flcPersonDetails.lblHouseNumberAddition.text = "";
    frmPersonResult.flcPersonDetails.lblPlaceOfResidency.text = "";
    frmPersonResult.flcPersonDetails.lblMailbox.text = "";
    frmPersonResult.flcPersonDetails.lblReplyNumber.text = "";
    frmPersonResult.flcPersonDetails.lblAddressline1.text = "";
    frmPersonResult.flcPersonDetails.lblAddressline2.text = "";
    frmPersonResult.flcPersonDetails.lblAddressline3.text = "";
    frmPersonResult_frmResume_flcFooterMain_setVisibility(false);
}

function frmPersonResult_callbackfunctionBSN(result) {
    voltmx.application.dismissLoadingScreen();
    //wsstatuscode 0 is ok, 1 is not found, 2 is multiple results, 99 is error
    voltmx.print("#### frmPersonResult_callbackfunctionBSN");
    voltmx.print("#### frmPersonResult_callbackfunctionBSN result: " + JSON.stringify(result));
    if (result.opstatus === 0 && result.httpStatusCode == 200) {
        Global.vars.personAlternativeEnableExtrafields = false;
        Global.vars.personInputMethod = Global.vars.personInputMethod === "ReadId" ? "ReadId-BvBSN" : "BvBSN";
        voltmx.print("#### frmPersonResult_callbackfunctionBSN service httpStatusCode 200");
        if (result.wsstatuscode !== undefined) {
            result.person[0].wsstatuscode = result.wsstatuscode;
        }
        if (result.wsstatustext !== undefined) {
            result.person[0].wsstatustext = result.wsstatustext;
        }
        voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].wsstatustext: " + result.person[0].wsstatustext);
        voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].wsstatuscode: " + result.person[0].wsstatuscode);
        if (Number(result.person[0].wsstatuscode) == 23001 || Number(result.person[0].wsstatuscode) == 23004) {
            voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].wsstatuscode 1: " + Number(result.person[0].wsstatuscode));
            voltmx.print("#### frmPersonResult_callbackfunctionBSN no result");
            voltmx.application.dismissLoadingScreen();
            if (voltmx.application.getCurrentForm().id == "frmPersonAlternativeSearch" && Global.vars.readIDScanned === true) {
                voltmx.ui.Alert(result.person[0].wsstatustext.replace("Response querying BvBSN: ", "") + ". Wilt u de gegevens handmatig aanvullen?", frmPersonAlternativeSearch_goToManual, "confirmation", voltmx.i18n.getLocalizedString("bt_yes"), voltmx.i18n.getLocalizedString("bt_no"), "Info", null);
            } else {
                voltmx.ui.Alert(result.person[0].wsstatustext.replace("Response querying BvBSN: ", ""), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
            }
        } else if (Number(result.person[0].wsstatuscode) == 23006) {
            voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].wsstatuscode 2: " + Number(result.person[0].wsstatuscode));
            voltmx.print("#### frmPersonResult_callbackfunctionBSN more then one person found");
            voltmx.application.dismissLoadingScreen();
            voltmx.ui.Alert(result.person[0].wsstatustext.replace("Response querying BvBSN: ", ""), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
            voltmx.print("#### frmPersonResult_callbackfunctionBSN open more search fields");
            Global.vars.personAlternativeEnableExtrafields = true;
            frmPersonAlternativeSearch_enableExtraFields();
        } else if (Number(result.person[0].wsstatuscode) == 23003 || Number(result.person[0].wsstatuscode) == 23002 || Number(result.person[0].wsstatuscode) === 0) {
            voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].wsstatuscode 3: " + Number(result.person[0].wsstatuscode));
            if (((result.person[0].personUnderInvestigation !== undefined && result.person[0].personUnderInvestigation != "null" && result.person[0].personUnderInvestigation !== "false") || (result.person[0].addressUnderInvestigation !== undefined && result.person[0].addressUnderInvestigation != "null" && result.person[0].addressUnderInvestigation !== "false")) && Global.vars.showPersonWarnings === true) {
                if (result.person[0].personUnderInvestigation !== undefined && result.person[0].personUnderInvestigation != "null" && result.person[0].personUnderInvestigation !== "false") {
                    //personUnderInvestigation
                    var reasonPersonUnderinvestigation = voltmx.i18n.getLocalizedString("l_personUnderInvestigation");
                    //personstartdateInvestigation
                    var datePersonUnderinvestigation = result.person[0].personstartdateInvestigation;
                    alert(reasonPersonUnderinvestigation + " (" + datePersonUnderinvestigation + ")");
                }
                if (result.person[0].addressUnderInvestigation !== undefined && result.person[0].addressUnderInvestigation != "null" && result.person[0].addressUnderInvestigation !== "false") {
                    //addressUnderInvestigation
                    var reasonAddressUnderInvestigation = voltmx.i18n.getLocalizedString("l_addressUnderInvestigation");
                    //addressstartdateInvestigation
                    var dateAddressUnderInvestigation = result.person[0].addressstartdateInvestigation;
                    alert(reasonAddressUnderInvestigation + " (" + dateAddressUnderInvestigation + ")");
                }
            }
            //Set succesfull service call
            voltmx.print("#### frmPersonResult_callbackfunctionBSN Global.vars.gPersonGenderResult: " + JSON.stringify(Global.vars.gPersonGenderResult));
            //get gender description
            for (var i = 0;
                ((Global.vars.gPersonGenderResult) != null) && i < Global.vars.gPersonGenderResult.length; i++) {
                var v = Global.vars.gPersonGenderResult[i];
                voltmx.print("#### frmPersonResult_callbackfunctionBSN Global.vars.gPersonGenderResult: " + JSON.stringify(v) + " index: " + i);
                if ((v.number_value == Number(result.person[0].gendercode))) {
                    result.person[0].gender = v.descripton;
                    break;
                }
            }
            // empty globals
            //preserve nationality
            var nationalitycode = Global.vars.gCasePersons.nationality;
            var inddutch = Global.vars.gCasePersons.indicationDutch;
            var nationalitydesc = Global.vars.gCasePersons.nationalityDesc;
            //preserve document info
            var doctypedesc = Global.vars.gCasePersons.idenDocTypeDesc;
            var doctype = Global.vars.gCasePersons.idenDocType;
            var docnumber = Global.vars.gCasePersons.documentNumber;
            var docCountry = Global.vars.gCasePersons.countryIdenDoc;
            var docCountryDesc = Global.vars.gCasePersons.countryIdenDocDesc;
            var documentTypeCheckable = Global.vars.gCasePersons.documentTypeCheckable;
            var documentNumberChecked = Global.vars.gCasePersons.documentNumberChecked;
            var documentNumberValid = Global.vars.gCasePersons.documentNumberValid;
            //
            Global.vars.gCasePersons = {};
            Global.vars.gCasePersons = CaseData_setNewperson();
            // fill globals
            Global.vars.gCasePersons.personFilled = true;
            // set preserved nationality
            Global.vars.gCasePersons.nationality = nationalitycode;
            Global.vars.gCasePersons.indicationDutch = inddutch;
            Global.vars.gCasePersons.nationalityDesc = nationalitydesc;
            // end set preserved nationality
            //set preserved document info
            Global.vars.gCasePersons.idenDocTypeDesc = doctypedesc;
            Global.vars.gCasePersons.idenDocType = doctype;
            Global.vars.gCasePersons.documentNumber = docnumber;
            Global.vars.gCasePersons.countryIdenDoc = docCountry;
            Global.vars.gCasePersons.countryIdenDocDesc = docCountryDesc;
            Global.vars.gCasePersons.documentTypeCheckable = documentTypeCheckable;
            Global.vars.gCasePersons.documentNumberChecked = documentNumberChecked;
            Global.vars.gCasePersons.documentNumberValid = documentNumberValid;
            // end set preserved document
            Global.vars.gCasePersons.indRegCalled = true; // when succesfull call
            Global.vars.gCasePersons.givenNames = result.person[0].firstname;
            //get intitials from names
            if (result.person[0].firstname !== "" && result.person[0].firstname != "null") {
                var initials = "";
                var str = Global.vars.gCasePersons.givenNames + "";
                str = str.split(" ");
                for (i = 0; i < str.length; i++) {
                    initials += str[i].substr(0, 1) + ".";
                    initials = initials.toUpperCase();
                }
                voltmx.print("#### frmPersonResult_callbackfunctionBSN initials: " + initials);
                Global.vars.gCasePersons.initials = initials;
            } else {
                // if no givenNames then proceed with first part of surname and set intial.
                var _initial = "";
                if (result.person[0].surname !== "" && result.person[0].surname != "null") {
                    _initial = result.person[0].surname.substr(0, 1);
                } else {
                    _initial = "o"; // onbekend
                }
                _initial = _initial.toUpperCase();
                voltmx.print("#### frmPersonResult_callbackfunctionBSN alternative initials: " + _initial);
                Global.vars.gCasePersons.givenNames = _initial;
                Global.vars.gCasePersons.initials = _initial + ".";
            }
            //end get initials
            Global.vars.gCasePersons.surname = result.person[0].surname;
            if (result.person[0].middlename === undefined || result.person[0].middlename == "null") {
                Global.vars.gCasePersons.middlename = "";
            } else {
                if (result.person[0].middlename != "null" && result.person[0].middlename.length > 0) {
                    var formatMiddlename = result.person[0].middlename.toLowerCase();
                    Global.vars.gCasePersons.middlename = formatMiddlename.trim();
                } else {
                    Global.vars.gCasePersons.middlename = "";
                }
            }
            var _birthDate = null;
            var _birthYear = null;
            var _age = null;
            voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].dateofbirth: " + result.person[0].dateofbirth);
            if (result.person[0].dateofbirth != null && result.person[0].dateofbirth != "null" && result.person[0].dateofbirth.length > 0) {
                voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].dateofbirth: " + result.person[0].dateofbirth);
                var date = result.person[0].dateofbirth + "";
                var replacedate = (date).replace(/-/g, "/");
                var dateParts = replacedate.split("/");
                var year = dateParts[0];
                var month = dateParts[1];
                var day = dateParts[2];
                voltmx.print("#### frmPersonResult_callbackfunctionBSN year: " + year);
                if (month == 00 || day == 00) {
                    if (day == 00) {
                        day = 01;
                    }
                    if (month == 00) {
                        month = 01;
                    }
                    // if year only
                    // set birthdate
                    _birthDate = (year + "-" + month + "-" + day).toDate("yyyy-mm-dd");
                    voltmx.print("#### frmPersonResult_callbackfunctionBSN _birthDate: " + JSON.stringify(_birthDate));
                    //end set birthdate
                    _birthYear = _birthDate.year;
                    Global.vars.gCasePersons.age = Utility_getAge(_birthDate);
                    Global.vars.gCasePersons.birthdate = null;
                    Global.vars.gCasePersons.birthdateDesc = year + "";
                } else {
                    //if full date
                    // set birthdate
                    var birthDateJavascript = new Date((year + "/" + month + "/" + day));
                    voltmx.print("#### frmPersonResult_callbackfunctionBSN birthDateJavascript: " + birthDateJavascript);
                    _birthDate = (year + "-" + month + "-" + day).toDate("yyyy-mm-dd");
                    voltmx.print("#### frmPersonResult_callbackfunctionBSN _birthDate: " + JSON.stringify(_birthDate));
                    //end set birthdate
                    _birthYear = _birthDate.year;
                    Global.vars.gCasePersons.age = Utility_getAge(_birthDate);
                    Global.vars.gCasePersons.birthdate = Utility_getDateTimeString(_birthDate);
                    Global.vars.gCasePersons.birthdateDesc = Utility_getLocaleShortDateString(birthDateJavascript);
                    Global.vars.gCasePersons.birthDateSet = true;
                }
                // set datecomponent
                var receiveddate = [_birthDate.day, _birthDate.month, _birthDate.year, 0, 0, 0];
                Global.vars.gCasePersons.birthdateComponents = receiveddate;
                voltmx.print("#### frmPersonResult_callbackfunctionBSN receiveddate: " + JSON.stringify(receiveddate));
                voltmx.print("#### Date of birth datecomponents receiveddate: " + receiveddate);
                Global.vars.gCasePersons.yearOfBirth = _birthYear;
                // end set to datecomponent
            } else {
                Global.vars.gCasePersons.age = null;
                Global.vars.gCasePersons.birthdate = null;
                Global.vars.gCasePersons.birthdateDesc = null;
                Global.vars.gCasePersons.yearOfBirth = _birthYear;
                Global.vars.gCasePersons.birthDateSet = false;
            }
            voltmx.print("#### frmPersonResult_callbackfunctionBSN YearOfBirth: " + Global.vars.gCasePersons.yearOfBirth);
            voltmx.print("#### frmPersonResult_callbackfunctionBSN Birthdate: " + Global.vars.gCasePersons.birthdate);
            voltmx.print("#### frmPersonResult_callbackfunctionBSN BirthdateDesc: " + Global.vars.gCasePersons.birthdateDesc);
            voltmx.print("#### frmPersonResult_callbackfunctionBSN Age: " + Global.vars.gCasePersons.age);
            Global.vars.gCasePersons.ssn = result.person[0].socialsecuritynr.lpad("0", 9);
            voltmx.print("#### frmPersonResult_callbackfunctionBSN ssn: " + Global.vars.gCasePersons.ssn);
            if (result.person[0].placeofbirthforeign !== undefined && result.person[0].placeofbirthforeign !== "" && result.person[0].placeofbirthforeign != "null") {
                Global.vars.gCasePersons.birthplace = result.person[0].placeofbirthforeign;
                voltmx.print("#### frmPersonResult_callbackfunctionBSN Birthplace: " + Global.vars.gCasePersons.birthplace);
            }
            if (result.person[0].birthMunicipality !== undefined && result.person[0].birthMunicipality !== "" && result.person[0].birthMunicipality != "null") {
                Global.vars.gCasePersons.birthMunicipNLDesc = result.person[0].birthMunicipality;
                Global.vars.gCasePersons.birthMunicipNL = result.person[0].birthMunicipalityCode; //controleren
            }
            Global.vars.gCasePersons.gender = Number(result.person[0].gendercode);
            Global.vars.gCasePersons.genderDesc = result.person[0].gender;
            try {
                if (result.person[0].countryoforigincode !== undefined && result.person[0].countryoforigincode !== "" && result.person[0].countryoforigincode != "null") {
                    Global.vars.gCasePersons.countryOfBirth = Number(result.person[0].countryoforigincode);
                    voltmx.print("#### frmPersonResult_callbackfunctionBSN CountryOfBirthDesc: " + Global.vars.gCasePersons.countryOfBirth);
                } else {
                    Global.vars.gCasePersons.countryOfBirth = 0; //Onbekend
                    voltmx.print("#### frmPersonResult_callbackfunctionBSN countryoforigin is empty");
                }
            } catch (error) {
                Global.vars.gCasePersons.countryOfBirth = 0; //Onbekend
                voltmx.print("#### frmPersonResult_callbackfunctionBSN countryoforigin is empty " + JSON.stringify(error));
            }
            try {
                if (result.person[0].countryoforigin !== undefined && result.person[0].countryoforigin !== "" && result.person[0].countryoforigin != "null") {
                    Global.vars.gCasePersons.countryOfBirthDesc = result.person[0].countryoforigin;
                    voltmx.print("#### frmPersonResult_callbackfunctionBSN CountryOfBirthDesc: " + Global.vars.gCasePersons.countryOfBirthDesc);
                } else {
                    voltmx.print("#### frmPersonResult_callbackfunctionBSN countryoforigin is empty");
                }
            } catch (error) {
                voltmx.print("#### frmPersonResult_callbackfunctionBSN countryoforigin is empty " + JSON.stringify(error));
            }
            //Global.vars.gCasePersons.addresses = [];
            //Global.vars.gCasePersons.addresses.push(Global.vars.AddressInit);
            voltmx.print("#### frmPersonResult_enterAddress Global.vars.gCasePersons.addresses: " + JSON.stringify(Global.vars.gCasePersons.addresses));
            Global.vars.gCasePersons.addresses[0].addressSubType = "SA";
            //frmPersonResult_clearAddress();
            voltmx.print("#### frmPersonResult_callbackfunctionBSN Global.vars.gPersonAdressType: " + JSON.stringify(Global.vars.gPersonAdressType));
            //get adresstype description
            for (var p = 0; Global.vars.gPersonAdressType != null && p < Global.vars.gPersonAdressType.length; p++) {
                var w = Global.vars.gPersonAdressType[p];
                voltmx.print("#### frmPersonResult_callbackfunctionBSN Global.vars.gPersonAdressType: " + JSON.stringify(w) + " index: " + p);
                if ((w.string_value == result.person[0].addressTypeCode)) {
                    Global.vars.gCasePersons.addresses[Global.vars.gCasePersonsIndex].addressTypeDesc = w.descripton;
                    voltmx.print("#### frmPersonResult_callbackfunctionBSN Global.vars.gCasePersons.addresses[0].AddressTypeDesc: " + Global.vars.gCasePersons.addresses[0].addressTypeDesc);
                    break;
                }
            }
            if (result.person[0].secretind == "true") {
                voltmx.print("#### 1");
                Global.vars.gCasePersons.addresses[0].indSecret = true;
                Global.vars.gCasePersons.addresses[0].addressType = addressType.emptyDataGBA.value;
                Global.vars.gCasePersons.addresses[0].addressSubType = "";
                Global.vars.gCasePersons.addresses[0].street = null;
                Global.vars.gCasePersons.addresses[0].streetNumber = null;
                Global.vars.gCasePersons.addresses[0].streetNumAdditn = null;
            } else {
                voltmx.print("#### 2");
                Global.vars.gCasePersons.addresses[0].indSecret = 0;
                voltmx.print("##### frmPersonResult_callbackfunctionBSN result.person[0].country: " + result.person[0].country);
                voltmx.print("##### frmPersonResult_callbackfunctionBSN result.person[0].countrycode: " + result.person[0].countrycode);
                if (result.person[0].country !== "null") {
                    Global.vars.gCasePersons.addresses[0].country = result.person[0].country;
                }
                if (result.person[0].countrycode != null && result.person[0].countrycode !== "null") {
                    Global.vars.gCasePersons.addresses[0].countryCode = Number(result.person[0].countrycode);
                } else {
                    Global.vars.gCasePersons.addresses[0].countryCode = 0;
                }
                voltmx.print("##### frmPersonResult_callbackfunctionBSN Global.vars.gCasePersons.addresses[0].countryCode: " + Global.vars.gCasePersons.addresses[0].countryCode);
                Global.vars.gCasePersons.addresses[0].source = "01"; // source is basisregister, use 03 for manual, use 02 for orther register
                voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].postponedreason: " + result.person[0].postponedreason);
                if (result.person[0].postponedreason !== undefined && result.person[0].postponedreason != "null") {
                    voltmx.print("#### 2a");
                    Global.vars.gCasePersons.addresses[0].IndInResearch = 1;
                }
                if ((result.person[0].addressline1 !== undefined && result.person[0].addressline1 != "null") || (result.person[0].addressline2 !== undefined && result.person[0].addressline2 != "null") || (result.person[0].addressline3 !== undefined && result.person[0].addressline3 != "null")) { //check if foreign address is filled
                    voltmx.print("#### 3");
                    Global.vars.gCasePersons.addresses[0].addressType = addressType.stayAddress.value; // hardcoded set to woon/verblijfadres 02 when foreign address
                    var _validAddressline2 = true;
                    if (result.person[0].addressline2 != null && result.person[0].addressline2 != "null" && Global.vars.isNumberCheckOnAddressline2 === true) {
                        voltmx.print("##### frmPersonResult_callbackfunctionBSN result.person[0].addressline2: " + result.person[0].addressline2);
                        _validAddressline2 = Utility_isThisNumeric(result.person[0].addressline2);
                        voltmx.print("##### frmPersonResult_callbackfunctionBSN _validAddressline2: " + _validAddressline2);
                    }
                    if (Global.vars.gCasePersons.addresses[0].countryCode !== 0 && _validAddressline2 === true) {
                        if (result.person[0].addressline1 != null && result.person[0].addressline1 != "null") {
                            Global.vars.gCasePersons.addresses[0].addressLine1 = result.person[0].addressline1;
                        }
                        if (result.person[0].addressline2 != null && result.person[0].addressline2 != "null") {
                            Global.vars.gCasePersons.addresses[0].addressLine2 = result.person[0].addressline2;
                        }
                        if (result.person[0].addressline3 != null && result.person[0].addressline3 != "null") {
                            Global.vars.gCasePersons.addresses[0].addressLine3 = result.person[0].addressline3;
                        }
                    }
                    Global.vars.gCasePersons.addresses[0].addressSubType = null;
                    voltmx.print("##### frmPersonResult_callbackfunctionBSN Global.vars.gCasePersons.addresses[0]: " + JSON.stringify(Global.vars.gCasePersons.addresses[0]));
                } else {
                    voltmx.print("#### 4");
                    if (result.person[0].addressTypeCode !== undefined && result.person[0].addressTypeCode != "null") {
                        if (result.person[0].addressTypeCode.length > 0) {
                            Global.vars.gCasePersons.addresses[0].addressType = result.person[0].addressTypeCode;
                            if (Global.vars.gCasePersons.addresses[0].addressType == addressType.emptyDataGBA.value || Global.vars.gCasePersons.addresses[0].addressType == addressType.withoutFixedStay.value || Global.vars.gCasePersons.addresses[0].addressType == addressType.departedDestinationUnknown.value) {
                                Global.vars.gCasePersons.addresses[0].country = "";
                                Global.vars.gCasePersons.addresses[0].countryCode = 0;
                                Global.vars.gCasePersons.addresses[0].addressSubType = null;
                            }
                        } else {
                            Global.vars.gCasePersons.addresses[0].addressType = addressType.stayAddress.value;
                        }
                    } else {
                        Global.vars.gCasePersons.addresses[0].addressType = addressType.stayAddress.value;
                    }
                    Global.vars.gCasePersons.addresses[0].street = result.person[0].street;
                    Global.vars.gCasePersons.addresses[0].streetNumber = result.person[0].streetnumber;
                    var houseletter = "";
                    var housenumberaddition = "";
                    if (result.person[0].houseletter !== undefined && result.person[0].houseletter != "null") {
                        houseletter = result.person[0].houseletter;
                    }
                    if (result.person[0].housenumberaddition !== undefined && result.person[0].housenumberaddition != "null") {
                        housenumberaddition = result.person[0].housenumberaddition;
                    }
                    Global.vars.gCasePersons.addresses[0].streetNumAdditn = (houseletter + " " + housenumberaddition).trim();
                    Global.vars.gCasePersons.addresses[0].zipcode = result.person[0].zipcode;
                }
                voltmx.print("#### 5");
                Global.vars.gCasePersons.addresses[0].cityCode = result.person[0].placeofresidencycode;
                Global.vars.gCasePersons.addresses[0].city = result.person[0].placeofresidency;
                Global.vars.gCasePersons.addresses[0].municipality = result.person[0].municipality;
                Global.vars.gCasePersons.addresses[0].municipalCode = result.person[0].municipalitycode;
                if (result.person[0].street == "Postbus") {
                    voltmx.print("#### 6");
                    Global.vars.gCasePersons.addresses[0].postOfficeBox = result.person[0].streetnumber; //if street is Postbus
                    Global.vars.gCasePersons.addresses[0].street = "";
                    Global.vars.gCasePersons.addresses[0].streetNumber = "";
                    Global.vars.gCasePersons.addresses[0].cityCode = result.person[0].municipalitycode; // if street is Postbus
                    Global.vars.gCasePersons.addresses[0].city = result.person[0].municipality; // if street is Postbus
                    Global.vars.gCasePersons.addresses[0].addressType = addressType.postalAddress.value;
                    Global.vars.gCasePersons.addresses[0].addressSubType = "PA";
                }
            }
            //check postponedreason		
            voltmx.print("#### frmPersonResult_callbackfunctionBSN adresstype: " + result.person[0].adresstype);
            if ((result.person[0].adresstype == addressType.emptyDataGBA.code && result.person[0].addressTypeCode == addressType.emptyDataGBA.value) || ((Global.vars.gCasePersons.addresses[0].countryCode === 0 || Global.vars.gCasePersons.addresses[0].countryCode === null) && Global.vars.gCasePersons.addresses[0].addressType != addressType.withoutFixedStay.value) && (Global.vars.gCasePersons.personFilled === true)) {
                voltmx.application.dismissLoadingScreen();
                var popUpText = voltmx.i18n.getLocalizedString("l_enteraddress") + ".";
                if (result.person[0].postponedreason == "E") {
                    voltmx.print("#### frmPersonResult_callbackfunctionBSN adresstype 2 ");
                    popUpText = voltmx.i18n.getLocalizedString("i_emigrate") + ". " + popUpText;
                }
                if (Global.vars.gCaseVehicles.vehicleOwnerUsed === true) {
                    if (Global.vars.gCaseVehicles.vehicleOwnerName.indexOf(Global.vars.gCasePersons.surname) == -1) {
                        popUpText = voltmx.i18n.getLocalizedString("i_mismatch") + ". " + popUpText;
                    }
                }
                if (Global.vars.gCasePersons.yearOfBirth === null) {
                    popUpText = "Persoon heeft geen geboortedatum of geboortejaar, voer geboortedatum of jaar op" + ". " + popUpText;
                    voltmx.ui.Alert(popUpText, frmPersonResult_enterDateOfBirth, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
                } else {
                    voltmx.ui.Alert(popUpText, frmPersonResult_enterAddress, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
                }
            } else if (Global.vars.gCasePersons.yearOfBirth === null) {
                voltmx.ui.Alert("Persoon heeft geen geboortedatum of geboortejaar, voer geboortedatum of jaar op", frmPersonResult_enterDateOfBirth, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
            } else {
                if (result.person[0].postponedreason == "E") {
                    voltmx.ui.Alert(voltmx.i18n.getLocalizedString("i_emigrate"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
                } else if (result.person[0].postponedreason == "O") {
                    voltmx.ui.Alert(voltmx.i18n.getLocalizedString("i_deceased"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
                } else if (result.person[0].postponedreason == "F") {
                    voltmx.ui.Alert(voltmx.i18n.getLocalizedString("i_faulted"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
                } else if (Global.vars.gCasePersons.age < Global.vars.minAge) {
                    voltmx.ui.Alert("Let op! Persoon is jonger dan " + Number(Global.vars.minAge).toString(), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
                } else if (Global.vars.gCasePersons.age > Global.vars.maxAge) {
                    voltmx.ui.Alert("Let op! Persoon is ouder dan " + Number(Global.vars.maxAge).toString(), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
                }
                if (Global.vars.gCaseVehicles.vehicleOwnerUsed === true) {
                    if (Global.vars.gCaseVehicles.vehicleOwnerName.indexOf(Global.vars.gCasePersons.surname) == -1) {
                        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("i_mismatch"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
                    }
                }
            }
            if (Global.vars.previousForm == "frmPersonAlternativeSearch") {
                frmPersonAlternativeSearch_clearAllFields();
            }
            frmPersonResult.show();
        } else if (Number(result.person[0].wsstatuscode) == 2) {
            voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].wsstatuscode: " + Number(result.person[0].wsstatuscode));
            voltmx.print("#### frmPersonResult_callbackfunctionBSN result: " + JSON.stringify(result));
            voltmx.print("### frmPersonResult_callbackfunctionBSN stop loader 3");
            voltmx.application.dismissLoadingScreen();
            voltmx.print("### frmPersonResult_callbackfunctionBSN too many results");
            voltmx.ui.Alert(voltmx.i18n.getLocalizedString("i_tooManyPersonRecords"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
        } else if (Number(result.person[0].wsstatuscode) == 1) {
            voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].wsstatuscode: " + Number(result.person[0].wsstatuscode));
            voltmx.print("#### frmPersonResult_callbackfunctionBSN result: " + JSON.stringify(result));
            voltmx.print("### frmPersonResult_callbackfunctionBSN stop loader 5");
            voltmx.application.dismissLoadingScreen();
            voltmx.print("### frmPersonResult_callbackfunctionBSN person not found");
            voltmx.ui.Alert(voltmx.i18n.getLocalizedString("i_personNotFound"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
        } else {
            voltmx.print("#### frmPersonResult_callbackfunctionBSN result.person[0].wsstatuscode 4: " + Number(result.person[0].wsstatuscode));
            voltmx.print("#### frmPersonResult_callbackfunctionBSN result: " + JSON.stringify(result));
            voltmx.application.dismissLoadingScreen();
            var messageText = result.person[0].wsstatustext;
            if (voltmx.string.startsWith(messageText, "Fout in") === true) {
                messageText = voltmx.i18n.getLocalizedString("e_ser0002");
            } else if (voltmx.string.startsWith(messageText, "Persoon niet") === true) {
                messageText = messageText; //voltmx.i18n.getLocalizedString("e_ser0003");
            }
            voltmx.ui.Alert(messageText, null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
        }
    } else {
        voltmx.application.dismissLoadingScreen();
        voltmx.ui.Alert("BSN - " + voltmx.i18n.getLocalizedString("e_ser0001"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
    }
}

function frmPersonResult_enterAddress(response) {
    if (response) {
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        frmPersonManualAddress.show();
    }
}

function frmPersonResult_enterDateOfBirth(response) {
    if (response) {
        Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
        frmPersonManualPerson.show();
    }
}

function frmPersonResult_btnback() {
    voltmx.print("### frmPersonResult_btnback Global.vars.previousForm: " + Global.vars.previousForm);
    if (Global.vars.previousForm == "frmPerson") {
        frmPerson.show();
    } else if (Global.vars.previousForm == "frmPersonSSN") {
        frmPersonSSN.show();
    } else if (Global.vars.previousForm == "frmPersonManualPerson") {
        frmPersonManualPerson.show();
    } else if (Global.vars.previousForm == "frmPersonManualAddress") {
        frmPersonManualAddress.show();
    } else if (Global.vars.previousForm == "frmPersonAlternativeSearch") {
        frmPersonAlternativeSearch.show();
    } else if (Global.vars.previousForm == "frmResume") {
        frmResume.show();
    } else if (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") {
        frmRegister.show();
    } else if (Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_registerconcept") && Global.vars.cameToPersonFromForm !== "") {
        if (Global.vars.cameToPersonFromForm == "frmRegisterConcept") {
            frmRegisterConcept.show();
        } else if (Global.vars.cameToPersonFromForm == "frmRegisterResume") {
            frmRegisterResume.show();
        }
    } else if (Global.vars.previousForm == "frmPersonResult") {
        frmResume.show();
    }
}

function frmPersonResult_onDone() {
    Global.vars.gCasePersons.index = Global.vars.gCasePersonsIndex;
    if (Global.vars.gCasePersons.yearOfBirth !== undefined && Global.vars.gCasePersons.yearOfBirth != null && Global.vars.gCasePersons.yearOfBirth !== "") {
        Global.vars.gCasePersons.yearOfBirth = Number(Global.vars.gCasePersons.yearOfBirth);
    }
    CaseData.person[Global.vars.gCasePersonsIndex] = Global.vars.gCasePersons;
    voltmx.print("### frmPersonResult_onDone Global.vars.gCasePersonsIndex: " + Global.vars.gCasePersonsIndex);
    voltmx.print("### frmPersonResult_onDone CaseData.person: " + JSON.stringify(CaseData.person[Global.vars.gCasePersonsIndex]));
    voltmx.print("### frmPersonResult_onDone CaseData text: " + JSON.stringify(CaseData.text));
    voltmx.print("### frmPersonResult_onDone CaseData.person index: " + CaseData.person[Global.vars.gCasePersonsIndex].index);
    // set edited to true
    CaseData.person[Global.vars.gCasePersonsIndex].validated = true; // once person is filled set to true
    CaseData.person[Global.vars.gCasePersonsIndex].edited = true; // once person is filled set to true
    CaseData.person[Global.vars.gCasePersonsIndex].used = true; // once person is filled set to true
    CaseData.offence.person = true; // once person is filled set to true
    if (Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") {
        if (Global.vars.openedFromResume === true) {
            Global.vars.openedFromResume = false;
            Global.vars.previousForm = Global.vars.openedFromResumePreviousForm;
            frmResume.show();
        } else {
            frmRegister.show();
        }
    } else if (Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_registerconcept") && Global.vars.cameToPersonFromForm !== "") {
        if (Global.vars.cameToPersonFromForm == "frmRegisterConcept") {
            frmRegisterConcept.show();
        } else if (Global.vars.cameToPersonFromForm == "frmRegisterResume") {
            frmRegisterResume.show();
        }
    } else {
        if (Global.vars.historyEnabled === true) {
            frmResume_historyCheck();
        }
        frmResume.show();
    }
}

function frmPersonResult_showAlertAddAnother() {
    voltmx.ui.Alert("Wilt u nog een persoon opvoeren?", frmPersonResult_confirm_AddNewPerson, "confirmation", voltmx.i18n.getLocalizedString("bt_yes"), voltmx.i18n.getLocalizedString("bt_no"), "Info", null);
}

function frmPersonResult_confirm_AddNewPerson(response) {
    if (response) {
        frmPersonResult_AddNewPerson();
    } else {
        frmResume.show();
    }
}

function frmPersonResult_AddNewPerson() {
    Global.vars.gCasePersonsIndex = CaseData.person.length;
    Global.vars.gCasePersons = CaseData_setNewperson(Global.vars.gCasePersonsIndex);
    CaseData.person.push(Global.vars.gCasePersons);
    frmPerson.show();
}

function frmPersonResult_onclickBtnEdit() {
    voltmx.print("### frmPersonResult_onclickBtnEdit Global.vars.gCasePersonsIndex: " + Global.vars.gCasePersonsIndex);
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    Global.vars.gCasePersons = {};
    if (CaseData.person[Global.vars.gCasePersonsIndex] !== undefined) {
        Global.vars.gCasePersons = JSON.parse(JSON.stringify(CaseData.person[Global.vars.gCasePersonsIndex]));
    }
    voltmx.print("### frmPersonResult_onclickBtnEdit Global.vars.gCasePersons: " + JSON.stringify(Global.vars.gCasePersons));
    Global.vars.setEditPerson = true;
    frmPerson.show();
}

function frmPersonResult_printPersonData() {
    Print.initLabel();
    Print.addEmptyLine();
    Print.addSectionHeader(voltmx.i18n.getLocalizedString("l_persondata"), 0);
    if (Global.vars.gCasePersons.middlename !== "" && Global.vars.gCasePersons.middlename != null) {
        Print.addLineNoLabel(Global.vars.gCasePersons.initials + " " + Global.vars.gCasePersons.middlename + " " + Global.vars.gCasePersons.surname, true, false, 1);
    } else {
        Print.addLineNoLabel(Global.vars.gCasePersons.initials + " " + Global.vars.gCasePersons.surname, true, false, 1);
    }
    // adress choice
    voltmx.print("#### Addresses: " + JSON.stringify(Global.vars.gCasePersons.addresses[0]));
    if (Global.vars.gCasePersons.addresses[0].countryCode === "" || Global.vars.gCasePersons.addresses[0].countryCode === null) {
        Print.addLineNoLabel(Global.vars.gCasePersons.addresses[0].addressTypeDesc, true, false, 0);
        Print.addSmallEmptyLine();
    } else if (Global.vars.gCasePersons.addresses[0].countryCode == Global.vars.CountryCode) {
        if (Global.vars.gCasePersons.addresses[0].street != null) {
            if (Global.vars.gCasePersons.addresses[0].streetNumAdditn !== undefined) {
                Print.addLineNoLabel(Global.vars.gCasePersons.addresses[0].street + " " + Global.vars.gCasePersons.addresses[0].streetNumber + Global.vars.gCasePersons.addresses[0].streetNumAdditn, true, false, 0);
            } else {
                Print.addLineNoLabel(Global.vars.gCasePersons.addresses[0].street + " " + Global.vars.gCasePersons.addresses[0].streetNumber, true, false, 0);
            }
        } else if (Global.vars.gCasePersons.addresses[0].postOfficeBox != null) {
            Print.addLineNoLabel("Postbus " + Global.vars.gCasePersons.addresses[0].postOfficeBox, true, false, 0);
        } else if (Global.vars.gCasePersons.addresses[0].replyNumber != null) {
            Print.addLineNoLabel("Antwoordnummer " + Global.vars.gCasePersons.addresses[0].replyNumber, true, false, 0);
        }
        Print.addLineNoLabel(Global.vars.gCasePersons.addresses[0].zipcode + " " + Global.vars.gCasePersons.addresses[0].city, true, false, 0);
        Print.addLineNoLabel(Global.vars.gCasePersons.addresses[0].country, true, false, 0);
        Print.addSmallEmptyLine();
    } else {
        if (Global.vars.gCasePersons.addresses[0].addressLine1 !== "") {
            Print.addLineNoLabel(Global.vars.gCasePersons.addresses[0].addressLine1, true, false, 0);
        }
        if (Global.vars.gCasePersons.addresses[0].addressLine2 !== "") {
            Print.addLineNoLabel(Global.vars.gCasePersons.addresses[0].addressLine2, true, false, 0);
        }
        if (Global.vars.gCasePersons.addresses[0].addressLine3 !== "") {
            Print.addLineNoLabel(Global.vars.gCasePersons.addresses[0].addressLine3, true, false, 0);
        }
        Print.addLineNoLabel(Global.vars.gCasePersons.addresses[0].country, true, false, 0);
        Print.addSmallEmptyLine();
    }
    if (Global.vars.gCasePersons.idenDocTypeDesc != null) {
        Print.addLineNoLabel(Global.vars.gCasePersons.idenDocTypeDesc + ", " + Global.vars.gCasePersons.documentNumber, true, false, 0);
    }
    // i18n
    if (Global.vars.gCasePersons.countryOfBirth == Global.vars.CountryCode) {
        Print.addLineNoLabel(Global.vars.gCasePersons.birthdateDesc + " " + Global.vars.gCasePersons.birthMunicipNLDesc + " " + Global.vars.gCasePersons.countryOfBirthDesc, true, false, 0);
    } else {
        Print.addLineNoLabel(Global.vars.gCasePersons.birthdateDesc + " " + Global.vars.gCasePersons.birthplace + " " + Global.vars.gCasePersons.countryOfBirthDesc, true, false, 0);
    }
    Print.addLineNoLabel(voltmx.i18n.getLocalizedString("l_nationality") + ": " + Global.vars.gCasePersons.nationalityDesc, true, false, 0);
    Print.addLineNoLabel(voltmx.i18n.getLocalizedString("l_gender") + ": " + Global.vars.gCasePersons.genderDesc, true, false, 0);
    if (Global.vars.gCasePersons.SSN !== "") {
        Print.addLineNoLabel(voltmx.i18n.getLocalizedString("l_socialsecuritynr") + ": " + Global.vars.gCasePersons.ssn, true, false, 0);
    }
    Print.addSectionHeader(" ", 0);
    var time = new Date();
    var lDateTime = Utility_getLocalizedDateTimeString(time, true);
    Print.addLineTwoValues(voltmx.i18n.getLocalizedString("l_officerNumberPrint") + " " + Global.vars.gOfficerNumber + "", lDateTime, true, 0);
    Print.addEmptyLine();
    //
    Print.callback = frmPersonResult_printCallback;
    //
    Print.executePrint();
}

function frmPersonResult_printCallback() {
    voltmx.application.dismissLoadingScreen();
    // reset gCase object
    // show message if print not succesful
    if (Print.printSuccessful == false) {
        voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_prt0001"), null, "info", voltmx.i18n.getLocalizedString("bt_ok"), null, voltmx.i18n.getLocalizedString("l_info"), null);
    }
}