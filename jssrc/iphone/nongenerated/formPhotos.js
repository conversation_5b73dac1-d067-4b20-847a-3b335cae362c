var _getCurrentForm = null;
var _photopop = 0;

function frmPhotos_Camera1_setVisibility(boolean) {
    voltmx.print("### frmPhotos_Camera1_setVisibility");

    function Camera1_setVisibility() {
        voltmx.print("### frmPhotos_Camera1_setVisibility Camera1_setVisibility: " + boolean);
        frmPhotos.Camera1.setVisibility(boolean);
    }
    voltmx.runOnMainThread(Camera1_setVisibility, []);
}

function frmPhotos_flcPhoto1_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcPhoto1_setVisibility");

    function flcPhoto1_setVisibility() {
        voltmx.print("### frmPhotos_flcPhoto1_setVisibility flcPhoto1_setVisibility: " + boolean);
        frmPhotos.flcPhoto1.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcPhoto1_setVisibility, []);
}

function frmPhotos_flcPhoto2_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcPhoto2_setVisibility");

    function flcPhoto2_setVisibility() {
        voltmx.print("### frmPhotos_flcPhoto2_setVisibility flcPhoto2_setVisibility: " + boolean);
        frmPhotos.flcPhoto2.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcPhoto2_setVisibility, []);
}

function frmPhotos_flcPhoto3_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcPhoto3_setVisibility");

    function flcPhoto3_setVisibility() {
        voltmx.print("### frmPhotos_flcPhoto3_setVisibility flcPhoto3_setVisibility: " + boolean);
        frmPhotos.flcPhoto3.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcPhoto3_setVisibility, []);
}

function frmPhotos_lblMorePhotos_setVisibility(boolean) {
    voltmx.print("### frmPhotos_lblMorePhotos_setVisibility");

    function lblMorePhotos_setVisibility() {
        voltmx.print("### frmPhotos_lblMorePhotos_setVisibility lblMorePhotos_setVisibility: " + boolean);
        frmPhotos.lblMorePhotos.setVisibility(boolean);
    }
    voltmx.runOnMainThread(lblMorePhotos_setVisibility, []);
}

function frmPhotos_flcNoMedia_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcNoMedia_setVisibility");

    function flcNoMedia_setVisibility() {
        voltmx.print("### frmPhotos_flcNoMedia_setVisibility flcNoMedia_setVisibility: " + boolean);
        frmPhotos.flcNoMedia.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcNoMedia_setVisibility, []);
}

function frmPhotos_flcSetReceipt_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcSetReceipt_setVisibility");

    function flcSetReceipt_setVisibility() {
        voltmx.print("### frmPhotos_flcSetReceipt_setVisibility flcSetReceipt_setVisibility: " + boolean);
        frmPhotos.flcSetReceipt.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcSetReceipt_setVisibility, []);
}

function frmPhotos_flcFooterMainChooseReceiptDelete_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcFooterMainChooseReceiptDelete_setVisibility");

    function flcFooterMainChooseReceiptDelete_setVisibility() {
        voltmx.print("### frmPhotos_flcFooterMainChooseReceiptDelete_setVisibility flcFooterMainChooseReceiptDelete_setVisibility: " + boolean);
        frmPhotos.flcFooterMainChooseReceiptDelete.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcFooterMainChooseReceiptDelete_setVisibility, []);
}

function frmPhotos_flcFooterMainPhoto_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcFooterMainPhoto_setVisibility");

    function flcFooterMainPhoto_setVisibility() {
        voltmx.print("### frmPhotos_flcFooterMainPhoto_setVisibility flcFooterMainPhoto_setVisibility: " + boolean);
        frmPhotos.flcFooterMainPhoto.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcFooterMainPhoto_setVisibility, []);
}

function frmPhotos_flcEmptyDataset_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcEmptyDataset_setVisibility");

    function flcEmptyDataset_setVisibility() {
        voltmx.print("### frmPhotos_flcEmptyDataset_setVisibility flcEmptyDataset_setVisibility: " + boolean);
        frmPhotos.flcEmptyDataset.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcEmptyDataset_setVisibility, []);
}

function frmPhotos_btnDone_setVisibility(boolean) {
    voltmx.print("### frmPhotos_btnDone_setVisibility");

    function btnDone_setVisibility() {
        voltmx.print("### frmPhotos_btnDone_setVisibility btnDone_setVisibility: " + boolean);
        frmPhotos.btnDone.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnDone_setVisibility, []);
}

function frmPhotos_flcButtonLeft_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcButtonLeft_setVisibility");

    function flcButtonLeft_setVisibility() {
        voltmx.print("### frmPhotos_flcButtonLeft_setVisibility flcButtonLeft_setVisibility: " + boolean);
        frmPhotos.flcButtonLeft.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcButtonLeft_setVisibility, []);
}

function frmPhotos_flcCaptureButton_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcCaptureButton_setVisibility");

    function flcCaptureButton_setVisibility() {
        voltmx.print("### frmPhotos_flcCaptureButton_setVisibility flcCaptureButton_setVisibility: " + boolean);
        frmPhotos.flcCaptureButton.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcCaptureButton_setVisibility, []);
}

function frmPhotos_flcRemove_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcRemove_setVisibility");

    function flcRemove_setVisibility() {
        voltmx.print("### frmPhotos_flcRemove_setVisibility flcRemove_setVisibility: " + boolean);
        frmPhotos.flcRemove.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcRemove_setVisibility, []);
}

function frmPhotos_flcFooterMain_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcFooterMain_setVisibility");

    function flcFooterMain_setVisibility() {
        voltmx.print("### frmPhotos_flcFooterMain_setVisibility flcFooterMain_setVisibility: " + boolean);
        frmPhotos.flcFooterMain.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcFooterMain_setVisibility, []);
}

function frmPhotos_flcSegPhotosSelect_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcSegPhotosSelect_setVisibility");

    function flcSegPhotosSelect_setVisibility() {
        voltmx.print("### frmPhotos_flcSegPhotosSelect_setVisibility flcSegPhotosSelect_setVisibility: " + boolean);
        frmPhotos.flcSegPhotosSelect.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcSegPhotosSelect_setVisibility, []);
}

function frmPhotos_flcSegPhotos_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcSegPhotos_setVisibility");

    function flcSegPhotos_setVisibility() {
        voltmx.print("### frmPhotos_flcSegPhotos_setVisibility flcSegPhotos_setVisibility: " + boolean);
        frmPhotos.flcSegPhotos.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcSegPhotos_setVisibility, []);
}

function frmPhotos_btnPhoto_setVisibility(boolean) {
    voltmx.print("### frmPhotos_btnPhoto_setVisibility");

    function btnPhoto_setVisibility() {
        voltmx.print("### frmPhotos_btnPhoto_setVisibility btnPhoto_setVisibility: " + boolean);
        frmPhotos.btnPhoto.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnPhoto_setVisibility, []);
}

function frmPhotos_frmANPROverlay_btnDone_setVisibility(boolean) {
    voltmx.print("### frmPhotos_frmANPROverlay_btnDone_setVisibility");

    function btnDone_setVisibility() {
        voltmx.print("### frmPhotos_frmANPROverlay_btnDone_setVisibility btnDone_setVisibility: " + boolean);
        frmANPROverlay.btnDone.setVisibility(boolean);
    }
    voltmx.runOnMainThread(btnDone_setVisibility, []);
}

function frmPhotos_frmANPROverlay_flcCaptureButton_setVisibility(boolean) {
    voltmx.print("### frmPhotos_frmANPROverlay_flcCaptureButton_setVisibility");

    function flcCaptureButton_setVisibility() {
        voltmx.print("### frmPhotos_frmANPROverlay_flcCaptureButton_setVisibility flcCaptureButton_setVisibility: " + boolean);
        frmANPROverlay.flcCaptureButton.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcCaptureButton_setVisibility, []);
}

function frmPhotos_frmANPROverlay_flcButtonLeft_setVisibility(boolean) {
    voltmx.print("### frmPhotos_frmANPROverlay_flcButtonLeft_setVisibility");

    function flcButtonLeft_setVisibility() {
        voltmx.print("### frmPhotos_frmANPROverlay_flcButtonLeft_setVisibility flcButtonLeft_setVisibility: " + boolean);
        frmANPROverlay.flcButtonLeft.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcButtonLeft_setVisibility, []);
}

function frmPhotos_CameraSonim1_setVisibility(boolean) {
    voltmx.print("### frmPhotos_CameraSonim1_setVisibility");

    function CameraSonim1_setVisibility() {
        voltmx.print("### frmPhotos_CameraSonim1_setVisibility CameraSonim1_setVisibility: " + boolean);
        frmPhotos.CameraSonim1.setVisibility(boolean);
    }
    voltmx.runOnMainThread(CameraSonim1_setVisibility, []);
}

function frmPhotos_flcEmptyDataset_setVisibility(boolean) {
    voltmx.print("### frmPhotos_flcEmptyDataset_setVisibility");

    function flcEmptyDataset_setVisibility() {
        voltmx.print("### frmPhotos_flcEmptyDataset_setVisibility flcEmptyDataset_setVisibility: " + boolean);
        frmPhotos.flcEmptyDataset.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flcEmptyDataset_setVisibility, []);
}

function frmPhotos_init() {
    voltmx.print("#### frmPhotos_init");
    // Disable Back Button
    frmPhotos.onDeviceBack = Global_onDeviceBack;
    //
    voltmx.print("### frmPhotos init");
    frmPhotos.contentSize = {
        height: "100%",
        width: "100%"
    };
    voltmx.print("### end of init");
    var swipeSettings = {
        fingers: 1,
        swipedistance: 75,
        swipevelocity: 75
    };
    var swipeGesture = frmPhotos.setGestureRecognizer(2, swipeSettings, frmPhotos_handleGesture);
    frmPhotos.segPhotos.widgetDataMap = {
        lblLocationHeader: "lblLocationHeader",
        lblLocation: "lblLocation",
        imgPhoto: "imgPhoto",
        lblDate: "lblDate",
        imgReceipt: "imgReceipt"
    };
    frmPhotos.segPhotosSelect.widgetDataMap = {
        lblLocationHeader: "lblLocationHeader",
        lblLocation: "lblLocation",
        imgPhoto: "imgPhoto",
        lblDate: "lblDate",
        imgSelect: "imgSelect",
        imgReceipt: "imgReceipt"
    };
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        /*frmPhotos.Camera1.maxSideOfTheImage = 640;
        var deviceInfo=voltmx.os.deviceInfo();
        var deviceWidth=deviceInfo.screenWidth;
        var deviceHeight=deviceInfo.screenHeight;
      	voltmx.print("### frmPhotos_preshow deviceWidth: " + deviceWidth);
      	voltmx.print("### frmPhotos_preshow deviceHeight: " + deviceHeight);
        var cameraAspectRatio=4.0/3.0;
        var cameraHeight=deviceWidth*cameraAspectRatio;
        var transform = voltmx.ui.makeAffineTransform();
        voltmx.print("### frmPhotos_preshow maxside of the image iphone cameraHeight: " + cameraHeight);
        transform.translate(0, (deviceHeight-cameraHeight)/2);
        voltmx.print("### frmPhotos_preshow maxside of the image iphone (deviceHeight-cameraHeight)/2: " + (deviceHeight-cameraHeight)/2);
        frmPhotos.Camera1.enableOverlay = true;
        frmPhotos.Camera1.overlayConfig = {overlayForm: frmANPROverlay};
        frmPhotos.Camera1.previewTransform = transform;*/
        frmPhotos_Camera1_setVisibility(false);
        frmPhotos_CameraSonim1_setVisibility(false);
        frmPhotos_btnPhoto_setVisibility(true);
    } else {
        if (Global.vars.gDeviceInfo.manufacturer == "Sonimtech") {
            voltmx.print("### frmPhotos Sonimtech");
            //disable regular camera
            frmPhotos_Camera1_setVisibility(false);
            //enable sonimCamera
            frmPhotos_CameraSonim1_setVisibility(true);
            //set maxside of the image
            frmPhotos.CameraSonim1.maxSideOfTheImage = 800;
        } else {
            //enable regular camera
            frmPhotos_Camera1_setVisibility(true);
            //enable sonimCamera
            frmPhotos_CameraSonim1_setVisibility(false);
            //set maxside
            frmPhotos.Camera1.maxSideOfTheImage = 1600;
            voltmx.print("### frmPhotos maxSideOfTheImage: " + frmPhotos.Camera1.maxSideOfTheImage);
        }
    }
}

function frmPhotos_handleGesture(myWidget, gestureInfo) {
    voltmx.print("#### frmPhotos_handleGesture: " + gestureInfo.swipeDirection);
    if (gestureInfo.swipeDirection == 2) {
        voltmx.print("### swipe direction 2");
        //frmPhotos_onclick_btnBack();
    }
}

function frmPhotos_preshow() {
    Analytics_logScreenView("photos");
    voltmx.print("#### frmPhotos_preshow");
    frmPhotos.segPhotos.removeAll();
    frmPhotos.flcLayout.bottom = "44dp";
    frmPhotos_flcSegPhotos_setVisibility(true);
    frmPhotos_flcSegPhotosSelect_setVisibility(false);
    frmPhotos_flcFooterMain_setVisibility(false);
    frmPhotos_flcRemove_setVisibility(false);
    //frmPhotos.Camera1.cameraOptions = {flashMode : constants.FLASH_MODE_AUTO, focusMode: constants.FOCUS_MODE_CONTINUOUS};
    //camera
    frmPhotos.Camera1.enableOverlay = true;
    frmPhotos.Camera1.overlayConfig = {
        overlayForm: frmANPROverlay
    };
    frmPhotos_frmANPROverlay_flcCaptureButton_setVisibility(true);
    frmANPROverlay.flcLicenseplateInfo.top = "-200%";
    frmANPROverlay.btnPhotoCapture.onClick = frmPhotos_takePicture;
    frmANPROverlay.btnBack.onClick = frmPhotos_closeCamera;
    frmPhotos_frmANPROverlay_flcButtonLeft_setVisibility(true);
    frmPhotos_frmANPROverlay_btnDone_setVisibility(false);
    var cameraOptions1 = {
        flashMode: constants.FLASH_MODE_AUTO,
        focusMode: constants.FOCUS_MODE_AUTO,
        hideControlBar: true
    };
    if (Global.vars.gDeviceInfo.manufacturer == "Sonimtech") {
        voltmx.print("### frmPhotos_preshow Sonimtech");
        frmPhotos.CameraSonim1.cameraOptions = cameraOptions1;
    } else {
        frmPhotos.Camera1.cameraOptions = cameraOptions1;
    }
    voltmx.runOnMainThread(frmPhotos_checkPhotoNumber, []); //werkt alleen op iOS
    Utility_getCaseTypeDescription(CaseData.caseinfo.caseType, frmPhotos_caseTypeCallback);
    if (Global.vars.previousForm == "frmRegisterOverview") {
        frmPhotos_EnableFooter(false);
    } else {
        frmPhotos_EnableFooter(true);
    }
}

function frmPhotos_takePicture() {
    voltmx.print("#### frmPhotos_takePicture");
    frmPhotos_setCameraOptions();
    if (Global.vars.gDeviceInfo.manufacturer == "Sonimtech") {
        frmPhotos.CameraSonim1.takePicture();
    } else {
        frmPhotos.Camera1.takePicture();
    }
}

function frmPhotos_closeCamera() {
    voltmx.print("#### frmPhotos_closeCamera");
    //voltmx.runOnMainThread(frmPhotos_checkPhotoNumber, []);
    if (Global.vars.gDeviceInfo.manufacturer == "Sonimtech") {
        frmPhotos.CameraSonim1.closeCamera();
    } else {
        frmPhotos.Camera1.closeCamera();
    }
    frmPhotos.show();
}

function frmPhotos_takePhoto() {
    voltmx.print("#### frmPhotos_takePhoto");
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        //Creates an object of class 'CameraSwiftiOS'
        var CameraSwiftiOSObject = new REDLINE.CameraSwiftiOS();
        //Invokes method 'openCamera' on the object
        CameraSwiftiOSObject.openCamera(
            /**Function*/
            frmPhotos_iOSCameraCallback,
            /**String licensePlateImage */
            "",
            /**String licensePlateString */
            "",
            /**String messageString */
            "NoScan",
            /**String licensePlateStringcountry */
            "");
    }
}

function frmPhotos_iOSCameraCallback(string) {
    voltmx.print("#### frmPhotos_iOSCameraCallback string length: " + string.length);
    if (string.length < 30) {
        voltmx.print("### frmPhotos_iOSCameraCallback string: " + string);
    }
    if (string == "###Assess") {
        voltmx.print("### frmPhotos_iOSCameraCallback Assess");
        Global.vars.assessANPRPhoto = true;
    } else if (string == "###NotAssess") {
        voltmx.print("### frmPhotos_iOSCameraCallback NotAssess");
        Global.vars.assessANPRPhoto = false;
    } else if (string == "###AssessValid") {
        voltmx.print("### frmPhotos_iOSCameraCallback AssessValid");
        //phtoto is valid continue with checking parking rights
        frmPhotos_onselection_photoiOS(string);
    } else if (string == "###AssessNotValid") {
        voltmx.print("### frmPhotos_iOSCameraCallback AssessNotValid");
    } else if (string == "###DismissCamera") {
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            frmHandle.destroy();
            frmOverviewTask.destroy();
            //frmNHA.destroy();
            //frmResume.destroy();
            frmLocation.destroy();
        }
        voltmx.runOnMainThread(frmPhotos_checkPhotoNumber, []);
        voltmx.print("### frmPhotos_iOSCameraCallback DismissCamera");
    } else {
        //send foto
        voltmx.print("frmPhotos_iOSCameraCallback string is base64");
        frmPhotos_onselection_photoiOS(string);
    }
}

function frmPhotos_dismiss_iOSCamera() {
    voltmx.print("#### frmPhotos_dismiss_iOSCamera");
    var setValidMessage = "dismissCamera"; //send to dismiss camera
    var CameraSwiftiOSObject = new REDLINE.CameraSwiftiOS();
    //Invokes method 'openCamera' on the object
    CameraSwiftiOSObject.openCamera(
        /**Function*/
        frmPhotos_iOSCameraCallback,
        /**String licensePlateImage */
        "",
        /**String licensePlateString */
        "",
        /**String messageString */
        setValidMessage,
        /**String licensePlateStringcountry */
        "");
}

function frmPhotos_setCameraOptions() {
    voltmx.print("#### frmPhotos_setCameraOptions");
    var cameraOptions = {
        flashMode: constants.FLASH_MODE_AUTO,
        hideControlBar: true
    };
    if (Global.vars.gScannerLight == "1" || Global.vars.gScannerLight == "0") {
        //Automatisch
        cameraOptions = {
            flashMode: constants.FLASH_MODE_AUTO,
            hideControlBar: true
        };
    } else if (Global.vars.gScannerLight == "2") {
        //Aan
        cameraOptions = {
            flashMode: constants.FLASH_MODE_ON,
            hideControlBar: true
        };
    } else if (Global.vars.gScannerLight == "3") {
        //Uit
        cameraOptions = {
            flashMode: constants.FLASH_MODE_OFF,
            hideControlBar: true
        };
    }
    frmPhotos.Camera1.cameraOptions = cameraOptions;
}

function frmPhotos_caseTypeCallback(result) {
    voltmx.print("#### frmPhotos_caseTypeCallback: " + JSON.stringify(result));
    if (result !== undefined && result != null && result.length > 0) {
        Global.vars.caseTypeDescription = result[0].description;
    }
}

function frmPhotos_posthow() {
    voltmx.print("#### frmPhotos_posthow");
    voltmx.application.dismissLoadingScreen();
}

function frmPhotos_reset() {
    voltmx.print("#### frmPhotos_reset");
    frmPhotos.flcLayout.bottom = "44dp";
    frmPhotos.segPhotos.removeAll();
    frmPhotos_flcSegPhotos_setVisibility(true);
    frmPhotos_flcSegPhotosSelect_setVisibility(false);
    frmPhotos_flcFooterMainChooseReceiptDelete_setVisibility(false);
    frmPhotos_flcFooterMainPhoto_setVisibility(true);
    frmPhotos.btnEdit.text = voltmx.i18n.getLocalizedString("l_select");
    voltmx.runOnMainThread(frmPhotos_checkPhotoNumber, []);
}

function frmPhotos_checkPhotoNumber() {
    voltmx.print("#### frmPhotos_checkPhotoNumber");
    if (Global.vars.newPhotos !== undefined && Global.vars.newPhotos != null && Global.vars.newPhotos.length > 0) {
        voltmx.print("### frmPhotos_checkPhotoNumber Global.vars.newPhotos length: " + Global.vars.newPhotos.length);
        frmPhotos_flcEmptyDataset_setVisibility(false);
        frmPhotos_btnDone_setVisibility(true);
        frmPhotos_flcButtonLeft_setVisibility(false);
        frmPhotos_flcSegPhotos_setVisibility(true);
        voltmx.print("### frmPhotos_checkPhotoNumber frmPhotos.segPhotos.isVisible: " + frmPhotos.segPhotos.isVisible);
        var photoData = [];
        voltmx.print("### frmPhotos_checkPhotoNumber Global.vars.newPhotos signature check length: " + Global.vars.newPhotos.length);
        for (var j = 0; j < Global.vars.newPhotos.length; j++) {
            var w = Global.vars.newPhotos[j];
            voltmx.print("### frmPhotos_checkPhotoNumber Global.vars.newPhotos signature check length: " + Global.vars.newPhotos.length);
            if ((w.fileName !== undefined && w.fileName != null && w.fileName.startsWith("signature") === false) || w.id === undefined || w.id === null) {
                photoData.push(w);
            }
        }
        if (photoData.length > 0) {
            frmPhotos.segPhotos.setData(photoData);
        } else {
            voltmx.print("### frmPhotos_checkPhotoNumber photoData < 0");
            frmPhotos_flcEmptyDataset_setVisibility(true);
            frmPhotos_btnDone_setVisibility(false);
            frmPhotos_flcButtonLeft_setVisibility(true);
        }
    } else {
        voltmx.print("### frmPhotos_checkPhotoNumber < 0");
        frmPhotos_flcEmptyDataset_setVisibility(true);
        frmPhotos_btnDone_setVisibility(false);
        frmPhotos_flcButtonLeft_setVisibility(true);
    }
}

function frmPhotos_onEdit() {
    voltmx.print("#### frmPhotos_onEdit");
    if (frmPhotos.btnEdit.text == voltmx.i18n.getLocalizedString("l_select")) {
        frmPhotos.segPhotos.removeAll();
        frmPhotos_flcSegPhotos_setVisibility(false);
        frmPhotos_flcSegPhotosSelect_setVisibility(true);
        frmPhotos.segPhotosSelect.setData(Global.vars.newPhotos);
        frmPhotos.btnEdit.text = voltmx.i18n.getLocalizedString("bt_done");
        frmPhotos_flcFooterMainPhoto_setVisibility(false);
        frmPhotos_flcFooterMainChooseReceiptDelete_setVisibility(false);
        if ((CaseData.caseinfo.indPayed === true && (CaseData.caseinfo.onStreetPaymentMethodKey == "2" || CaseData.caseinfo.onStreetPaymentMethodKey == "3")) || CaseData.processinfo.lastTaskProcessed.taskOutcome == "betaling_nhas" || CaseData.processinfo.lastTaskProcessed.taskOutcome == "ontklemd") {
            frmPhotos.flcRemovePhotos.centerX = 75 + '%';
            frmPhotos_flcSetReceipt_setVisibility(true);
        } else {
            frmPhotos.flcRemovePhotos.centerX = 50 + '%';
            frmPhotos_flcSetReceipt_setVisibility(false);
        }
        frmPhotos.flcLayout.bottom = "0dp";
    } else {
        frmPhotos_flcSegPhotos_setVisibility(true);
        var photoData = [];
        for (var j = 0; j < Global.vars.newPhotos.length; j++) {
            voltmx.print("### frmPhotos_setPhotosOnForm Global.vars.newPhotos signature check length: " + Global.vars.newPhotos.length);
            var w = Global.vars.newPhotos[j];
            if ((w.fileName !== undefined && w.fileName != null && w.fileName.startsWith("signature") === false) || w.id === undefined || w.id === null) {
                photoData.push(w);
            }
        }
        frmPhotos.segPhotos.setData(photoData);
        frmPhotos.segPhotosSelect.selectedRowIndices = null;
        frmPhotos.segPhotosSelect.removeAll();
        frmPhotos_flcSegPhotosSelect_setVisibility(false);
        frmPhotos.btnEdit.text = voltmx.i18n.getLocalizedString("l_select");
        frmPhotos_flcFooterMainPhoto_setVisibility(true);
        frmPhotos_flcFooterMainChooseReceiptDelete_setVisibility(false);
        frmPhotos.flcLayout.bottom = "44dp";
    }
}

function frmPhotos_EnableFooter(enablefooter) {
    voltmx.print("##### frmTrackDown_enableFooters runOnMainThread frmTrackDown_enableFooters_MT");
    voltmx.runOnMainThread(frmPhotos_EnableFooter_MT, [{
        "boolean": enablefooter
    }]);
}

function frmPhotos_EnableFooter_nonMT(enablefooter) {
    voltmx.print("#### frmPhotos_EnableFooter_nonMT");
    if (enablefooter === false) {
        frmPhotos_flcFooterMainPhoto_setVisibility(false);
        frmPhotos.flcLayout.bottom = "0dp";
    } else {
        frmPhotos_flcFooterMainPhoto_setVisibility(true);
        frmPhotos.flcLayout.bottom = "44dp";
    }
}

function frmPhotos_EnableFooter_MT(inputparams) {
    voltmx.print("#### frmPhotos_EnableFooter_MT");
    voltmx.print("### frmPhotos_EnableFooter_MT inputparams: " + JSON.stringify(inputparams));
    var enablefooter = false;
    if (inputparams.boolean === true) {
        enablefooter = true;
    } else {
        enablefooter = false;
    }
    voltmx.print("### frmPhotos_EnableFooter_MT enablefooter: " + enablefooter);
    if (enablefooter === false) {
        frmPhotos_flcFooterMainPhoto_setVisibility(false);
        frmPhotos.flcLayout.bottom = "0dp";
    } else {
        frmPhotos_flcFooterMainPhoto_setVisibility(true);
        frmPhotos.flcLayout.bottom = "44dp";
    }
}

function frmPhotos_onclick_btnSelectAll() {
    voltmx.print("### frmPhotos_onclick_btnSelectAll selected indices: " + JSON.stringify(frmPhotos.segPhotosSelect.selectedIndices));
    if (frmPhotos.btnSelectAll.text == voltmx.i18n.getLocalizedString("bt_deselectAll")) {
        frmPhotos.btnSelectAll.text = voltmx.i18n.getLocalizedString("bt_selectAll");
        frmPhotos.segPhotosSelect.selectedRowIndices = null;
        frmPhotos_flcRemove_setVisibility(false);
    } else {
        frmPhotos.btnSelectAll.text = voltmx.i18n.getLocalizedString("bt_deselectAll");
        var lsegOverviewSelect = frmPhotos.segPhotosSelect.data;
        var lIndices = [];
        for (var i = 0;
            (lsegOverviewSelect != null) && i < lsegOverviewSelect.length; i++) {
            voltmx.print("#### frmPhotos_onclick_btnSelectAll lsegOverviewSelect selectAll select: " + i + "");
            lIndices.push(i);
        }
        voltmx.print("#### frmPhotos_onclick_btnSelectAll lsegOverviewSelect selectAll selected indices: " + JSON.stringify(lIndices));
        if (lIndices.length > 0) {
            voltmx.print("### frmPhotos_onclick_btnSelectAll selectAll 1");
            frmPhotos_flcRemove_setVisibility(true);
            frmPhotos.btnRemove.text = voltmx.i18n.getLocalizedString("bt_remove") + " (" + lIndices.length + ")";
            frmPhotos.segPhotosSelect.selectedRowIndices = [
                [0, lIndices]
            ];
        }
    }
}

function frmPhotos_onclick_segmentSelect() {
    var lIndices = frmPhotos.segPhotosSelect.selectedItems;
    if (lIndices != null && lIndices.length > 0) {
        frmPhotos_flcFooterMainChooseReceiptDelete_setVisibility(true);
        frmPhotos.flcLayout.bottom = "44dp";
        if (lIndices.length > 1) {
            if ((CaseData.caseinfo.indPayed === true && (CaseData.caseinfo.onStreetPaymentMethodKey == "2" || CaseData.caseinfo.onStreetPaymentMethodKey == "3")) || CaseData.processinfo.lastTaskProcessed.taskOutcome == "betaling_nhas" || CaseData.processinfo.lastTaskProcessed.taskOutcome == "ontklemd") {
                frmPhotos.flcRemovePhotos.centerX = 75 + '%';
                frmPhotos.flcSetReceipt.setEnabled(false);
                frmPhotos.imgSetReceipt.src = "butreceiptunselect.png";
                frmPhotos.lblSetReceipt.skin = lblScanUnselect;
            } else {
                frmPhotos.flcRemovePhotos.centerX = 50 + '%';
                frmPhotos_flcSetReceipt_setVisibility(false);
                frmPhotos.imgSetReceipt.src = "butreceipt.png";
                frmPhotos.lblSetReceipt.skin = lblScan;
            }
        } else {
            if ((CaseData.caseinfo.indPayed === true && (CaseData.caseinfo.onStreetPaymentMethodKey == "2" || CaseData.caseinfo.onStreetPaymentMethodKey == "3")) || CaseData.processinfo.lastTaskProcessed.taskOutcome == "betaling_nhas" || CaseData.processinfo.lastTaskProcessed.taskOutcome == "ontklemd") {
                frmPhotos.flcRemovePhotos.centerX = 75 + '%';
                frmPhotos_flcSetReceipt_setVisibility(true);
                frmPhotos.flcSetReceipt.setEnabled(true);
                frmPhotos.imgSetReceipt.src = "butreceipt.png";
                frmPhotos.lblSetReceipt.skin = lblScan;
            } else {
                frmPhotos.flcRemovePhotos.centerX = 50 + '%';
                frmPhotos_flcSetReceipt_setVisibility(false);
                frmPhotos.imgSetReceipt.src = "butreceipt.png";
                frmPhotos.lblSetReceipt.skin = lblScan;
            }
        }
    } else {
        frmPhotos_flcFooterMainChooseReceiptDelete_setVisibility(false);
        frmPhotos.flcLayout.bottom = "0dp";
    }
}

function frmPhotos_onclick_segPhotos() {
    if (Global.vars.previousForm == "frmRegisterOverview") {
        // Do nothing
    } else {
        var selecteditem = frmPhotos.segPhotos.selectedItems;
        if (selecteditem == null) {
            voltmx.print("### frmPhotos_onclick_segPhotos selecteditem is null");
            return;
        }
        voltmx.print("### frmPhotos_onclick_segPhotos selecteditem: " + JSON.stringify(selecteditem));
        if (selecteditem.length > 0 && ((selecteditem[0].fileName.toLowerCase().startsWith("anprphoto") === false) && (selecteditem[0].fileName.toLowerCase().startsWith("anproverviewphoto") === false))) {
            voltmx.print("### frmPhotos_onclick_segPhotos selecteditem: " + selecteditem[0].id);
            voltmx.print("### frmPhotos_onclick_segPhotos selecteditem: " + selecteditem[0].fileName);
            frmPhotoOverview.imgPhoto.base64 = selecteditem[0].imgPhoto.base64;
            Global.vars.selectedPhoto = selecteditem[0];
            frmPhotoOverview.show();
        }
    }
}

function frmPhotos_finishedUpdateMultimedia() {
    voltmx.print("#### frmPhotos_finishedUpdateMultimedia");
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
        var setValidMessage = "noscanphotovalid"; //send to dismiss camera
        var CameraSwiftiOSObject = new REDLINE.CameraSwiftiOS();
        //Invokes method 'openCamera' on the object
        CameraSwiftiOSObject.openCamera(
            /**Function*/
            frmPhotos_iOSCameraCallback,
            /**String licensePlateImage */
            "",
            /**String licensePlateString */
            "",
            /**String messageString */
            setValidMessage,
            /**String licensePlateStringcountry */
            "");
    }
    voltmx.application.dismissLoadingScreen();
}

function frmPhotos_Acceleration_onsuccesscallback(accelerometerdata) {
    voltmx.print("### Acceleration X: " + accelerometerdata.x + "Y: " + accelerometerdata.y + "Z: " + accelerometerdata.z + "Timestamp: " + accelerometerdata.timestamp);
    var G = 1 / 9.80665;
    var Gpz = accelerometerdata.z * G;
    var Gpy = accelerometerdata.y * G;
    var Gpx = accelerometerdata.x * G;
    if (Gpx > 0.5 && Gpy < 0.4) {
        voltmx.print("### orientation2 is left"); // ok
    } else if (Gpx < -0.5 && Gpy < 0.4) {
        voltmx.print("### orientation2 is right"); // rotate 180 clockwise
    } else if (Gpy > 0.5 && Gpx < 0.4) {
        voltmx.print("### orientation2 is portrait"); //rotate 90 clockwise
    } else if (Gpy < -0.5 && Gpx < 0.4) {
        voltmx.print("### orientation2 is upside down"); // rotate 270 clockwise
    }
}
// onfailurecallback 
function frmPhotos_Acceleration_onfailurecallback(error) {
    voltmx.print("### Acceleration code: " + error.code + "message: " + error.message);
}

function frmPhotos_retrieveCurrentAcceleration() {
    // Set the callbacks for getting the acceleration information. 
    voltmx.print("#### frmPhotos_retrieveCurrentAcceleration");
    voltmx.print("### hasAccelerometerSupport: " + voltmx.os.hasAccelerometerSupport());
    voltmx.print("### orientation: " + voltmx.os.getDeviceCurrentOrientation());
    voltmx.print("### orientation support: " + voltmx.os.hasOrientationSupport());
    voltmx.accelerometer.retrieveCurrentAcceleration(frmPhotos_Acceleration_onsuccesscallback, frmPhotos_Acceleration_onfailurecallback);
}

function frmPhotos_oncapture_photo(camera) {
    voltmx.print("#### frmPhotos_oncapture_photo ###");
    Global.vars.mediaSequence = CaseData.multimedia.length;
    voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loadPhoto"), "center", false, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    try {
        // get image
        Global.vars.mediaSequence = Global.vars.mediaSequence + 1;
        var d = new Date();
        var n = d.getTime();
        var lMediaName = "extrafoto" + n;
        if (CaseData.caseinfo.caseType == "CONTROL_K") {
            lMediaName = CaseData.processinfo.lastTaskProcessed.taskType + n;
        }
        // set photo
        var resizeimage = voltmx.image.createImage(camera.rawBytes);
        voltmx.print("### frmPhotos_oncapture_photo image width: " + resizeimage.getImageWidth());
        voltmx.print("### frmPhotos_oncapture_photo image heigth: " + resizeimage.getImageHeight());
        var pixels = 0;
        var originalHeight = resizeimage.getImageHeight();
        var originalWidth = resizeimage.getImageWidth();
        var actualHeight = resizeimage.getImageHeight();
        var actualWidth = resizeimage.getImageWidth();
        var maxHeight = 800;
        var maxWidth = 600;
        var imgRatio = actualWidth / actualHeight;
        var maxRatio = maxWidth / maxHeight;
        if (actualHeight > maxHeight || actualWidth > maxWidth) {
            if (imgRatio < maxRatio) {
                //adjust width according to maxHeight
                imgRatio = maxHeight / actualHeight;
                actualWidth = imgRatio * actualWidth;
                actualHeight = maxHeight;
            } else if (imgRatio > maxRatio) {
                //adjust height according to maxWidth
                imgRatio = maxWidth / actualWidth;
                actualHeight = imgRatio * actualHeight;
                actualWidth = maxWidth;
            } else {
                actualHeight = maxHeight;
                actualWidth = maxWidth;
            }
        }
        voltmx.print("### frmPhotos_oncapture_photo image actualHeight: " + actualHeight);
        voltmx.print("### frmPhotos_oncapture_photo image actualWidth: " + actualWidth);
        voltmx.print("### frmPhotos_oncapture_photo image imgRatio: " + imgRatio);
        if (originalHeight > originalWidth) {
            pixels = originalHeight;
        } else {
            pixels = originalWidth;
        }
        var scalefactor = (1 / pixels) * 800;
        voltmx.print("### frmPhotos_oncapture_photo scalefactor: " + scalefactor);
        //resizeimage.cropToRect([0, 0, actualWidth, actualHeight]);
        if (scalefactor < 1) {
            resizeimage.scale(scalefactor);
        }
        voltmx.print("### frmPhotos_oncapture_photo image width 2: " + resizeimage.getImageWidth());
        //Save photo to couch
        var imgData = resizeimage.getImageAsRawBytes();
        var description = "Extra foto " + Global.vars.caseTypeDescription; // + " " + d.getHours().toString().lpad("0",2)+":"+d.getMinutes().toString().lpad("0",2)+":"+d.getSeconds().toString().lpad("0",2);
        frmPhotos_addAttachmentInline(imgData, lMediaName, description);
    } catch (err) {
        voltmx.print("### frmPhotos_oncapture_photo Failed to process photo: " + err);
        voltmx.application.dismissLoadingScreen();
    }
    // release the camera bytes
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") !== true) {
        voltmx.print("### frmPhotos release rawbytes");
        if (Global.vars.gDeviceInfo.manufacturer == "Sonimtech") {
            frmPhotos.CameraSonim1.releaseRawBytes(camera.rawBytes);
            frmPhotos.CameraSonim1.closeCamera();
        } else {
            frmPhotos.Camera1.releaseRawBytes(camera.rawBytes);
            frmPhotos.Camera1.closeCamera();
        }
    }
    frmPhotos.show();
}

function frmPhotos_str2ab(str) {
    var buf = new ArrayBuffer(str.length);
    var bufView = new Uint8Array(buf);
    for (var i = 0, strLen = str.length; i < strLen; i++) {
        bufView[i] = str.charCodeAt(i);
    }
    voltmx.print("### BUF byteLength: " + buf.byteLength);
    return buf;
}

function frmPhotos_Couch_addAttachmentiOS(imgData, name, description) {
    voltmx.print("#### frmPhotos_Couch_addAttachmentiOS");
    try {
        var base64FromImage = imgData;
        voltmx.print("### frmPhotos_Couch_addAttachmentiOS photo size: " + base64FromImage.length);
        voltmx.print("### frmPhotos_Couch_addAttachmentiOS photo size calculated: " + ((base64FromImage.length * 0.75) / 1024));
        var documentType = "ExtraPhoto";
        var fileExtension = ".jpg";
        var imageMimeType = "image/jpeg";
        if (name.includes(".jpg") === false && name.includes(".png") === false) {
            name = name + fileExtension;
        }
        //save inline
        voltmx.print("### frmPhotos_Couch_addAttachmentiOS save photo inline");
        frmPhotos_addAttachmentInline(base64FromImage, name, description, true);
        //showPhotos
        //Utility_showPhotos(base64FromImage,name,frmPhotos_finishedUpdateMultimedia);
    } catch (e) {
        voltmx.print("### frmPhotos_Couch_addAttachmentiOS errored: " + JSON.stringify(e));
        var setValidMessage = "noscanphotonotvalid"; //send to dismiss camera
        var CameraSwiftiOSObject = new REDLINE.CameraSwiftiOS();
        //Invokes method 'openCamera' on the object
        CameraSwiftiOSObject.openCamera(
            /**Function*/
            frmPhotos_iOSCameraCallback,
            /**String licensePlateImage */
            "",
            /**String licensePlateString */
            "",
            /**String messageString */
            setValidMessage,
            /**String licensePlateStringcountry */
            "");
    }
}

function frmPhotos_addAttachmentInline(imgData, name, description, isBase64) {
    voltmx.print("#### frmPhotos_addAttachmentInline: " + Global.vars.addPhotos.length);
    if (isBase64 === undefined || isBase64 === null) {
        isBase64 = false;
    }
    var base64FromImage = voltmx.convertToBase64(imgData);
    if (isBase64 === true) {
        base64FromImage = imgData;
    }
    voltmx.print("### frmPhotos_addAttachmentInline photo size: " + base64FromImage.length);
    voltmx.print("### frmPhotos_addAttachmentInline photo size calculated: " + ((base64FromImage.length * 0.75) / 1024));
    var documentType = "ExtraPhoto";
    ///
    var fileExtension = ".jpg";
    var imageMimeType = "image/jpeg";
    var filename = "";
    //   if(voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")){
    //     imageMimeType = 'image/png';
    //     fileExtension = '.png';
    //   }
    if (name.includes(".jpg") === false && name.includes(".png") === false) {
        filename = name + fileExtension;
    } else {
        filename = name;
    }
    //   if(voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true){
    //     fileExtension = ".png";
    //     imageMimeType = "image/png";
    //   }
    if (name.includes(".jpg") === false && name.includes(".png") === false) {
        name = name + fileExtension;
    }
    var photoinfo = {
        fileDate: Utility_getUTCJavascriptDate(null),
        //datetime stamp of the file
        fileName: filename,
        //name of media file
        description: "Extra photo: " + name,
        //description of media
        documentType: "ExtraPhoto",
        //type of media document for example "photo" or "licenseplatePhoto"
        contentType: imageMimeType,
        //attachmentId: filename, deze weglaten anders wordt ie niet verwerkt naar Minio
        //Couch attachment id
        uploaded: true,
        photoStatus: "draft",
        base64: base64FromImage
    };
    Utility_addPhotoToCase(photoinfo);
    //frmPhotos_finishedUpdateMultimedia();
    //showPhotos
    Utility_showPhotos(base64FromImage, name, frmPhotos_finishedUpdateMultimedia);
}

function frmPhotos_setPhotosOnForm() {
    voltmx.print("#### frmPhotos_setPhotosOnForm");
    voltmx.print("### frmPhotos_setPhotosOnForm Global.vars.previousForm: " + Global.vars.previousForm);
    //check hoeveel foto's er zijn
    voltmx.print("### frmPhotos_setPhotosOnForm Global.vars.newPhotos: " + JSON.stringify(Global.vars.newPhotos));
    for (var i = 0; i < Global.vars.newPhotos.length; i++) {
        voltmx.print("### frmPhotos_setPhotosOnForm Global.vars.newPhotos length: " + Global.vars.newPhotos.length);
        var w = Global.vars.newPhotos[i].imgPhoto.base64;
        voltmx.print("### frmPhotos_setPhotosOnForm photo length: " + w.length);
        if (i === 0) {
            if (Global.vars.previousForm == "frmResume" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmResume")) {
                frmResume.multimedia.imgPhoto1.base64 = w;
                frmResume_multimedia_flcPhoto1_setVisibility(true);
                voltmx.print("### frmPhotos_setPhotosOnForm frmResume_multimedia_flcPhoto1 base64: " + frmResume.multimedia.imgPhoto1.base64);
            } else if (Global.vars.previousForm == "frmNHA" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmNHA")) {
                frmNHA.multimedia.imgPhoto1.base64 = w;
                frmNHA_multimedia_flcPhoto1_setVisibility(true);
            } else if (Global.vars.previousForm == "frmClamp") {
                frmClamp.imgPhoto1.base64 = w;
                frmClamp.flcPhoto1.setVisibility(true);
            } else if (Global.vars.previousForm == "frmActiveCaseResume") {
                frmActiveCaseResume.imgPhoto1.base64 = w;
                frmActiveCaseResume.flcPhoto1.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterConcept") {
                frmRegisterConcept.imgPhoto1.base64 = w;
                frmRegisterConcept.flcPhoto1.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterResume") {
                frmRegisterResume.imgPhoto1.base64 = w;
                frmRegisterResume.flcPhoto1.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterLabel") {
                frmRegisterLabel.imgPhoto1.base64 = w;
                frmRegisterLabel.flcPhoto1.setVisibility(true);
            } else if (Global.vars.previousForm == "frmCheckLabel") {
                frmCheckLabel.imgPhoto1.base64 = w;
                frmCheckLabel.flcPhoto1.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterOverview") {
                frmRegisterOverview.imgPhoto1.base64 = w;
                frmRegisterOverview.flcPhoto1.setVisibility(true);
            }
        } else if (i == 1) {
            if (Global.vars.previousForm == "frmResume" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmResume")) {
                frmResume.multimedia.imgPhoto2.base64 = w;
                frmResume_multimedia_flcPhoto2_setVisibility(true);
            } else if (Global.vars.previousForm == "frmNHA" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmNHA")) {
                frmNHA.multimedia.imgPhoto2.base64 = w;
                frmNHA_multimedia_flcPhoto2_setVisibility(true);
            } else if (Global.vars.previousForm == "frmClamp") {
                frmClamp.imgPhoto2.base64 = w;
                frmClamp.flcPhoto2.setVisibility(true);
            } else if (Global.vars.previousForm == "frmActiveCaseResume") {
                frmActiveCaseResume.imgPhoto2.base64 = w;
                frmActiveCaseResume.flcPhoto2.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterConcept") {
                frmRegisterConcept.imgPhoto2.base64 = w;
                frmRegisterConcept.flcPhoto2.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterResume") {
                frmRegisterResume.imgPhoto2.base64 = w;
                frmRegisterResume.flcPhoto2.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterLabel") {
                frmRegisterLabel.imgPhoto2.base64 = w;
                frmRegisterLabel.flcPhoto2.setVisibility(true);
            } else if (Global.vars.previousForm == "frmCheckLabel") {
                frmCheckLabel.imgPhoto2.base64 = w;
                frmCheckLabel.flcPhoto2.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterOverview") {
                frmRegisterOverview.imgPhoto2.base64 = w;
                frmRegisterOverview.flcPhoto2.setVisibility(true);
            }
        } else if (i == 2) {
            if (Global.vars.previousForm == "frmResume" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmResume")) {
                frmResume.multimedia.imgPhoto3.base64 = w;
                frmResume_multimedia_flcPhoto3_setVisibility(true);
            } else if (Global.vars.previousForm == "frmNHA" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmNHA")) {
                frmNHA.multimedia.imgPhoto3.base64 = w;
                frmNHA_multimedia_flcPhoto3_setVisibility(true);
            } else if (Global.vars.previousForm == "frmClamp") {
                frmClamp.imgPhoto3.base64 = w;
                frmClamp.flcPhoto3.setVisibility(true);
            } else if (Global.vars.previousForm == "frmActiveCaseResume") {
                frmActiveCaseResume.imgPhoto3.base64 = w;
                frmActiveCaseResume.flcPhoto3.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterConcept") {
                frmRegisterConcept.imgPhoto3.base64 = w;
                frmRegisterConcept.flcPhoto3.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterResume") {
                frmRegisterResume.imgPhoto3.base64 = w;
                frmRegisterResume.flcPhoto3.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterLabel") {
                frmRegisterLabel.imgPhoto3.base64 = w;
                frmRegisterLabel.flcPhoto3.setVisibility(true);
            } else if (Global.vars.previousForm == "frmCheckLabel") {
                frmCheckLabel.imgPhoto3.base64 = w;
                frmCheckLabel.flcPhoto3.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterOverview") {
                frmRegisterOverview.imgPhoto3.base64 = w;
                frmRegisterOverview.flcPhoto3.setVisibility(true);
            }
        } else if (i > 2) {
            if (Global.vars.previousForm == "frmResume" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmResume")) {
                frmResume_multimedia_lblMorePhotos_setVisibility(true);
            } else if (Global.vars.previousForm == "frmNHA" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmNHA")) {
                frmNHA_multimedia_lblMorePhotos_setVisibility(true);
            } else if (Global.vars.previousForm == "frmClamp") {
                frmClamp.lblMorePhotos.setVisibility(true);
            } else if (Global.vars.previousForm == "frmActiveCaseResume") {
                frmActiveCaseResume.lblMorePhotos.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterConcept") {
                frmRegisterConcept.lblMorePhotos.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterResume") {
                frmRegisterResume.lblMorePhotos.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterLabel") {
                frmRegisterLabel.lblMorePhotos.setVisibility(true);
            } else if (Global.vars.previousForm == "frmCheckLabel") {
                frmCheckLabel.lblMorePhotos.setVisibility(true);
            } else if (Global.vars.previousForm == "frmRegisterOverview") {
                frmRegisterOverview.flcNoMedia.setVisibility(true);
            }
        }
    }
    if (Global.vars.newPhotos.length === 0) {
        voltmx.print("### frmPhotos_setPhotosOnForm 0");
        if (Global.vars.previousForm == "frmResume" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmResume")) {
            frmResume_multimedia_flcPhoto1_setVisibility(false);
            frmResume_multimedia_flcPhoto2_setVisibility(false);
            frmResume_multimedia_flcPhoto3_setVisibility(false);
            frmResume_multimedia_lblMorePhotos_setVisibility(false);
            frmResume_multimedia_flcNoMedia_setVisibility(true);
        } else if (Global.vars.previousForm == "frmNHA" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmNHA")) {
            frmNHA_multimedia_flcPhoto1_setVisibility(false);
            frmNHA_multimedia_flcPhoto2_setVisibility(false);
            frmNHA_multimedia_flcPhoto3_setVisibility(false);
            frmNHA_multimedia_lblMorePhotos_setVisibility(false);
            frmNHA_multimedia_flcNoMedia_setVisibility(true);
        } else if (Global.vars.previousForm == "frmClamp") {
            frmClamp.flcPhoto1.setVisibility(false);
            frmClamp.flcPhoto2.setVisibility(false);
            frmClamp.flcPhoto3.setVisibility(false);
            frmClamp.lblMorePhotos.setVisibility(false);
            frmClamp.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmActiveCaseResume") {
            frmActiveCaseResume.flcPhoto1.setVisibility(false);
            frmActiveCaseResume.flcPhoto2.setVisibility(false);
            frmActiveCaseResume.flcPhoto3.setVisibility(false);
            frmActiveCaseResume.lblMorePhotos.setVisibility(false);
            frmActiveCaseResume.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterConcept") {
            frmRegisterConcept.flcPhoto1.setVisibility(false);
            frmRegisterConcept.flcPhoto2.setVisibility(false);
            frmRegisterConcept.flcPhoto3.setVisibility(false);
            frmRegisterConcept.lblMorePhotos.setVisibility(false);
            frmRegisterConcept.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterResume") {
            frmRegisterResume.flcPhoto1.setVisibility(false);
            frmRegisterResume.flcPhoto2.setVisibility(false);
            frmRegisterResume.flcPhoto3.setVisibility(false);
            frmRegisterResume.lblMorePhotos.setVisibility(false);
            frmRegisterResume.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterLabel") {
            frmRegisterLabel.flcPhoto1.setVisibility(false);
            frmRegisterLabel.flcPhoto2.setVisibility(false);
            frmRegisterLabel.flcPhoto3.setVisibility(false);
            frmRegisterLabel.lblMorePhotos.setVisibility(false);
            frmRegisterLabel.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmCheckLabel") {
            frmCheckLabel.flcPhoto1.setVisibility(false);
            frmCheckLabel.flcPhoto2.setVisibility(false);
            frmCheckLabel.flcPhoto3.setVisibility(false);
            frmCheckLabel.lblMorePhotos.setVisibility(false);
            frmCheckLabel.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterOverview") {
            frmRegisterOverview.flcPhoto1.setVisibility(false);
            frmRegisterOverview.flcPhoto2.setVisibility(false);
            frmRegisterOverview.flcPhoto3.setVisibility(false);
            frmRegisterOverview.lblMorePhotos.setVisibility(false);
            frmRegisterOverview.flcNoMedia.setVisibility(true);
        }
    } else if (Global.vars.newPhotos.length === 1) {
        voltmx.print("### frmPhotos_setPhotosOnForm 1");
        if (Global.vars.previousForm == "frmResume" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmResume")) {
            frmResume_multimedia_flcPhoto2_setVisibility(false);
            frmResume_multimedia_flcPhoto3_setVisibility(false);
            frmResume_multimedia_lblMorePhotos_setVisibility(false);
            frmResume_multimedia_flcNoMedia_setVisibility(false);
        } else if (Global.vars.previousForm == "frmNHA" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmNHA")) {
            frmNHA_multimedia_flcPhoto2_setVisibility(false);
            frmNHA_multimedia_flcPhoto3_setVisibility(false);
            frmNHA_multimedia_lblMorePhotos_setVisibility(false);
            frmNHA_multimedia_flcNoMedia_setVisibility(false);
        } else if (Global.vars.previousForm == "frmClamp") {
            frmClamp.flcPhoto2.setVisibility(false);
            frmClamp.flcPhoto3.setVisibility(false);
            frmClamp.lblMorePhotos.setVisibility(false);
            frmClamp.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmActiveCaseResume") {
            frmActiveCaseResume.flcPhoto2.setVisibility(false);
            frmActiveCaseResume.flcPhoto3.setVisibility(false);
            frmActiveCaseResume.lblMorePhotos.setVisibility(false);
            frmActiveCaseResume.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterConcept") {
            frmRegisterConcept.flcPhoto2.setVisibility(false);
            frmRegisterConcept.flcPhoto3.setVisibility(false);
            frmRegisterConcept.lblMorePhotos.setVisibility(false);
            frmRegisterConcept.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterResume") {
            frmRegisterResume.flcPhoto2.setVisibility(false);
            frmRegisterResume.flcPhoto3.setVisibility(false);
            frmRegisterResume.lblMorePhotos.setVisibility(false);
            frmRegisterResume.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterLabel") {
            frmRegisterLabel.flcPhoto2.setVisibility(false);
            frmRegisterLabel.flcPhoto3.setVisibility(false);
            frmRegisterLabel.lblMorePhotos.setVisibility(false);
            frmRegisterLabel.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmCheckLabel") {
            frmCheckLabel.flcPhoto2.setVisibility(false);
            frmCheckLabel.flcPhoto3.setVisibility(false);
            frmCheckLabel.lblMorePhotos.setVisibility(false);
            frmCheckLabel.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterOverview") {
            frmRegisterOverview.flcPhoto2.setVisibility(false);
            frmRegisterOverview.flcPhoto3.setVisibility(false);
            frmRegisterOverview.lblMorePhotos.setVisibility(false);
            frmRegisterOverview.flcNoMedia.setVisibility(true);
        }
    } else if (Global.vars.newPhotos.length === 2) {
        voltmx.print("### frmPhotos_setPhotosOnForm 2");
        if (Global.vars.previousForm == "frmResume" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmResume")) {
            frmResume_multimedia_flcPhoto3_setVisibility(false);
            frmResume_multimedia_lblMorePhotos_setVisibility(false);
            frmResume_multimedia_flcNoMedia_setVisibility(false);
        } else if (Global.vars.previousForm == "frmNHA" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmNHA")) {
            frmNHA_multimedia_flcPhoto3_setVisibility(false);
            frmNHA_multimedia_lblMorePhotos_setVisibility(false);
            frmNHA_multimedia_flcNoMedia_setVisibility(false);
        } else if (Global.vars.previousForm == "frmClamp") {
            frmClamp.flcPhoto3.setVisibility(false);
            frmClamp.lblMorePhotos.setVisibility(false);
            frmClamp.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmActiveCaseResume") {
            frmActiveCaseResume.flcPhoto3.setVisibility(false);
            frmActiveCaseResume.lblMorePhotos.setVisibility(false);
            frmActiveCaseResume.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterConcept") {
            frmRegisterConcept.flcPhoto3.setVisibility(false);
            frmRegisterConcept.lblMorePhotos.setVisibility(false);
            frmRegisterConcept.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterResume") {
            frmRegisterResume.flcPhoto3.setVisibility(false);
            frmRegisterResume.lblMorePhotos.setVisibility(false);
            frmRegisterResume.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterLabel") {
            frmRegisterLabel.flcPhoto3.setVisibility(false);
            frmRegisterLabel.lblMorePhotos.setVisibility(false);
            frmRegisterLabel.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmCheckLabel") {
            frmCheckLabel.flcPhoto3.setVisibility(false);
            frmCheckLabel.lblMorePhotos.setVisibility(false);
            frmCheckLabel.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterOverview") {
            frmRegisterOverview.flcPhoto3.setVisibility(false);
            frmRegisterOverview.lblMorePhotos.setVisibility(false);
            frmRegisterOverview.flcNoMedia.setVisibility(true);
        }
    } else if (Global.vars.newPhotos.length === 3) {
        voltmx.print("### frmPhotos_setPhotosOnForm 3");
        if (Global.vars.previousForm == "frmResume" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmResume")) {
            frmResume_multimedia_lblMorePhotos_setVisibility(false);
            frmResume_multimedia_flcNoMedia_setVisibility(false);
        } else if (Global.vars.previousForm == "frmNHA" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmNHA")) {
            frmNHA_multimedia_lblMorePhotos_setVisibility(false);
            frmNHA_multimedia_flcNoMedia_setVisibility(false);
        } else if (Global.vars.previousForm == "frmClamp") {
            frmClamp.lblMorePhotos.setVisibility(false);
            frmClamp.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmActiveCaseResume") {
            frmActiveCaseResume.lblMorePhotos.setVisibility(false);
            frmActiveCaseResume.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterConcept") {
            frmRegisterConcept.lblMorePhotos.setVisibility(false);
            frmRegisterConcept.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterResume") {
            frmRegisterResume.lblMorePhotos.setVisibility(false);
            frmRegisterResume.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterLabel") {
            frmRegisterLabel.lblMorePhotos.setVisibility(false);
            frmRegisterLabel.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmCheckLabel") {
            frmCheckLabel.lblMorePhotos.setVisibility(false);
            frmCheckLabel.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterOverview") {
            frmRegisterOverview.lblMorePhotos.setVisibility(false);
            frmRegisterOverview.flcNoMedia.setVisibility(true);
        }
    } else if (Global.vars.newPhotos.length > 3) {
        voltmx.print("### frmPhotos_setPhotosOnForm more then 3");
        if (Global.vars.previousForm == "frmResume" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmResume")) {
            frmResume.multimedia.lblMorePhotos.text = "+" + (Global.vars.newPhotos.length - 3);
            frmResume_multimedia_flcNoMedia_setVisibility(false);
        } else if (Global.vars.previousForm == "frmNHA" || (Global.vars.caseOpenedFromConcepts === true && CaseData.caseinfo.formSaved !== undefined && CaseData.caseinfo.formSaved == "frmNHA")) {
            frmNHA.multimedia.lblMorePhotos.text = "+" + (Global.vars.newPhotos.length - 3);
            frmNHA_multimedia_flcNoMedia_setVisibility(false);
        } else if (Global.vars.previousForm == "frmClamp") {
            frmClamp.lblMorePhotos.text = "+" + (Global.vars.newPhotos.length - 3);
            frmClamp.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmActiveCaseResume") {
            frmActiveCaseResume.lblMorePhotos.text = "+" + (Global.vars.newPhotos.length - 3);
            frmActiveCaseResume.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterConcept") {
            frmRegisterConcept.lblMorePhotos.text = "+" + (Global.vars.newPhotos.length - 3);
            frmRegisterConcept.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterResume") {
            frmRegisterResume.lblMorePhotos.text = "+" + (Global.vars.newPhotos.length - 3);
            frmRegisterResume.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterLabel") {
            frmRegisterLabel.lblMorePhotos.text = "+" + (Global.vars.newPhotos.length - 3);
            frmRegisterLabel.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmCheckLabel") {
            frmCheckLabel.lblMorePhotos.text = "+" + (Global.vars.newPhotos.length - 3);
            frmCheckLabel.flcNoMedia.setVisibility(true);
        } else if (Global.vars.previousForm == "frmRegisterOverview") {
            frmRegisterOverview.lblMorePhotos.text = "+" + (Global.vars.newPhotos.length - 3);
            frmRegisterOverview.flcNoMedia.setVisibility(true);
        }
    }
    if (Global.vars.previousForm == "frmResume") {
        frmResume.multimedia.forceLayout();
    }
}

function frmPhotos_onclick_btnBack() {
    frmPhotos_reset();
    if (Global.vars.previousForm == "frmResume") {
        frmResume.show();
    } else if (Global.vars.previousForm == "frmNHA") {
        frmNHA.show();
    } else if (Global.vars.previousForm == "frmClamp") {
        frmClamp.show();
    } else if (Global.vars.previousForm == "frmActiveCaseResume") {
        frmActiveCaseResume.show();
    } else if (Global.vars.previousForm == "frmRegisterConcept") {
        frmRegisterConcept.show();
    } else if (Global.vars.previousForm == "frmRegisterResume") {
        frmRegisterResume.show();
    } else if (Global.vars.previousForm == "frmRegisterLabel") {
        frmRegisterLabel.show();
    } else if (Global.vars.previousForm == "frmCheckLabel") {
        frmCheckLabel.show();
    } else if (Global.vars.previousForm == "frmRegisterOverview") {
        frmRegisterOverview.show();
    } else {
        frmHandleCharacteristic_showForm();
    }
}

function frmPhotos_onclick_btnDone() {
    frmPhotos_reset();
    if (Global.vars.previousForm == "frmResume") {
        frmResume.show();
    } else if (Global.vars.previousForm == "frmNHA") {
        frmNHA.show();
    } else if (Global.vars.previousForm == "frmClamp") {
        frmClamp.show();
    } else if (Global.vars.previousForm == "frmActiveCaseResume") {
        frmActiveCaseResume.show();
    } else if (Global.vars.previousForm == "frmRegisterConcept") {
        frmRegisterConcept.show();
    } else if (Global.vars.previousForm == "frmRegisterResume") {
        frmRegisterResume.show();
    } else if (Global.vars.previousForm == "frmRegisterLabel") {
        frmRegisterLabel.show();
    } else if (Global.vars.previousForm == "frmCheckLabel") {
        frmCheckLabel.show();
    } else if (Global.vars.previousForm == "frmRegisterOverview") {
        frmRegisterOverview.show();
    } else {
        frmHandleCharacteristic_showForm();
    }
}

function frmPhotos_onclickRemove_FooterReceiptRemove() {
    frmPhotos.segPhotos.removeAll();
    frmPhotos_flcSegPhotos_setVisibility(false);
    frmPhotos_flcSegPhotosSelect_setVisibility(true);
    frmPhotos.segPhotosSelect.setData(Global.vars.newPhotos);
    frmPhotos.btnEdit.text = voltmx.i18n.getLocalizedString("bt_done");
    frmPhotos_flcFooterMain_setVisibility(true);
    frmPhotos.flcLayout.bottom = "73dp";
}

function frmPhotos_setReceipt(photoinfo) {
    voltmx.print("#### frmPhotos_setReceipt: " + JSON.stringify(photoinfo));
    //receipt
    for (var i in Global.vars.newPhotos) {
        var v = Global.vars.newPhotos[i];
        if (v.id == photoinfo.id) {
            v.imgReceipt = "butreceiptinverse.png";
        } else {
            v.imgReceipt = "empty.png";
        }
    }
    var photoData = [];
    for (var j = 0; j < Global.vars.newPhotos.length; j++) {
        voltmx.print("### frmPhotos_setPhotosOnForm Global.vars.newPhotos signature check length: " + Global.vars.newPhotos.length);
        var w = Global.vars.newPhotos[j];
        if ((w.fileName !== undefined && w.fileName != null && w.fileName.startsWith("signature") === false) || w.id === undefined || w.id === null) {
            photoData.push(w);
        }
    }
    frmPhotos.segPhotos.setData(photoData);
    frmPhotos_updatePhotoInCase(photoinfo);
}

function frmPhotos_updatePhotoInCase(photoinfo) {
    voltmx.print("#### frmPhotos_updatePhotoInCase CaseData multimedia before: " + JSON.stringify(CaseData.multimedia));
    voltmx.print("#### frmPhotos_updatePhotoInCase photoinfo: " + JSON.stringify(photoinfo));
    if (photoinfo != null) {
        // add record to CaseData.text
        var loctextindex = null;
        for (var p = 0;
            ((CaseData.multimedia) != null) && p < CaseData.multimedia.length; p++) {
            var v = CaseData.multimedia[p];
            if ((v.fileName == photoinfo.fileName)) {
                voltmx.print("#### frmPhotos_updatePhotoInCase photo exists: " + v + " index: " + p);
                loctextindex = p;
                v.documentType = "Receipt";
                v.description = v.description.replace("Extra foto", "Betaalbewijs");
            } else if (v.documentType == "Receipt") {
                var documentType = "ExtraPhoto";
                if (CaseData.caseinfo.caseType == "CONTROL_K") {
                    documentType = documentType + CaseData.caseinfo.caseType;
                }
                v.documentType = documentType;
                v.description = v.description.replace("Betaalbewijs", "Extra foto");
            }
        }
        if (loctextindex === null) {
            voltmx.print("### frmPhotos_updatePhotoInCase addrecord: " + JSON.stringify(photoinfo));
            alert(voltmx.i18n.getLocalizedString("l_photoNotFound"));
        }
        voltmx.print("### frmPhotos_updatePhotoInCase CaseData multimedia after: " + JSON.stringify(CaseData.multimedia));
    }
    frmPhotos_reset();
}

function frmPhotos_openMediaGallery() {
    try {
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) {
            //Creates an object of class 'CameraSwiftiOS'
            var CameraSwiftiOSObject = new REDLINE.CameraSwiftiOS();
            //Invokes method 'getImageFromGallery' on the object
            CameraSwiftiOSObject.getImageFromGallery(
                /**Function*/
                frmPhotos_onselection_photoiOS);
        } else {
            var querycontext = {
                mimetype: "image/*"
            };
            returnStatus = voltmx.phone.openMediaGallery(frmPhotos_onselection_photo, querycontext);
        }
    } catch (err) {
        alert(voltmx.i18n.getLocalizedString("l_mediaGalleryError") + err);
    }
}

function frmPhotos_onselection_photo(rawbytes) {
    if (rawbytes == null) {
        return;
    }
    voltmx.print("#### frmPhotos_onselection_photo ###");
    Global.vars.mediaSequence = CaseData.multimedia.length;
    voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loadPhoto"), "center", false, true, {
        enablemenukey: true,
        enablebackkey: true
    });
    try {
        // get image
        Global.vars.mediaSequence = Global.vars.mediaSequence + 1;
        var d = new Date();
        var n = d.getTime();
        var lMediaName = "extrafoto" + n;
        if (CaseData.caseinfo.caseType == "CONTROL_K") {
            lMediaName = CaseData.processinfo.lastTaskProcessed.taskType + n;
        }
        // set photo
        voltmx.print("### frmPhotos_onselection_photo rawbytes: " + rawbytes);
        var resizeimage = voltmx.image.createImage(rawbytes);
        voltmx.print("### image width: " + resizeimage.getImageWidth());
        voltmx.print("### image heigth: " + resizeimage.getImageHeight());
        var pixels = 0;
        if (resizeimage.getImageHeight() > resizeimage.getImageWidth()) {
            pixels = resizeimage.getImageHeight();
        } else {
            pixels = resizeimage.getImageWidth();
        }
        var scalefactor = (1 / pixels) * 800;
        voltmx.print("### scalefactor: " + scalefactor);
        resizeimage.scale(scalefactor);
        voltmx.print("### image width 2: " + resizeimage.getImageWidth());
        //Save photo to couch
        var imgData = resizeimage.getImageAsRawBytes();
        var description = "Extra foto " + Global.vars.caseTypeDescription; // + " " + d.getHours().toString().lpad("0",2)+":"+d.getMinutes().toString().lpad("0",2)+":"+d.getSeconds().toString().lpad("0",2);
        frmPhotos_addAttachmentInline(imgData, lMediaName, description);
        voltmx.runOnMainThread(frmPhotos_checkPhotoNumber, []);
    } catch (err) {
        voltmx.print("### Failed to process photo: " + err);
        alert(voltmx.i18n.getLocalizedString("l_photoSelectFailed"));
        voltmx.application.dismissLoadingScreen();
    }
}

function frmPhotos_onselection_photoiOS(base64) {
    voltmx.print("#### frmPhotos_onselection_photoiOS ###");
    if (base64 !== "###CancelGallery") {
        Global.vars.mediaSequence = CaseData.multimedia.length;
        voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loadPhoto"), "center", false, true, {
            enablemenukey: true,
            enablebackkey: true
        });
        try {
            // get image
            Global.vars.mediaSequence = Global.vars.mediaSequence + 1;
            var d = new Date();
            var n = d.getTime();
            var lMediaName = "extrafoto" + n;
            if (CaseData.caseinfo.caseType == "CONTROL_K") {
                lMediaName = CaseData.processinfo.lastTaskProcessed.taskType + n;
            }
            var imgData = base64;
            var description = "Extra foto " + Global.vars.caseTypeDescription; // + " " + d.getHours().toString().lpad("0",2)+":"+d.getMinutes().toString().lpad("0",2)+":"+d.getSeconds().toString().lpad("0",2);
            frmPhotos_Couch_addAttachmentiOS(imgData, lMediaName, description);
            voltmx.runOnMainThread(frmPhotos_checkPhotoNumber, []);
        } catch (err) {
            voltmx.print("### Failed to process photo: " + err);
            alert(voltmx.i18n.getLocalizedString("l_photoSelectFailed"));
            voltmx.application.dismissLoadingScreen();
        }
    }
}

function frmPhotos_getOnlineAttachments() {
    voltmx.print("#### frmPhotos_getOnlineAttachments");
    voltmx.print("### frmPhotos_getOnlineAttachments Global.vars.photosLoaded: " + Global.vars.photosLoaded);

    function service_getAttachmentListCallback(result) {
        voltmx.print("### frmPhotos_getImages frmPhotos_getOnlineAttachments currentform: " + voltmx.application.getCurrentForm().id);
        voltmx.print("#### frmPhotos_getOnlineAttachments service_getAttachmentListCallback");
        voltmx.print("### frmPhotos_getOnlineAttachments service_getAttachmentListCallback result: " + JSON.stringify(result));
        if (result.opstatus === 0 && result.httpStatusCode == 200) {
            if (result.response !== undefined && result.response.length > 0) {
                var getOnlinePhotos = false;
                _photopop = 0;
                _getCurrentForm = null;
                for (var i in result.response) {
                    var v = result.response[i];
                    if ((v.contentType == 'image/png' || v.contentType == 'image/jpeg') && v.documentType != "SmallMap") {
                        voltmx.print("### frmPhotos_getOnlineAttachments service_getAttachmentListCallback photos available online, get attachments and place in formPhotos");
                        var attachmentId = v.attachmentId;
                        var filename = v.fileName;
                        voltmx.print("### frmPhotos_getOnlineAttachments service_getAttachmentListCallback photos available online, check if photo is already on form");
                        if (Global.vars.newPhotos.length > 0) {
                            for (var j in Global.vars.newPhotos) {
                                var w = Global.vars.newPhotos[j];
                                if (voltmx.string.startsWith(w.id, attachmentId) === false) { //dit checkt of de foto er al is in de reguliere foto lijst
                                    if (Global.vars.getPhotos.indexOf(attachmentId) == -1) { //dit checkt of het attachmentid al voorkomt in de getphotos lijst
                                        voltmx.print("### frmPhotos_getOnlineAttachments service_getAttachmentListCallback photos available online, photo not on form, add 1");
                                        //NOT found
                                        Global.vars.getPhotos.push(attachmentId);
                                        _photopop++;
                                        voltmx.print("### frmPhotos_getImages frmPhotos_getOnlineAttachments _photopop: " + _photopop);
                                        getOnlinePhotos = true;
                                    }
                                }
                            }
                        } else {
                            if (Global.vars.getPhotos.indexOf(attachmentId) == -1) { //dit checkt of het attachmentid al voorkomt in de getphotos lijst
                                voltmx.print("### frmPhotos_getOnlineAttachments service_getAttachmentListCallback photos available online, photo not on form, add 2");
                                //NOT found
                                Global.vars.getPhotos.push(attachmentId);
                                _photopop++;
                                voltmx.print("### frmPhotos_getImages frmPhotos_getOnlineAttachments _photopop: " + _photopop);
                                getOnlinePhotos = true;
                            }
                        }
                    }
                }
                voltmx.print("### frmPhotos_getOnlineAttachments Global.vars.getPhotos: " + Global.vars.getPhotos.length);
                if (getOnlinePhotos === true) {
                    frmPhotos_getImages();
                } else {
                    Global.vars.photosLoaded = true;
                }
            }
        }
    }

    function service_getAttachmentListErrorCallback(error) {
        voltmx.print("#### frmPhotos_getOnlineAttachments service_getAttachmentListErrorCallback");
        voltmx.print("### frmPhotos_getOnlineAttachments service_getAttachmentListErrorCallback error: " + JSON.stringify(error));
    }
    //
    if (Global.vars.photosLoaded === false) {
        voltmx.print("### frmPhotos_getOnlineAttachments service_getAttachmentList");
        voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_loading"), "center", false, true, {
            enablemenukey: true,
            enablebackkey: true
        });
        service_getAttachmentList(Global.vars.claimedDocID, service_getAttachmentListCallback, service_getAttachmentListErrorCallback)
    }
}

function frmPhotos_getImages() {
    voltmx.print("#### frmPhotos_getImages");
    voltmx.print("### frmPhotos_getImages Global.vars.newPhotos.length: " + Global.vars.newPhotos.length);
    var getPhoto = null;
    var image = {
        imgPhoto: {
            base64: ""
        }
    };

    function service_getAttachmentErrorCallback(err) {
        voltmx.print("### frmPhotos_getImages service_getAttachmentErrorCallback err: " + JSON.stringify(err));
        //voltmx.application.dismissLoadingScreen();
        frmPhotos_getImages();
    }

    function service_getAttachmentCallback(result) {
        voltmx.print("### frmPhotos_getImages service_getAttachmentCallback result: " + JSON.stringify(result));
        if (result.opstatus === 0 && result.httpStatusCode == 200) {
            if (result.response !== undefined && result.response.length > 0) {
                Global.vars.mediaSequence = Global.vars.mediaSequence + 1;
                var imageBase64 = result.response[0].base64;
                // handle result
                var lLocationText = "";
                var houseNumberAddition = "";
                if (CaseData.location.houseNumberAddition != null) {
                    houseNumberAddition = " " + CaseData.location.houseNumberAddition;
                }
                if ((CaseData.location.houseNumber != null && CaseData.location.houseNumber !== "null")) {
                    lLocationText = CaseData.location.street + " " + CaseData.location.houseNumber + houseNumberAddition;
                } else {
                    lLocationText = CaseData.location.street + houseNumberAddition;
                }
                var time = new Date();
                voltmx.print("### frmPhotos_getImages file modification time 1b: " + time);
                var formatted = Utility_getLocalizedDateTimeString(time, false);
                if (CaseData.time.localeShortDate != null) {
                    formatted = CaseData.time.localeShortDate;
                } else {
                    formatted = Utility_getLocalizedDateTimeString(new Date(CaseData.time.utcDateTime), false);
                }
                voltmx.print("### frmPhotos_getImages service_getAttachmentCallback Global.vars.newPhotos fileName: " + JSON.stringify(result.response[0].fileName));
                var _imgReceipt = result.response[0].documentType == "Receipt" ? "butreceiptinverse.png" : "empty.png";
                var newPhoto = {
                    lblLocationHeader: "Locatie",
                    lblLocation: lLocationText,
                    imgPhoto: {
                        base64: imageBase64
                    },
                    lblDate: formatted,
                    imgReceipt: _imgReceipt,
                    id: getPhoto,
                    fileName: result.response[0].fileName,
                    nr: Global.vars.mediaSequence,
                    imgSelect: "unchecked.png"
                };
                Global.vars.newPhotos.push(newPhoto);
                voltmx.print("### frmPhotos_getImages Global.vars.newPhotos 1: " + JSON.stringify(Global.vars.newPhotos));
                if (Global.vars.newPhotos.length > 0) {
                    Global.vars.newPhotos.sort(function(a, b) {
                        return b.nr - a.nr;
                    });
                }
                voltmx.runOnMainThread(frmPhotos_checkPhotoNumber, []); //werkt alleen op iOS
                frmPhotos_getImages();
            } else {
                //voltmx.application.dismissLoadingScreen();
                frmPhotos_getImages();
            }
        } else {
            voltmx.print("### frmPhotos_getImages service_getAttachmentCallback failed, try next one");
            //voltmx.application.dismissLoadingScreen();
            frmPhotos_getImages();
        }
    }
    voltmx.print("### frmPhotos_getImages Global.vars.getPhotos.length: " + Global.vars.getPhotos.length);
    if (Global.vars.getPhotos.length > 0) {
        voltmx.print("### frmPhotos_getImages getPhotos.pop currentform: " + voltmx.application.getCurrentForm().id);
        if (_getCurrentForm == null) {
            _getCurrentForm = voltmx.application.getCurrentForm().id;
            voltmx.print("### frmPhotos_getImages getPhotos.pop _getCurrentForm: " + _getCurrentForm);
            voltmx.print("### frmPhotos_getImages getPhotos.pop _photopop: " + _photopop);
        }
        getPhoto = Global.vars.getPhotos.pop();
        _photopop--;
        voltmx.print("### frmPhotos_getImages Photo: " + getPhoto); //dit is het attachmentid wat opgehaald moet worden
        service_getAttachment(Global.vars.claimedDocID, getPhoto, service_getAttachmentCallback, service_getAttachmentErrorCallback);
    } else {
        Global.vars.photosLoaded = true;
        voltmx.application.dismissLoadingScreen();
        voltmx.print("### frmPhotos_getImages getAttachment done");
        voltmx.print("### frmPhotos_getImages getAttachment currentform: " + voltmx.application.getCurrentForm().id);
        voltmx.print("### frmPhotos_getImages getPhotos.pop _photopop: " + _photopop);
        if (_getCurrentForm != voltmx.application.getCurrentForm().id && _photopop !== 0) {
            voltmx.print("### frmPhotos_getImages all ready moved from orig form " + _getCurrentForm + " to " + voltmx.application.getCurrentForm().id);
            _getCurrentForm = null;
            _photopop = 0;
            Global.vars.photosLoaded = false;
            Global.vars.getPhotos = [];
            Global.vars.newPhotos = [];
        }
        if (Global.vars.newPhotos.length > 0) {
            voltmx.print("### frmPhotos_getImages getAttachment place photos on form");
            voltmx.runOnMainThread(frmPhotos_setPhotosOnForm, []);
        } else {
            voltmx.print("### frmPhotos_getImages getAttachment no photos to place on form");
        }
    }
}