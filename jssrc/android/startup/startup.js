//startup.js
var globalhttpheaders = {};
var appConfig = {
    appId: "Redline",
    appName: "Twyns Test",
    appVersion: "2.6.0",
    isturlbase: "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services",
    isDebug: false,
    isMFApp: true,
    appKey: "9279fc1517211a022f36ab50d723b951",
    appSecret: "********************************",
    serviceUrl: "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/authService/100000002/appconfig",
    svcDoc: {
        "identity_meta": {
            "idpPORTOFROTTERDAM": {
                "success_url": "allow_any"
            },
            "idpTWYNS": {
                "success_url": "allow_any"
            },
            "idpREDORA": {
                "success_url": "allow_any"
            },
            "MobileAppAzureAD": {
                "success_url": "allow_any"
            },
            "idpTWYNSPLATFORM": {
                "success_url": "allow_any"
            }
        },
        "app_version": "1.0",
        "baseId": "4e6f9c52-e5ef-4ea0-8d9b-c61b8aee1972",
        "app_default_version": "1.0",
        "login": [{
            "provider_type": "oauth2",
            "enable_identity_pkce": false,
            "alias": "idpTWYNSPLATFORM",
            "type": "oauth2",
            "prov": "idpTWYNSPLATFORM",
            "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/authService/100000002"
        }, {
            "provider_type": "oauth2",
            "enable_identity_pkce": false,
            "alias": "idpREDORA",
            "type": "oauth2",
            "prov": "idpREDORA",
            "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/authService/100000002"
        }, {
            "provider_type": "oauth2",
            "enable_identity_pkce": false,
            "alias": "idpPORTOFROTTERDAM",
            "type": "oauth2",
            "prov": "idpPORTOFROTTERDAM",
            "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/authService/100000002"
        }, {
            "provider_type": "oauth2",
            "enable_identity_pkce": false,
            "alias": "idpTWYNS",
            "type": "oauth2",
            "prov": "idpTWYNS",
            "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/authService/100000002"
        }, {
            "provider_type": "oauth2",
            "enable_identity_pkce": false,
            "alias": "MobileAppAzureAD",
            "type": "oauth2",
            "prov": "MobileAppAzureAD",
            "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/authService/100000002"
        }],
        "services_meta": {
            "KVKapi": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/KVKapi"
            },
            "AssignCloud": {
                "type": "integsvc",
                "version": "2.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/AssignCloud"
            },
            "ScanUnit": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/ScanUnit",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/ScanUnit"
            },
            "CaseManagementData": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/CaseManagementData",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/CaseManagementData"
            },
            "AddSig": {
                "offline": false,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/AddSig",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/AddSig"
            },
            "Waterwaymarkers": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/Waterwaymarkers"
            },
            "LogonServiceTWYNS": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LogonServiceTWYNS"
            },
            "Vehicle": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/Vehicle",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/Vehicle"
            },
            "GetUserByOfficerName": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetUserByOfficerName"
            },
            "HectometerMarkerService": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/HectometerMarkerService"
            },
            "ApplicationItemAuthorization": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/ApplicationItemAuthorization",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/ApplicationItemAuthorization"
            },
            "ParkingCheckCard": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/ParkingCheckCard"
            },
            "setTaskOutcome": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/setTaskOutcome"
            },
            "Waterwaymarker": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/Waterwaymarker",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/Waterwaymarker"
            },
            "Tasks": {
                "type": "integsvc",
                "version": "3.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/Tasks"
            },
            "GetPersonInfoSSN": {
                "type": "integsvc",
                "version": "3.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetPersonInfoSSN"
            },
            "RelationDBPostgres": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/RelationDBPostgres",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/RelationDBPostgres"
            },
            "GetBoatInfo": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetBoatInfo"
            },
            "HectometerMarkerData": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/HectometerMarkerData",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/HectometerMarkerData"
            },
            "KilometerMarker": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/KilometerMarker",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/KilometerMarker"
            },
            "ParkingCheck": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/ParkingCheck"
            },
            "Planning": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/Planning",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/Planning"
            },
            "Person": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/Person",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/Person"
            },
            "ParkingService": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/ParkingService"
            },
            "AttachmentService": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/AttachmentService"
            },
            "CouchDBGetAttachment": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/CouchDBGetAttachment"
            },
            "Instance": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/Instance",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/Instance"
            },
            "GetPersonInfoValidateDocument": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetPersonInfoValidateDocument"
            },
            "HectometerMarker": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/HectometerMarker",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/HectometerMarker"
            },
            "CaseServiceActions": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/CaseServiceActions"
            },
            "AppVersion": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/AppVersion",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/AppVersion"
            },
            "LocationService": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LocationService"
            },
            "GetVehicleInfo": {
                "type": "integsvc",
                "version": "2.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetVehicleInfo"
            },
            "ANPRapi": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/ANPRapi"
            },
            "HectometerPost": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/HectometerPost"
            },
            "CaseServiceCouchDB": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/CaseServiceCouchDB"
            },
            "LogonServiceTWYNSPLATFORM": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LogonServiceTWYNSPLATFORM"
            },
            "GetLocationObjects": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetLocationObjects"
            },
            "CaseApi": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/CaseApi"
            },
            "GetUserByOfficerNumber": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetUserByOfficerNumber"
            },
            "KVKHandelsregister": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/KVKHandelsregister"
            },
            "GetPersonInfoOther": {
                "type": "integsvc",
                "version": "3.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetPersonInfoOther"
            },
            "LogonService": {
                "type": "integsvc",
                "version": "4.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LogonService"
            },
            "LogonServiceREDORA": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LogonServiceREDORA"
            },
            "ReferenceData": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/ReferenceData",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/ReferenceData"
            },
            "GetInstanceUsers": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetInstanceUsers"
            },
            "KilometerMarkerService": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/KilometerMarkerService"
            },
            "LogonServicePORTOFROTTERDAM": {
                "type": "integsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LogonServicePORTOFROTTERDAM"
            },
            "Theme": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/Theme",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/Theme"
            },
            "Offence": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/Offence",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/Offence"
            },
            "Location": {
                "offline": true,
                "metadata_url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/metadata/v1/Location",
                "type": "objectsvc",
                "version": "1.0",
                "url": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/data/v1/Location"
            }
        },
        "selflink": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/authService/100000002/appconfig",
        "integsvc": {
            "KVKapi": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/KVKapi",
            "AssignCloud": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/AssignCloud",
            "CouchDBGetAttachment": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/CouchDBGetAttachment",
            "GetPersonInfoValidateDocument": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetPersonInfoValidateDocument",
            "Waterwaymarkers": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/Waterwaymarkers",
            "LogonServiceTWYNS": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LogonServiceTWYNS",
            "CaseServiceActions": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/CaseServiceActions",
            "LocationService": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LocationService",
            "GetUserByOfficerName": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetUserByOfficerName",
            "GetVehicleInfo": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetVehicleInfo",
            "ANPRapi": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/ANPRapi",
            "HectometerMarkerService": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/HectometerMarkerService",
            "HectometerPost": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/HectometerPost",
            "ParkingCheckCard": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/ParkingCheckCard",
            "setTaskOutcome": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/setTaskOutcome",
            "CaseServiceCouchDB": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/CaseServiceCouchDB",
            "LogonServiceTWYNSPLATFORM": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LogonServiceTWYNSPLATFORM",
            "GetLocationObjects": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetLocationObjects",
            "CaseApi": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/CaseApi",
            "GetUserByOfficerNumber": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetUserByOfficerNumber",
            "Tasks": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/Tasks",
            "GetPersonInfoSSN": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetPersonInfoSSN",
            "KVKHandelsregister": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/KVKHandelsregister",
            "GetPersonInfoOther": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetPersonInfoOther",
            "LogonService": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LogonService",
            "_internal_logout": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/IST",
            "LogonServiceREDORA": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LogonServiceREDORA",
            "GetInstanceUsers": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetInstanceUsers",
            "GetBoatInfo": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/GetBoatInfo",
            "KilometerMarkerService": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/KilometerMarkerService",
            "ParkingCheck": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/ParkingCheck",
            "LogonServicePORTOFROTTERDAM": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/LogonServicePORTOFROTTERDAM",
            "ParkingService": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/ParkingService",
            "AttachmentService": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/AttachmentService"
        },
        "service_doc_etag": "000001973AF97748",
        "appId": "fab42518-ab02-46ce-b63e-4385c96667a6",
        "identity_features": {
            "reporting_params_header_allowed": true
        },
        "name": "REDLINE",
        "reportingsvc": {
            "session": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/IST",
            "custom": "https://voltmx-foundry.dev.eu-central-1.rl.twyns.com/services/CMS"
        }
    },
    runtimeAppVersion: "1.0",
    eventTypes: ["ServiceResponse", "Error", "Crash"],
};
sessionID = "";

function setAppBehaviors() {
    voltmx.application.setApplicationBehaviors({
        applyMarginPaddingInBCGMode: false,
        adherePercentageStrictly: true,
        retainSpaceOnHide: true,
        marginsIncludedInWidgetContainerWeight: true,
        APILevel: 9200,
        isCompositeApp: false
    });
};

function appInit(params) {
    skinsInit();
    initializeMVCTemplates();
    initializeUserWidgets();
    initializeTemp05251e73d5f5a43();
    initializeTemp0de015fc99f5b42();
    initializeTemp0fa3eb36ccd354b();
    initializeTemp0i8a0697aa3b444();
    initializesegTplsegHeaderDiagnostics();
    initializesegTplsegHeaderGreen();
    initializesegTplsegHeaderGreen2();
    initializesegTplsegHeaderGrey();
    initializesegTplsegHeaderGreyBold();
    initializesegTplsegHeaderNA();
    initializesegTplsegHeaderOrange();
    initializesegTplsegHeaderQuestions();
    initializesegTplsegHeaderRed();
    initializesegTplsegHeaderYellow();
    initializetmpCardCheck();
    initializetmpCases();
    initializetmpCasesFull();
    initializetmpCasesFullNoPerson();
    initializetmpCasesFullNoPersonNoVehicle();
    initializetmpCasesFullPerson();
    initializetmpCasesFullPersonNoVehicle();
    initializetmpCasesFullPersonNoVehicleCountry();
    initializetmpChoosePrinter();
    initializetmpFlcSectionHeader1();
    initializetmpFlcSectionHeaderChecks();
    initializetmpFlcSectionHeaderHotspot();
    initializetmpFlcSectionHeaderTask();
    initializetmpFullPhoto();
    initializetmpHeaderUpdateCase();
    initializetmpHistoricLicenseplate();
    initializetmpHistoryCase();
    initializetmpHistoryCase2();
    initializetmpHistoryCase3();
    initializetmpHistoryCaseText();
    initializetmpHistoryCaseText2();
    initializetmpHistoryCaseText3();
    initializetmpLabelCases();
    initializetmpLabelCasesSelect();
    initializetmpListItemNoPermit();
    initializetmpListItemNoViolation();
    initializetmpListItemReport();
    initializetmpListItemTimeExpired();
    initializetmpMenu();
    initializetmpMenuSettingItem();
    initializetmpOffenceSearchItem();
    initializetmpOverviewTaskCases();
    initializetmpPhotos();
    initializetmpPhotosSelect();
    initializetmpProhibitions();
    initializetmpQuestionTypesSegitemChoose();
    initializetmpQuestionTypesSegitemChooseFilled();
    initializetmpQuestionTypesSegitemChooseMandatory();
    initializetmpRoadSignSearchItem();
    initializetmpSegAreas();
    initializetmpSegAreasSmall();
    initializetmpSegChoiceItem();
    initializetmpSegDoubleitem();
    initializetmpSegDoubleitemGrow();
    initializetmpSegDoubleitemRedHeader();
    initializetmpSegDoubleitemResumeOverview();
    initializetmpSegDoubleitemReverse();
    initializetmpSegFilteritem();
    initializetmpSegGrowTexStatement();
    initializetmpSegGrowText();
    initializetmpSegItemsVehicleCountry();
    initializetmpSegListItem();
    initializetmpSegListItemClamp();
    initializetmpSegLocationitem();
    initializetmpSegitem();
    initializetmpSegitemGrow();
    initializetmpSegitemHandle();
    initializetmpSegitemHandleSelect();
    initializetmpSegitemRichText();
    initializetmpShortOffencePresentationItem();
    initializetmpTaskCases();
    initializetmpTaskCasesNoVehicleCountry();
    initializetmpTaskCasesPerson();
    initializetmpTimeline();
    initializetmpMapCallOut();
    initializetmpMapCallOutLabel();
    CopyfrmResumeGlobals();
    SaveKeepingGlobals();
    frmANPROverlayGlobals();
    frmActiveCaseResumeGlobals();
    frmActiveCasesGlobals();
    frmAreaGlobals();
    frmBusRideGlobals();
    frmBusRideNumbersGlobals();
    frmBusStationGlobals();
    frmCardCheckGlobals();
    frmCaseTypeSelectGlobals();
    frmCaseTypeSelectListGlobals();
    frmCheckCardGlobals();
    frmCheckLabelGlobals();
    frmCheckLocationGlobals();
    frmCheckLocationObjectGlobals();
    frmCheckLocationObjectListGlobals();
    frmCheckVehicleGlobals();
    frmChooseRegistrationGlobals();
    frmClampGlobals();
    frmCloudRouteGlobals();
    frmConceptsGlobals();
    frmCouchSyncGlobals();
    frmDummyGlobals();
    frmEnforcementObjectGlobals();
    frmEnforcementObjectListGlobals();
    frmFaqNSGlobals();
    frmFirstLoginGlobals();
    frmFollowGlobals();
    frmHandleGlobals();
    frmHandleCharacteristicGlobals();
    frmHistoryGlobals();
    frmImageViewerGlobals();
    frmInfoGlobals();
    frmKilometerRibbonGlobals();
    frmLocationGlobals();
    frmLocationCharacteristicGlobals();
    frmLocationCitiesGlobals();
    frmLogOnGlobals();
    frmLoginGlobals();
    frmNHAGlobals();
    frmOffenceSelectGlobals();
    frmOnBusStationGlobals();
    frmOnStationGlobals();
    frmOpenTasksGlobals();
    frmOptionsGlobals();
    frmOptionsVariablesGlobals();
    frmOutboxGlobals();
    frmOverlayGlobals();
    frmOverlayImageGlobals();
    frmOverviewLabelGlobals();
    frmOverviewTaskGlobals();
    frmPDFGlobals();
    frmPDFViewerGlobals();
    frmPersonGlobals();
    frmPersonAlternativeSearchGlobals();
    frmPersonCitiesGlobals();
    frmPersonCountriesGlobals();
    frmPersonDocumentGlobals();
    frmPersonLegalEntityAddressGlobals();
    frmPersonLegalEntityResultGlobals();
    frmPersonManualAddressGlobals();
    frmPersonManualPersonGlobals();
    frmPersonMunicipalitiesGlobals();
    frmPersonNationalitiesGlobals();
    frmPersonResultGlobals();
    frmPersonSSNGlobals();
    frmPersonVehicleTypesGlobals();
    frmPhotoOverviewGlobals();
    frmPhotoOverview2Globals();
    frmPhotoViewGlobals();
    frmPhotosGlobals();
    frmPinLoginGlobals();
    frmPreferencesGlobals();
    frmPrinterSelectGlobals();
    frmProfileGlobals();
    frmProhibitionDetailsGlobals();
    frmProhibitionsGlobals();
    frmQuestionTypesGlobals();
    frmQuestionsGlobals();
    frmReadIDGlobals();
    frmRegisterGlobals();
    frmRegisterConceptGlobals();
    frmRegisterLabelGlobals();
    frmRegisterOverviewGlobals();
    frmRegisterResumeGlobals();
    frmResumeGlobals();
    frmSelectCaseGlobals();
    frmSelectUserGlobals();
    frmSelectVehicleTypeGlobals();
    frmShiftDetailsGlobals();
    frmStartGlobals();
    frmStatementGlobals();
    frmStatementLanguageGlobals();
    frmStationGlobals();
    frmStatisticsGlobals();
    frmSyncInitGlobals();
    frmSynchronizationGlobals();
    frmTaskOutcomesGlobals();
    frmTeamSelectGlobals();
    frmTest2Globals();
    frmTest3Globals();
    frmTrackDownGlobals();
    frmTrainRideGlobals();
    frmTrainRideOVGlobals();
    frmTrainTrackGlobals();
    frmTransportationTicketGlobals();
    frmVehicleGlobals();
    frmVehicleBrandsGlobals();
    frmVehicleColorsGlobals();
    frmVehicleCountriesGlobals();
    frmVehicleTypesGlobals();
    setAppBehaviors();
};
voltmx.visualizer.actions.postAppInitCallBack = function(eventObj) {
    return AS_AppEvents_a8aa03af0b8e4ffb9970b171e3347360(eventObj);
};

function themeCallBack() {
    initializeGlobalVariables();
    callAppMenu();
    voltmx.application.setApplicationInitializationEvents({
        init: appInit,
        preappinit: AS_AppEvents_e98e7ba2a707448c94cf5e2402dcb623,
        appservice: function(eventObject) {
            var value = AS_AppEvents_gfc962826da34888abad67df0e5b3e7c(eventObject);
            return value;
        },
        postappinit: voltmx.visualizer.actions.postAppInitCallBack,
        showstartupform: function() {
            frmStart.show();
        }
    });
};

function onSuccess(oldlocalname, newlocalename, info) {
    loadResources();
};

function onFailure(errorcode, errormsg, info) {
    loadResources();
};

function loadResources() {
    _kony.mvc.initCompositeApp(false);
    globalhttpheaders = {};
    voltmx.os.loadLibrary({
        "javaclassname": "com.konylabs.ffi.N_REDLINE"
    });
    voltmx.os.loadLibrary({
        "javaclassname": "com.konylabs.ffi.N_KonyLogger"
    });
    voltmx.os.loadLibrary({
        "javaclassname": "com.konylabs.ffi.N_binarydata"
    });
    sdkInitConfig = {
        "appConfig": appConfig,
        "isMFApp": appConfig.isMFApp,
        "appKey": appConfig.appKey,
        "appSecret": appConfig.appSecret,
        "eventTypes": appConfig.eventTypes,
        "serviceUrl": appConfig.serviceUrl
    }
    voltmx.setupsdks(sdkInitConfig, onSuccessSDKCallBack, onSuccessSDKCallBack);
};

function onSuccessSDKCallBack() {
    voltmx.theme.setCurrentTheme("REDORA", themeCallBack, themeCallBack);
}
voltmx.application.setApplicationMode(constants.APPLICATION_MODE_NATIVE);
voltmx.print = function() {
    return;
};
//This is the entry point for the application.When Locale comes,Local API call will be the entry point.
voltmx.i18n.setDefaultLocaleAsync("en", onSuccess, onFailure, null);