define("loader/load/userloadController", function() {
    return {
        constructor: function(baseConfig, layoutConfig, pspConfig) {},
        //Logic for getters/setters of custom properties
        initGettersSetters: function() {}
    };
});
define("loader/load/loadControllerActions", {
    /*
          This is an auto generated file and any modifications to it may result in corruption of the action sequence.
        */
});
define("loader/load/loadController", ["loader/load/userloadController", "loader/load/loadControllerActions"], function() {
    var controller = require("loader/load/userloadController");
    var actions = require("loader/load/loadControllerActions");
    for (var key in actions) {
        controller[key] = actions[key];
    }
    controller.initializeProperties = function() {
        defineSetter(this, "ProgressText", function(val) {
            this.view.lblProgressText.text = val;
        });
        defineGetter(this, "ProgressText", function() {
            return this.view.lblProgressText.text;
        });
        defineSetter(this, "ProgressTextVisibility", function(val) {
            this.view.flcProgressText.isVisible = val;
        });
        defineGetter(this, "ProgressTextVisibility", function() {
            return this.view.flcProgressText.isVisible;
        });
        defineSetter(this, "RedlineTextVisibility", function(val) {
            this.view.lblRedline.isVisible = val;
        });
        defineGetter(this, "RedlineTextVisibility", function() {
            return this.view.lblRedline.isVisible;
        });
        defineSetter(this, "ProgressBarWidth", function(val) {
            this.view.flcProgress.width = val;
        });
        defineGetter(this, "ProgressBarWidth", function() {
            return this.view.flcProgress.width;
        });
        defineSetter(this, "ProgressBarVisibility", function(val) {
            this.view.flcProgressBar.isVisible = val;
        });
        defineGetter(this, "ProgressBarVisibility", function() {
            return this.view.flcProgressBar.isVisible;
        });
        defineSetter(this, "SliderselectedValue", function(val) {
            this.view.slider.selectedValue = val;
        });
        defineGetter(this, "SliderselectedValue", function() {
            return this.view.slider.selectedValue;
        });
        if (this.initGettersSetters) {
            this.initGettersSetters.apply(this, arguments);
        }
    };
    return controller;
});
