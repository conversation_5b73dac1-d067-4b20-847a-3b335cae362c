define("labelFields/onstreetsanctioninfo/useronstreetsanctioninfoController", function() {
    return {};
});
define("labelFields/onstreetsanctioninfo/onstreetsanctioninfoControllerActions", {
    /*
      This is an auto generated file and any modifications to it may result in corruption of the action sequence.
    */
});
define("labelFields/onstreetsanctioninfo/onstreetsanctioninfoController", ["labelFields/onstreetsanctioninfo/useronstreetsanctioninfoController", "labelFields/onstreetsanctioninfo/onstreetsanctioninfoControllerActions"], function() {
    var controller = require("labelFields/onstreetsanctioninfo/useronstreetsanctioninfoController");
    var actions = require("labelFields/onstreetsanctioninfo/onstreetsanctioninfoControllerActions");
    for (var key in actions) {
        controller[key] = actions[key];
    }
    return controller;
});
