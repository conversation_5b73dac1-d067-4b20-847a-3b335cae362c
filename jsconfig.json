{
    "compilerOptions": {
        "module": "AMD",
        "target": "ES2020",
        "allowJs": true,
        "checkJs": false, // Disabled to avoid VoltMX API conflicts
        "baseUrl": ".",
        "paths": {
            // VoltMX module mappings
            "Global": ["./modules/Global"],
            "Utility": ["./modules/Utility"],
            "Service": ["./modules/Service"],
            "Analytics": ["./modules/Analytics"],
            "CaseData": ["./modules/CaseData"],
            "GPS": ["./modules/GPS"],
            "MapData": ["./modules/MapData"],
            "PrintLawEnforcement": ["./modules/PrintLawEnforcement"],
            "PrintParking": ["./modules/PrintParking"],
            "SyncUtil": ["./modules/SyncUtil"],
            "Validate": ["./modules/Validate"],
            "constants": ["./modules/constants"],

            // Directory mappings
            "controllers/*": ["./controllers/*"],
            "modules/*": ["./modules/*"],
            "forms/*": ["./forms/*"],
            "models/*": ["./models/*"],
            "resources/*": ["./resources/*"],
            "userwidgets/*": ["./userwidgets/*"]
        },
        "lib": ["dom", "es2020", "esnext"],
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "skipLibCheck": true,
        "forceConsistentCasingInFileNames": true,
        "moduleResolution": "node"
    },
    "exclude": [
        "node_modules",
        "**/node_modules",
        "**/build",
        "**/dist",
        "**/binaries",
        "**/buildAutomation",
        "**/certificates",
        "**/*.kar",
        "**/*.ipa",
        "**/*.apk",
        "**/*.aab",
        "**/syncclientcode.zip",
        "jssrc",
        "**/nongenerated"
    ],
    "include": ["modules/*.js", "**/*.json", "modules/**/*", "controllers/**/*", "forms/**/*"]
}
