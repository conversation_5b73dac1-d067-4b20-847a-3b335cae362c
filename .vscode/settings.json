{
    // VoltMX Iris specific settings
    "javascript.preferences.includePackageJsonAutoImports": "off",
    "javascript.suggest.autoImports": false,
    "typescript.preferences.includePackageJsonAutoImports": "off",
    "typescript.suggest.autoImports": false,

    // File associations for VoltMX files
    "files.associations": {
        "*.sm": "json",
        "*.properties": "properties",
        "*.xml": "xml"
    },

    // Search and file explorer settings
    "search.exclude": {
        "**/node_modules": true,
        "**/binaries": true,
        "**/build": true,
        "**/dist": true,
        "**/*.kar": true,
        "**/*.ipa": true,
        "**/*.apk": true,
        "**/*.aab": true,
        "**/certificates": true,
        "**/buildAutomation": true,
        "**/syncclientcode.zip": true
    },

    "files.exclude": {
        "**/binaries": true,
        "**/build": true,
        "**/dist": true,
        "**/*.kar": true,
        "**/*.ipa": true,
        "**/*.apk": true,
        "**/*.aab": true
    },

    // Editor settings for better VoltMX development
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": true,
    "editor.formatOnSave": false,
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
    },

    // Language specific settings
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.tabSize": 4
    },

    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },

    // VoltMX specific file watching
    "files.watcherExclude": {
        "**/binaries/**": true,
        "**/build/**": true,
        "**/node_modules/**": true,
        "**/certificates/**": true
    },

    // Emmet settings for VoltMX widgets
    "emmet.includeLanguages": {
        "javascript": "javascriptreact"
    },

    // Terminal settings
    "terminal.integrated.defaultProfile.osx": "bash",
    "terminal.integrated.defaultProfile.windows": "Command Prompt",

    // Git settings
    "git.ignoreLimitWarning": true,

    // Intellisense settings for VoltMX
    "javascript.suggest.completeFunctionCalls": true,
    "javascript.suggest.names": true,
    "javascript.suggest.paths": true,

    // Bracket pair colorization
    "editor.bracketPairColorization.enabled": true,
    "editor.guides.bracketPairs": "active"
}
