function Analytics_logEvent(id, name, type = "action") {
  voltmx.print("### Analytics_logEvent: id: " + id + " name: " + name + " action: " + type);
  const appName = "" + appConfig.appName;
  //#ifdef android
  var bundle = new Bundle();
  bundle.putString(FirebaseAnalytics.Param.ITEM_ID, id);
  bundle.putString(FirebaseAnalytics.Param.ITEM_NAME, name);
  bundle.putString(FirebaseAnalytics.Param.CONTENT_TYPE, type);
  bundle = setDefaultParams(bundle);
  Global.vars.firebaseAnalytics.logEvent(name, bundle);
  //#else
  if (Global.vars.firebaseAnalyticsiOS != null && 
      Global.vars.gInstanceId != null && 
      Global.vars.gUsername != null && 
      Global.vars.environmentName != null) {
    Global.vars.firebaseAnalyticsiOS.logEventWithNameScreenIdTypeInstanceEnvironmentEmailAppName(
      name,
      id,
      type,
      Global.vars.gInstanceId,
      Global.vars.environmentName,
      Global.vars.gUsername,
      appName);
  }   
  //#endif
}

function Analytics_logException(exception) {
  const screenClass = "" + voltmx.application.getCurrentForm().id;
  const appName = "" + appConfig.appName;
  //#ifdef android
  voltmx.print("### Analytics_logException: " + exception);
  var bundle = new Bundle();
  bundle.putString(FirebaseAnalytics.Param.ITEM_ID, screenClass);
  bundle.putString(FirebaseAnalytics.Param.ITEM_NAME, "exception");
  bundle.putString(FirebaseAnalytics.Param.VALUE, exception);
  bundle = setDefaultParams(bundle);
  Global.vars.firebaseAnalytics.logEvent("app_exception", bundle);
  //#else
  if (Global.vars.firebaseAnalyticsiOS != null && 
      Global.vars.gInstanceId != null && 
      Global.vars.gUsername != null && 
      Global.vars.environmentName != null) {
    Global.vars.firebaseAnalyticsiOS.logEventWithNameScreenIdTypeInstanceEnvironmentEmailAppName(
      exception,
      screenClass,
      "app_exception_ios",
      Global.vars.gInstanceId,
      Global.vars.environmentName,
      Global.vars.gUsername,
      appName);
  }
  //#endif
}

function Analytics_logScreenView(screenName) {
  voltmx.print("### Analytics_logScreenView: screenName: " + screenName);
  voltmx.runOnMainThread(Analytics_screenEventsOnMainThread, [screenName]);
}

function Analytics_screenEventsOnMainThread(screenName) {
  voltmx.print("### Analytics_screenEventsOnMainThread: screenName: " + screenName);
  const screenClass = "" + voltmx.application.getCurrentForm().id;
  voltmx.print("### Analytics_screenEventsOnMainThread: screenName: " + screenName + " screenClass: " + screenClass);
  const appName = "" + appConfig.appName;
  //#ifdef android
  if (Global.vars.firebaseAnalytics) {
   	var bundle = new Bundle();
    bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, screenName);
    bundle.putString(FirebaseAnalytics.Param.SCREEN_CLASS, screenClass);
    bundle = setDefaultParams(bundle);
    Global.vars.firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle); 
  }
  //#else
  if (Global.vars.firebaseAnalyticsiOS != null && 
      Global.vars.gInstanceId != null && 
      Global.vars.gUsername != null && 
      Global.vars.environmentName != null) {
    try {
     Global.vars.firebaseAnalyticsiOS.logScreenEventWithNameScreenIdInstanceEnvironmentEmailAppName(
      screenName, 
      screenClass, 
      Global.vars.gInstanceId, 
      Global.vars.environmentName, 
      Global.vars.gUsername,
      appName); 
    } catch (error) {
      voltmx.print("### Analytics_screenEventsOnMainThread error: " + error);
    }
  } 
  //#endif
}

function Analytics_setGlobalEventParameters() {
  //#ifdef android
  var bundle = new Bundle();
  bundle = setDefaultParams(bundle);
  Global.vars.firebaseAnalytics.setDefaultEventParameters(bundle);
  Global.vars.firebaseAnalytics.setUserProperty("user_id", Global.vars.gUsername);
  Global.vars.firebaseAnalytics.setUserProperty("email", Global.vars.gUsername);
  //#endif
}

function setDefaultParams(bundle) {
  if (Global.vars.gInstanceId != null) {
    bundle.putString("instance", Global.vars.gInstanceId);
  } 
  
  if (Global.vars.gUsername != null) {
    bundle.putString("user_id", Global.vars.gUsername);
    bundle.putString("email", Global.vars.gUsername);
  } 
  
  if (Global.vars.environmentName != null) {
    bundle.putString("environment", Global.vars.environmentName);
  }
  const appName = "" + appConfig.appName;
  bundle.putString("app_name", appName);
  return bundle;
}