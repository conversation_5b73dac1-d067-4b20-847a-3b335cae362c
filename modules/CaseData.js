let CaseData = {};

function CaseData_init() {
    CaseData = {
        statuscode: null,
        //status code
        status: null,
        //status
        indArchived: false,
        metainfo: {
            version: "1.0.1"
            //version number
        },
        processinfo: {
            activeTaskType: null,
            lastTaskProcessed: {},
            tasks: [CaseData_setNewTask()],
            statuses: [CaseData_setNewStatus()],
            assignment: {
                operationalAreaId: null, //id of the operational area (cloud) the zone has been assigned to number
                operationalAreaDesc: null, //Description of the operational area (cloud) the zone has been assigned to
                assignee: null, //the user assigned to the oprationalArea
                function: null //function assigned to the oprationalArea
            }
        },
        caseinfo: CaseData_setNewCaseInfo(),
        time: CaseData_setNewTime(),
        location: CaseData_setNewlocation(),
        vehicle: [],
        person: [],
        offence: CaseData_setNewoffence(),
        assessment: CaseData_setNewAssessment(),
        permits: [CaseData_setNewPermit()],
        parking: CaseData_setNewParking(),
        text: [],
        multimedia: [],
        option: [],
        globals: {},
        transportticket: CaseData_setNewTransportTicket(),
        prohibitions: CaseData_setProhibitionObject(),
        enforcementObject: CaseData_setNewEnforcementObject(),
        questions: [],
        registerLabel: CaseData_setRegisterLabelObject(),
        payment: CaseData_setNewPayment()
    };
    CaseData_setUserInformationToCaseInfo();
    Global.vars.preserve = [];
    Global.vars.preserveData = {};
}

function CaseData_setNewTask() {
    voltmx.print("### CaseData_setNewTask ###");
    var task = {
        taskType: null,
        //name of the task
        taskTypeId: null,
        //id of the task
        taskTypeDescription: null, //
        // type: string description: description of the taskType
        taskOutcome: null,
        //name of the task outcome
        taskOutcomeId: null,
        taskOutcomeDescription: null,
        taskOutcomeExplanation: null,
        //id of the task outcome
        taskCompletedOn: null,
        //date in UTC when task was completed
        taskClaimedBy: null,
        taskClaimedByName: null,
        //Fistname + MiddlenName + Surname
        taskCreatedOn: null,
        // type: string format: date-time description: date in UTC when task was set
        taskClaimedOn: null, //
        // type: string format: date-time description: date in UTC when task was set
        processName: null
        //type: string description: name of the process the task is linked to
    };
    return task;
}

function CaseData_setNewStatus() {
    voltmx.print("### CaseData_setNewStatus ###");
    var status = {
        status: null,
        //name of the taskType
        statuscode: null,
        //Id of the task outcome
        statusSetOn: null,
        //date in UTC when status was set
        statusSetBy: null,
        //username (email) that set the status
        statusSetByName: null
        //Fistname + MiddleName + Surname
    };
    return status;
}

function CaseData_setNewPermit() {
    voltmx.print("### CaseData_setNewPermit ###");
    var permit = {
        sneCode: null,
        //signal code (in dit geval altijd "PERMIT_ADAM")
        sneDescription: null,
        //signal code description
        sneType: null,
        //signal type
        permitDescription: null,
        //description of the Permit - (komt uit comment zie tabel)
        identNumber: null,
        //license plate not formatted
        cityCode: null,
        //code of the city (nu altijd 88)
        validFrom: null,
        //valid from date
        validTo: null
        //valid to date
    };
    return permit;
}

function CaseData_setNewParking() {
    voltmx.print("### CaseData_setNewParking ###");
    var parking = {
        serviceResult: {
            valid: null,
            //valid
            licensePlate: null,
            //licensePlate
            checkTime: null,
            //checkTime
            countryCode: null,
            //countryCode
            responses: []
            //responses
        }
    };
    return parking;
}

function CaseData_setNewPayment() {
    voltmx.print("### CaseData_setNewPayment ###");
    var payment = {
        sealbagNumber: null,
        //The number of the sealbag that carries the cash
        receiptNumber: null,
        //The receipt number of the cash payment
        cashAmount: null
        //The amount of received cash
    };
    return payment;
}

function CaseData_setNewLocationObject() {
    voltmx.print("### CaseData_setNewLocationObject ###");
    var locationobject = {
        objectnumber: null,
        subjectnumber: null,
        cocnumber: null,
        name_taxpayer: null,
        name: null,
        ssn: null,
        external_collector: null,
        street: null,
        housenumber: null,
        houseletter: null,
        addition: null,
        designation: null,
        zipcode: null,
        city: null,
        address: null,
        mastercode: null,
        connection_description: null,
        number_m2: null,
        sbi_code: null,
        exemption_start_date: null
    };
    return locationobject;
}

function CaseData_setDemoLocationObject() {
    voltmx.print("### CaseData_setDemoLocationObject ###");
    var locationobject = {
        objectnumber: 798989,
        subjectnumber: 8558951,
        cocnumber: 27124701,
        name_taxpayer: "TWYNS",
        ssn: 9292056,
        external_collector: "A01ROZENB",
        street: "Acacialaan",
        housenumber: "65",
        houseletter: "G",
        addition: null,
        designation: " ",
        zipcode: "9823WE",
        city: "Rozenburg",
        mastercode: 9308,
        connection_description: "Telefooncentrale",
        number_m2: 0,
        Sbi_code: 6190,
        exemption_start_date: "1-1-2011"
    };
    return locationobject;
}

function CaseData_setNewCaseInfo() {
    voltmx.print("### CaseData_setNewCaseInfo ###");
    var caseinfo = {
        id: null,
        //Unique GUID of the case
        externalId: null,
        //External id
        appModus: null,
        //Modus of the app
        appVersion: null,
        //type: string description: Version of the mobile app
        offenceModus: null,
        //Offence modus
        indClaimedByUser: null,
        timeClaimedByUser: null,
        indUserOnLocation: null,
        timeUserOnLocation: null,
        indComplete: false,
        //Indication if case is completed
        timeComplete: null,
        //date in UTC when case was completed
        indPrinted: false,
        //Indication if case is printed from the device
        timePrinted: null,
        //date in UTC when case was printed
        indDropped: false,
        //Indication if case is dropped
        timeDropped: null,
        //date in UTC when case was dropped
        indProcessed: false,
        //Indication if case is processed
        timeProcessed: null,
        //date in UTC when case was processed
        indPayed: false,
        //Indication if case is payed for
        incidentType: null,
        //number of the incidentType
        ticketNumber: "",
        //number that is printed on the ticket
        officerRemarks: null, //type: string description: remarks of the officer concerning the ticket
        ticketType: null,
        //tickettype number
        ticketText: null,
        //text of the ticket
        ticketTypeDescription: null,
        //description of hte tickettype
        officerNumber: null,
        //number of the officer
        officerDocumentNumber: null,
        //oath document number of the officer
        officerIdentification: null,
        //stamnummer
        officerName: null,
        //firstname, middlename and surname of the officer
        officerOath: null, //type: string description: oath of the officer (eed of belofte)
        teamName: null,
        //name of the team
        teamNumber: null,
        //number of the team
        secondOfficerUserName: null,
        //username of the second officer
        secondOfficerNumber: null,
        //number of the second officer
        secondOfficerName: null,
        //firstname, middlename and surname of the second officer
        secondOfficerOath: null,
        //type: string description: oath of the second officer (eed of belofte)
        secondOfficerIdentification: null,
        //type: string description: stam nummer of the second officer
        secondOfficerDocumentNumber: null,
        //type: string description: document numbr of the second officer
        officerFunctions: [],
        //functions of the officer
        instanceID: null,
        //instance
        caseType: null,
        //description of the case type
        caseTypeId: null,
        //id number of the case type
        caseTypeDescription: null, //Door Jeroen toegevoegd op deze plek maar wordt al in Prohibitions gevuld omdat ik ze toen nodig had
        // type: string
        // description: description of the Case type
        caseTypeCategory: null,
        //description of the case type category
        caseTypeCategoryId: null,
        //id number of the case type categorie
        caseTypeCategoryDescription: null, //Door Jeroen toegevoegd op deze plek maar wordt al in Prohibitions gevuld omdat ik ze toen nodig had
        //type: string
        //description: description of the Case type category
        timeRegistered: null,
        //date in UTC when case was registered
        updateUser: null,
        //user that updated the case
        lastUpdateTime: null,
        //date in UTC when case was updated
        createdUser: null,
        //user that created the case
        creationTime: null,
        //date in UTC when case was created
        deviceID: null,
        //id of the device
        deviceLocation: {
            latitude: 0.0,
            //Latitude for the location
            longitude: 0.0
            //Longitude for the location
        },
        inserted: false,
        //indication that case is inserted
        scanUnit: null,
        // device that created the case
        scanUnitDescription: null,
        //type: string
        //description: scan unit descriptive text
        scanId: null,
        // number of the scan
        clampNumber: null,
        //type: string description: clamp put on the vehicle
        clampWrecker: null,
        //type: boolean description: indication that this licenseplate owner is known as a clamp wrecker
        formSaved: null,
        //type: string description: the application form where the case was last saved (for example 'frmTrackDown')
        noCouchCaseNameSaved: null,
        //type: string description: when you save a no couch case offline the name is put in this string
        anprConfidenceLevel: null,
        //         type: number
        //         format: float
        //         minimum: 0
        //         maximum: 1
        //         description: confidence level of ANPR scan, between 0 and 1
        relatedId: null,
        relatedExternalId: null,
        title: null,
        uniformCivilian: "Uniform",
        registeredVehicleUsage: null, // type: string
        naturalPersonUsage: null,
        legalPersonUsage: null,
        themeCode: null,
        themeCodeDescription: null,
        themeCodeShortDescription: null,
        externalReference: null,
        notepad: null,
        indConcept: false,
        metaDataExternal: null,
        onStreetPaymentMethodValue: null,
        onStreetPaymentMethodKey: null,
        thirdOfficerUserName: null,
        //username of the third officer
        thirdOfficerNumber: null,
        //number of the third officer
        thirdOfficerName: null,
        //firstname, middlename and surname of the third officer
        thirdOfficerOath: null,
        //type: string description: oath of the third officer (eed of belofte)
        thirdOfficerIdentification: null,
        //type: string description: stam nummer of the third officer
        thirdOfficerDocumentNumber: null
        //type: string description: document numbr of the third officer
    };
    return caseinfo;
}

function CaseData_setNewTime() {
    voltmx.print("### CaseData_setNewTime ###");
    var time = {
        dateComponents: [],
        caseDate: null,
        //date of the case
        caseTime: null,
        //time of the case
        localeShortDate: null,
        //date time in localized form used in the app
        savedTime: false,
        //indication if time is saved
        utcDateTime: null,
        //utcDateTime string
        timeZoneOffset: null,
        //time Zone
        offenceStartTime: null
        //         type: string
        //         format: date-time
        //         description: offence start time in UTC in case of in-out camera
    };
    return time;
}

function CaseData_setNewlocation() {
    voltmx.print("### CaseData_setNewlocation ###");
    var location = {
        preserveLocation: false,
        //Indication if location should be preserved
        areaType: null,
        //Area type
        areaCode: null,
        //Area code
        area: null,
        //Area name
        municipality: null,
        //Municipality
        municipalityCode: null,
        //Municipality code
        city: null,
        //City
        cityCode: null,
        //City code
        street: null,
        //Street
        streetCode: null,
        //Street Code
        houseNumber: null,
        //House number
        houseLetter: null,
        //House letter additional to houseNumber
        houseNumberAddition: null,
        //Additonal info to housenumber
        latitude: 0.0,
        //Latitude for the location
        longitude: 0.0,
        //Longitude for the location
        zipcode: null,
        //Zip code
        zone: null,
        //Zone name
        zoneCode: null,
        //Zone code
        operationalAreaId: null,
        //string
        //description: Id of the operational area (cloud) the zone has been assigned to
        operationalAreaDesc: null,
        //type: string
        //description: Description of the operational area (cloud) the zone has been assigned to
        neighbourhood: null,
        //type: string
        //description: buurt
        neighbourhoodDesc: null,
        borough: null,
        //type: string
        //description: stadsdeel
        boroughDesc: null,
        district: null,
        //type: string
        //description: buurt combinatie/wijk
        districtDesc: null,
        enforcementArea: null,
        //type: string
        //description: controle gebied
        enforcementAreaDesc: null,
        urbanAreaId: null,
        //Id of the urban area
        roadType: null,
        //type of road code
        roadTypeDescription: null,
        //description of the type of road
        furtherIndication: null,
        //further indication of the road/location
        roadSignNum: null,
        //number on the road sign
        inserted: false,
        //indication that location is inserted
        edited: false,
        //indication that location is edited
        validated: false,
        //indication that location is validated
        checkTime: null,
        //Time location service was called
        chosenFurtherIndication: null,
        // type: string description: chosen further indication
        chosenFurtherIndicationKey: null,
        //type: string description: chosen further indication key
        station: null,
        //type: string description: train station where ticket is written
        stationCode: null,
        //type: sting
        platform: null,
        //type: string description: train station platform where ticket is written
        locationDescription: null,
        //type: string description: description of the location
        stationFrom: null,
        //type: string description: train station from where the train was riding
        stationFromCode: null,
        //type: sting
        stationTo: null,
        // type: string description: train station to where the train was riding
        stationToCode: null,
        //type: sting
        trackSection: null,
        // type: string description: section of the train track
        rideNumber: null,
        // type: number description: ride number of the train
        kilometerIndication: null,
        //type: string description: kilometer indication for the train track
        locationWithinUrbanArea: null,
        //type: boolean description: indication that location is within a urban area (binnen bebouwde kom) kan ook leeg zijn
        overlayAreas: [],
        //type: array description: zones to show as polygon on map to see in what kind of area you are
        busOrTrainStation: null,
        //         type: string
        //         description: bus station where ticket is written
        busOrTrainStationCode: null,
        //         type: string
        //         description: bus station code where ticket is written
        busOrTrainStationFrom: null,
        //         type: string
        //         description: bus station from where the bus was riding
        busOrTrainStationFromCode: null,
        //         type: string
        //         description: bus station from code where the bus was riding
        busOrTrainStationTo: null,
        //         type: string
        //         description: bus station to where the bus was riding
        busOrTrainStationToCode: null,
        //         type: string
        //         description: bus station code to where the bus was riding
        busOrTrainRideType: null,
        busOrTrainRideNumber: null,
        //         type: number
        //         description: ride number of the bus
        busOrTrainRideNumberCode: null,
        //         type: string
        //         description: code of ride number of the bus/bus line
        busOrTrainRideNumberVariant: null,
        busOrTrainRideNumberHeading: null,
        trainRide: null,
        //         type: string
        //         description: trainride from-to description
        trainRideCode: null,
        //         type: string
        //         description: trainride from-to code
        selectedProposition: null,
        //			type: number
        //			description: selected rownum from proposition
        propositions: []
        //			type: array
        //			description: proposition of streets
    };
    return location;
}

function CaseData_setNewvehicle() {
    voltmx.print("### CaseData_setNewvehicle ###");
    var vehicle = {
        inserted: false,
        //indication that vehicle is inserted
        edited: false,
        //indication that vehicle is edited
        validated: false,
        //indication that vehicle is validated
        objectType: null,
        //type of vehicle object for example "VML"
        vehicleType: null,
        vehicleTypeGroupId: null,
        //type of vehicle for example "P"
        vehicleTypeDesc: null,
        //discription of type of vehicle for example "Personenauto"
        vehicleTypePrintDesc: null,
        //discription of type of vehicle for printing
        vehicleTypeGroup: null,
        //vehicle type group
        vehicleTypeGroupDesc: null,
        //description of vehicle type group
        identType: vehicleIdentType.vehicle, // "01"
        //type of licenseplate vehicle
        identNumber: null,
        identNumberFormatted: null,
        //licenseplate number of vehicle
        prefix: null,
        // licensplate prefix Sharjah/AE0001
        countryLicense: Global.vars.licenseplateCountryModule, //"NL",
        //licensplate country for example "NL"
        countryLicenseCode: Global.vars.licenseplateCountryCode, //6030,
        //licensplate country code for example 6030
        countryLicenseDesc: Global.vars.licenseplateCountryDesc, //"Nederland",
        //discription of the licensplate country for example "Nederland"
        vehicleRegCheckDate: null,
        //datetime from service when vehicle registration was checked
        colorDesc: null,
        //discription of the color for example "Rood"
        brandDesc: null,
        //discription of the brand for example "Volkswagen"
        brandTypeDesc: null,
        //discription of the brandtype for example "Polo"
        indTowed: false,
        //indication that car is to be towed
        dupNumberPlate: null,
        //duplicate number of the license plate
        regionNumberPlate: null,
        //region number of the license plate (France)
        environmentalClassification: null,
        // environmental Classification
        europeanVehicleType: null,
        // european VehicleType Classification
        inspectionExpirationDate: null,
        // inspection Expiration Date (APK)
        interior: null,
        // interior of the vehicle
        fuels: [],
        // fuels
        indRegCalled: false,
        //indication that car vehicle registry service is called
        app_unLinkedToPerson: false,
        //indication that person is linked to vehicle
        app_unLinkedToLegalEntity: false,
        //indication that legal entity is linked to vehicle
        vehicleOwnerName: null,
        //name of the vehicle owner
        vehicleOwnerSSN: null,
        //ssn of the vehicle owner
        vehicleOwnerUsed: false,
        //indication that vhicle owner is to be shown
        ownerInformation: {},
        //information on the owner
        signalInfo: CaseData_setNewsignalinfo(),
        //signal information on the vehicle
        vehicleName: null,
        //name of the vehicle in case off vessel
        updateDate: null
    };
    return vehicle;
}

function CaseData_setNewperson(index) {
    if (index === undefined || index === null) {
        index = 0;
    }
    voltmx.print("### CaseData_setNewperson ###");
    var person = {
        inserted: false,
        partyFinished: false,
        personFinished: false,
        addressesFinished: false,
        identificationFinished: false,
        statusFinished: false,
        registrationPersonsFinished: false,
        interrogationSuspectFinished: false,
        edited: false,
        validated: false,
        used: false,
        index: index, //number
        personFilled: false,
        birthDateSet: false,
        accountNumber: null, //string
        partyType: null, //string
        partyId: null, //number
        idenDocType: null, //number
        idenDocTypeDesc: null, //string
        countryIdenDoc: null, //number
        countryIdenDocDesc: null, //string
        documentNumber: null, //string
        documentTypeCheckable: null, //null
        documentNumberChecked: null, //type: boolean description: document number checked agianst service
        documentNumberValid: null, //type: string description: if document number is checked set if valid or reason why not valid
        documentAdditionalDescription: null, //type: string description: document additional description
        countryAddress: null, //string
        addresses: [CaseData_setNewaddress()], //array of type addresses
        surname: null, //string
        middlename: null, //string
        givenNames: null, //string
        initials: null, //string
        fullName: null,
        birthdate: null, //string
        birthdateDesc: null, //string
        birthdateComponents: [], //array with kony date components
        yearOfBirth: null, //number
        birthMunicipNL: null, //number
        birthMunicipNLDesc: null, //string
        birthPlaceNL: null, //number
        birthPlaceNLDesc: null, //string
        age: null, //number
        birthplace: null, //string
        countryOfBirth: null, //number
        countryOfBirthDesc: null, //string
        gender: null, //number
        genderDesc: null, //string
        indicationDutch: false,
        nationality: null, //number (6030)
        nationalityDesc: null, //string
        indRegCalled: false,
        indOwnerVehicle: false,
        ssn: null, //string
        vnumber: null, //string
        anumber: null, //string
        readIDnfcVerificationStatusDS: null, //string
        readIDnfcVerificationStatusHT: null, //string
        readIDnfcVerificationStatusCS: null, //string
        readIDnfcVerificationStatusCSReason: null, //string
        readIDnfcVerificationStatusAA: null, //string
        readIDnfcVerificationStatusEACCA: null, //string
        indMRZ: false, //boolean
        indNFC: false, //boolean
        indManual: false //boolean
    };
    return person;
}

function CaseData_setNewaddress() {
    voltmx.print("### CaseData_setNewaddress ###");
    var address = {
        addressType: null, //string
        addressTypeDesc: null, //string
        countryCode: null, //number
        country: null, //string
        source: null, //string
        indInResearch: false,
        indPreferred: false,
        addressInstance: null, //string
        description: null, //string
        zipcode: null, //string
        city: null, //string
        cityCode: null, //number
        municipalCode: null, //number
        municipality: null, //string
        street: null, //string
        streetNumber: null, //number
        streetNumAdditn: null, //string
        state: null, //string
        indMobileHome: false,
        indHouseBoat: false,
        postOfficeBox: null, //string
        replyNumber: null, //string
        addressSubType: null, //string
        indSecret: false,
        addressLine1: null, //string
        addressLine2: null, //string
        addressLine3: null, //string
        inputMode: null //string for example "SSNA1"
    };
    return address;
}

function CaseData_setNewsignalinfo() {
    voltmx.print("### CaseData_setNewsignalinfo ###");
    var signalinfo = {
        signalServiceId: null,
        //Signal
        signalDescription: null
        //Signal Description
    };
    return signalinfo;
}

function CaseData_setNewoffence() {
    voltmx.print("### CaseData_setNewoffence ###");
    var offence = {
        offenceCode: null,
        //code of the offence
        offenceDescription: null,
        //Offense Description
        offenceShortDescription: null,
        //Offense Short Description
        inserted: false,
        //indication that offence is inserted
        edited: false,
        //indication that offence is edited
        validated: false,
        //indication that offence is validated
        person: false,
        //indication that a person is linked to the offence
        vehicle: false,
        //indication that a vehicle is linked to the offence
        caseId: null,
        //Unique GUID of the case
        rightsRead: null,
        //indication that a rights are read to a person
        offenceCommunicated: null,
        //indication that a offence is communicated to a person
        legalAssistCommunicated: null,
        //indication that legal assistance is communicated to a person
        usesLegalAssistance: null,
        //indication that a person is using legal assistance
        declinedLegalAssistance: null, //type: boolean description: if a person declined legal assistance, did you communicate chance to repeal
        interpreterCommunicated: null,
        //indication that interpretation is communicated to a person
        usesInterpreter: null,
        //indication that a person is using interpretation
        interpreter: null,
        //interprator
        translationLanguage: null,
        //languagecode of the translation
        translationLanguageDesc: null,
        //description of language of the translation
        statementEdited: false,
        //indication if the statement is edited
        statementKey: null,
        //string of the keyvalue chosen in the statement listbox
        statementText: null, //type: string description: the statement text (type 1)
        statementNoPledge: null, //type: string description: statement why there is no pledge (Cautie Nee) (type 20 text)
        offenceCommunicatedNoStatement: null, //type: string description: statement why there is no offence comunicated (Reden voor het niet geven van mededeling) (type 20 text)
        execByPartner: false,
        //indication if the statement is hearded by the second officer
        countryLicense: null,
        //country code of the license plate
        identNumber: null,
        //string of the license plate number
        app_offenceModus: null,
        //offence modus
        apvRequired: false,
        //indication if apv is required
        apvCode: null,
        //code of the apv
        apvDesc: null,
        //description of the apv
        apvOffenceCode: null,
        //code of the offence
        apvOffenceDesc: null,
        //description of the apv offence
        apvNumber: null,
        //number of the apv
        offenceTypeCode: null,
        //code of the offence type
        optionUsage: "",
        //indication if option is used example 1,1
        offenceCatCode: null,
        //code of the offence categorie
        offenceCatSuspects: null,
        //code of the offence categorie suspects
        offenceCatDesc: null,
        //description of the offence categorie
        amount: null,
        //amount of the offence
        amountTax: null,
        //tax amount of the offence
        amountExtra: null,
        //extra amount of the offence
        amountDisplay: null,
        //display of the amount of the offence
        amountTaxDisplay: null,
        //display of the tax amount of the offence
        amountExtraDisplay: null,
        //display of the extra amount of the offence
        ageCatCode: null,
        //age category code for the person linked to the offence
        sanctionType: null,
        //sanction type
        sanctionPartType: null,
        //sanction part type
        sanctionPartUnit: null,
        //sanction part unit
        ticketTypeId: null
        //the ttt_id for the offence, type integer int32
    };
    return offence;
}

function CaseData_setNewproposition() {
    voltmx.print("### CaseData_setNewproposition ###");
    var proposition = {
        municipality: null,
        municipalityCode: null,
        city: null,
        cityCode: null,
        street: null,
        streetCode: null,
        houseNumber: null,
        houseLetter: null,
        houseNumberAddition: null,
        zipcode: null,
        rownum: null,
        distance: null,
        areas: []
    };
    return proposition;
}

function CaseData_setNewtext() {
    voltmx.print("### CaseData_setNewtext ###");
    var text = {
        inserted: false,
        //indication text is inserted
        edited: false,
        //indication text is edited
        type: null,
        //text type (see CJIB text type descriptions)
        addedText: null,
        //text that is added
        value: null
        //value of the added
    };
    return text;
}

function CaseData_setNewmultimedia() {
    voltmx.print("### CaseData_setNewmultimedia ###");
    var multimedia = {
        fileDate: null,
        //datetime stamp of the file
        fileName: null,
        //name of media file
        description: null,
        //description of media
        documentType: null,
        //type of media document for example "photo" or "licenseplatePhoto"
        contentType: null,
        //type: string description: type of content
        attachmentId: null,
        //Couch attachment id
        base64: null,
        uploaded: null,
        //type: boolean description: indication if photo is send to server
        photoStatus: null
        //type: string description: status of the photo, can be draft or done
    };
    return multimedia;
}

function CaseData_setNewAssessment() {
    voltmx.print("### CaseData_setNewAssessment");
    var assessment = {
        assessmentRules: [CaseData_setNewAssessmentRules()],
        assessmentRulesCount: null,
        //aantal NHA regels
        assessmentRulesParkingTotal: null,
        //totaal van de parkeerkosten
        assessmentRulesCostsTotal: null,
        //totaal van de NHA kosten
        assessmentRulesCollectionCostsTotal: null,
        //totaal van de invorderingskosten
        assessmentRulesInterestAmountTotal: null,
        //totaal van het Rentebedrag
        assessmentRulesTransferredAmountTotal: null,
        //totaal van het Afgeboekt bedrag
        assessmentRulesSubTotal: null,
        //sub totaal
        clampCosts: null,
        //kosten klemmen (wordt op straat bepaald en moet uit een instantieparameter komen)
        totalAmount: null
        //subtotaal + klemkosten
    };
    return assessment;
}

function CaseData_setNewAssessmentRules() {
    voltmx.print("### CaseData_setNewAssessment");
    var assessmentRules = {
        identificatie: null,
        datum: null,
        //string met alleen de datum
        tijdstip: null,
        //string met alleen de tijd
        locatie: null,
        //string met straat, huisnummer en evt. toevoeging
        kenteken: null,
        //string met het kenteken
        landcode: null,
        //string de landcode van het kenteken
        boetebedrag: null,
        belastingbedrag: null,
        bedrag_invorderingskosten: null,
        rentebedrag: null,
        bedrag_afgeboekt: null,
        bedrag_openstaand: null,
        indicatieBezwaar: false
    };
    return assessmentRules;
}

// function CaseData_setNewOption(){
//   var Option = {
//       inserted: false,
//         //description: indication text is inserted
//       edited: false,
//       //  description: indication text is edited
//       optionId: null,
//         //type: integer
//         //description: id of the option
//       completeText: null,
//         //type: string
//         //description: text of the option
//       variables: [],
//         //description: variables that can be added
//       filledValues: []
//        //description: filled values
//   };
//   return Option;
// }

function CaseData_setNewOption() {
    var Option = {
        inserted: false,
        //description: indication text is inserted
        edited: false,
        //  description: indication text is edited
        optionCode: null,
        //type: integer
        //description: id of the option
        description: null,
        //description: short presentation of the option
        indRequired: null,
        //description: boolean if the option is required
        offenceCode: null,
        //description: the offenceCode that the option is linked to
        apvCode: null,
        //description: the apvCode that the option is linked to
        text: null,
        //type: string
        //description: text of the option
        variables: [],
        //description: variables that can be added
        filledText: null
        //description: the text filled in with the values of the variables
    };
    return Option;
}

function CaseData_setNewVariable() {
    var Variables = {
        optionreplacetext: null,
        //description: the unique value that is found in the option text that needs to be replaced
        prompt: null,
        //description: prompt/header of the variabel
        fieldType: null,
        //description: the type of field that needs to be set to the form (choose, string number, decimal1, time, etc.)
        length: null,
        //description: the max length of the field
        chooseType: null,
        //description: filled when fieldType is choose, with this you can find the items for your list
        query: null,
        //description: boolean to indicate if the value of variable needs to be queried
        queryfield: null,
        //description: filled when query is true, this indicates which query should be run in the app
        offenceCode: null,
        //description: the offencecode the variable is linked to (through option)
        indRequired: null,
        //description: boolean indication if the option is required to be filled
        optionCode: null,
        //description: code of the option where the variable is linked to
        filledValue: null,
        //description: the string of the filled in value
        vreId: null
        //type: integer
        //description: id of the variable
    };
    return Variables;
}

function CaseData_setNewTransportTicket() {
    var TransportTicket = {
        dogs: null, //integer
        bikes: null, //integer
        firstClass: null, //boolean
        retour: null, //boolean
        railrunner: null, //        type: integer
        transitionTwoOne: null, //        type: boolean
        surtax: null, //       type: boolean
        surtaxIcd: null, // type: boolean
        gratuitous: null, // type: boolean
        stationFrom: null,
        //type: string description: train station from where the journey started
        stationFromCode: null,
        //type: string description: train station code from where the journey started
        stationTo: null,
        //type: string description: train station to where the journey is headed
        stationToCode: null,
        //type: string description: train station code to where the journey is headed
        trainRide: null,
        // type: string
        // description: trainride from-to description
        trainRideCode: null,
        // type: string
        // description: trainride from-to code
        amount: null,
        //type: number
        //format: float
        //description: calculated amount
        fineAmount: null,
        //type: number
        //format: float
        //description: amount of the fine
        totalAmount: null,
        //type: number
        //format: float
        //description: cummulated amount
        offenderEmail: null,
        //  type: string
        //  description: mail adress of the offender
        offenderTelephone: null
        //  type: string
        //  description: telephone number of the offender (string because of 0 before the number)
    };
    return TransportTicket;
}

function CaseData_setProhibitionObject() {
    var ProhibitiontObject = {
        duration: null,
        // type: integer
        // description: duration in days
        dateFrom: null,
        //type: string
        //format: date-time
        //description: dateFrom in UTC
        dateTo: null,
        //         type: string
        //         format: date-time
        //         description: dateTo in UTC
        station1: null,
        //type: string
        //description: train station1
        station1Code: null,
        //type: string
        //description: train station1
        station2: null,
        //type: string
        //description: train station1
        station2Code: null,
        //type: string
        //description: train station1
        station3: null,
        //type: string
        //description: train station1
        station3Code: null,
        //type: string
        //description: train station1
        signature: null,
        //type: string
        //description: base64 file content of signature
        caseTypeCategoryDescription: null,
        //type: string
        //description: description of the Case type category
        caseTypeDescription: null,
        //type: string
        //description: description of the Case type
        policeOfficerEmail: null,
        //         type: string
        //         description: mail adress of the police officer
        policeOfficerMobile: null,
        //         type: string
        //         description: mobile number of the police officer (string because of 0 before the number)
        prohibitionHanded: null,
        //    type: boolean
        //    description: true or false value to set in the mobile app to see if prohibition was handed to the suspect, starts out as een empty value!
        prohibitionPDFPassword: null,
        //   type: string
        //   description: password for the PDF send by email
        prohibitionPersonTraces: []
        // type: array
        // items:
        //   type: object
        // description: array can be empty array [], this array gets filled with person information from before the infromation is changed in the mobile application for traceability purposes
    };
    return ProhibitiontObject;
}

function CaseData_setNewEnforcementObject() {
    var EnforcementObject = {
        cocNumber: null,
        name: null,
        zipcode: null,
        street: null,
        streetNumber: null,
        streetNumberAdditional: null,
        latitude: null,
        longitude: null,
        city: null,
        cityCode: null,
        municipality: null,
        municipalityCode: null,
        validated: false, // once object is filled set to true
        edited: false, // once object is filled set to true
        used: false, // once object is filled set to true
        remark: null, //string
        branchNumber: null, // digits: Vestigingsnummer, identifying number for a branch. Consists of 12 digits
        legalForm: null, //string: This returns legal form description
        sbiCode: null, //digits
        sbiCodeDescription: null, //string
        inputModus: null //string : manual, table, service
        //questions:[]
    };
    return EnforcementObject;
}

function CaseData_setRegisterLabelObject() {
    var RegisterLabelObject = {
        labelNumber: null,
        // type: string
        // description: number on the scanned label
        secondLabelNumber: null,
        sharedBike: false,
        //type: boolean
        //description: if it is a sharedbike or not
        kindOfSharedBike: null
        //         type: string
        //description: what kind of shared bike is it
    };
    return RegisterLabelObject;
}

function CaseData_setUserInformationToCaseInfo() {
    //set userdata to case
    if (CaseData.caseinfo.createdUser === null || CaseData.caseinfo.createdUser === "") {
        CaseData.caseinfo.createdUser = Global.vars.gUsername;
    }
    CaseData.caseinfo.updateUser = Global.vars.gUsername;
    CaseData.caseinfo.instanceID = Global.vars.gInstanceId;
    CaseData.caseinfo.deviceID =
        (Global.vars.gDeviceInfo && Global.vars.gDeviceInfo.deviceid) || "-";
    CaseData.caseinfo.officerName = Global.vars.gFullName;
    CaseData.caseinfo.officerNumber = Global.vars.gOfficerNumber;
    CaseData.caseinfo.officerDocumentNumber = Global.vars.gOfficerDocumentNumber;
    CaseData.caseinfo.officerIdentification = Global.vars.gOfficerIdentification;
    CaseData.caseinfo.teamNumber = Global.vars.gTeamNumber;
    CaseData.caseinfo.teamName = Global.vars.gTeamName;
    CaseData.caseinfo.officerOath = Global.vars.gOfficerOath;
    CaseData.caseinfo.officerFunctions = Global.vars.officerFunctions;
    CaseData.caseinfo["appVersion"] = appConfig.appVersion.toString();
    voltmx.print("### CaseData_setUserInformationToCaseInfo: " + JSON.stringify(CaseData.caseinfo));
    // RL-2428 Fix to get concepts on device
    CaseData.processinfo.assignment.assignee = Global.vars.gUsername;
}

function CaseData_setDateTime(inputDateComponents, callback) {
    inputDateComponents = Utility_normalizeDateObject(inputDateComponents);
    voltmx.print("### CaseData_setDateTime(): " + JSON.stringify(inputDateComponents));
    if (callback !== undefined && callback != null) {
        Global.vars.setCurrentDateTimeCallback = callback;
    } else {
        Global.vars.setCurrentDateTimeCallback = null;
    }
    if (inputDateComponents == null) {
        voltmx.print(
            "### CaseData_setDateTime voltmx.os.date(*t) components: " +
                JSON.stringify(voltmx.os.date("*t"))
        );
        var dateTime = voltmx.os.date("*t");
        var dateComponents = [
            dateTime.day,
            dateTime.month,
            dateTime.year,
            dateTime.hour,
            dateTime.min,
            dateTime.sec
        ];
        CaseData.time.dateComponents = dateComponents;
    } else {
        CaseData.time.dateComponents = inputDateComponents;
    }
    voltmx.print(
        "### CaseData_setDateTime CaseData.time.dateComponents: " +
            JSON.stringify(CaseData.time.dateComponents)
    );
    var date = CaseData.time.dateComponents[0];
    var month = CaseData.time.dateComponents[1];
    var year = CaseData.time.dateComponents[2];
    var hours = CaseData.time.dateComponents[3];
    var minutes = CaseData.time.dateComponents[4];
    var seconds = CaseData.time.dateComponents[5];
    var dateJavascript = new Date(year, month - 1, date, hours, minutes, seconds, 0);
    // set local short date
    CaseData.time.localeShortDate = Utility_getLocalizedDateTimeString(dateJavascript, false);
    voltmx.print("### CaseData_setDateTime localeShortDate: " + CaseData.time.localeShortDate);
    // set date
    CaseData.time.caseDate =
        year + "-" + month.toString().lpad("0", 2) + "-" + date.toString().lpad("0", 2);
    voltmx.print("### CaseData_setDateTime caseDate: " + CaseData.time.caseDate);
    // set time
    CaseData.time.caseTime = Utility_getLocaleShortTimeString(dateJavascript, false);
    voltmx.print("### CaseData_setDateTime caseTime: " + CaseData.time.caseTime);
    // set utc
    CaseData.time.utcDateTime = Utility_getUTCJavascriptDate(dateJavascript);
    voltmx.print("### CaseData_setDateTime caseDate: " + CaseData.time.utcDateTime);
    // set timezone
    CaseData.time.timeZoneOffset = dateJavascript.getTimezoneOffset();
    voltmx.print("### CaseData_setDateTime caseDate: " + CaseData.time.caseDate);
    // Set ticketnumber
    Utility_setTicketNumberToCaseData();
    //
    if (Global.vars.setCurrentDateTimeCallback != null) {
        Global.vars.setCurrentDateTimeCallback();
    }
}
