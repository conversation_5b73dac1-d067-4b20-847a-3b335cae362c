var btnPrintResume = false;
var activeTaskTypeInOpenTaskTypesToQuery = false;

function frmResume_contentOffset(){
  frmResume.contentSize = {height : "100%", width : "100%"};
  frmResume.contentOffset = {"x":"0px", "y":"0px"};
}

function frmResume_locationdescription_setVisibility(boolean){
  voltmx.print("### frmResume_locationdescription_setVisibility");

  function locationdescription_setVisibility(){
    voltmx.print("### frmResume_locationdescription_setVisibility locationdescription_setVisibility: " + boolean);
  	frmResume.locationdescription.setVisibility(boolean);
  }
  voltmx.runOnMainThread(locationdescription_setVisibility, []);
}

function frmResume_locationspecification_setVisibility(boolean){
  voltmx.print("### frmResume_locationspecification_setVisibility");

  function locationspecification_setVisibility(){
    voltmx.print("### frmResume_locationspecification_setVisibility locationspecification_setVisibility: " + boolean);
  	frmResume.locationspecification.setVisibility(boolean);
  }
  voltmx.runOnMainThread(locationspecification_setVisibility, []);
}

function frmResume_furtherlocationindication_setVisibility(boolean){
  voltmx.print("### frmResume_furtherlocationindication_setVisibility");

  function furtherlocationindication_setVisibility(){
    voltmx.print("### frmResume_furtherlocationindication_setVisibility furtherlocationindication_setVisibility: " + boolean);
  	frmResume.furtherlocationindication.setVisibility(boolean);
  }
  voltmx.runOnMainThread(furtherlocationindication_setVisibility, []);
}

function frmResume_flcCaseNumber_setVisibility(boolean){
  voltmx.print("### frmResume_flcCaseNumber_setVisibility");

  function flcCaseNumber_setVisibility(){
    voltmx.print("### frmResume_flcCaseNumber_setVisibility flcCaseNumber_setVisibility: " + boolean);
  	frmResume.flcCaseNumber.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcCaseNumber_setVisibility, []);
}

function frmResume_offendertelephone_setVisibility(boolean){
  voltmx.print("### frmResume_offendertelephone_setVisibility");

  function offendertelephone_setVisibility(){
    voltmx.print("### frmResume_offendertelephone_setVisibility offendertelephone_setVisibility: " + boolean);
  	frmResume.offendertelephone.setVisibility(boolean);
  }
  voltmx.runOnMainThread(offendertelephone_setVisibility, []);
}

function frmResume_offenderemail_setVisibility(boolean){
  voltmx.print("### frmResume_offenderemail_setVisibility");

  function offenderemail_setVisibility(){
    voltmx.print("### frmResume_offenderemail_setVisibility offenderemail_setVisibility: " + boolean);
  	frmResume.offenderemail.setVisibility(boolean);
  }
  voltmx.runOnMainThread(offenderemail_setVisibility, []);
}

function frmResume_licenseplate_flcLicensePlatePrefix_setVisibility(boolean){
  voltmx.print("### frmResume_licenseplate.flcLicensePlatePrefix_setVisibility");

  function licenseplate_flcLicensePlatePrefix_setVisibility(){
    voltmx.print("### frmResume_licenseplate.flcLicensePlatePrefix_setVisibility licenseplate.flcLicensePlatePrefix_setVisibility: " + boolean);
  	frmResume.licenseplate.flcLicensePlatePrefix.setVisibility(boolean);
  }
  voltmx.runOnMainThread(licenseplate_flcLicensePlatePrefix_setVisibility, []);
}

function frmResume_flcPersonAdress_setVisibility(boolean){
  voltmx.print("### frmResume_flcPersonAdress_setVisibility");

  function flcPersonAdress_setVisibility(){
    voltmx.print("### frmResume_flcPersonAdress_setVisibility flcPersonAdress_setVisibility: " + boolean);
  	frmResume.flcPersonAdress.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcPersonAdress_setVisibility, []);
}

function frmResume_time_setVisibility(boolean){
  voltmx.print("### frmResume_time_setVisibility");

  function time_setVisibility(){
    voltmx.print("### frmResume_time_setVisibility time_setVisibility: " + boolean);
  	frmResume.time.setVisibility(boolean);
  }
  voltmx.runOnMainThread(time_setVisibility, []);
}

function frmResume_mapdisplay_setVisibility(boolean){
  //#ifdef android
  voltmx.print("### frmResume_mapdisplay_setVisibility");

  function mapdisplay_setVisibility(){
    voltmx.print("### frmResume_mapdisplay_setVisibility mapdisplay_setVisibility: " + boolean);
    frmResume.mapdisplay.setVisibility(boolean);
  }
  voltmx.runOnMainThread(mapdisplay_setVisibility, []);
  //#endif
}

function frmResume_location_setVisibility(boolean){
  voltmx.print("### frmResume_location_setVisibility");

  function location_setVisibility(){
    voltmx.print("### frmResume_location_setVisibility location_setVisibility: " + boolean);
  	frmResume.location.setVisibility(boolean);
  }
  voltmx.runOnMainThread(location_setVisibility, []);
}

function frmResume_flcButShowPDF_setVisibility(boolean){
  voltmx.print("### frmResume_flcButShowPDF_setVisibility");

  function flcButShowPDF_setVisibility(){
    voltmx.print("### frmResume_flcButShowPDF_setVisibility flcButShowPDF_setVisibility: " + boolean);
  	frmResume.flcButShowPDF.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcButShowPDF_setVisibility, []);
}

function frmResume_policeofficermobile_setVisibility(boolean){
  voltmx.print("### frmResume_policeofficermobile_setVisibility");

  function policeofficermobile_setVisibility(){
    voltmx.print("### frmResume_policeofficermobile_setVisibility policeofficermobile_setVisibility: " + boolean);
  	frmResume.policeofficermobile.setVisibility(boolean);
  }
  voltmx.runOnMainThread(policeofficermobile_setVisibility, []);
}

function frmResume_policeofficeremail_setVisibility(boolean){
  voltmx.print("### frmResume_policeofficeremail_setVisibility");

  function policeofficeremail_setVisibility(){
    voltmx.print("### frmResume_policeofficeremail_setVisibility policeofficeremail_setVisibility: " + boolean);
  	frmResume.policeofficeremail.setVisibility(boolean);
  }
  voltmx.runOnMainThread(policeofficeremail_setVisibility, []);
}

function frmResume_flcOnStreetPaymentPopup_setVisibility(boolean){
  voltmx.print("### frmResume_flcOnStreetPaymentPopup_setVisibility");

  function flcOnStreetPaymentPopup_setVisibility(){
    voltmx.print("### frmResume_flcOnStreetPaymentPopup_setVisibility flcOnStreetPaymentPopup_setVisibility: " + boolean);
  	frmResume.flcOnStreetPaymentPopup.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcOnStreetPaymentPopup_setVisibility, []);
}

function frmResume_flcDone_setVisibility(boolean){
  voltmx.print("### frmResume_flcDone_setVisibility");

  function flcDone_setVisibility(){
    voltmx.print("### frmResume_flcDone_setVisibility flcDone_setVisibility: " + boolean);
  	frmResume.flcDone.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcDone_setVisibility, []);
}

function frmResume_flcPrintSend_setVisibility(boolean){
  voltmx.print("### frmResume_flcPrintSend_setVisibility");

  function flcPrintSend_setVisibility(){
    voltmx.print("### frmResume_flcPrintSend_setVisibility flcPrintSend_setVisibility: " + boolean);
  	frmResume.flcPrintSend.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcPrintSend_setVisibility, []);
}

function frmResume_flcPrint_setVisibility(boolean){
  voltmx.print("### frmResume_flcPrint_setVisibility");

  function flcPrint_setVisibility(){
    voltmx.print("### frmResume_flcPrint_setVisibility flcPrint_setVisibility: " + boolean);
  	frmResume.flcPrint.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcPrint_setVisibility, []);
}

function frmResume_statement_setVisibility(boolean){
  voltmx.print("### frmResume_statement_setVisibility");

  function statement_setVisibility(){
    voltmx.print("### frmResume_statement_setVisibility statement_setVisibility: " + boolean);
  	frmResume.statement.setVisibility(boolean);
  }
  voltmx.runOnMainThread(statement_setVisibility, []);
}

function frmResume_tickettype_setVisibility(boolean){
  voltmx.print("### frmResume_tickettype_setVisibility");

  function tickettype_setVisibility(){
    voltmx.print("### frmResume_tickettype_setVisibility tickettype_setVisibility: " + boolean);
  	frmResume.tickettype.setVisibility(boolean);
  }
  voltmx.runOnMainThread(tickettype_setVisibility, []);
}

function frmResume_tickettype_imgRight_setVisibility(boolean){
  voltmx.print("### frmResume_tickettype_imgRight_setVisibility");

  function tickettype_imgRight_setVisibility(){
    voltmx.print("### frmResume_tickettype_imgRight_setVisibility tickettype_imgRight_setVisibility: " + boolean);
  	frmResume.tickettype.imgRight.setVisibility(boolean);
  }
  voltmx.runOnMainThread(tickettype_imgRight_setVisibility, []);
}

function frmResume_offenceinfo_lblOffenceCategoryHeader_setVisibility(boolean){
  voltmx.print("### frmResume_offenceinfo.lblOffenceCategoryHeader_setVisibility");

  function offenceinfo_lblOffenceCategoryHeader_setVisibility(){
    voltmx.print("### frmResume_offenceinfo.lblOffenceCategoryHeader_setVisibility offenceinfo.lblOffenceCategoryHeader_setVisibility: " + boolean);
  	frmResume.offenceinfo.lblOffenceCategoryHeader.setVisibility(boolean);
  }
  voltmx.runOnMainThread(offenceinfo_lblOffenceCategoryHeader_setVisibility, []);
}

function frmResume_offenceinfo_lblOffenceCategory_setVisibility(boolean){
  voltmx.print("### frmResume_offenceinfo.lblOffenceCategory_setVisibility");

  function offenceinfo_lblOffenceCategory_setVisibility(){
    voltmx.print("### frmResume_offenceinfo.lblOffenceCategory_setVisibility offenceinfo.lblOffenceCategory_setVisibility: " + boolean);
  	frmResume.offenceinfo.lblOffenceCategory.setVisibility(boolean);
  }
  voltmx.runOnMainThread(offenceinfo_lblOffenceCategory_setVisibility, []);
}

function frmResume_offenceinfo_flcVerticalLine_setVisibility(boolean){
  voltmx.print("### frmResume_offenceinfo.flcVerticalLine_setVisibility");

  function offenceinfo_flcVerticalLine_setVisibility(){
    voltmx.print("### frmResume_offenceinfo.flcVerticalLine_setVisibility offenceinfo.flcVerticalLine_setVisibility: " + boolean);
  	frmResume.offenceinfo.flcVerticalLine.setVisibility(boolean);
  }
  voltmx.runOnMainThread(offenceinfo_flcVerticalLine_setVisibility, []);
}

function frmResume_sanctioninfo_setVisibility(boolean){
  voltmx.print("### frmResume_sanctioninfo_setVisibility");

  function sanctioninfo_setVisibility(){
    voltmx.print("### frmResume_sanctioninfo_setVisibility sanctioninfo_setVisibility: " + boolean);
  	frmResume.sanctioninfo.setVisibility(boolean);
  }
  voltmx.runOnMainThread(sanctioninfo_setVisibility, []);
}

function frmResume_secondofficer_setVisibility(boolean){
  voltmx.print("### frmResume_secondofficer_setVisibility");

  function secondofficer_setVisibility(){
    voltmx.print("### frmResume_secondofficer_setVisibility secondofficer_setVisibility: " + boolean);
  	frmResume.secondofficer.setVisibility(boolean);
  }
  voltmx.runOnMainThread(secondofficer_setVisibility, []);
}

function frmResume_thirdofficer_setVisibility(boolean){
  voltmx.print("### frmResume_thirdofficer_setVisibility boolean: " + boolean);
  var _boolean = Global.vars.superCaseEnabled === false ? false : boolean;
  function thirdofficer_setVisibility(){
    voltmx.print("### frmResume_thirdofficer_setVisibility thirdofficer_setVisibility: " + _boolean);
  	frmResume.thirdofficer.setVisibility(_boolean);
  }
  voltmx.runOnMainThread(thirdofficer_setVisibility, []);
}

function frmResume_relatedcase_setVisibility(boolean){
  voltmx.print("### frmResume_relatedcase_setVisibility boolean: " + boolean);
  var _boolean = Global.vars.superCaseEnabled === false ? false : boolean;
  function relatedcase_setVisibility(){
    voltmx.print("### frmResume_relatedcase_setVisibility relatedcase_setVisibility: " + _boolean);
  	frmResume.relatedcase.setVisibility(_boolean);
  }
  voltmx.runOnMainThread(relatedcase_setVisibility, []);
}

function frmResume_duplicate_setVisibility(boolean){
  voltmx.print("### frmResume_duplicate_setVisibility");

  function duplicate_setVisibility(){
    voltmx.print("### frmResume_duplicate_setVisibility duplicate_setVisibility: " + boolean);
  	frmResume.duplicate.setVisibility(boolean);
  }
  voltmx.runOnMainThread(duplicate_setVisibility, []);
}

function frmResume_locationwithinurbanarea_setVisibility(boolean){
  voltmx.print("### frmResume_locationwithinurbanarea_setVisibility");

  function locationwithinurbanarea_setVisibility(){
    voltmx.print("### frmResume_locationwithinurbanarea_setVisibility locationwithinurbanarea_setVisibility: " + boolean);
  	frmResume.locationwithinurbanarea.setVisibility(boolean);
  }
  voltmx.runOnMainThread(locationwithinurbanarea_setVisibility, []);
}

function frmResume_flcSaveCase_setVisibility(boolean){
  voltmx.print("### frmResume_flcSaveCase_setVisibility");

  function flcSaveCase_setVisibility(){
    voltmx.print("### frmResume_flcSaveCase_setVisibility flcSaveCase_setVisibility: " + boolean);
  	frmResume.flcSaveCase.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcSaveCase_setVisibility, []);
}

function frmResume_onstreetpayment_setVisibility(boolean){
  voltmx.print("### frmResume_onstreetpayment_setVisibility");

  function onstreetpayment_setVisibility(){
    voltmx.print("### frmResume_onstreetpayment_setVisibility onstreetpayment_setVisibility: " + boolean);
  	frmResume.onstreetpayment.setVisibility(boolean);
  }
  voltmx.runOnMainThread(onstreetpayment_setVisibility, []);
}

function frmResume_multimedia_setVisibility(boolean){
  voltmx.print("### frmResume_multimedia_setVisibility");

  function multimedia_setVisibility(){
    voltmx.print("### frmResume_multimedia_setVisibility multimedia_setVisibility: " + boolean);
  	frmResume.multimedia.setVisibility(boolean);
  }
  voltmx.runOnMainThread(multimedia_setVisibility, []);
}

function frmResume_settext2_flcCopyPaste_setVisibility(boolean){
  voltmx.print("### frmResume_settext2_flcCopyPaste_setVisibility");

  function settext2_flcCopyPaste_setVisibility(){
    voltmx.print("### frmResume_settext2_flcCopyPaste_setVisibility settext2.flcCopyPaste_setVisibility: " + boolean);
  	frmResume.settext2.flcCopyPaste.setVisibility(boolean);
  }
  voltmx.runOnMainThread(settext2_flcCopyPaste_setVisibility, []);
}

function frmResume_settext2_flcCopy_setVisibility(boolean){
  voltmx.print("### frmResume_settext2_flcCopy_setVisibility");

  function settext2_flcCopy_setVisibility(){
    voltmx.print("### frmResume_settext2_flcCopy_setVisibility settext2.flcCopy_setVisibility: " + boolean);
  	frmResume.settext2.flcCopy.setVisibility(boolean);
  }
  voltmx.runOnMainThread(settext2_flcCopy_setVisibility, []);
}

function frmResume_settext2_flcPaste_setVisibility(boolean){
  voltmx.print("### frmResume_settext2_flcPaste_setVisibility");

  function settext2_flcPaste_setVisibility(){
    voltmx.print("### frmResume_settext2_flcPaste_setVisibility settext2.flcPaste_setVisibility: " + boolean);
  	frmResume.settext2.flcPaste.setVisibility(boolean);
  }
  voltmx.runOnMainThread(settext2_flcPaste_setVisibility, []);
}

function frmResume_mapdisplay_mapLocation_setVisibility(boolean){
  //#ifdef android
  voltmx.print("### frmResume_mapdisplay_mapLocation_setVisibility");

  function mapdisplay_mapLocation_setVisibility(){
    voltmx.print("### frmResume_mapdisplay.mapLocation_setVisibility mapdisplay.mapLocation_setVisibility: " + boolean);
  	frmResume.mapdisplay.mapLocation.setVisibility(boolean);
  }
  voltmx.runOnMainThread(mapdisplay_mapLocation_setVisibility, []);
  //#endif
}

function frmResume_mapdisplay_flcImage_setVisibility(boolean){
  //#ifdef android
  voltmx.print("### frmResume_mapdisplay_flcImage_setVisibility");

  function mapdisplay_flcImage_setVisibility(){
    voltmx.print("### frmResume_mapdisplay_flcImage_setVisibility mapdisplay.flcImage_setVisibility: " + boolean);
  	frmResume.mapdisplay.flcImage.setVisibility(boolean);
  }
  voltmx.runOnMainThread(mapdisplay_flcImage_setVisibility, []);
  //#endif
}

function frmResume_person_setVisibility(boolean){
  voltmx.print("### frmResume_person_setVisibility");

  function person_setVisibility(){
    voltmx.print("### frmResume_person_setVisibility person_setVisibility: " + boolean);
  	frmResume.person.setVisibility(boolean);
  }
  voltmx.runOnMainThread(person_setVisibility, []);
}

function frmResume_flcTravellerTicket_setVisibility(boolean){
  voltmx.print("### frmResume_flcTravellerTicket_setVisibility");

  function flcTravellerTicket_setVisibility(){
    voltmx.print("### frmResume_flcTravellerTicket_setVisibility flcTravellerTicket_setVisibility: " + boolean);
  	frmResume.flcTravellerTicket.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcTravellerTicket_setVisibility, []);
}

function frmResume_flcSignature_setVisibility(boolean){
  voltmx.print("### frmResume_flcSignature_setVisibility");

  function flcSignature_setVisibility(){
    voltmx.print("### frmResume_flcSignature_setVisibility flcSignature_setVisibility: " + boolean);
  	frmResume.flcSignature.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcSignature_setVisibility, []);
}

function frmResume_flcProhibition_setVisibility(boolean){
  voltmx.print("### frmResume_flcProhibition_setVisibility");

  function flcProhibition_setVisibility(){
    voltmx.print("### frmResume_flcProhibition_setVisibility flcProhibition_setVisibility: " + boolean);
  	frmResume.flcProhibition.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcProhibition_setVisibility, []);
}

function frmResume_internalremark_setVisibility(boolean){
  voltmx.print("### frmResume_internalremark_setVisibility");

  function internalremark_setVisibility(){
    voltmx.print("### frmResume_internalremark_setVisibility internalremark_setVisibility: " + boolean);
  	frmResume.internalremark.setVisibility(boolean);
  }
  voltmx.runOnMainThread(internalremark_setVisibility, []);
}

function frmResume_observation_setVisibility(boolean){
  voltmx.print("### frmResume_observation_setVisibility");

  function observation_setVisibility(){
    voltmx.print("### frmResume_observation_setVisibility observation_setVisibility: " + boolean);
  	frmResume.observation.setVisibility(boolean);
  }
  voltmx.runOnMainThread(observation_setVisibility, []);
}

function frmResume_prohibitionhanded_setVisibility(boolean){
  voltmx.print("### frmResume_prohibitionhanded_setVisibility");

  function prohibitionhanded_setVisibility(){
    voltmx.print("### frmResume_prohibitionhanded_setVisibility prohibitionhanded_setVisibility: " + boolean);
  	frmResume.prohibitionhanded.setVisibility(boolean);
  }
  voltmx.runOnMainThread(prohibitionhanded_setVisibility, []);
}

function frmResume_questions_setVisibility(boolean){
  voltmx.print("### frmResume_questions_setVisibility");

  function questions_setVisibility(){
    voltmx.print("### frmResume_questions_setVisibility questions_setVisibility: " + boolean);
  	frmResume.questions.setVisibility(boolean);
  }
  voltmx.runOnMainThread(questions_setVisibility, []);
}

function frmResume_flcStatusLayout_setVisibility(boolean){
  voltmx.print("### frmResume_flcStatusLayout_setVisibility");

  function flcStatusLayout_setVisibility(){
    voltmx.print("### frmResume_flcStatusLayout_setVisibility flcStatusLayout_setVisibility: " + boolean);
  	frmResume.flcStatusLayout.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcStatusLayout_setVisibility, []);
}

function frmResume_flcOffenceSanction_setVisibility(boolean){
  voltmx.print("### frmResume_flcOffenceSanction_setVisibility");

  function flcOffenceSanction_setVisibility(){
    voltmx.print("### frmResume_flcOffenceSanction_setVisibility flcOffenceSanction_setVisibility: " + boolean);
  	frmResume.flcOffenceSanction.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcOffenceSanction_setVisibility, []);
}

function frmResume_imgStatusLeft_setVisibility(boolean){
  voltmx.print("### frmResume_imgStatusLeft_setVisibility");

  function imgStatusLeft_setVisibility(){
    voltmx.print("### frmResume_imgStatusLeft_setVisibility imgStatusLeft_setVisibility: " + boolean);
  	frmResume.imgStatusLeft.setVisibility(boolean);
  }
  voltmx.runOnMainThread(imgStatusLeft_setVisibility, []);
}

function frmResume_flcOffenceSanction_sanctioninfo_setVisibility(boolean){
  voltmx.print("### frmResume_flcOffenceSanction.sanctioninfo_setVisibility");

  function flcOffenceSanction_sanctioninfo_setVisibility(){
    voltmx.print("### frmResume_flcOffenceSanction.sanctioninfo_setVisibility flcOffenceSanction.sanctioninfo_setVisibility: " + boolean);
  	frmResume.flcOffenceSanction.sanctioninfo.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcOffenceSanction_sanctioninfo_setVisibility, []);
}


function frmResume_flcTimeArrivedClampNumber_setVisibility(boolean){
  voltmx.print("### frmResume_flcTimeArrivedClampNumber_setVisibility");

  function flcTimeArrivedClampNumber_setVisibility(){
    voltmx.print("### frmResume_flcTimeArrivedClampNumber_setVisibility flcTimeArrivedClampNumber_setVisibility: " + boolean);
  	frmResume.flcTimeArrivedClampNumber.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcTimeArrivedClampNumber_setVisibility, []);
}

function frmResume_flcVehicleInfo_setVisibility(boolean){
  voltmx.print("### frmResume_flcVehicleInfo_setVisibility");

  function flcVehicleInfo_setVisibility(){
    voltmx.print("### frmResume_flcVehicleInfo_setVisibility flcVehicleInfo_setVisibility: " + boolean);
  	frmResume.flcVehicleInfo.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcVehicleInfo_setVisibility, []);
}

function frmResume_brand_setVisibility(boolean){
  voltmx.print("### frmResume_brand_setVisibility");

  function brand_setVisibility(){
    voltmx.print("### frmResume_brand_setVisibility brand_setVisibility: " + boolean);
  	frmResume.brand.setVisibility(boolean);
  }
  voltmx.runOnMainThread(brand_setVisibility, []);
}

function frmResume_licenseplate_flcVehicleCountry_setVisibility(boolean){
  voltmx.print("### frmResume_licenseplate.flcVehicleCountry_setVisibility");

  function licenseplate_flcVehicleCountry_setVisibility(){
    voltmx.print("### frmResume_licenseplate.flcVehicleCountry_setVisibility licenseplate.flcVehicleCountry_setVisibility: " + boolean);
  	frmResume.licenseplate.flcVehicleCountry.setVisibility(boolean);
  }
  voltmx.runOnMainThread(licenseplate_flcVehicleCountry_setVisibility, []);
}

function frmResume_option_setVisibility(boolean){
  voltmx.print("### frmResume_option_setVisibility");

  function option_setVisibility(){
    voltmx.print("### frmResume_option_setVisibility option_setVisibility: " + boolean);
  	frmResume.option.setVisibility(boolean);
  }
  voltmx.runOnMainThread(option_setVisibility, []);
}

function frmResume_findings_setVisibility(boolean){
  voltmx.print("### frmResume_findings_setVisibility");

  function findings_setVisibility(){
    voltmx.print("### frmResume_findings_setVisibility findings_setVisibility: " + boolean);
  	frmResume.findings.setVisibility(boolean);
  }
  voltmx.runOnMainThread(findings_setVisibility, []);
}

function frmResume_flcRailrunner_setVisibility(boolean){
  voltmx.print("### frmResume_flcRailrunner_setVisibility");

  function flcRailrunner_setVisibility(){
    voltmx.print("### frmResume_flcRailrunner_setVisibility flcRailrunner_setVisibility: " + boolean);
  	frmResume.flcRailrunner.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcRailrunner_setVisibility, []);
}

function frmResume_flcDogs_setVisibility(boolean){
  voltmx.print("### frmResume_flcDogs_setVisibility");

  function flcDogs_setVisibility(){
    voltmx.print("### frmResume_flcDogs_setVisibility flcDogs_setVisibility: " + boolean);
  	frmResume.flcDogs.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcDogs_setVisibility, []);
}

function frmResume_flcgratuitous_setVisibility(boolean){
  voltmx.print("### frmResume_flcgratuitous_setVisibility");

  function flcgratuitous_setVisibility(){
    voltmx.print("### frmResume_flcgratuitous_setVisibility flcgratuitous_setVisibility: " + boolean);
  	frmResume.flcgratuitous.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcgratuitous_setVisibility, []);
}

function frmResume_flcsurtaxIcd_setVisibility(boolean){
  voltmx.print("### frmResume_flcsurtaxIcd_setVisibility");

  function flcsurtaxIcd_setVisibility(){
    voltmx.print("### frmResume_flcsurtaxIcd_setVisibility flcsurtaxIcd_setVisibility: " + boolean);
  	frmResume.flcsurtaxIcd.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcsurtaxIcd_setVisibility, []);
}

function frmResume_flcsurtax_setVisibility(boolean){
  voltmx.print("### frmResume_flcsurtax_setVisibility");

  function flcsurtax_setVisibility(){
    voltmx.print("### frmResume_flcsurtax_setVisibility flcsurtax_setVisibility: " + boolean);
  	frmResume.flcsurtax.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcsurtax_setVisibility, []);
}

function frmResume_flctransitionTwoOne_setVisibility(boolean){
  voltmx.print("### frmResume_flctransitionTwoOne_setVisibility");

  function flctransitionTwoOne_setVisibility(){
    voltmx.print("### frmResume_flctransitionTwoOne_setVisibility flctransitionTwoOne_setVisibility: " + boolean);
  	frmResume.flctransitionTwoOne.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flctransitionTwoOne_setVisibility, []);
}

function frmResume_flcRetour_setVisibility(boolean){
  voltmx.print("### frmResume_flcRetour_setVisibility");

  function flcRetour_setVisibility(){
    voltmx.print("### frmResume_flcRetour_setVisibility flcRetour_setVisibility: " + boolean);
  	frmResume.flcRetour.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcRetour_setVisibility, []);
}

function frmResume_flcfirstClass_setVisibility(boolean){
  voltmx.print("### frmResume_flcfirstClass_setVisibility");

  function flcfirstClass_setVisibility(){
    voltmx.print("### frmResume_flcfirstClass_setVisibility flcfirstClass_setVisibility: " + boolean);
  	frmResume.flcfirstClass.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcfirstClass_setVisibility, []);
}

function frmResume_flcStationTo_setVisibility(boolean){
  voltmx.print("### frmResume_flcStationTo_setVisibility");

  function flcStationTo_setVisibility(){
    voltmx.print("### frmResume_flcStationTo_setVisibility flcStationTo_setVisibility: " + boolean);
  	frmResume.flcStationTo.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcStationTo_setVisibility, []);
}

function frmResume_flcStationFrom_setVisibility(boolean){
  voltmx.print("### frmResume_flcStationFrom_setVisibility");

  function flcStationFrom_setVisibility(){
    voltmx.print("### frmResume_flcStationFrom_setVisibility flcStationFrom_setVisibility: " + boolean);
  	frmResume.flcStationFrom.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcStationFrom_setVisibility, []);
}

function frmResume_transportticket_setVisibility(boolean){
  voltmx.print("### frmResume_transportticket_setVisibility");

  function transportticket_setVisibility(){
    voltmx.print("### frmResume_transportticket_setVisibility transportticket_setVisibility: " + boolean);
  	frmResume.transportticket.setVisibility(boolean);
  }
  voltmx.runOnMainThread(transportticket_setVisibility, []);
}

function frmResume_lblCaseTypeCategoryHeader_setVisibility(boolean){
  voltmx.print("### frmResume_lblCaseTypeCategoryHeader_setVisibility");

  function lblCaseTypeCategoryHeader_setVisibility(){
    voltmx.print("### frmResume_lblCaseTypeCategoryHeader_setVisibility lblCaseTypeCategoryHeader_setVisibility: " + boolean);
  	frmResume.lblCaseTypeCategoryHeader.setVisibility(boolean);
  }
  voltmx.runOnMainThread(lblCaseTypeCategoryHeader_setVisibility, []);
}

function frmResume_lblCaseTypeCategory_setVisibility(boolean){
  voltmx.print("### frmResume_lblCaseTypeCategory_setVisibility");

  function lblCaseTypeCategory_setVisibility(){
    voltmx.print("### frmResume_lblCaseTypeCategory_setVisibility lblCaseTypeCategory_setVisibility: " + boolean);
  	frmResume.lblCaseTypeCategory.setVisibility(boolean);
  }
  voltmx.runOnMainThread(lblCaseTypeCategory_setVisibility, []);
}

function frmResume_lblCaseType_setVisibility(boolean){
  voltmx.print("### frmResume_lblCaseType_setVisibility");

  function lblCaseType_setVisibility(){
    voltmx.print("### frmResume_lblCaseType_setVisibility lblCaseType_setVisibility: " + boolean);
  	frmResume.lblCaseType.setVisibility(boolean);
  }
  voltmx.runOnMainThread(lblCaseType_setVisibility, []);
}

function frmResume_regioncode_setVisibility(boolean){
  voltmx.print("### frmResume_regioncode_setVisibility");

  function regioncode_setVisibility(){
    voltmx.print("### frmResume_regioncode_setVisibility regioncode_setVisibility: " + boolean);
  	frmResume.regioncode.setVisibility(boolean);
  }
  voltmx.runOnMainThread(regioncode_setVisibility, []);
}

function frmResume_setvehicleinfo_regioncodepopup_setVisibility(boolean){
  voltmx.print("### frmResume_setvehicleinfo.regioncodepopup_setVisibility");

  function setvehicleinfo_regioncodepopup_setVisibility(){
    voltmx.print("### frmResume_setvehicleinfo.regioncodepopup_setVisibility setvehicleinfo.regioncodepopup_setVisibility: " + boolean);
  	frmResume.setvehicleinfo.regioncodepopup.setVisibility(boolean);
  }
  voltmx.runOnMainThread(setvehicleinfo_regioncodepopup_setVisibility, []);
}

function frmResume_setvehicleinfo_duplicatepopup_setVisibility(boolean){
  voltmx.print("### frmResume_setvehicleinfo.duplicatepopup_setVisibility");

  function setvehicleinfo_duplicatepopup_setVisibility(){
    voltmx.print("### frmResume_setvehicleinfo.duplicatepopup_setVisibility setvehicleinfo.duplicatepopup_setVisibility: " + boolean);
  	frmResume.setvehicleinfo.duplicatepopup.setVisibility(boolean);
  }
  voltmx.runOnMainThread(setvehicleinfo_duplicatepopup_setVisibility, []);
}

function frmResume_lblCaseTypeHeader_setVisibility(boolean){
  voltmx.print("### frmResume_lblCaseTypeHeader_setVisibility");

  function lblCaseTypeHeader_setVisibility(){
    voltmx.print("### frmResume_lblCaseTypeHeader_setVisibility lblCaseTypeHeader_setVisibility: " + boolean);
  	frmResume.lblCaseTypeHeader.setVisibility(boolean);
  }
  voltmx.runOnMainThread(lblCaseTypeHeader_setVisibility, []);
}

function frmResume_settext2_setVisibility(boolean){
  voltmx.print("### frmResume_settext2_setVisibility");

  function settext2_setVisibility(){
    voltmx.print("### frmResume_settext2_setVisibility settext2_setVisibility: " + boolean);
  	frmResume.settext2.setVisibility(boolean);
  }
  voltmx.runOnMainThread(settext2_setVisibility, []);
}

function frmResume_setvehicleinfo_brandpopup_setVisibility(boolean){
  voltmx.print("### frmResume_setvehicleinfo.brandpopup_setVisibility");

  function setvehicleinfo_brandpopup_setVisibility(){
    voltmx.print("### frmResume_setvehicleinfo.brandpopup_setVisibility setvehicleinfo.brandpopup_setVisibility: " + boolean);
  	frmResume.setvehicleinfo.brandpopup.setVisibility(boolean);
  }
  voltmx.runOnMainThread(setvehicleinfo_brandpopup_setVisibility, []);
}

function frmResume_taskoutcome_setVisibility(boolean){
  voltmx.print("### frmResume_taskoutcome_setVisibility");

  function taskoutcome_setVisibility(){
    voltmx.print("### frmResume_taskoutcome_setVisibility taskoutcome_setVisibility: " + boolean);
  	frmResume.taskoutcome.setVisibility(boolean);
  }
  voltmx.runOnMainThread(taskoutcome_setVisibility, []);
}

function frmResume_setvehicleinfo_setVisibility(boolean){
  voltmx.print("### frmResume_setvehicleinfo_setVisibility");

  function setvehicleinfo_setVisibility(){
    voltmx.print("### frmResume_setvehicleinfo_setVisibility setvehicleinfo_setVisibility: " + boolean);
  	frmResume.setvehicleinfo.setVisibility(boolean);
  }
  voltmx.runOnMainThread(setvehicleinfo_setVisibility, []);
}

function frmResume_history_setVisibility(boolean){
  voltmx.print("### frmResume_history_setVisibility");

  function history_setVisibility(){
    voltmx.print("### frmResume_history_setVisibility history_setVisibility: " + boolean);
  	frmResume.history.setVisibility(boolean);
  }
  voltmx.runOnMainThread(history_setVisibility, []);
}

function frmResume_uploadcase_setVisibility(boolean){
  voltmx.print("### frmResume_uploadcase_setVisibility");

  function uploadcase_setVisibility(){
    voltmx.print("### frmResume_uploadcase_setVisibility uploadcase_setVisibility: " + boolean);
  	frmResume.uploadcase.setVisibility(boolean);
  }
  voltmx.runOnMainThread(uploadcase_setVisibility, []);
}

function frmResume_flcButtonLeft_setVisibility(boolean){
  voltmx.print("### frmResume_flcButtonLeft_setVisibility");

  function flcButtonLeft_setVisibility(){
    voltmx.print("### frmResume_flcButtonLeft_setVisibility flcButtonLeft_setVisibility: " + boolean);
  	frmResume.flcButtonLeft.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcButtonLeft_setVisibility, []);
}

function frmResume_flcFooterPersonPopup_flcRemovePerson_setVisibility(boolean){
  voltmx.print("### frmResume_flcFooterPersonPopup.flcRemovePerson_setVisibility");

  function flcFooterPersonPopup_flcRemovePerson_setVisibility(){
    voltmx.print("### frmResume_flcFooterPersonPopup.flcRemovePerson_setVisibility flcFooterPersonPopup.flcRemovePerson_setVisibility: " + boolean);
  	frmResume.flcFooterPersonPopup.flcRemovePerson.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcFooterPersonPopup_flcRemovePerson_setVisibility, []);
}

function frmResume_flcFooterPersonPopup_flcShowPersonDocumentDetails_setVisibility(boolean){
  voltmx.print("### frmResume_flcFooterPersonPopup.flcShowPersonDocumentDetails_setVisibility");

  function flcFooterPersonPopup_flcShowPersonDocumentDetails_setVisibility(){
    voltmx.print("### frmResume_flcFooterPersonPopup.flcShowPersonDocumentDetails_setVisibility flcFooterPersonPopup.flcShowPersonDocumentDetails_setVisibility: " + boolean);
  	frmResume.flcFooterPersonPopup.flcShowPersonDocumentDetails.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcFooterPersonPopup_flcShowPersonDocumentDetails_setVisibility, []);
}

function frmResume_flcShowExistingOptionCompleteText_setVisibility(boolean){
  voltmx.print("### frmResume_flcShowExistingOptionCompleteText_setVisibility");

  function flcShowExistingOptionCompleteText_setVisibility(){
    voltmx.print("### frmResume_flcShowExistingOptionCompleteText_setVisibility flcShowExistingOptionCompleteText_setVisibility: " + boolean);
  	frmResume.flcShowExistingOptionCompleteText.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcShowExistingOptionCompleteText_setVisibility, []);
}

function frmResume_flcSavedOptions_setVisibility(boolean){
  voltmx.print("### frmResume_flcSavedOptions_setVisibility");

  function flcSavedOptions_setVisibility(){
    voltmx.print("### frmResume_flcSavedOptions_setVisibility flcSavedOptions_setVisibility: " + boolean);
  	frmResume.flcSavedOptions.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcSavedOptions_setVisibility, []);
}

function frmResume_showOptionsVariables_setVisibility(boolean){
  voltmx.print("### frmResume_showOptionsVariables_setVisibility");

  function showOptionsVariables_setVisibility(){
    voltmx.print("### frmResume_showOptionsVariables_setVisibility showOptionsVariables_setVisibility: " + boolean);
  	frmResume.showOptionsVariables.setVisibility(boolean);
  }
  voltmx.runOnMainThread(showOptionsVariables_setVisibility, []);
}

function frmResume_setdocumentdescription_setVisibility(boolean){
  voltmx.print("### frmResume_setdocumentdescription_setVisibility");

  function setdocumentdescription_setVisibility(){
    voltmx.print("### frmResume_setdocumentdescription_setVisibility setdocumentdescription_setVisibility: " + boolean);
  	frmResume.setdocumentdescription.setVisibility(boolean);
  }
  voltmx.runOnMainThread(setdocumentdescription_setVisibility, []);
}

function frmResume_searchuser_flcInfo_setVisibility(boolean){
  voltmx.print("### frmResume_searchuser.flcInfo_setVisibility");

  function searchuser_flcInfo_setVisibility(){
    voltmx.print("### frmResume_searchuser.flcInfo_setVisibility searchuser.flcInfo_setVisibility: " + boolean);
  	frmResume.searchuser.flcInfo.setVisibility(boolean);
  }
  voltmx.runOnMainThread(searchuser_flcInfo_setVisibility, []);
}

function frmResume_searchuser_setVisibility(boolean){
  voltmx.print("### frmResume_searchuser_setVisibility");

  function searchuser_setVisibility(){
    voltmx.print("### frmResume_searchuser_setVisibility searchuser_setVisibility: " + boolean);
  	frmResume.searchuser.setVisibility(boolean);
  }
  voltmx.runOnMainThread(searchuser_setVisibility, []);
}

function frmResume_flcTransportticketPopup_setVisibility(boolean){
  voltmx.print("### frmResume_flcTransportticketPopup_setVisibility");

  function flcTransportticketPopup_setVisibility(){
    voltmx.print("### frmResume_flcTransportticketPopup_setVisibility flcTransportticketPopup_setVisibility: " + boolean);
  	frmResume.flcTransportticketPopup.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcTransportticketPopup_setVisibility, []);
}

function frmResume_flcWarnings_setVisibility(boolean){
  voltmx.print("### frmResume_flcWarnings_setVisibility");

  function flcWarnings_setVisibility(){
    voltmx.print("### frmResume_flcWarnings_setVisibility flcWarnings_setVisibility: " + boolean);
  	frmResume.flcWarnings.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcWarnings_setVisibility, []);
}

function frmResume_multimedia_flcPhoto1_setVisibility(boolean){
  voltmx.print("### frmResume_multimedia.flcPhoto1_setVisibility");

  function multimedia_flcPhoto1_setVisibility(){
    voltmx.print("### frmResume_multimedia.flcPhoto1_setVisibility multimedia.flcPhoto1_setVisibility: " + boolean);
  	frmResume.multimedia.flcPhoto1.setVisibility(boolean);
  }
  voltmx.runOnMainThread(multimedia_flcPhoto1_setVisibility, []);
}

function frmResume_multimedia_flcPhoto2_setVisibility(boolean){
  voltmx.print("### frmResume_multimedia.flcPhoto2_setVisibility");

  function multimedia_flcPhoto2_setVisibility(){
    voltmx.print("### frmResume_multimedia.flcPhoto2_setVisibility multimedia.flcPhoto2_setVisibility: " + boolean);
  	frmResume.multimedia.flcPhoto2.setVisibility(boolean);
  }
  voltmx.runOnMainThread(multimedia_flcPhoto2_setVisibility, []);
}

function frmResume_multimedia_flcPhoto3_setVisibility(boolean){
  voltmx.print("### frmResume_multimedia.flcPhoto3_setVisibility");

  function multimedia_flcPhoto3_setVisibility(){
    voltmx.print("### frmResume_multimedia.flcPhoto3_setVisibility multimedia.flcPhoto3_setVisibility: " + boolean);
  	frmResume.multimedia.flcPhoto3.setVisibility(boolean);
  }
  voltmx.runOnMainThread(multimedia_flcPhoto3_setVisibility, []);
}

function frmResume_multimedia_flcNoMedia_setVisibility(boolean){
  voltmx.print("### frmResume_multimedia.flcNoMedia_setVisibility");

  function multimedia_flcNoMedia_setVisibility(){
    voltmx.print("### frmResume_multimedia.flcNoMedia_setVisibility multimedia.flcNoMedia_setVisibility: " + boolean);
  	frmResume.multimedia.flcNoMedia.setVisibility(boolean);
  }
  voltmx.runOnMainThread(multimedia_flcNoMedia_setVisibility, []);
}

function frmResume_multimedia_lblMorePhotos_setVisibility(boolean){
  voltmx.print("### frmResume_multimedia_lblMorePhotos_setVisibility");

  function multimedia_lblMorePhotos_setVisibility(){
    voltmx.print("### frmResume_multimedia_lblMorePhotos_setVisibility multimedia_lblMorePhotos_setVisibility: " + boolean);
  	frmResume.multimedia.lblMorePhotos.setVisibility(boolean);
  }
  voltmx.runOnMainThread(multimedia_lblMorePhotos_setVisibility, []);
}

function frmResume_flcHistory_setVisibility(boolean){
  voltmx.print("### frmResume_flcHistory_setVisibility");

  function flcHistory_setVisibility(){
    voltmx.print("### frmResume_flcHistory_setVisibility flcHistory_setVisibility: " + boolean);
  	frmResume.flcHistory.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcHistory_setVisibility, []);
}

function frmResume_segHistoricCaseText_setVisibility(boolean){
  voltmx.print("### frmResume_segHistoricCaseText_setVisibility");

  function segHistoricCaseText_setVisibility(){
    voltmx.print("### frmResume_segHistoricCaseText_setVisibility segHistoricCaseText_setVisibility: " + boolean);
  	frmResume.segHistoricCaseText.setVisibility(boolean);
  }
  voltmx.runOnMainThread(segHistoricCaseText_setVisibility, []);
}

function frmResume_segHistoryCases_setVisibility(boolean){
  voltmx.print("### frmResume_segHistoryCases_setVisibility");

  function segHistoryCases_setVisibility(){
    voltmx.print("### frmResume_segHistoryCases_setVisibility segHistoryCases_setVisibility: " + boolean);
  	frmResume.segHistoryCases.setVisibility(boolean);
  }
  voltmx.runOnMainThread(segHistoryCases_setVisibility, []);
}

function frmResume_enforcementobject_setVisibility(boolean){
  voltmx.print("### frmResume_enforcementobject_setVisibility");

  function enforcementobject_setVisibility(){
    voltmx.print("### frmResume_enforcementobject_setVisibility enforcementobject_setVisibility: " + boolean);
  	frmResume.enforcementobject.setVisibility(boolean);
  }
  voltmx.runOnMainThread(enforcementobject_setVisibility, []);
}

function frmResume_questions_flcDelete_setVisibility(boolean){
  voltmx.print("### frmResume_questions_flcDelete_setVisibility");

  function questions_flcDelete_setVisibility(){
    voltmx.print("### frmResume_questions_flcDelete_setVisibility questions_flcDelete_setVisibility: " + boolean);
  	frmResume.questions.flcDelete.setVisibility(boolean);
  }
  voltmx.runOnMainThread(questions_flcDelete_setVisibility, []);
}

function frmResume_lblEmptySignature_setVisibility(boolean, callback){
  voltmx.print("### frmResume_lblEmptySignature_setVisibility");

  function lblEmptySignature_setVisibility(){
    voltmx.print("### frmResume_lblEmptySignature_setVisibility lblEmptySignature_setVisibility: " + boolean);
  	frmResume.lblEmptySignature.setVisibility(boolean);
    if (callback != null){
      callback();
    }
  }
  voltmx.runOnMainThread(lblEmptySignature_setVisibility, []);
}

function frmResume_flcStreet_setVisibility(boolean){
  voltmx.print("### frmResume_flcStreet_setVisibility");

  function flcStreet_setVisibility(){
    voltmx.print("### frmResume_flcStreet_setVisibility flcStreet_setVisibility: " + boolean);
  	frmResume.flcStreet.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcStreet_setVisibility, []);
}

function frmResume_flcZipPlace_setVisibility(boolean){
  voltmx.print("### frmResume_flcZipPlace_setVisibility");

  function flcZipPlace_setVisibility(){
    voltmx.print("### frmResume_flcZipPlace_setVisibility flcZipPlace_setVisibility: " + boolean);
  	frmResume.flcZipPlace.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcZipPlace_setVisibility, []);
}

function frmResume_flcAddressline1_setVisibility(boolean){
  voltmx.print("### frmResume_flcAddressline1_setVisibility");

  function flcAddressline1_setVisibility(){
    voltmx.print("### frmResume_flcAddressline1_setVisibility flcAddressline1_setVisibility: " + boolean);
  	frmResume.flcAddressline1.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcAddressline1_setVisibility, []);
}

function frmResume_flcAddressline2_setVisibility(boolean){
  voltmx.print("### frmResume_flcAddressline2_setVisibility");

  function flcAddressline2_setVisibility(){
    voltmx.print("### frmResume_flcAddressline2_setVisibility flcAddressline2_setVisibility: " + boolean);
  	frmResume.flcAddressline2.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcAddressline2_setVisibility, []);
}

function frmResume_flcAddressline3_setVisibility(boolean){
  voltmx.print("### frmResume_flcAddressline3_setVisibility");

  function flcAddressline3_setVisibility(){
    voltmx.print("### frmResume_flcAddressline3_setVisibility flcAddressline3_setVisibility: " + boolean);
  	frmResume.flcAddressline3.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcAddressline3_setVisibility, []);
}

function frmResume_flcMailbox_setVisibility(boolean){
  voltmx.print("### frmResume_flcMailbox_setVisibility");

  function flcMailbox_setVisibility(){
    voltmx.print("### frmResume_flcMailbox_setVisibility flcMailbox_setVisibility: " + boolean);
  	frmResume.flcMailbox.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcMailbox_setVisibility, []);
}

function frmResume_flcReplyNumber_setVisibility(boolean){
  voltmx.print("### frmResume_flcReplyNumber_setVisibility");

  function flcReplyNumber_setVisibility(){
    voltmx.print("### frmResume_flcReplyNumber_setVisibility flcReplyNumber_setVisibility: " + boolean);
  	frmResume.flcReplyNumber.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcReplyNumber_setVisibility, []);
}

function frmResume_setsignature_setVisibility(boolean){
  voltmx.print("### frmResume_setsignature_setVisibility");

  function setsignature_setVisibility(){
    voltmx.print("### frmResume_setsignature_setVisibility setsignature_setVisibility: " + boolean);
  	frmResume.setsignature.setVisibility(boolean);
  }
  voltmx.runOnMainThread(setsignature_setVisibility, []);
}

function frmResume_flcFooterMain_setVisibility(boolean){
  voltmx.print("### frmResume_flcFooterMain_setVisibility");

  function flcFooterMain_setVisibility(){
    voltmx.print("### frmResume_flcFooterMain_setVisibility flcFooterMain_setVisibility: " + boolean);
  	frmResume.flcFooterMain.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcFooterMain_setVisibility, []);
}

function frmResume_flcSetClampNumber_setVisibility(boolean){
  voltmx.print("### frmResume_flcSetClampNumber_setVisibility");

  function flcSetClampNumber_setVisibility(){
    voltmx.print("### frmResume_flcSetClampNumber_setVisibility flcSetClampNumber_setVisibility: " + boolean);
  	frmResume.flcSetClampNumber.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcSetClampNumber_setVisibility, []);
}

function frmResume_flcClampDetail_setVisibility(boolean){
  voltmx.print("### frmResume_flcClampDetail_setVisibility");

  function flcClampDetail_setVisibility(){
    voltmx.print("### frmResume_flcClampDetail_setVisibility flcClampDetail_setVisibility: " + boolean);
  	frmResume.flcClampDetail.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcClampDetail_setVisibility, []);
}


function frmResume_imgPopupClampLogo_setVisibility(boolean){
  voltmx.print("### frmResume_imgPopupClampLogo_setVisibility");

  function imgPopupClampLogo_setVisibility(){
    voltmx.print("### frmResume_imgPopupClampLogo_setVisibility imgPopupClampLogo_setVisibility: " + boolean);
  	frmResume.imgPopupClampLogo.setVisibility(boolean);
  }
  voltmx.runOnMainThread(imgPopupClampLogo_setVisibility, []);
}



function frmResume_onHide(){
  //#ifdef android
  	voltmx.print("### frmResume_onHide start");
	frmResume.mapdisplay.mapLocation.clear();
  	frmResume_mapdisplay_mapLocation_setVisibility(false);
  	voltmx.print("### frmResume_onHide end");
  //#endif
}

function frmResume_init(){
  	voltmx.print("### frmResume_init");
  	// Disable Back Button
  	frmResume.onDeviceBack = Global_onDeviceBack;
  	frmResume.contentSize = {height : "100%", width : "100%"};
  	var orientation = voltmx.os.getDeviceCurrentOrientation();
  	voltmx.print("### frmResume init orientation" + orientation);
	//var swipeSettings = {fingers:1,swipedistance:75,swipevelocity:75};
	//var swipeGesture = frmResume.setGestureRecognizer(2,swipeSettings,frmResume_handleGesture);
  	//var gesture2 = frmResume.segPersons.rowTemplate.flcPersonPopupDetails.addGestureRecognizer(constants.GESTURE_TYPE_SWIPE, {fingers: 1}, frmResume_swipeToDeletePersons);
  	frmResume.uploadcase.imgLoader.src = "empty.png";
  	//#ifdef iphone
  	frmResume.locationwithinurbanarea.lbxList.centerX = 52 +"%";
  	frmResume.lbxChooseTaskOutcome.centerX = 52 +"%";
  	frmResume.tickettype.lbxList.centerX = 52 +"%";
  	if (orientation == constants.DEVICE_ORIENTATION_PORTRAIT) {
        frmResume.imgPopupLogoOptionVariables.centerY = 33 +"%";
    } else if (orientation == "1") {
        frmResume.imgPopupLogoOptionVariables.centerY = 27 +"%";
    }
  	//#endif
  	//#ifdef ipad
  	frmResume.locationwithinurbanarea.lbxList.centerX = 50 +"%";
  	frmResume.lbxChooseTaskOutcome.centerX = 50 +"%";
  	frmResume.tickettype.lbxList.centerX = 50 +"%";
  	if (orientation == constants.DEVICE_ORIENTATION_PORTRAIT) {
        frmResume.imgPopupLogoOptionVariables.centerY = 33 +"%";
    } else if (orientation == "1") {
        frmResume.imgPopupLogoOptionVariables.centerY = 27 +"%";
    }
  	//#endif
  	// RL-384
    frmResume.locationwithinurbanarea.lbxList.expandListItemToParentWidth = true;
    frmResume.lbxChooseTaskOutcome.expandListItemToParentWidth = true;
    frmResume.tickettype.lbxList.expandListItemToParentWidth = true;
    //
  	//frmResume_tickettype_setVisibility(false);
    //frmResume_person_setVisibility(Global.vars.setPersonOnResume);
  	//frmResume.segSavedOptions.widgetDataMap = {lbl1: "optiondescription"};
  	//frmResume_mapdisplay_flcImage_setVisibility(false);
    //frmResume_mapdisplay_mapLocation_setVisibility(true);
  	// init
}

function frmResume_handleGesture(myWidget, gestureInfo){
 	voltmx.print("#### frmResume_handleGesture: " + gestureInfo.swipeDirection);
	if(gestureInfo.swipeDirection == 2) {
		voltmx.print("### swipe direction 2");
		//frmResume_onclick_btnBack();
	}
}

function frmResume_preshow(){
	Analytics_logScreenView("resume");
  	voltmx.print("#### frmResume_preshow"); 
  	var _activeTaskTypeInOpenTaskTypesToQuery = Utility_getIndexIfObjWithAttr(Global.vars.openTaskTypes,"taskType",CaseData.processinfo.activeTaskType);
  	if (_activeTaskTypeInOpenTaskTypesToQuery == -1){
      activeTaskTypeInOpenTaskTypesToQuery = false;
    } else {
      activeTaskTypeInOpenTaskTypesToQuery = true;
    }
   	voltmx.print("### frmResume_preshow activeTaskTypeInOpenTaskTypesToQuery: " + activeTaskTypeInOpenTaskTypesToQuery);
  	//check for neccesary Objects/Arrays
    voltmx.print("### frmResume_preshow CaseData.caseinfo.secondOfficerName: " + CaseData.caseinfo.secondOfficerName);
  	voltmx.print("### frmResume_preshow CaseData.caseinfo.thirdOfficerName: " + CaseData.caseinfo.thirdOfficerName);
  	if(CaseData.person == null){
      CaseData.person = [CaseData_setNewperson(null)];
    }
    if(CaseData.offence == null){
      CaseData["offence"] = CaseData_setNewoffence();
    }
  	if(CaseData.assessment == null){
      CaseData["assessment"] = CaseData_setNewAssessment();
    }
  	if(CaseData.permits == null){
      CaseData["permits"] = [CaseData_setNewPermit()];
    }
  	if(CaseData.parking == null){
      CaseData["parking"] = CaseData_setNewParking();
    }
  	if(CaseData.text == null){
      CaseData["text"] = [];
    }
  	if(CaseData.multimedia == null){
      CaseData["multimedia"] = [];
    }
  	if(CaseData.option == null){
      CaseData["option"] = [];
    }
  	if(CaseData.globals == null){
      CaseData["globals"] = {};
    }
  	if(CaseData.transportticket == null){
      CaseData["transportticket"] = CaseData_setNewTransportTicket();
    }
  	if(CaseData.enforcementObject == null){
      CaseData["enforcementObject"] = CaseData_setNewEnforcementObject();
    }
  	if(CaseData.questions == null){
      CaseData["questions"] = [];
    }
  	// RL-15110
	Utility_checkOffenceTypeCode();
  	//
  	frmResume.lblEmptyDatasetText.text = voltmx.i18n.getLocalizedString("l_clickToAddPhotos");
  	///set header title
  	var caseType = "";
    var caseTypePrefix = "";
  	if(CaseData.caseinfo.caseType !== undefined && CaseData.caseinfo.caseType != null){
      caseType = CaseData.caseinfo.caseType.split("_");
      caseTypePrefix = caseType[0];
    } 
  	if (caseTypePrefix != null && caseTypePrefix.length > 0){
      if(caseTypePrefix.endsWith("DVV") || caseTypePrefix.endsWith("DRV")){
        frmResume.lblSubHeader.text = "Uitreiken verbod";
      }
      if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" ){
          frmResume.lblSubHeader.text = "Overdragen politie";
          CaseData.caseinfo.createdUser = Global.vars.gUsername;
      }else if(CaseData.processinfo.activeTaskType == "CorrigerenKandidaat"){
          frmResume.lblSubHeader.text = "Uitreiken verbod";
          CaseData.caseinfo.createdUser = Global.vars.gUsername;
          //user dingen
          CaseData_setUserInformationToCaseInfo();
      }else if(CaseData.processinfo.activeTaskType == "CorrigerenVerbod"){
          frmResume.lblSubHeader.text = "Uitreiken verbod"; 
      }else if(CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod"){
          frmResume.lblSubHeader.text = "Aanvullen verbod";
      }else if(CaseData.processinfo.activeTaskType == "VersturenVerbod"){
          frmResume.lblSubHeader.text = "Versturen verbod";
      }else if(caseTypePrefix.endsWith("DVV") === false && caseTypePrefix.endsWith("DRV") === false){
          frmResume.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_resume");
      }
    } else {
      frmResume.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_resume");
    }  
  	//SAVE original amounts to be able to set back when you change these (for example when setting Governmental fine)
  	Global.vars.originalOffenceAmounts.amount = CaseData.offence.amount;
    Global.vars.originalOffenceAmounts.amountDisplay = CaseData.offence.amountDisplay;
    Global.vars.originalOffenceAmounts.amountExtra = CaseData.offence.amountExtra;
    Global.vars.originalOffenceAmounts.amountExtraDisplay = CaseData.offence.amountExtraDisplay;
  	//
  	frmResume_mapdisplay_setVisibility(true);
  	//
  	if(CaseData.location !== undefined && CaseData.location.latitude === undefined){
      var originalData = JSON.parse(JSON.stringify(CaseData.location));
      CaseData.location = CaseData_setNewlocation();
      Utility_MergeRecursive(CaseData.location,originalData);
      voltmx.print("### frmResume_preshow CaseData.caseinfo: " + JSON.stringify(CaseData.location));
    }
    if(CaseData.caseinfo !== undefined && CaseData.caseinfo.deviceLocation === undefined){
      var originalDataInfo = JSON.parse(JSON.stringify(CaseData.caseinfo));
      CaseData.caseinfo = CaseData_setNewCaseInfo();
      Utility_MergeRecursive(CaseData.caseinfo,originalDataInfo);
      voltmx.print("### frmResume_preshow CaseData.caseinfo: " + JSON.stringify(CaseData.caseinfo));
    }
  	if(CaseData.prohibitions !== undefined && (CaseData.prohibitions.prohibitionHanded === undefined || CaseData.prohibitions.prohibitionPersonTraces === undefined)){
      var originalProhibitionData = JSON.parse(JSON.stringify(CaseData.prohibitions));
      CaseData.prohibitions = CaseData_setProhibitionObject();
      Utility_MergeRecursive(CaseData.prohibitions,originalProhibitionData);
      voltmx.print("### frmResume_preshow CaseData.prohibitions: " + JSON.stringify(CaseData.prohibitions));
      //code to replace CaseData person from CaseData with person information filled in in app
    }
  	if(CaseData.prohibitions !== undefined && CaseData.prohibitions.prohibitionPersonTraces !== undefined && Global.vars.currentPersonInformationProhibitions.length > 0){
      voltmx.print("### frmResume_preshow CaseData.prohibitions save original person data and set current persondata to case");
      if(CaseData.person !== undefined && CaseData.person[Global.vars.gCasePersonsIndex] !== undefined){
        CaseData.prohibitions.prohibitionPersonTraces.push(CaseData.person[Global.vars.gCasePersonsIndex]);
        CaseData.person = Global.vars.currentPersonInformationProhibitions;
      }
    }
  	if(CaseData.vehicle !== undefined && CaseData.vehicle.length === 0){
      voltmx.print("### frmResume_preshow CaseData.vehicle is empty, set as person if person is available");
      if(CaseData.person !== undefined && CaseData.person[Global.vars.gCasePersonsIndex] !== undefined){
        //fill vehicletype data for person
        Utility_setPersonAsVehicleType();
      }
    }
  	frmResume.flcOffenceSanction.offenceinfo.width = "48%";
  	//Utility_preloadMaps("frmResume");
  	if(Global.vars.backToPreviousForm != "frmHistory" && (activeTaskTypeInOpenTaskTypesToQuery === false && CaseData.processinfo.activeTaskType != "ValidateDocument" && CaseData.processinfo.activeTaskType != "ValidateAlibi") && Global.vars.preserveData !== undefined && Global.vars.preserveData != null && Utility_isEmptyObject(Global.vars.preserveData)=== false){
      voltmx.print("### frmResume_preshow preserved option data available");
      if(Global.vars.preserveData.offence != null && Utility_isEmptyObject(Global.vars.preserveData.offence)=== false){
        if(CaseData.offence.offenceCode == Global.vars.preserveData.offence.offenceCode && CaseData.caseinfo.externalId == Global.vars.preserveData.caseID){
          CaseData.option = Global.vars.preserveData.option;
          voltmx.print("### frmResume_preshow set preserved option data");
          voltmx.print("### frmResume_preshow set preserved option data Global.vars.preserveData.offenceStatement.person: " + Global.vars.preserveData.offenceStatement.person);
          voltmx.print("### frmResume_preshow set preserved option data Global.vars.preserveData.offenceStatement.offenceCommunicated: " + Global.vars.preserveData.offenceStatement.offenceCommunicated);
          voltmx.print("### frmResume_preshow set preserved option data CaseData.person[Global.vars.gCasePersonsIndex]: " + JSON.stringify(CaseData.person[Global.vars.gCasePersonsIndex]));
          voltmx.print("### frmResume_preshow set preserved data Global.vars.preserveData.offenceStatement: " + JSON.stringify(Global.vars.preserveData.offenceStatement));
          voltmx.print("### frmResume_preshow set preserved option data Global.vars.preserveData.offenceStatement.rightsRead: " + Global.vars.preserveData.offenceStatement.rightsRead);
          if(Global.vars.preserveData.offenceStatement.person === true && CaseData.person[Global.vars.gCasePersonsIndex] !== undefined && Global.vars.preserveData.offenceStatement.offenceCommunicated != null && Global.vars.preserveData.offenceStatement.offenceCommunicated !== ""){
            Utility_setStatementDataToCase(Global.vars.preserveData.offenceStatement,Global.vars.preserveData.StatementNoPledge,Global.vars.preserveData.OffenceCommunicatedNoStatement,Global.vars.preserveData.statementText,Global.vars.preserveData.enableDefaultRepealText);
            voltmx.print("### frmResume_preshow set preserved statement data");
          }
        }
      }
    }
  	frmResume_settext2_flcCopyPaste_setVisibility(false);
  	voltmx.print("### frmResume_preshow check history");
  	voltmx.print("#### frmResume_preshow check CaseData.processinfo.activeTaskType: "+ CaseData.processinfo.activeTaskType);
  	var _caseType = " ";
    if (CaseData.caseinfo != null && CaseData.caseinfo.caseType != null){
      _caseType = CaseData.caseinfo.caseType;
    }
    if (Global.vars.backToPreviousForm == "frmHistory"){
      frmResume.flcLayoutInput.setEnabled(false);
    } else if(activeTaskTypeInOpenTaskTypesToQuery === true){
       voltmx.print("#### EditTicket frmResume_preshow NO frmPhotos_getOnlineAttachments");
      Utility_getTaskOutcomesForTask(CaseData.processinfo.activeTaskType);
    } else if((CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi" ||  
               CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod" || CaseData.processinfo.activeTaskType == "OverdrachtPolitie" || 
              (_caseType.startsWith("MLD_") && Global.vars.backToPreviousForm == "frmFollow"))){
      Utility_getTaskOutcomesForTask(CaseData.processinfo.activeTaskType);
      voltmx.print("#### frmResume_preshow frmPhotos_getOnlineAttachments");
      frmPhotos_getOnlineAttachments();
    }
  	frmResume_dismissLoaderUploadCase();
  	GPS_watchMypositionCancel();
  	voltmx.print("### frmResume_preshow CaseData.offence: " + JSON.stringify(CaseData.offence));
  	voltmx.print("### frmResume_preshow data: " + JSON.stringify(CaseData));
  	voltmx.print("### frmResume_preshow data CaseData.caseinfo.caseType: " + JSON.stringify(CaseData.caseinfo.caseType));
  	voltmx.print("### frmResume_preshow data multimedia: " + JSON.stringify(CaseData.multimedia));
  	frmResume.statement.lblText.text = voltmx.i18n.getLocalizedString("l_statement");
  	frmResume.statement.lblText.skin = lblFieldNotFilled;
  	//empty amount
  	frmResume.sanctioninfo.lblTotalAmount.text = "";
  	voltmx.print("### preshow data activeTaskType: " + CaseData.processinfo.activeTaskType);
  	//#ifdef android
  	frmResume.mapdisplay.top = 44 + 'dp';
  	//#endif
  	//
  	voltmx.print("### frmResume_preshow set duplicate - RegionDuplicatePopupSet: " + Global.vars.RegionDuplicatePopupSet);
  	frmResume.duplicate.lblText.text = voltmx.i18n.getLocalizedString("l_nr");
  	frmResume.duplicate.lblText.skin = lblFieldNotFilled;
  	if(Global.vars.RegionDuplicatePopupSet === false){
      	frmResume.setvehicleinfo.duplicatepopup.txtInputText.text = "";
  		frmResume.setvehicleinfo.regioncodepopup.txtInputText.text = "";
    }
    frmResume.regioncode.lblText.text = voltmx.i18n.getLocalizedString("l_code");
  	frmResume.regioncode.lblText.skin = lblFieldNotFilled;
  	//
  	frmResume.imgSignature.src = "empty.png";
  	//Set map
  	Global.vars.previousForm = "frmResume";
  	//frmResume.mapdisplay.imgLocation.base64 = Global.vars.base64MapImage;
  	//set photo element
  	voltmx.print("### frmResume_preshow set photo element");
    if(Global.vars.showMultimediaElement === false && CaseData.offence.offenceCode !== undefined && CaseData.offence.offenceCode == "R397B" && (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV")){
  	//if(Global.vars.showMultimediaElement === false && ((CaseData.offence.offenceCode !== undefined && CaseData.offence.offenceCode == "R397B") || (Global.vars.handleCharacteristicType == "OutcomeType" && Global.vars.handleCharacteristicType == "OutcomeTypeCategory")) && Global.vars.buildFor == "NS"){
      frmResume_multimedia_setVisibility(true);
    }else if(Global.vars.showMultimediaElement === false && (Global.vars.handleCharacteristicType == "OutcomeType" || Global.vars.handleCharacteristicType == "OutcomeTypeCategory") && (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV") && Global.vars.useDemo === true){
      frmResume_multimedia_setVisibility(true);
    }else if(Global.vars.showMultimediaElement === false && (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV") && (_caseType.startsWith("DVV_") || _caseType.startsWith("DRV_") || _caseType.startsWith("RV_") || _caseType.startsWith("VV_") || _caseType.startsWith("KRV_") || _caseType.startsWith("KVV_") || _caseType.startsWith("SRV_") || _caseType.startsWith("SVV_"))){
      frmResume_multimedia_setVisibility(false);
    }else if(Global.vars.showMultimediaElement === false){
      frmResume_multimedia_setVisibility(false);
    }else{
      frmResume_multimedia_setVisibility(true);
    }
  	if(CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod" && (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV") && (_caseType.startsWith("DVV_") || _caseType.startsWith("DRV_"))){
      frmResume_multimedia_setVisibility(true);
    }
  	//set data
  	voltmx.print("#### frmResume_preshow setData");
  	frmResume_setExtendedReportMode();
	voltmx.print("### frmResume_preshow set onStreetPayment");
  	if (Global.vars.onStreetPayment === true  && CaseData.caseinfo.enforcementType == "S"){
      frmResume_onstreetpayment_setVisibility(true);
      if (CaseData.caseinfo.indPayed === true){
       frmResume.onstreetpayment.lblText.text = voltmx.i18n.getLocalizedString("l_yes");
      } else {
       frmResume.onstreetpayment.lblText.text = voltmx.i18n.getLocalizedString("l_no");
      }
    } else {
      frmResume_onstreetpayment_setVisibility(false);
    }
	if(Global.vars.saveCaseEnabled === true && (activeTaskTypeInOpenTaskTypesToQuery === false && CaseData.processinfo.activeTaskType != "ValidateDocument" && 
                                                CaseData.processinfo.activeTaskType != "ValidateAlibi" && CaseData.processinfo.activeTaskType != "AanvullenDirectVerbod" && 
                                                CaseData.processinfo.activeTaskType != "OverdrachtPolitie" && CaseData.processinfo.activeTaskType != "CorrigerenKandidaat") && 
       											Global.vars.backToPreviousForm !== "frmHistory" && CaseData.processinfo.activeTaskType != "OpvolgenControleOpStraat")
    {
      frmResume_flcSaveCase_setVisibility(true);
    }else{
      frmResume_flcSaveCase_setVisibility(false);
    }
  	voltmx.print("### frmResume_preshow check anprphotos: " + JSON.stringify(Global.vars.addANPRPhotos));
	if(Global.vars.addANPRPhotos != null && Global.vars.addANPRPhotos.length > 0 && (activeTaskTypeInOpenTaskTypesToQuery === false && 
                                                                                           CaseData.processinfo.activeTaskType != "ValidateDocument" && 
                                                                                           CaseData.processinfo.activeTaskType != "ValidateAlibi") && 
       																						CaseData.processinfo.activeTaskType != "AanvullenDirectVerbod" &&
      																						CaseData.processinfo.activeTaskType != "OverdrachtPolitie" &&
       																						CaseData.processinfo.activeTaskType != "CorrigerenKandidaat" &&
       																						frmResume.multimedia.isVisible === true && Global.vars.showPhotosANPRLoaded === false){
    	voltmx.print("### frmResume_preshow Global.vars.addANPRPhotos length: " + Global.vars.addANPRPhotos.length);
      	var anprpictures = [];
      	for(var a in Global.vars.addANPRPhotos){
          anprpictures.push(Global.vars.addANPRPhotos[a]);
        }
      	voltmx.print("### frmResume_preshow Global.vars.addANPRPhotos: " + JSON.stringify(Global.vars.addANPRPhotos));
      	Utility_showPhotosANPR(anprpictures);
      	Utility_addANPRAttachmentInline();
  	}
  	voltmx.print("### frmResume_preshow end");
  //	frmResume_setParkiusMode();
}

function frmResume_setExtendedReportMode(){
  voltmx.print("### frmResume_setExtendedReportMode CaseData.caseinfo.caseTypeCategory: " + CaseData.caseinfo.caseTypeCategory);
  if (CaseData.caseinfo.caseTypeCategory == "dpv"){
    frmResume.time.lblTimeHeader.text = voltmx.i18n.getLocalizedString("l_registrationDateTime");
    frmResume_secondofficer_setVisibility(true);
    frmResume_thirdofficer_setVisibility(true);
    frmResume.thirdofficer.lblHeader.text = voltmx.i18n.getLocalizedString("l_assessor");
    frmResume_relatedcase_setVisibility(false);
    frmResume_clearRelatedCase();
  	frmResume_sanctioninfo_setVisibility(false);
    //
    frmResume_locationspecification_setVisibility(false);
    frmResume_furtherlocationindication_setVisibility(false);
    frmResume_multimedia_setVisibility(false);
    frmResume_enforcementobject_setVisibility(false);
    frmResume_person_setVisibility(false);
    frmResume_flcVehicleInfo_setVisibility(false);
  } else if (Utility_isExtendReportCaseTypeCategory(CaseData.caseinfo.caseTypeCategory) === true){
    frmResume.time.lblTimeHeader.text = voltmx.i18n.getLocalizedString("l_registrationDateTime");
    frmResume_secondofficer_setVisibility(true);
    frmResume_thirdofficer_setVisibility(true);
    frmResume.thirdofficer.lblHeader.text = voltmx.i18n.getLocalizedString("l_thirdOfficer");
    frmResume_relatedcase_setVisibility(true);
    //
    frmResume_sanctioninfo_setVisibility(false);
    frmResume_locationspecification_setVisibility(true);
    frmResume_furtherlocationindication_setVisibility(true);
    frmResume_multimedia_setVisibility(true);
    frmResume_enforcementobject_setVisibility(true);
    frmResume_person_setVisibility(true);
    frmResume_flcVehicleInfo_setVisibility(true);
  } else {
    frmResume.time.lblTimeHeader.text = voltmx.i18n.getLocalizedString("l_checkTime");
    frmResume_thirdofficer_setVisibility(false);
    frmResume_clearThirdOfficer();
    frmResume_relatedcase_setVisibility(false);
    frmResume_clearRelatedCase();
    //
    frmResume_sanctioninfo_setVisibility(true);
    frmResume_locationspecification_setVisibility(true);
    frmResume_furtherlocationindication_setVisibility(true);
    //frmResume_multimedia_setVisibility(true); it is already set before
    frmResume_enforcementobject_setVisibility(true);
    frmResume_person_setVisibility(true);
    frmResume_flcVehicleInfo_setVisibility(true);
  }
  voltmx.print("### frmResume_setExtendedReportMode end");
}


function frmResume_setParkiusMode(){
  voltmx.print("### frmResume_setParkiusMode:" + Global.vars.enableParkiusMode);
  if (Global.vars.enableParkiusMode === true){
    frmResume_locationwithinurbanarea_setVisibility(false);
    frmResume_duplicate_setVisibility(false);
    frmResume_tickettype_setVisibility(false);
    frmResume.licenseplate.width = '100%';
    frmResume.licenseplate.flcLicensePlate.centerX = '50%';
    frmResume.licenseplate.flcLicensePlate.width = '82.3%';
    frmResume_secondofficer_setVisibility(false);
    frmResume_thirdofficer_setVisibility(false);
    frmResume_sanctioninfo_setVisibility(false);
    frmResume.observation.lblHeader.text = voltmx.i18n.getLocalizedString("pl_internalRemark");
    frmResume_offenceinfo_flcVerticalLine_setVisibility(false);
    frmResume_offenceinfo_lblOffenceCategory_setVisibility(false);
    frmResume_offenceinfo_lblOffenceCategoryHeader_setVisibility(false);
  	frmResume.offenceinfo.width = '100%';
  	frmResume.offenceinfo.lblOffenceCode.width = '100%';
  	frmResume.offenceinfo.lblOffenceCode.centerX = '50%';
   	frmResume.offenceinfo.lblOffenceHeader.centerX = '50%';
   }
  voltmx.print("### frmResume_setParkiusMode end");
}


function frmResume_postshow(){
	voltmx.print("#### frmResume_postshow");
    frmResume_setData();
  	if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true){
      voltmx.print("### frmResume_postshow destroy frm Photos");
//       frmPhotos.destroy();
//       frmPhotoOverview.destroy();
    }
    //set location
    voltmx.print("### frmResume postshow frmResume_set_location");
  	frmResume_set_location();
    voltmx.print("### frmResume postshow map");
  	frmResume_mapdisplay_flcImage_setVisibility(false);
  	frmResume_mapdisplay_mapLocation_setVisibility(true);
    voltmx.print("### frmResume postshow setTaskoutcomeOnForm");
    frmResume.btnDone.text = voltmx.i18n.getLocalizedString("bt_send");
  	var _mld_casetype = false;
	voltmx.print("### frmResume postshow setTaskoutcomeOnForm CaseData.caseinfo.caseType: " + CaseData.caseinfo.caseType);
    if (CaseData.caseinfo != null && CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.length !== ""){
      _mld_casetype = voltmx.string.startsWith(CaseData.caseinfo.caseType,"MLD_");
    }
    var _unhandledTaskTypes = Utility_getIndexIfObjWithAttr(Global.vars.unhandledTaskTypes,"taskType",CaseData.processinfo.activeTaskType);
   	if(
      	(
      		(
              activeTaskTypeInOpenTaskTypesToQuery === true 
             || CaseData.processinfo.activeTaskType == "ValidateDocument" 
             || CaseData.processinfo.activeTaskType == "ValidateAlibi"
            ) && Global.vars.buildFor !== "NS" && Global.vars.backToPreviousForm !== "frmHistory"
        ) 
      || CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod" 
      || CaseData.processinfo.activeTaskType == "OverdrachtPolitie" 
      || (
        	_mld_casetype === true && Global.vars.backToPreviousForm == "frmFollow"
         )
      || _unhandledTaskTypes !== -1
    ){
      frmResume_setTaskOutcomeOnForm();
      frmResume_tickettype_setVisibility(false);
    }else{
      frmResume_taskoutcome_setVisibility(false);
    }
  	voltmx.application.dismissLoadingScreen();
	voltmx.print("### frmResume postshow end");
}

function frmResume_tryToGetImageMapCallback(){
  //#ifdef android
  frmResume.mapdisplay.imgLocation.base64 = Global.vars.base64MapImage;
  frmResume_mapdisplay_flcImage_setVisibility(true);
  frmResume_mapdisplay_mapLocation_setVisibility(false);
  //#endif
  voltmx.application.dismissLoadingScreen();
}

function frmResume_setPersonDetailsOnForm(){
  voltmx.print("### frmResume_setPersonDetailsOnForm");
  if(CaseData.person[Global.vars.gCasePersonsIndex] != null){
    var name = "";
    if(CaseData.person[Global.vars.gCasePersonsIndex].initials !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].initials != null && CaseData.person[Global.vars.gCasePersonsIndex].initials !== ""){
      name = CaseData.person[Global.vars.gCasePersonsIndex].initials;
    }
    if(CaseData.person[Global.vars.gCasePersonsIndex].middlename !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].middlename != null && CaseData.person[Global.vars.gCasePersonsIndex].middlename !== ""){
      name = name + " " + CaseData.person[Global.vars.gCasePersonsIndex].middlename;
    }
    if(CaseData.person[Global.vars.gCasePersonsIndex].surname !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].surname != null && CaseData.person[Global.vars.gCasePersonsIndex].surname !== ""){
      name = name + " " + CaseData.person[Global.vars.gCasePersonsIndex].surname;
    }
    frmResume.person.lblText.text = name;
    frmResume.person.lblText.skin = lblFieldInfo;
  }
  var _noCautionPersonCaseType = Utility_getIndexIfObjWithAttr(Global.vars.noCautionPersonCaseType,"caseType",CaseData.caseinfo.caseType);
  voltmx.print("### frmResume_setPersonDetailsOnForm _noCautionPersonCaseType: " + _noCautionPersonCaseType);
  if((CaseData.offence.person === true || CaseData.caseinfo.caseTypeCategory == 'hav') && _noCautionPersonCaseType == -1){
    frmResume_statement_setVisibility(true);
    frmResume.statement.lblText.text = voltmx.i18n.getLocalizedString("l_statement");
    frmResume.statement.lblText.skin = lblFieldNotFilled;
    voltmx.print("### frmResume_setPersonDetailsOnForm statement visible");
    //set data if available
    if(CaseData.offence.rightsRead === false && Global.vars.cancelAfterRightsReadFalse === true){
      	frmResume.statement.lblText.text = "Geen cautie";
      	frmResume.statement.lblText.skin = lblFieldInfo;
    }else{
      	voltmx.print("#### frmResume_setPersonDetailsOnForm CaseData.text: " + JSON.stringify(CaseData.text));
     	for(var i in CaseData.text){
          var v = CaseData.text[i];
          if((v.type == 2)){
              voltmx.print("#### Text: statement: " + v + " index: " + i);
              //verklaring
              frmResume.statement.lblText.text = v.value; //verklaring
              frmResume.statement.lblText.skin = lblFieldInfo;
          }
        } 
    }
  }else{
    frmResume_statement_setVisibility(false);
  }
}

function frmResume_setPrintButton(){
	voltmx.print("#### frmResume_setPrintButton: processinfo" + JSON.stringify(CaseData.processinfo));
    voltmx.print("#### frmResume_setPrintButton disable print buttons: " + Global.vars.disablePrintButtons);
    //Global.vars.handleCharacteristicType 	
  	if (Global.vars.buildFor == "GEN"){
        if (Global.vars.backToPreviousForm == "frmHistory"){
          if (Global.vars.disablePrintButtons === true || (CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("CTE_"))){
          	frmResume_flcPrint_setVisibility(false);
          } else {
          	frmResume_flcPrint_setVisibility(true);
          }
          frmResume_flcPrintSend_setVisibility(false);
          frmResume_flcDone_setVisibility(false);
        } else if((activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi") || (Global.vars.disablePrintButtons === true) || (CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("CTE_"))){
          frmResume_flcPrint_setVisibility(false);
          frmResume_flcPrintSend_setVisibility(false);
          frmResume_flcDone_setVisibility(true);
        }else{
          frmResume_flcPrint_setVisibility(false);
          frmResume_flcPrintSend_setVisibility(true);
          frmResume_flcDone_setVisibility(false);
        }
   	} else {
       if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense !== undefined &&
         (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense != "NL" && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense != "B") && Global.vars.buildFor == "EPS"){
          frmResume_flcPrint_setVisibility(true);
          frmResume_flcPrintSend_setVisibility(false);
          frmResume_flcDone_setVisibility(false);
   		}else{
          frmResume_flcPrint_setVisibility(false);
          frmResume_flcPrintSend_setVisibility(false);
          if(Global.vars.backToPreviousForm == "frmHistory"){
            frmResume_flcDone_setVisibility(false);
          }else{
            frmResume_flcDone_setVisibility(true);
          }
      	}
    }
}

function frmResume_onclick_btnOnStreetPayment(){
  voltmx.print("### frmResume_onclick_btnParkingCard show popup");
  if (CaseData.person[Global.vars.gCasePersonsIndex] === undefined || CaseData.person[Global.vars.gCasePersonsIndex].validated === false){
      voltmx.ui.Alert(voltmx.i18n.getLocalizedString("i_no_person"),
                    null,
                    "info",
                    voltmx.i18n.getLocalizedString("bt_ok"),
                    null,
                    voltmx.i18n.getLocalizedString("l_info"),
                    null);
  } else {
      if(CaseData.offence.amount != null && Number(CaseData.offence.amount) > 0){
        if (CaseData.caseinfo.indPayed === true){
          frmResume.lbxOnStreetPayment.selectedKey = true;
        } else {
          frmResume.lbxOnStreetPayment.selectedKey = false;
        }
        // Set sanctiontariff
        var administrationcosts = Number(Global.vars.administrationCosts).toFixed(2);
        var administrationcostsCurrency = administrationcosts.formatCurrency();
        frmResume.onstreetsanctioninfo.lblAdminCostsAmount.text = administrationcosts.replace(".", ","); 
        frmResume.onstreetsanctioninfo.lblAdminCostsCurrency.text = administrationcostsCurrency.replace(/[^a-zA-Z]+/g, '');
        var calculatedAmount = (Number(Global.vars.administrationCosts) + Number(CaseData.offence.amount)).toFixed(2);
        if(CaseData.offence.amount === voltmx.i18n.getLocalizedString("l_notariff_star")){
          calculatedAmount = Number(Global.vars.administrationCosts);
        }
        var calculatedAmountCurrency = calculatedAmount.formatCurrency();
        frmResume.onstreetsanctioninfo.lblTotalAmount.text = calculatedAmount.replace(".", ",");
        frmResume.onstreetsanctioninfo.lblTotalAmountCurrency.text = calculatedAmountCurrency.replace(/[^a-zA-Z]+/g, '');
        frmResume_flcOnStreetPaymentPopup_setVisibility(true);
        frmResume.flcMainPage.setEnabled(false);
      }
  } 
}

function frmResume_hideOnStreetPaymentPopup(){
  voltmx.print("### frmResume_onclick_btnParkingCard show popup");
  frmResume_flcOnStreetPaymentPopup_setVisibility(false);
  frmResume.flcMainPage.setEnabled(true);
}

function frmResume_onclick_btnDoneOnStreetPayment(){
  voltmx.print("### ffrmResume_onclick_btnDoneParkingCard");
  if (frmResume.lbxOnStreetPayment.selectedKey === "true"){
    CaseData.caseinfo.indPayed = true;
    frmResume.onstreetpayment.lblText.text = voltmx.i18n.getLocalizedString("l_yes");
  } else {
    CaseData.caseinfo.indPayed = false;
    frmResume.onstreetpayment.lblText.text = voltmx.i18n.getLocalizedString("l_no");
  }
  frmResume_flcOnStreetPaymentPopup_setVisibility(false);
  frmResume.flcMainPage.setEnabled(true);
}

function frmResume_set_location_mapLocation(){
  //#ifdef android
  voltmx.print("### frmResume_set_location_mapLocation");
  if(CaseData.location.latitude !== undefined && CaseData.location.longitude !== undefined && CaseData.location.latitude != null && CaseData.location.longitude != null){
    try{
      frmResume.mapdisplay.mapLocation.clear();
      var _imageMarker = Global.vars.markertakenImage === "" ? "marker.png" : Global.vars.markertakenImage;
      var	lmaptable = [
        {lat : CaseData.location.latitude,
         lon : CaseData.location.longitude,
         name: "",
         desc: "",
         image : _imageMarker,
         showCallout : false
        }
      ];
      voltmx.print("### frmResume_setData frmResume_set_location_mapLocation maptable:" + JSON.stringify(lmaptable));
      //location
      //
      frmResume.mapdisplay.mapLocation.locationData = lmaptable;
      frmResume.mapdisplay.mapLocation.zoomLevel = 18;
      frmResume.mapdisplay.mapLocation.navigateTo(0, false);
    } catch (err){
      voltmx.print("### frmResume_setData frmResume_set_location_mapLocation map crash:" + JSON.stringify(err));
    }
  }
  //#endif
}

function frmResume_set_location(){
  voltmx.print("### frmResume_set_location start");
  voltmx.runOnMainThread(frmResume_set_location_mapLocation, []);
  voltmx.print("### frmResume_set_location end");
}

function frmResume_setData(){
  voltmx.print("### frmResume_setData");
  voltmx.print("### frmResume_setData Global.vars.previousForm: " + Global.vars.previousForm);
  frmResume.segSavedOptions.widgetDataMap = {lbl1: "optiondescription"};
  //
  frmResume_policeofficeremail_setVisibility(false);
  frmResume_policeofficermobile_setVisibility(false);
  frmResume_flcButShowPDF_setVisibility(false);
  frmResume_furtherlocationindication_setVisibility(true);
  frmResume_locationspecification_setVisibility(true);
  frmResume_locationdescription_setVisibility(true);
  frmResume_location_setVisibility(true);
  frmResume_mapdisplay_setVisibility(true);
  if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true){
    frmResume.flcLayoutInput.top = 0 + 'dp';  
  } else {
    frmResume.flcLayoutInput.top = 98 + 'dp'; 
  }	
  frmResume_time_setVisibility(false);
  frmResume_flcPersonAdress_setVisibility(false);
  frmResume_flcTimeArrivedClampNumber_setVisibility(false);
  Global.vars.indCommentrequired = false;
  var _unhandledTaskTypes = Utility_getIndexIfObjWithAttr(Global.vars.unhandledTaskTypes,"taskType",CaseData.processinfo.activeTaskType);
  var _caseType = " ";
  if (CaseData.caseinfo != null && CaseData.caseinfo.caseType != null){
    _caseType = CaseData.caseinfo.caseType;
  }
  if(activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || 
     CaseData.processinfo.activeTaskType == "ValidateAlibi" || CaseData.processinfo.activeTaskType == "OverdrachtPolitie" ||
     CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod"  || CaseData.processinfo.activeTaskType == "VersturenVerbod" ||
     (_caseType.startsWith("MLD_") && Global.vars.backToPreviousForm == "frmFollow")  || _unhandledTaskTypes !== -1){
    frmResume_internalremark_setVisibility(true);
    if (Global.vars.outcomeTypes.length === 1){
      	voltmx.print("### frmResume_setData Global.vars.outcomeTypes: " + JSON.stringify(Global.vars.outcomeTypes));
		if (Global.vars.outcomeTypes[0].ind_comment_required === 1){
          Global.vars.indCommentrequired = true;
        }
    }
  } else {
    frmResume_internalremark_setVisibility(false);
  }
  // AE0001 prefix license plate
  frmResume_licenseplate_flcLicensePlatePrefix_setVisibility(Global.vars.licensePlatePrefixEnabled);
  //
  frmResume_setPrintButton();
  var showOptions = 0;
  Global.vars.options = [];
  Global.vars.optionvariablesText = "";
  Global.vars.optionvariablesSet = false;
  Global.vars.selectedOption = {};
  var showtransportTicket = false;
  frmResume_offenderemail_setVisibility(false);
  frmResume_offendertelephone_setVisibility(false);
  //set location
  //frmResume_set_location();
  //CaseNumber
  var casetypeDescription = Global.vars.caseTypeDescription;
  if(CaseData.caseinfo.externalId !== undefined && CaseData.caseinfo.externalId != null && CaseData.caseinfo.externalId !== ""){
    frmResume_flcCaseNumber_setVisibility(true);
    frmResume.lblCaseNumber.text = "Zaak ID: " + CaseData.caseinfo.externalId;
    if(CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("MLD_")){
      frmResume.lblCaseNumber.text = "Melding met Zaak ID: " + CaseData.caseinfo.externalId;
      if(Global.vars.caseTypeDescription !== ""){
        if(Global.vars.caseTypeDescription.length > 16){
          casetypeDescription = Global.vars.caseTypeDescription.substring(0, 15) + "...";
        }
        frmResume.lblCaseNumber.text = casetypeDescription + " met Zaak ID: " + CaseData.caseinfo.externalId;
      }
    }
  }else{
	if(CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("MLD_")){
      frmResume.lblCaseNumber.text = "Melding";
      if(casetypeDescription !== ""){
        if(casetypeDescription > 16){
          casetypeDescription = casetypeDescription.substring(0, 15) + "...";
        }
        frmResume.lblCaseNumber.text = casetypeDescription;
      }
      frmResume_flcCaseNumber_setVisibility(true);
    } else {
      frmResume_flcCaseNumber_setVisibility(false);
      frmResume.lblCaseNumber.text = "";
    }
  }
  //location specification
  if(CaseData.location.chosenFurtherIndicationKey !== undefined && CaseData.location.chosenFurtherIndicationKey != null){
    voltmx.print("### frmResume_setData CaseData.location.chosenFurtherIndicationKey not null");
    frmResume.furtherlocationindication.lblText.text = CaseData.location.chosenFurtherIndication;
    if(CaseData.location.locationDescription != null && CaseData.location.locationDescription !== voltmx.i18n.getLocalizedString("pl_locationDescription")){
      frmResume.locationdescription.lblText.text = CaseData.location.locationDescription;
    }
  }else{
    frmResume.furtherlocationindication.lblText.text = "Overig";
    if (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV"){
      if(CaseData.processinfo.activeTaskType !== "CorrigerenKandidaat"){
        frmResume_furtherlocationindication_setVisibility(true);
      }
    } else {
      frmResume_furtherlocationindication_setVisibility(false);
    }
    frmResume.locationspecification.lblText.text = voltmx.i18n.getLocalizedString("pl_locationSpec");
    frmResume_locationspecification_setVisibility(false);
    if(CaseData.location.locationDescription != null && CaseData.location.locationDescription !== voltmx.i18n.getLocalizedString("pl_locationDescription")){
      frmResume.locationdescription.lblText.text = CaseData.location.locationDescription;
      frmResume.locationdescription.lblText.skin = lblFieldInfo;
      frmResume_locationdescription_setVisibility(true);
    } else {
      frmResume.locationdescription.lblText.text = voltmx.i18n.getLocalizedString("pl_locationDescription");
      frmResume.locationdescription.lblText.skin = lblFieldNotFilled;
      frmResume_locationdescription_setVisibility(Global.vars.locationDescriptionEnabled);
    }
  }
  if(CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.atTheStation){
    if(CaseData.location.station != null && CaseData.location.platform != null){
      frmResume.locationspecification.lblText.text = CaseData.location.stationCode + " - " + CaseData.location.station + " - " + CaseData.location.platform; 
    }
    frmResume_locationspecification_setVisibility(true);
    frmResume_locationdescription_setVisibility(true);
  }else if(CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.inTheTrain){
    if(CaseData.location.stationFrom != null && CaseData.location.stationTo != null && CaseData.location.rideNumber != null){
      frmResume.locationspecification.lblText.text = "Rit: " + CaseData.location.rideNumber + " " + CaseData.location.stationFromCode + " - " + CaseData.location.stationFrom + " - " + CaseData.location.stationToCode + " - " + CaseData.location.stationTo;
    }
    frmResume_locationspecification_setVisibility(true);
    frmResume_locationdescription_setVisibility(true);
  }else if(CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.alongTheTrack){
    if(CaseData.location.trackSection != null && CaseData.location.kilometerIndication != null){
      frmResume.locationspecification.lblText.text = CaseData.location.trackSection + ", km: " + CaseData.location.kilometerIndication; 
    }
    frmResume_locationspecification_setVisibility(true);
    frmResume_locationdescription_setVisibility(true);
  }else if(CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.atTheBusStop || CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.atTheStationOV){
    if(CaseData.location.busOrTrainStation != null){
      frmResume.locationspecification.lblText.text = CaseData.location.busOrTrainStationCode + " - " + CaseData.location.busOrTrainStation; 
    }
    frmResume_locationspecification_setVisibility(true);
    frmResume_locationdescription_setVisibility(true);
  }else if(CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.inTheBus || CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.inTheTrainOV){
    if(CaseData.location.busOrTrainStationFrom != null && CaseData.location.busOrTrainRideNumber != null){
      frmResume.locationspecification.lblText.text = CaseData.location.busOrTrainRideNumber + " " + CaseData.location.busOrTrainStationFromCode + " - " + CaseData.location.busOrTrainStationFrom;
    }
    frmResume_locationspecification_setVisibility(true);
    frmResume_locationdescription_setVisibility(true);
  }else if(CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.others || CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.onTheWater || CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.alongTheRoad){
    frmResume.locationspecification.lblText.text = voltmx.i18n.getLocalizedString("pl_locationSpec");
    if (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV"){
      frmResume_furtherlocationindication_setVisibility(true);
    } else {
      frmResume_furtherlocationindication_setVisibility(false);
    }
    frmResume_locationspecification_setVisibility(false);
  	if(CaseData.location.locationDescription != null && CaseData.location.locationDescription !== voltmx.i18n.getLocalizedString("pl_locationDescription")){
      frmResume.locationdescription.lblText.text = CaseData.location.locationDescription;
      frmResume.locationdescription.lblText.skin = lblFieldInfo;
      frmResume_locationdescription_setVisibility(true);
    } else {
      frmResume.locationdescription.lblText.text = voltmx.i18n.getLocalizedString("pl_locationDescription");
      frmResume.locationdescription.lblText.skin = lblFieldNotFilled;
      frmResume_locationdescription_setVisibility(Global.vars.locationDescriptionEnabled);
    }
  }
  if(frmResume.locationspecification.lblText.text.length > 28){
    frmResume.locationspecification.lblText.skin = lblFieldInfoSmall;
  }else{
    if(frmResume.locationspecification.lblText.text != null && frmResume.locationspecification.lblText.text !== ""){
      frmResume.locationspecification.lblText.skin = lblFieldInfo;
    }else{
      frmResume.locationspecification.lblText.skin = lblFieldNotFilled;
      frmResume.locationspecification.lblText.text = voltmx.i18n.getLocalizedString("pl_locationSpec");
    }
  }
  //adress
  frmResume.location.lblText.text = Utility_defineLocationAddress();  
  if(frmResume.location.lblText.text != null && frmResume.location.lblText.text.length > 24){
    frmResume.location.lblText.skin = lblFieldInfoSmall;
  }else{
    if(frmResume.location.lblText.text != null && frmResume.location.lblText.text !== ""){
      frmResume.location.lblText.skin = lblFieldInfo;
    }else{
      frmResume.location.lblText.skin = lblFieldNotFilled;
      frmResume.location.lblText.text = voltmx.i18n.getLocalizedString("l_location");
    }
  }
  //set vehicle
  frmResume_setVehicleDetails();
  //set Time
  frmResume.time.lblTime.text = CaseData.time.localeShortDate;
  //set Person
  frmResume.person.lblText.text = voltmx.i18n.getLocalizedString("Person");
  frmResume.person.lblText.skin = lblFieldNotFilled;
  frmResume_setPersonDetailsOnForm();
  if (CaseData.caseinfo.caseTypeCategory !== "dpv"){
    frmResume_setEnforcementObjectDetailsOnForm();
    //frmResume_setEnforcementQuestionDetailsOnForm();
  }
  //set Offence data
  if(CaseData.offence !== undefined && CaseData.offence != null){
    if(CaseData.offence.offenceCatSuspects !== undefined && CaseData.offence.offenceCatSuspects != null){
      frmResume.offenceinfo.lblOffenceCategory.text = CaseData.offence.offenceCatSuspects.toString();
    } else if(CaseData.offence.offenceCatCode != null){
      frmResume.offenceinfo.lblOffenceCategory.text = CaseData.offence.offenceCatCode.toString();
    }
    if(CaseData.offence.offenceCode != null){
      if (Global.vars.enableParkiusMode === true){
        frmResume.offenceinfo.lblOffenceCode.text = CaseData.offence.offenceDescription;
        if(frmResume.offenceinfo.lblOffenceCode.text != null && frmResume.offenceinfo.lblOffenceCode.text.length > 32){
          frmResume.offenceinfo.lblOffenceCode.skin = lblFieldInfoSmall;
        }else{
          frmResume.offenceinfo.lblOffenceCode.skin = lblFieldInfo;
        }
      } else {
        frmResume.offenceinfo.lblOffenceCode.text = CaseData.offence.offenceCode;
        frmResume.offenceinfo.lblOffenceCode.skin = lblFieldInfo;
      }
    }
    if(CaseData.offence.amount != null){
      frmResume.sanctioninfo.lblTotalAmount.text = CaseData.offence.amount.replace(".", ","); 
      if(CaseData.offence.amount !== voltmx.i18n.getLocalizedString("l_notariff_star")){
      	frmResume.sanctioninfo.lblTotalAmount.text = frmResume.sanctioninfo.lblTotalAmount.text.toLocaleString();
      }
    }
    if(CaseData.offence.amountDisplay != null){
      frmResume.sanctioninfo.lblTotalAmountCurrency.text = CaseData.offence.amountDisplay.replace(/[^a-zA-Z]+/g, '');
    }
    //binnen bebouwde kom
    //if(CaseData.offence.offenceCode != null && voltmx.string.startsWith(CaseData.offence.offenceCode, "R")){
    if(CaseData.offence.offenceCode != null){
      frmResume_locationwithinurbanarea_setVisibility(true);
      frmResume.locationwithinurbanarea.lbxList.placeholder = "Kies";
      voltmx.print("### locationwithinurbanarea selectedKey: " + frmResume.locationwithinurbanarea.lbxList.selectedKey);
      if(CaseData.location.locationWithinUrbanArea !== undefined && CaseData.location.locationWithinUrbanArea != null){
        if(CaseData.location.locationWithinUrbanArea === true){
          frmResume.locationwithinurbanarea.lbxList.selectedKey = "Yes";
        }else{
          frmResume.locationwithinurbanarea.lbxList.selectedKey = "No";
        }
      }else{
        if (Global.vars.locationWithinUrbanArea === false){
          frmResume.locationwithinurbanarea.lbxList.selectedKey = "No";
          CaseData.location.locationWithinUrbanArea = false;
        } else {
          frmResume.locationwithinurbanarea.lbxList.selectedKey = "Yes";
          CaseData.location.locationWithinUrbanArea = true;
        }  
      }
    }else{
      frmResume_locationwithinurbanarea_setVisibility(false);
    }
    for(var i in Global.vars.UVB.UVBOffenceCodes){
      if(CaseData.offence.offenceCode == Global.vars.UVB.UVBOffenceCodes[i]){
        showtransportTicket = true;
        break;
      }
    }
    if(showtransportTicket === true){
      frmResume_transportticket_setVisibility(true);
      frmResume.transportticket.lblText.text = "€ " + CaseData.transportticket.totalAmount;
      frmResume.lblStationFromtext.text = "";
      frmResume.lblStationToText.text = "";
      frmResume.lblOffFirstClass.text = "Nee";
      frmResume.lblOffRetour.text = "Nee";
      frmResume.lbltransitionTwoOneText.text = "Nee";
      frmResume.lblsurtaxText.text = "Nee";
      frmResume.lblsurtaxIcdText.text = "Nee";
      frmResume.lblgratuitousText.text = "Nee";
      frmResume.lblTravellerTicketText.text = "Nee";
      frmResume.lblDogsText.text = "0";
      frmResume.lblBikesText.text = "0";
      frmResume.lblRailrunnerText.text = "0";
      if(CaseData.transportticket.stationFrom != null && CaseData.transportticket.stationFrom !== ""){
        frmResume_flcStationFrom_setVisibility(true);
        frmResume.lblStationFromtext.text = CaseData.transportticket.stationFromCode + " - " + CaseData.transportticket.stationFrom;
      }else{
        frmResume_flcStationFrom_setVisibility(false);
      }
      if(CaseData.transportticket.stationTo != null && CaseData.transportticket.stationTo !== ""){
        frmResume_flcStationTo_setVisibility(true);
        frmResume.lblStationToText.text = CaseData.transportticket.stationToCode + " - " + CaseData.transportticket.stationTo;
      }else{
        frmResume_flcStationTo_setVisibility(false);
      }
      if(CaseData.transportticket.firstClass){
        frmResume.lblOffFirstClass.text = "Ja";
      }
      if(CaseData.transportticket.retour){
        frmResume.lblOffRetour.text = "Ja";
      }
      if(CaseData.transportticket.transitionTwoOne){
        frmResume.lbltransitionTwoOneText.text = "Ja";
      }
      if(CaseData.transportticket.surtax){
        frmResume.lblsurtaxText.text = "Ja";
      }
      if(CaseData.transportticket.surtaxIcd){
        frmResume.lblsurtaxIcdText.text = "Ja";
      }
      //             if(CaseData.transportticket.gratuitous){
      //              frmResume.lblgratuitousText.text = "Ja";
      //             }
      if(CaseData.transportticket.gratuitous){
        frmResume.lblTravellerTicketText.text = "Ja";
      }
      if(CaseData.transportticket.dogs != null && CaseData.transportticket.dogs > 0){
        frmResume.lblDogsText.text = CaseData.transportticket.dogs.toString();
      }
      if(CaseData.transportticket.bikes != null && CaseData.transportticket.bikes > 0){
        frmResume.lblBikesText.text = CaseData.transportticket.bikes.toString();
      }
      if(CaseData.transportticket.railrunner != null && CaseData.transportticket.railrunner > 0){
        frmResume.lblRailrunnerText.text = CaseData.transportticket.railrunner.toString();
      }
      voltmx.print("### frmResume_setData lblOffRetour: " + frmResume.lblOffRetour.text);
      if(Global.vars.buildFor == "OV"){
        voltmx.print("### frmResume_hideFieldsOV (or show field)");
        frmResume_flcfirstClass_setVisibility(false);
        frmResume_flcRetour_setVisibility(false);
        frmResume_flctransitionTwoOne_setVisibility(false);
        frmResume_flcsurtax_setVisibility(false);
        frmResume_flcsurtaxIcd_setVisibility(false);
        frmResume_flcgratuitous_setVisibility(false);
        frmResume_flcDogs_setVisibility(false);
        frmResume_flcRailrunner_setVisibility(false);
        frmResume_flcTravellerTicket_setVisibility(true);
        frmResume_offenderemail_setVisibility(true);
        frmResume_offendertelephone_setVisibility(true);
        if(CaseData.transportticket.offenderTelephone != null && CaseData.transportticket.offenderTelephone !== ""){
          frmResume.offendertelephone.txtInputText.text = CaseData.transportticket.offenderTelephone;
        }
        if(CaseData.transportticket.offenderEmail != null && CaseData.transportticket.offenderEmail !== ""){
          frmResume.offenderemail.txtInputText.text = CaseData.transportticket.offenderEmail;
        }
      }else{
        frmResume_offenderemail_setVisibility(false);
        frmResume_offendertelephone_setVisibility(false);
      }
    }else{
      frmResume_transportticket_setVisibility(false);
    }
  }
  voltmx.print("### frmResume_setData CaseData.caseinfo.secondOfficerName: " + CaseData.caseinfo.secondOfficerName);
  if(CaseData.caseinfo.secondOfficerName != null && CaseData.caseinfo.secondOfficerName !== ""){
    frmResume.secondofficer.lblText.text = CaseData.caseinfo.secondOfficerName;
    frmResume.secondofficer.lblText.skin = lblFieldInfo;
  }else{
    frmResume.secondofficer.lblText.text = voltmx.i18n.getLocalizedString("l_secondOfficer");
    frmResume.secondofficer.lblText.skin = lblFieldNotFilled;
  }
  if(CaseData.caseinfo.thirdOfficerName != null && CaseData.caseinfo.thirdOfficerName !== ""){
    frmResume.thirdofficer.lblText.text = CaseData.caseinfo.thirdOfficerName;
    frmResume.thirdofficer.lblText.skin = lblFieldInfo;
  }else{
    if (CaseData.caseinfo.caseTypeCategory == "dpv"){
    	frmResume.thirdofficer.lblText.text = voltmx.i18n.getLocalizedString("l_assessor");
    } else {
    	frmResume.thirdofficer.lblText.text = voltmx.i18n.getLocalizedString("l_thirdOfficer");
    }
    frmResume.thirdofficer.lblText.skin = lblFieldNotFilled;
  }
  if(CaseData.caseinfo.relatedExternalId != null && CaseData.caseinfo.relatedExternalId !== ""){
    frmResume.relatedcase.lblText.text = CaseData.caseinfo.relatedExternalId.toString();
    frmResume.relatedcase.lblText.skin = lblFieldInfo;
  }else{
    frmResume.relatedcase.lblText.text = voltmx.i18n.getLocalizedString("l_relatedcase");
    frmResume.relatedcase.lblText.skin = lblFieldNotFilled;
  }
  voltmx.print("### frmResume_setData Global.vars.handleCharacteristicType: " + Global.vars.handleCharacteristicType);
  frmResume_observation_setVisibility(true);
  frmResume_findings_setVisibility(false);
  voltmx.print("### frmResume_setData Global.vars.appMode: " + Global.vars.appMode);
  if(Global.vars.handleCharacteristicType != "OutcomeType" && Global.vars.handleCharacteristicType != "OutcomeTypeCategory" && Global.vars.buildFor !== "NS" && Global.vars.buildFor !== "OV"){
    if((activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi") && CaseData.person[Global.vars.gCasePersonsIndex] === undefined){
      voltmx.print("### frmResume_setData offence and do not show person");
      frmResume_person_setVisibility(false);
    }else{
      voltmx.print("### frmResume_setData offence and show person");
      voltmx.print("### frmResume_setData offence and show person Global.vars.setPersonOnResume: " + Global.vars.setPersonOnResume);
      if (CaseData.caseinfo.caseTypeCategory == "dpv"){
        frmResume_person_setVisibility(false);
      } else {
        frmResume_person_setVisibility(Global.vars.setPersonOnResume);
      }
    }
    //check for Vehicle details
    voltmx.print("### frmResume_setData show vehicle edited: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].edited);
    voltmx.print("### frmResume_setData show vehicle vehicleType: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType);
    voltmx.print("### frmResume_setData show vehicle ident: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType);
    voltmx.print("### frmResume_setData show vehicle: " + JSON.stringify(CaseData.vehicle[Global.vars.gCaseVehiclesIndex]));
    if (CaseData.caseinfo.caseTypeCategory !== "dpv"){
      if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vehicle && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null){
        voltmx.print("### frmResume_setData show vehicle");
        frmResume_flcVehicleInfo_setVisibility(true);
        frmResume_licenseplate_flcVehicleCountry_setVisibility(true);
        frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfoLicenseplate;
        frmResume_brand_setVisibility(true);
        frmResume.time.top = 12 + 'dp';
      } else if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vehicle && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType === null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber != null && ((CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc !== "") || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc === null || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc === "" || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc == voltmx.i18n.getLocalizedString("l_brand"))){
        voltmx.print("### frmResume_setData show vehicle fill brand");
        frmResume_flcVehicleInfo_setVisibility(true);
        frmResume_licenseplate_flcVehicleCountry_setVisibility(true);
        frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfoLicenseplate;
        frmResume_brand_setVisibility(true);
        frmResume.time.top = 12 + 'dp';
      } else if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] != null && (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vessel || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.commercialShip || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.recreationalBoat) && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null){
        voltmx.print("### frmResume_setData show vehicle fill brand with identification");
        frmResume_flcVehicleInfo_setVisibility(true);
        frmResume_licenseplate_flcVehicleCountry_setVisibility(true);
        if(frmResume.licenseplate.lblLicensePlate.text != null && frmResume.licenseplate.lblLicensePlate.text.length > 18){
          frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfoSmall;
        }else{
          frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfo;
        }
        if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber != null){
          frmResume_brand_setVisibility(true);
        } else {
          frmResume_brand_setVisibility(false);
        }
        frmResume.time.top = 12 + 'dp';
      } else if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType != vehicleIdentType.vehicle && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber != null){
        frmResume_flcVehicleInfo_setVisibility(true);
        frmResume_licenseplate_flcVehicleCountry_setVisibility(false);
        if(frmResume.licenseplate.lblLicensePlate.text != null && frmResume.licenseplate.lblLicensePlate.text.length > 18){
          frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfoSmall;
        }else{
          frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfo;
        }
        if(frmResume.brand.lblText.text !== ""){
          frmResume_brand_setVisibility(true);
        } else {
          frmResume_brand_setVisibility(false);
        }
        frmResume.time.top = 12 + 'dp';
      }else{
        voltmx.print("### frmResume_setData hide vehiclebrand");
        //frmResume.licenseplate.width = '48%';
        frmResume_flcVehicleInfo_setVisibility(false);
        frmResume_brand_setVisibility(false);
        frmResume.time.top = 0 + 'dp';
      }  
    }else{
      voltmx.print("### frmResume_setData hide vehiclebrand");
      //frmResume.licenseplate.width = '48%';
      frmResume_flcVehicleInfo_setVisibility(false);
      frmResume_brand_setVisibility(false);
      frmResume.time.top = 0 + 'dp';
    }  
    frmResume_flcStatusLayout_setVisibility(false);
    if(Utility_stringToBoolean(Global.vars.UVB.UVB) === true && showtransportTicket === true){
      frmResume_flcOffenceSanction_sanctioninfo_setVisibility(false);
      frmResume.flcOffenceSanction.offenceinfo.width = "100%";
    }else{
      frmResume_flcOffenceSanction_sanctioninfo_setVisibility(true);
      frmResume.flcOffenceSanction.offenceinfo.width = "48%";
    }
    if((Global.vars.buildFor == "GEN" || Global.vars.buildFor == "OV") && Global.vars.appMode != voltmx.i18n.getLocalizedString("l_followUp")){
      frmResume_tickettype_setVisibility(true);
      frmResume_populateTicketType();
    } else if (Global.vars.buildFor == "NS"){
      if (CaseData.caseinfo.caseType == "TICKET_M"){
        frmResume_tickettype_setVisibility(true);
        frmResume_populateTicketType();
      } else {
        frmResume_tickettype_setVisibility(false);
      }
    }else if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_followUp")){
      var _unhandledTaskTypes = Utility_getIndexIfObjWithAttr(Global.vars.unhandledTaskTypes,"taskType",CaseData.processinfo.activeTaskType);
      if (_unhandledTaskTypes == -1){
        frmResume_tickettype_setVisibility(true);
        frmResume_populateTicketType();
      }
    }
    if(CaseData.offence.optionUsage != null){
      try{
        showOptions = CaseData.offence.optionUsage.split(',')[1];
      }catch(err){
        showOptions = 0;
      }
    }
    voltmx.print("### frmResume_setData showOptions A: " + showOptions);
    showOptions = Number(showOptions);
    if(showOptions > 0){
      voltmx.print("### frmResume_setData CaseData.option: " + JSON.stringify(CaseData.option));
      Utility_setOptionVariablesGlobals();
//       if(CaseData.option !== undefined && CaseData.option.length > 0){
//         for(var a in CaseData.option){
//           var b = CaseData.option[a];
//           b = Utility_transformKeys(b);
//           voltmx.print("### frmResume_setData CaseData option b: " + JSON.stringify(b));
//           if(b.offencecode == CaseData.offence.offenceCode){
//             Global.vars.selectedOption = b;
//             Global.vars.optionvariablesSet = true;
//            // if((activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi")){
//               voltmx.print("### frmResume_setData fill Global.vars.optionvariablesText 1");
//               Global.vars.optionvariablesText = b.filledText;
//            // }
//           }
//         }
//       }
      frmResume_option_setVisibility(true);
      if(Global.vars.optionvariablesSet === true){
        frmResume.option.lblText.text = Global.vars.selectedOption.optiondescription;
        if(frmResume.option.lblText.text != null && frmResume.option.lblText.text.length > 24){
          frmResume.option.lblText.skin = lblFieldInfoSmall;
        }else{
          frmResume.option.lblText.skin = lblFieldInfo;
        }
      }else{
        frmResume.option.lblText.text = voltmx.i18n.getLocalizedString("l_optionsVariables");
        frmResume.option.lblText.skin = lblFieldNotFilled;
      }
    }else{
      frmResume_option_setVisibility(false);
    }
  }else if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer"){
    voltmx.print("### frmResume_setData appMode register");
    //check for person details
    if(CaseData.person[Global.vars.gCasePersonsIndex] !== undefined && (CaseData.person[Global.vars.gCasePersonsIndex].edited === true || 
                                                                        CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod" || CaseData.processinfo.activeTaskType == "OverdrachtPolitie" || 
                                                                        CaseData.processinfo.activeTaskType == "CorrigerenKandidaat")){
      voltmx.print("### frmResume_setData show person");
      if (CaseData.caseinfo.caseTypeCategory == "dpv"){
        frmResume_person_setVisibility(false);
      } else {
        frmResume_person_setVisibility(true);
      }
      if(Global.vars.handleCharacteristicType != "OutcomeType" && Global.vars.handleCharacteristicType != "OutcomeTypeCategory"){
        if(Global.vars.useDemo === true || Global.vars.buildFor == "OV"){
          frmResume_tickettype_setVisibility(true);
          frmResume_populateTicketType();
        } else if (Global.vars.buildFor == "NS"){
          if (CaseData.caseinfo.caseType == "TICKET_M"){
            frmResume_tickettype_setVisibility(true);
            frmResume_populateTicketType();
          } else {
            frmResume_tickettype_setVisibility(false);
          }
        }
      }else{
        frmResume_statement_setVisibility(false);
        frmResume_tickettype_setVisibility(false);
      }
    }else{
      voltmx.print("### frmResume_setData hide person");
      frmResume_person_setVisibility(false);
      frmResume_statement_setVisibility(false);
    }
    //check for Vehicle details
    if (CaseData.caseinfo.caseTypeCategory !== "dpv"){
      if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined){
        if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined){
          voltmx.print("### frmResume_setData 2 show vehicle edited: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].edited);
          voltmx.print("### frmResume_setData 2 show vehicle vehicleType: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType);
          voltmx.print("### frmResume_setData 2 show vehicle identType: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType);
        }
        if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vehicle && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null){
          voltmx.print("### frmResume_setData show vehicle");
          frmResume_flcVehicleInfo_setVisibility(true);
          frmResume_brand_setVisibility(true);
          frmResume.time.top = 12 + 'dp';
        } else if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] != null && (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vessel || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.commercialShip || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.recreationalBoat) && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null){
          voltmx.print("### frmResume_setData show vehicle fill brand with identification");
          frmResume_flcVehicleInfo_setVisibility(true);
          frmResume_licenseplate_flcVehicleCountry_setVisibility(true);
          frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfoLicenseplate;
          if(frmResume.licenseplate.lblLicensePlate.text != null && frmResume.licenseplate.lblLicensePlate.text.length > 18){
            frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfoSmall;
          }else{
            frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfo;
          }
          if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber != null){
            frmResume_brand_setVisibility(true);
          } else {
            frmResume_brand_setVisibility(false);
          }
          frmResume.time.top = 12 + 'dp';
        }else{
          voltmx.print("### frmResume_setData hide vehiclebrand");
          frmResume_flcVehicleInfo_setVisibility(false);
          frmResume_brand_setVisibility(false);
          frmResume.time.top = 0 + 'dp';
        }  
        if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType == 'PS'){
          voltmx.print("### frmResume_setData show vehicle");
          frmResume_flcVehicleInfo_setVisibility(false);
          voltmx.print("### frmResume_setData hide vehiclebrand");
          frmResume_brand_setVisibility(false);
          frmResume.time.top = 0 + 'dp';
        } 
      } else {
        frmResume_flcVehicleInfo_setVisibility(false);
        frmResume_brand_setVisibility(false);
        frmResume.time.top = 0 + 'dp';

      }
    } else {
      frmResume_flcVehicleInfo_setVisibility(false);
      frmResume_brand_setVisibility(false);
      frmResume.time.top = 0 + 'dp';

    }
   if(Global.vars.handleCharacteristicType == "OutcomeType" || Global.vars.handleCharacteristicType == "OutcomeTypeCategory"){
      voltmx.print("### frmResume_setData taskoutcome for Register");
      frmResume_imgStatusLeft_setVisibility(false);
      frmResume_flcStatusLayout_setVisibility(true);
      frmResume_flcOffenceSanction_setVisibility(false);
      frmResume_tickettype_setVisibility(false);
      //set taskoutcome to status
      frmResume.lblStatus.text = Global.vars.chosenTaskOutcome;
    }else{
      frmResume_flcStatusLayout_setVisibility(false);
      voltmx.print("### frmResume_setData Global.vars.UVB: " + JSON.stringify(Global.vars.UVB));
      voltmx.print("### frmResume_setData showtransportTicket: " + showtransportTicket);
      if(Utility_stringToBoolean(Global.vars.UVB.UVB) === true && showtransportTicket === true){
        frmResume_flcOffenceSanction_sanctioninfo_setVisibility(false);
        frmResume.flcOffenceSanction.offenceinfo.width = "100%";
      }else{
        frmResume_flcOffenceSanction_sanctioninfo_setVisibility(true);
        frmResume.flcOffenceSanction.offenceinfo.width = "48%";
      }
      voltmx.print("### frmResume_setData Global.vars.buildFor: " + Global.vars.buildFor);
      if(Global.vars.useDemo === true || Global.vars.buildFor == "GEN" || Global.vars.buildFor == "OV"){
        frmResume_tickettype_setVisibility(true);
        frmResume_populateTicketType();
      } else if (Global.vars.buildFor == "NS"){
        if (CaseData.caseinfo.caseType == "TICKET_M"){
          frmResume_tickettype_setVisibility(true);
          frmResume_populateTicketType();
        } else {
          frmResume_tickettype_setVisibility(false);
        }
      }
    }
    if(CaseData.offence.optionUsage != null){
      try{
        showOptions = CaseData.offence.optionUsage.split(',')[1];
      }catch(err){
        showOptions = 0;
      }
    }
    voltmx.print("### frmResume_setData showOptions B: " + showOptions);
    showOptions = Number(showOptions);
    if(showOptions > 0){
      voltmx.print("### frmResume_setData CaseData.option 2: " + JSON.stringify(CaseData.option));
      Utility_setOptionVariablesGlobals();
//       if(CaseData.option !== undefined && CaseData.option.length > 0){
//         for(var c in CaseData.option){
//           var d = CaseData.option[c];
//           d = Utility_transformKeys(d);
//           voltmx.print("### frmResume_setData CaseData option d: " + JSON.stringify(d));
//           if(d.offencecode == CaseData.offence.offenceCode){
//             Global.vars.selectedOption = d;
//             Global.vars.optionvariablesSet = true;
//             voltmx.print("### frmResume_setData fill Global.vars.optionvariablesText 2");
//             Global.vars.optionvariablesText = d.filledText;
//           }
//         }
//       }
      frmResume_option_setVisibility(true);
      if(Global.vars.optionvariablesSet === true){
        frmResume.option.lblText.text = Global.vars.selectedOption.optiondescription;
        voltmx.print("### frmResume_setData Global.vars.selectedOption.optiondescription: " + Global.vars.selectedOption.optiondescription);
        if(Global.vars.selectedOption.optiondescription === undefined){
          frmResume.option.lblText.text = Global.vars.selectedOption.optiondescription;
          voltmx.print("### frmResume_setData Global.vars.optionvariablesSet: " + Global.vars.optionvariablesSet);
        }
        if(frmResume.option.lblText.text != null && frmResume.option.lblText.text.length > 24){
          frmResume.option.lblText.skin = lblFieldInfoSmall;
        }else{
          frmResume.option.lblText.skin = lblFieldInfo;
        }
      }else{
        frmResume.option.lblText.text = voltmx.i18n.getLocalizedString("l_optionsVariables");
        frmResume.option.lblText.skin = lblFieldNotFilled;
      }
    }else{
      frmResume_option_setVisibility(false);
    }
  }else{
    //Taskoutcome
    voltmx.print("### frmResume_setData taskoutcome");
    frmResume_person_setVisibility(false);
    frmResume_option_setVisibility(false);
    frmResume_statement_setVisibility(false);
    frmResume_imgStatusLeft_setVisibility(false);
    frmResume_flcStatusLayout_setVisibility(true);
    frmResume_flcOffenceSanction_setVisibility(false);
    //set taskoutcome to status
    frmResume.lblStatus.text = Global.vars.chosenTaskOutcome;
  }
  //See if there is any case text
  //         for (var p=0; ((CaseData.text) != null) && p < CaseData.text.length; p++ ){
  //           var v = CaseData.text[p];
  //           voltmx.print("### frmResume_setData value: " + v.value);
  //           if(v.type == 1){
  //             frmResume.observation.lblText.text = v.value;
  //           } else if(v.type == 4){
  //             frmResume.findings.lblText.text = v.value;
  //           }
  //         }
  //See if there is any case text
  frmResume.observation.lblText.text = "";
  for (var p=0; CaseData.text != null && p < CaseData.text.length; p++){
    var v = CaseData.text[p];
    voltmx.print("##### frmResume_setData value: " + v.value);
    voltmx.print("##### frmResume_setData type: " + v.type);
    if (v.type == 1  && v.value){
      frmResume.observation.lblText.text = v.value;
      break;
    } 
  }
  frmResume.findings.lblText.text = "";
  for (var r=0; CaseData.text != null && r < CaseData.text.length; r++){
    var w = CaseData.text[r];
    voltmx.print("##### frmResume_setData value: " + w.value);
    voltmx.print("##### frmResume_setData type: " + w.type);
    if(w.type == 4 && (w.value != null && w.value.length > 0)){ 
      if (voltmx.string.startsWith(w.value, "Beschrijving document(en): ") === false){//opmerking verbalisant
        frmResume.findings.lblText.text = w.value;
        break;
      }
    } 
  }

  //set taskOutcomeExplanation
  voltmx.print("#### frmResume_setData internalremark Global.vars.taskType: " + Global.vars.taskType);
  //sorteren van de taken jongste boven
//   CaseData.processinfo.tasks.sort(function(a, b) {
//     return (a.taskCreatedOn===null)-(b.taskCreatedOn===null) || -(a.taskCreatedOn>b.taskCreatedOn)||+(a.taskCreatedOn<b.taskCreatedOn);
//   });
  frmResume.internalremark.lblText.text = "";
  for(var j = CaseData.processinfo.tasks.length; j--;){
    var x = CaseData.processinfo.tasks[j];
    if(x.taskType == Global.vars.taskType){
      voltmx.print("#### frmResume_setData internalremark found task");
      if(x.taskOutcomeExplanation != null){
        frmResume.internalremark.lblText.text = x.taskOutcomeExplanation;
      }else{
        frmResume.internalremark.lblText.text = "";
      }
      break;
    }
  }
  //set fields based on caseType
  voltmx.print("#### frmResume_setData questionTypes: " + JSON.stringify(Global.vars.questionTypes));
  voltmx.print("#### frmResume_setData questionTypes #" + Global.vars.questionTypes.length);
  voltmx.print("#### frmResume_setData Global.vars.questionTypeChooseEnabled: " + Global.vars.questionTypeChooseEnabled);
  voltmx.print("#### frmResume_setData Global.vars.questionTypesUsage: " + Global.vars.questionTypesUsage);
  voltmx.print("#### frmResume_setData caseType: " + CaseData.caseinfo.caseType);
  if(CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("CTE_") || CaseData.caseinfo.caseType.startsWith("MLD_"))){
    //frmResume_enforcementobject_setVisibility(true); RKA
    var questions = false;
    if (CaseData.questions !== undefined && CaseData.questions != null && CaseData.questions.length > 0){
    	questions = true;
    }      
    if(CaseData.caseinfo.caseType != null && (Global.vars.questionTypesUsage !== "" || (Global.vars.questionTypes != null && Global.vars.questionTypeChooseEnabled === false && Global.vars.questionTypes.length == 1))){
      frmResume_questions_setVisibility(true);
      frmResume_questionsSet(Global.vars.QuestionsSet);
    } else {
      frmResume_questions_setVisibility(false);
    }
    frmResume_tickettype_setVisibility(false);
    frmResume_flcOffenceSanction_setVisibility(false);
    if (Utility_isExtendReportCaseTypeCategory(CaseData.caseinfo.caseTypeCategory) === false){
      frmResume_statement_setVisibility(false);
    }
  }else{
    //frmResume_enforcementobject_setVisibility(false); RKA
    frmResume_questions_setVisibility(false);         	
  }
  if(CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("DVV_") || CaseData.caseinfo.caseType.startsWith("DRV_") || CaseData.caseinfo.caseType.startsWith("RV_") || 
      CaseData.caseinfo.caseType.startsWith("VV_") || CaseData.caseinfo.caseType.startsWith("KRV_") || CaseData.caseinfo.caseType.startsWith("KVV_") || 
      CaseData.caseinfo.caseType.startsWith("SRV_") || CaseData.caseinfo.caseType.startsWith("SVV_")) && CaseData.processinfo.activeTaskType != "OverdrachtPolitie"){
    frmResume_prohibitionhanded_setVisibility(true);
    if(CaseData.caseinfo.caseType.startsWith("DVV_") || CaseData.caseinfo.caseType.startsWith("DRV_") || CaseData.processinfo.activeTaskType == "VersturenVerbod"){
      //CaseData.prohibitions.prohibitionHanded = true;
      frmResume.prohibitionhanded.setEnabled(true);
    }else if(CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressType == addressType.emptyDataGBA.value || CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressType == addressType.withoutFixedStay.value || CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressType == addressType.departedDestinationUnknown.value){
      //persoon heeft geen adres dus gelijk overhandigen
      alert("Let op: deze persoon heeft geen adres of een geheim adres, u dient het verbod gelijk te overhandigen");
      CaseData.prohibitions.prohibitionHanded = true;
      frmResume.prohibitionhanded.setEnabled(false);
    }else{
      frmResume.prohibitionhanded.setEnabled(true);
    }
    if(CaseData.prohibitions !== undefined && CaseData.prohibitions.prohibitionHanded !== undefined){
      if(CaseData.prohibitions.prohibitionHanded === null){
        frmResume.prohibitionhanded.segControlBtnNo.skin = subHeaderSegControlBtnNotSelectedNormal;
        frmResume.prohibitionhanded.segControlBtnYes.skin = subHeaderSegControlBtnNotSelectedNormal;
      }else if(CaseData.prohibitions.prohibitionHanded === false){
        frmResume.prohibitionhanded.segControlBtnYes.skin = subHeaderSegControlBtnNotSelectedNormal;
        frmResume.prohibitionhanded.segControlBtnNo.skin=  subHeaderSegControlBtnSelectedNormal;
      }else if(CaseData.prohibitions.prohibitionHanded === true){
        frmResume.prohibitionhanded.segControlBtnYes.skin = subHeaderSegControlBtnSelectedNormal;
        frmResume.prohibitionhanded.segControlBtnNo.skin=  subHeaderSegControlBtnNotSelectedNormal;
      }
    }else{
      //veld niet aanwezig dus zet maar op niet selected
      frmResume.prohibitionhanded.segControlBtnNo.skin = subHeaderSegControlBtnNotSelectedNormal;
      frmResume.prohibitionhanded.segControlBtnYes.skin = subHeaderSegControlBtnNotSelectedNormal;
    }
    if(CaseData.processinfo.activeTaskType == "VersturenVerbod"){
      //set Adress aan
      frmResume_showPersonAdress();
    }
  }else{
    frmResume_prohibitionhanded_setVisibility(false);
  }
  if(CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("DVV_") || CaseData.caseinfo.caseType.startsWith("DRV_") || 
                                             CaseData.caseinfo.caseType.startsWith("VV_") || CaseData.caseinfo.caseType.startsWith("RV_") || 
                                             CaseData.caseinfo.caseType.startsWith("KVV_") || CaseData.caseinfo.caseType.startsWith("KRV_") || 
                                             CaseData.caseinfo.caseType.startsWith("SVV_") || CaseData.caseinfo.caseType.startsWith("SRV_"))){
    frmResume_observation_setVisibility(false);
    frmResume_secondofficer_setVisibility(false);
    frmResume_thirdofficer_setVisibility(false);
    frmResume_flcOffenceSanction_setVisibility(false);
    frmResume_internalremark_setVisibility(true);
    frmResume_statement_setVisibility(false); //wordt bij set persondetails geinit als true dus hoeft niet in else
    //turn on prohibitions
    frmResume_flcProhibition_setVisibility(true);
    frmResume_flcSignature_setVisibility(true);
    if(CaseData.prohibitions.signature !== undefined && CaseData.prohibitions.signature != null && CaseData.prohibitions.signature !== ""){
      frmResume.imgSignature.base64 = CaseData.prohibitions.signature;
    }
    if(activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "OverdrachtPolitie"){
      frmResume.btnShowSignature.setEnabled(false);
    }else{
      frmResume.btnShowSignature.setEnabled(true);
    }
    if(CaseData.caseinfo.caseType != null && (CaseData.processinfo.activeTaskType == "CorrigerenKandidaat" || CaseData.processinfo.activeTaskType == "CorrigerenVerbod" || 
                                               CaseData.processinfo.activeTaskType == "VersturenVerbod" || CaseData.processinfo.activeTaskType == "OverdrachtPolitie" || 
       CaseData.caseinfo.caseType.startsWith("DVV_") || CaseData.caseinfo.caseType.startsWith("DRV_") || CaseData.caseinfo.caseType.startsWith("VV_") || CaseData.caseinfo.caseType.startsWith("RV_"))){
      //zet locatie uit
      frmResume_furtherlocationindication_setVisibility(false);
      frmResume_locationspecification_setVisibility(false);
      frmResume_locationdescription_setVisibility(false);
      frmResume_location_setVisibility(false);
      frmResume_mapdisplay_setVisibility(false);
      frmResume_time_setVisibility(false);
      frmResume.flcLayoutInput.top = 0 + 'dp';
    }else{
      frmResume_furtherlocationindication_setVisibility(true);
      frmResume_locationspecification_setVisibility(true);
      if(CaseData.location.chosenFurtherIndicationKey == furtherIndicationType.others){
        if (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV"){
          frmResume_furtherlocationindication_setVisibility(true);
        } else {
          frmResume_furtherlocationindication_setVisibility(false);
        }
      }
      frmResume_location_setVisibility(true);
      frmResume_mapdisplay_setVisibility(true);
      frmResume_time_setVisibility(true);
      //#ifdef android
  		frmResume.flcLayoutInput.top = 98 + 'dp';
      //#endif
    }
    if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie"){
      frmResume_policeofficeremail_setVisibility(true);
      frmResume.policeofficeremail.txtInputText.text = "";
      frmResume_policeofficermobile_setVisibility(true);
      frmResume.policeofficermobile.txtInputText.text = "";
      frmResume_flcButShowPDF_setVisibility(true);
      frmResume_flcSignature_setVisibility(false);
      voltmx.print("### frmResume_setData CaseData.prohibitions.policeOfficerEmail: " + CaseData.prohibitions.policeOfficerEmail);
      voltmx.print("### frmResume_setData CaseData.prohibitions.policeOfficerMobile: " + CaseData.prohibitions.policeOfficerMobile);
      if(CaseData.prohibitions.policeOfficerEmail !== undefined && CaseData.prohibitions.policeOfficerEmail != null && CaseData.prohibitions.policeOfficerEmail !== "" && 
         Global.vars.previousForm != "frmOpenTasks" && Global.vars.prohibitionsFillPoliceOfficerEmailFromCase === true){
        voltmx.print("### frmResume_setData CaseData.prohibitions.policeOfficerEmail 1");
        frmResume.policeofficeremail.txtInputText.text = CaseData.prohibitions.policeOfficerEmail;
      }
      if(CaseData.prohibitions.policeOfficerMobile !== undefined && CaseData.prohibitions.policeOfficerMobile != null && CaseData.prohibitions.policeOfficerMobile !== "" && 
         Global.vars.previousForm != "frmOpenTasks" && Global.vars.prohibitionsFillPoliceOfficerMobileFromCase === true){
        voltmx.print("### frmResume_setData CaseData.prohibitions.policeOfficerMobile 1");
        frmResume.policeofficermobile.txtInputText.text = CaseData.prohibitions.policeOfficerMobile;
      }
      var _notMandatoryForOutcome = -1;
      if(Global.vars.selectedOutcome !== undefined && 
         Global.vars.selectedOutcome != null && Global.vars.selectedOutcome.identification !== undefined && 
         Global.vars.selectedOutcome.identification != null){
        _notMandatoryForOutcome = Utility_getIndexIfObjWithAttr(Global.vars.notMandatoryForOutcome,"outcome",Global.vars.selectedOutcome.identification);
        if (_notMandatoryForOutcome !== -1){
          voltmx.print("### frmResume_setData Global.vars.selectedOutcome.identification: " + Global.vars.selectedOutcome.identification);
          frmResume.policeofficeremail.txtInputText.text = "";
          frmResume.policeofficermobile.txtInputText.text = "";
        }
      }
    }
    frmResume.lblProhibitionsHeader.text = Utility_determinKindOfProhibition(CaseData.caseinfo.caseType);
    frmResume_lblCaseTypeCategoryHeader_setVisibility(false);
    frmResume_lblCaseTypeCategory_setVisibility(false);
    //
    frmResume.lblCaseType.text = CaseData.prohibitions.caseTypeDescription;
    var caseType = CaseData.caseinfo.caseType.split("_");
    var caseTypePrefix = caseType[0];
    if(caseTypePrefix.startsWith("D") && CaseData.processinfo.activeTaskType != "OverdrachtPolitie"){          
      frmResume_lblCaseTypeHeader_setVisibility(true);
      frmResume_lblCaseType_setVisibility(true);
    }else{
      frmResume_lblCaseTypeHeader_setVisibility(false);
      frmResume_lblCaseType_setVisibility(false);
    }
    frmResume.lblProhibitionTimeFrom.text = Utility_getLocalizedDateTimeString(new Date(CaseData.prohibitions.dateFrom), false);
    frmResume.lblProhibitionTimeTo.text = Utility_getLocalizedDateTimeString(new Date(CaseData.prohibitions.dateTo), false);
    var prohibitionStations = "";
    if(CaseData.prohibitions.station1 !== undefined && CaseData.prohibitions.station1 != null){
      prohibitionStations = CaseData.prohibitions.station1;
    }
    if(CaseData.prohibitions.station2 !== undefined && CaseData.prohibitions.station2 != null){
      prohibitionStations = prohibitionStations + "<br>" + CaseData.prohibitions.station2;
    }
    if(CaseData.prohibitions.station3 !== undefined && CaseData.prohibitions.station3 != null){
      prohibitionStations = prohibitionStations + "<br>" + CaseData.prohibitions.station3;
    }
    frmResume.rtxStations.text = prohibitionStations;
  }else{
    frmResume_flcProhibition_setVisibility(false);
    frmResume_flcSignature_setVisibility(false);
    frmResume_time_setVisibility(true);
    if(CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("CONTROL_K") || (CaseData.caseinfo.caseType.startsWith("CTE_") || CaseData.caseinfo.caseType.startsWith("MLD_")))){
      frmResume_flcOffenceSanction_setVisibility(false);
    } else {
      if(CaseData.offence.offenceCode != null && CaseData.offence.offenceCode != ""){
        frmResume_flcOffenceSanction_setVisibility(true);
      }
    }
    if(CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("CONTROL_K")){
      frmResume_flcTimeArrivedClampNumber_setVisibility(true);
      if(CaseData.caseinfo.clampNumber != null && CaseData.caseinfo.clampNumber !== ""){
        frmResume.lblClampNumber.text = CaseData.caseinfo.clampNumber;
        frmResume.lblClampNumber.skin = lblFieldInfo;
      }else{
        frmResume.lblClampNumber.text = voltmx.i18n.getLocalizedString("l_nr");
        frmResume.lblClampNumber.skin = lblFieldNotFilled;
      }
      if(CaseData.status == Global.vars.unClampRequestStatus){
        frmResume.btnClampNumber.setEnabled(false);
      }else{
        frmResume.btnClampNumber.setEnabled(true);
      }
    } else {
      frmResume_flcTimeArrivedClampNumber_setVisibility(false);
      frmResume.lblClampNumber.text = voltmx.i18n.getLocalizedString("l_nr");
      frmResume.lblClampNumber.skin = lblFieldNotFilled;
    }
    if (CaseData.caseinfo.caseTypeCategory == "dpv"){
      frmResume_secondofficer_setVisibility(true);
    } else {
      frmResume_secondofficer_setVisibility(true);
    }
    //frmResume_internalremark_setVisibility(false);
  }
  //clear multimedia
  var numberExtraPhotos = 0;
  for (var q=0; ((CaseData.multimedia) != null) && q < CaseData.multimedia.length; q++ ){
    voltmx.print("### frmResume_setData CaseData.multimedia: " + JSON.stringify(CaseData.multimedia));
    var z = CaseData.multimedia[q];
    if((z.contentType == 'image/png' || z.contentType == 'image/jpeg') && z.documentType != "SmallMap"){
      numberExtraPhotos = numberExtraPhotos + 1;
    }
  }
  voltmx.print("### frmResume_setData numberExtraPhotos: " + numberExtraPhotos);
  voltmx.print("### frmResume_setData Global.vars.showPhotosANPRLoaded: " + Global.vars.showPhotosANPRLoaded);
  if(numberExtraPhotos === 0 && Global.vars.showPhotosANPRLoaded === false){
    frmResume_clearMultimedia();
  }else{
    Global.vars.previousForm = "frmResume";
    frmPhotos_setPhotosOnForm();
  }
  Global.vars.previousForm = "frmResume";
  frmResume_checkOption();
  frmResume_checkForSignatureImage(); //frmResume_validateShowFooter already called inside frmResume_checkForSignatureImage
}

function frmResume_validateShowFooter(){
  var showFooter = true;
  var mandatoryOptions = 0;
  var numberExtraPhotos = 0;
  var documentType = "ExtraPhoto";
  for (var p=0; ((CaseData.multimedia) != null) && p < CaseData.multimedia.length; p++ ){
    var v = CaseData.multimedia[p];
    if((v.documentType == documentType)){
      if(CaseData.caseinfo.caseType == "CONTROL_K"){
        if (v.fileName != null && voltmx.string.startsWith(v.fileName, CaseData.processinfo.lastTaskProcessed.taskType) === true){
          numberExtraPhotos = numberExtraPhotos + 1;
        }
      } else {
        numberExtraPhotos = numberExtraPhotos + 1;
      }
    }
  }
  var minimumNumberOfPhotos = Global.vars.minimumNumberOfPhotos;
  var maximumNumberOfPhotos = Global.vars.maximumNumberOfPhotos;
  var validateNumberOfPhotos = false;
  for (var i = 0; i < Global.vars.validatePhotosForCaseType.length; i++) {
  	var w =  Global.vars.validatePhotosForCaseType[i];
    voltmx.print("### frmResume_validateShowFooter caseType: " + w);
    if (w === CaseData.caseinfo.caseType){
      validateNumberOfPhotos = true;
      break;
    } 
  }  
  var _mandatoryForOutcome = -1;
  var validateMaxNumberOfPhotos = false;
  if(CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("CONTROL_K")){
    if (Global.vars.selectedOutcome != null){
      _mandatoryForOutcome = Utility_getIndexIfObjWithAttr(Global.vars.mandatoryForOutcome,"outcome",Global.vars.selectedOutcome.identification);
      if (_mandatoryForOutcome == -1){
        validateNumberOfPhotos = false;
        validateMaxNumberOfPhotos = true;
      }
    } else {
      validateNumberOfPhotos = false;
    }
  }
  var _indRequired = false;
  if (Global.vars.questionTypes != null && Global.vars.questionTypeChooseEnabled === false && Global.vars.questionTypes.length === 1) {
    Utility_questionsRequired(Global.vars.questionTypes[0].option_id, function(result) {
      _indRequired = result;
      voltmx.print("### frmResume_validateShowFooter Global.vars.questionTypes required: " + _indRequired);
    });
  } else {
    voltmx.print("### frmResume_validateShowFooter Global.vars.questionTypes required: " + _indRequired);
  }
  voltmx.print("### frmResume_validateShowFooter validateNumberOfPhotos: " + validateNumberOfPhotos);
  voltmx.print("### frmResume_validateShowFooter validateMaxNumberOfPhotos: " + validateMaxNumberOfPhotos);
  if(CaseData.offence.optionUsage != null){
     try{
       mandatoryOptions = CaseData.offence.optionUsage.split(',')[0];
     }catch(err){
       mandatoryOptions = 0;
     }
  }
  voltmx.print("### frmResume_validateShowFooter mandatoryOptions: " + mandatoryOptions);
  mandatoryOptions = Number(mandatoryOptions);
  //check alerts
  voltmx.print("### frmResume_validateShowFooter Global.vars.optionvariablesSet: " + Global.vars.optionvariablesSet);
  if(Global.vars.optionvariablesSet === false && frmResume.option.lblText.text !== "" && frmResume.option.lblText.text != null && frmResume.option.lblText.text !== voltmx.i18n.getLocalizedString("l_optionsVariables")){
    Global.vars.optionvariablesSet = true;
  }
  if(mandatoryOptions > 0 && Global.vars.optionvariablesSet === false && CaseData.caseinfo.enforcementLevel != "W"){
    voltmx.print("### frmResume_validateShowFooter: Het is verplicht de redenen van wetenschap in te vullen");
    //Reden van wetenschap - option
    frmResume.option.skin = flcFieldEdgeRed;
    showFooter = false;
  }else{
    frmResume.option.skin = flcFieldEdge;
  } 
  if(Global.vars.QuestionsSet === false && frmResume.questions.lblText.text !== "" && frmResume.questions.lblText.text != null && frmResume.questions.lblText.text !== "Vragen" && frmResume.enforcementobject.lblText.text !== voltmx.i18n.getLocalizedString("l_enforcementObject")){
    Global.vars.QuestionsSet = true;
  }
  voltmx.print("### frmResume_validateShowFooter Global.vars.QuestionsSet: " + Global.vars.QuestionsSet);
  voltmx.print("### frmResume_validateShowFooter Global.vars.questionsClearedOnResume: " + Global.vars.questionsClearedOnResume);
  if ((Global.vars.QuestionsSet === false || Global.vars.questionsClearedOnResume === true) && Utility_questionsMandatory(Global.vars.questionTypesUsage) === true){
    voltmx.print("### frmResume_validateShowFooter: Het is verplicht vragen in te vullen");
	frmResume.questions.skin = flcFieldEdgeRed;
    showFooter = false;
  } else if (Global.vars.QuestionsSet === false && _indRequired === true){
    voltmx.print("### frmResume_validateShowFooter: Het is verplicht vragen in te vullen");
	frmResume.questions.skin = flcFieldEdgeRed;
    showFooter = false;
  } else {
    frmResume.questions.skin = flcFieldEdge;
  }
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense == "F" && Global.vars.regionCodeEnabled === true && Global.vars.handleCharacteristicType != "OutcomeType" && Global.vars.handleCharacteristicType != "OutcomeTypeCategory" && (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate === null || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate === "")){
    voltmx.print("### frmResume_validateShowFooter:" + voltmx.i18n.getLocalizedString("l_mandatoryRegionCodeFrance"));
    //regionCode
    showFooter = false;
    frmResume.regioncode.skin = flcFieldEdgeRed;
  }else{
    frmResume.regioncode.skin = flcFieldEdge;
  } 
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vehicle && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber != null && (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc === null || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc === "" || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc == voltmx.i18n.getLocalizedString("l_brand")) && Global.vars.handleCharacteristicType != "OutcomeType" && Global.vars.handleCharacteristicType != "OutcomeTypeCategory"){
    voltmx.print("### frmResume_validateShowFooter:" + voltmx.i18n.getLocalizedString("l_mandatoryBrand"));
    //brand
    showFooter = false;
    frmResume.brand.skin = flcFieldEdgeRed;
  }else{
    frmResume.brand.skin = flcFieldEdge;
  } 
  if (frmResume.internalremark.lblText.text === "" && Global.vars.indCommentrequired === true && frmResume.internalremark.isVisible === true){
    voltmx.print("### frmResume_validateShowFooter: internal remark");
    showFooter = false;
    frmResume.internalremark.skin =  flcFieldEdgeRed;
  } else {
  	frmResume.internalremark.skin =  flcFieldEdge;
  }

  voltmx.print("### frmResume_validateShowFooter: clampnumber request status: " + CaseData.status);
  var _noCautionPersonCaseType = Utility_getIndexIfObjWithAttr(Global.vars.noCautionPersonCaseType,"caseType",CaseData.caseinfo.caseType);
  voltmx.print("### frmResume_validateShowFooter: _noCautionPersonCaseType: " + _noCautionPersonCaseType);
  if(CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("CONTROL_K") && (CaseData.status === Global.vars.clampRequestStatus && _mandatoryForOutcome !== -1) ){
    if (frmResume.lblClampNumber.text === voltmx.i18n.getLocalizedString("l_nr") || CaseData.caseinfo.clampNumber === ""){
      voltmx.print("### frmResume_validateShowFooter: clampnumber");
      showFooter = false;
      frmResume.flcClampNumber.skin =  flcFieldEdgeRed;
    } else {
      frmResume.flcClampNumber.skin =  flcFieldEdge;
    } 
  } else {
    frmResume.flcClampNumber.skin =  flcFieldEdge;
  }
  if(frmResume.statement.lblText.text === voltmx.i18n.getLocalizedString("l_statement") && CaseData.offence.person === true && CaseData.caseinfo.enforcementLevel != "W" &&
     Global.vars.handleCharacteristicType != "OutcomeType" && Global.vars.handleCharacteristicType != "OutcomeTypeCategory" && 
     CaseData.caseinfo.caseType != null && 
     (CaseData.caseinfo.caseType.startsWith("CTE_") === false) && (CaseData.caseinfo.caseType.startsWith("MLD_") === false) && 
     (CaseData.caseinfo.caseType.startsWith("DVV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("DRV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("RV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("VV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("KRV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("KVV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("SRV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("DVV_") === false)){
    voltmx.print("### frmResume_validateShowFooter 1:" + voltmx.i18n.getLocalizedString("l_mandatoryStatement"));
    //statement
    showFooter = false;
    frmResume.statement.skin = flcFieldEdgeRed;
  }else if(frmResume.statement.lblText.text === voltmx.i18n.getLocalizedString("l_statement") && 
           CaseData.offence.person === true && 
           Utility_isExtendReportCaseTypeCategory(CaseData.caseinfo.caseTypeCategory) === true &&
          _noCautionPersonCaseType == -1){
    voltmx.print("### frmResume_validateShowFooter 2:" + voltmx.i18n.getLocalizedString("l_mandatoryStatement"));
    //statement
    showFooter = false;
    frmResume.statement.skin = flcFieldEdgeRed;
  }else if(frmResume.statement.lblText.text !== voltmx.i18n.getLocalizedString("l_statement") && CaseData.offence.person === true && CaseData.caseinfo.enforcementLevel != "W" &&
     Global.vars.handleCharacteristicType != "OutcomeType" && Global.vars.handleCharacteristicType != "OutcomeTypeCategory" && 
     CaseData.caseinfo.caseType != null && 
     (CaseData.caseinfo.caseType.startsWith("CTE_") === false) && (CaseData.caseinfo.caseType.startsWith("MLD_") === false) && 
     (CaseData.caseinfo.caseType.startsWith("DVV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("DRV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("RV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("VV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("KRV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("KVV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("SRV_") === false) &&
	(CaseData.caseinfo.caseType.startsWith("DVV_") === false) ){
    voltmx.print("### frmResume_validateShowFooter 3:" + voltmx.i18n.getLocalizedString("l_mandatoryStatement"));
    if (CaseData.offence.legalAssistCommunicated === null && (CaseData.offence.offenceTypeCode != "1" && CaseData.offence.offenceTypeCode != "10"))  {
      //statement check switch from offencetype to 1 or 10 while statement was recorded earlier
      voltmx.print("### frmResume_validateShowFooter 3: statement check switch from offencetype to 1 or 10 while statement was recorded earlier");
      showFooter = false;
      frmResume.statement.skin = flcFieldEdgeRed;
    } else {
      frmResume.statement.skin = flcFieldEdge;
    }

  }else if(frmResume.statement.lblText.text !== voltmx.i18n.getLocalizedString("l_statement") && 
           CaseData.offence.person === true && 
           Utility_isExtendReportCaseTypeCategory(CaseData.caseinfo.caseTypeCategory) === true &&
          _noCautionPersonCaseType == -1){
    voltmx.print("### frmResume_validateShowFooter 4:" + voltmx.i18n.getLocalizedString("l_mandatoryStatement"));
    //statement
    if (CaseData.offence.legalAssistCommunicated === null)  {
      voltmx.print("### frmResume_validateShowFooter 4: statement check");
      showFooter = false;
      frmResume.statement.skin = flcFieldEdgeRed;
    } else {
      frmResume.statement.skin = flcFieldEdge;
    }
  }else {
    frmResume.statement.skin = flcFieldEdge;
  }
  var _notMandatoryForOutcome = -1;
  if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" && 
     Global.vars.selectedOutcome !== undefined && Global.vars.selectedOutcome != null 
     && Global.vars.selectedOutcome.identification !== undefined && Global.vars.selectedOutcome.identification != null){
    _notMandatoryForOutcome = Utility_getIndexIfObjWithAttr(Global.vars.notMandatoryForOutcome,"outcome",Global.vars.selectedOutcome.identification);
  }
  if((frmResume.policeofficeremail.txtInputText.text === null || frmResume.policeofficeremail.txtInputText.text === "" || CaseData.prohibitions.policeOfficerEmail === null || CaseData.prohibitions.policeOfficerEmail === "") && CaseData.processinfo.activeTaskType == "OverdrachtPolitie"){
    voltmx.print("### frmResume_validateShowFooter: policeofficer email is leeg");
    //
    if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" && Global.vars.selectedOutcome !== undefined && 
       Global.vars.selectedOutcome != null && Global.vars.selectedOutcome.identification !== undefined && 
       Global.vars.selectedOutcome.identification != null && _notMandatoryForOutcome !== -1){
      voltmx.print("### frmResume_validateShowFooter: policeofficer email is leeg maar taakuitkomst is nietOverTeDragen");
      //Zet checks uit
      frmResume.policeofficeremail.skin = flcFieldEdge; 
    }else{
      voltmx.print("### frmResume_validateShowFooter: policeofficer email is leeg 2");
      showFooter = false;
      frmResume.policeofficeremail.skin = flcFieldEdgeRed;
    }   
  }else{
  	if(CaseData.prohibitions.policeOfficerEmail != null && voltmx.string.isValidEmail(CaseData.prohibitions.policeOfficerEmail) === false){
      voltmx.print("### frmResume_validateShowFooter: policeofficer geen geldige email");
      showFooter = false;
      frmResume.policeofficeremail.skin = flcFieldEdgeRed;
    }else if(CaseData.prohibitions.policeOfficerEmail != null && voltmx.string.isValidEmail(CaseData.prohibitions.policeOfficerEmail) === true){
      var isEmailInDomain = false;
      voltmx.print("### frmResume_validateShowFooter: CaseData.prohibitions.policeOfficerEmail: " + CaseData.prohibitions.policeOfficerEmail);
      for(var a in Global.vars.prohibitionEmailDomain){
        var domainMail = Global.vars.prohibitionEmailDomain[a].domain;
        if(CaseData.prohibitions.policeOfficerEmail.endsWith("@" + domainMail) === true){
          isEmailInDomain = true;
          break;
        }
      }
      if(frmResume.policeofficeremail.isVisible === true && frmResume.policeofficeremail.txtInputText.text !== CaseData.prohibitions.policeOfficerEmail){
        voltmx.print("### frmResume_validateShowFooter: CaseData.prohibitions.policeOfficerEmail is niet gelijk aan gevuld email veld");
        isEmailInDomain = false;
      }
      if(isEmailInDomain){
        voltmx.print("### frmResume_validateShowFooter: policeofficer email in domain");
        frmResume.policeofficeremail.skin = flcFieldEdge; 
      }else{
        voltmx.print("### frmResume_validateShowFooter: policeofficer email NOT in domain");
        showFooter = false;
      	frmResume.policeofficeremail.skin = flcFieldEdgeRed;
      }
    }else{
      frmResume.policeofficeremail.skin = flcFieldEdge; 
    }
  }
  if((frmResume.policeofficermobile.txtInputText.text === null || frmResume.policeofficermobile.txtInputText.text === "" || CaseData.prohibitions.policeOfficerMobile === null || CaseData.prohibitions.policeOfficerMobile === "") && CaseData.processinfo.activeTaskType == "OverdrachtPolitie"){
    voltmx.print("### frmResume_validateShowFooter: policeoficer mobile is leeg");
    //
    if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" && Global.vars.selectedOutcome !== undefined && 
       Global.vars.selectedOutcome != null && Global.vars.selectedOutcome.identification !== undefined && 
       Global.vars.selectedOutcome.identification != null && _notMandatoryForOutcome !== -1){
      frmResume.policeofficermobile.skin = flcFieldEdge;
    }else{
      showFooter = false;
      frmResume.policeofficermobile.skin = flcFieldEdgeRed;
    }
  }else if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" && (Global.vars.selectedOutcome === undefined || 
       Global.vars.selectedOutcome === null || Global.vars.selectedOutcome.identification === undefined || 
       Global.vars.selectedOutcome.identification === null || _notMandatoryForOutcome == -1)){
        var str = CaseData.prohibitions.policeOfficerMobile;
        var is10Digits = (/^\d{10}$/.test(str));
        if(is10Digits === false){
          voltmx.print("### frmResume_validateShowFooter: policeoficer mobile niet 10 cijfers");
          showFooter = false;
          frmResume.policeofficermobile.skin = flcFieldEdgeRed;
        }else{
          if(str.startsWith("06") === false){
            voltmx.print("### frmResume_validateShowFooter: policeoficer mobile niet 06");
            showFooter = false;
            frmResume.policeofficermobile.skin = flcFieldEdgeRed;
          }else{
            frmResume.policeofficermobile.skin = flcFieldEdge;
          }
        }
  }else{
    voltmx.print("### frmResume_validateShowFooter else");
  }
  if(CaseData.prohibitions !== undefined && CaseData.prohibitions.prohibitionHanded !== undefined && CaseData.prohibitions.prohibitionHanded != null && CaseData.caseinfo.caseType !== undefined && CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("DVV_") || CaseData.caseinfo.caseType.startsWith("DRV_") || CaseData.caseinfo.caseType.startsWith("RV_") || 
           CaseData.caseinfo.caseType.startsWith("VV_") || CaseData.caseinfo.caseType.startsWith("KRV_") || CaseData.caseinfo.caseType.startsWith("KVV_") || 
           CaseData.caseinfo.caseType.startsWith("SRV_") || CaseData.caseinfo.caseType.startsWith("SVV_"))){
    frmResume.prohibitionhanded.skin = flcFieldEdge;
  }else if((CaseData.prohibitions.prohibitionHanded === undefined || CaseData.prohibitions.prohibitionHanded === null) && CaseData.caseinfo.caseType !== undefined && CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("DVV_") || CaseData.caseinfo.caseType.startsWith("DRV_") || CaseData.caseinfo.caseType.startsWith("RV_") || 
           CaseData.caseinfo.caseType.startsWith("VV_") || CaseData.caseinfo.caseType.startsWith("KRV_") || CaseData.caseinfo.caseType.startsWith("KVV_") || 
           CaseData.caseinfo.caseType.startsWith("SRV_") || CaseData.caseinfo.caseType.startsWith("SVV_")) && CaseData.processinfo.activeTaskType != "OverdrachtPolitie"){
    showFooter = false;
    frmResume.prohibitionhanded.skin = flcFieldEdgeRed;
  }
  if(CaseData.caseinfo.caseType !== undefined && CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("DVV_") || CaseData.caseinfo.caseType.startsWith("DRV_") || CaseData.caseinfo.caseType.startsWith("RV_") || CaseData.caseinfo.caseType.startsWith("VV_") || CaseData.caseinfo.caseType.startsWith("KRV_") || CaseData.caseinfo.caseType.startsWith("KVV_") || CaseData.caseinfo.caseType.startsWith("SRV_") || CaseData.caseinfo.caseType.startsWith("SVV_")) && 
     CaseData.processinfo.activeTaskType !== "EditTicket" && CaseData.processinfo.activeTaskType !== "OverdrachtPolitie" && (frmResume.imgSignature.base64 === undefined || frmResume.imgSignature.base64 === null || 
                                                                                                                                        frmResume.imgSignature.base64.length === 0 || frmResume.imgSignature.src == "empty.png" ||
                                                                                                                                        frmResume.lblEmptySignature.isVisible === true
                                                                                                                                       )){
    voltmx.print("### frmResume_validateShowFooter signature is niet gevuld");
    //
    showFooter = false;
    frmResume.flcSignature.skin = flcFieldEdgeRed;
  }else{   
    frmResume.flcSignature.skin = flcFieldEdge;
  }
  //
  if(Global.vars.validatePersonDocument === false){
    voltmx.print("### frmResume_validateShowFooter: validatePersonDocument");
    var documentDesc = "";
    for (var q=0; ((CaseData.text) != null) && q < CaseData.text.length; q++ ){
      var x = CaseData.text[q];
      if((x.type == 3 && x.value != null && x.value.length > 0)){ //beschrijving documenten
        if (voltmx.string.startsWith(x.value, "Beschrijving document(en): ")){
          documentDesc = x.value.replace("Beschrijving document(en): ", "");
        }
      }
    }
    if (documentDesc === voltmx.i18n.getLocalizedString("l_noDocument")){
      voltmx.print("### frmResume_validateShowFooter: geen document");
      showFooter = false;
      frmResume.person.skin = flcFieldEdgeRed;
      frmResume.btnShowPersonDocumentDetails.skin = butFooterRed;
	}else{
      frmResume.person.skin = flcFieldEdge;
      frmResume.btnShowPersonDocumentDetails.skin = butFooter;
    }
  }	else {
      frmResume.person.skin = flcFieldEdge;
      frmResume.btnShowPersonDocumentDetails.skin = butFooter;
  }
  //
  if (validateNumberOfPhotos === true && (numberExtraPhotos < minimumNumberOfPhotos  || (numberExtraPhotos > maximumNumberOfPhotos && maximumNumberOfPhotos > minimumNumberOfPhotos))){
    voltmx.print("### frmResume_validateShowFooter:" + voltmx.i18n.getLocalizedString("l_twoPhotosMandatory"));
    //multimedia
    //showFooter = false;
    frmResume.multimedia.skin = flcFieldEdgeRed;
  }else if ((validateNumberOfPhotos === false && validateMaxNumberOfPhotos === true) && (numberExtraPhotos > maximumNumberOfPhotos && maximumNumberOfPhotos > minimumNumberOfPhotos)){
    var _message = voltmx.i18n.getLocalizedString("l_maxPhotosTaken");
    if (_message != null){
      _message = _message.replace("#maximum#", maximumNumberOfPhotos);
      voltmx.print("### frmResume_validateShowFooter:" + voltmx.i18n.getLocalizedString("_message"));
    }
    frmResume.multimedia.skin = flcFieldEdgeRed;
  }else{
    frmResume.multimedia.skin = flcFieldEdge;
  }
  //Warnings or Governmental fiens exceeded
  if(Global.vars.maxWarningsReached === true){
    showFooter = false;
    frmResume.tickettype.skin = flcFieldEdgeRed;
  }else{
    frmResume.tickettype.skin = flcFieldEdge;
  }
  voltmx.print("#### frmResume_validateShowFooter flcPrint visible: " + frmResume.flcPrint.isVisible);
  voltmx.print("#### frmResume_validateShowFooter flcPrintSend visible: " + frmResume.flcPrintSend.isVisible);
  voltmx.print("#### frmResume_validateShowFooter flcDone visible: " + frmResume.flcDone.isVisible);
  voltmx.print("#### frmResume_validateShowFooter showFooter: " + showFooter);
  if(frmResume.flcPrint.isVisible === false && frmResume.flcPrintSend.isVisible === false && frmResume.flcDone.isVisible === false){
    voltmx.print("### frmResume_validateShowFooter: geen zichtbare knoppen");
    showFooter = false;
  }
  if(showFooter === true){
    frmResume_flcFooterMain_setVisibility(true);
    frmResume.flcsLayout.bottom = '117dp';
  }else{
    frmResume_flcFooterMain_setVisibility(false);
    frmResume.flcsLayout.bottom = '44dp';
  }
}

function frmResume_onselect_lbxlocationwithinurbanarea(){
  voltmx.print("### frmPerson_onselect_lbxlocationwithinurbanarea: " + frmResume.locationwithinurbanarea.lbxList.selectedKey);
  var locationwithinurbanarea = null;
  if(frmResume.locationwithinurbanarea.lbxList.selectedKey === "Yes"){
	locationwithinurbanarea = true;
  }else if(frmResume.locationwithinurbanarea.lbxList.selectedKey === "No"){
	locationwithinurbanarea = false;
  }
  voltmx.print("### frmPerson_onselect_lbxlocationwithinurbanarea locationwithinurbanarea: " + locationwithinurbanarea);
  CaseData.location.locationWithinUrbanArea = locationwithinurbanarea;
  //#ifdef iphone	
  frmResume.locationwithinurbanarea.lbxList.setFocus(false);
  //#endif
}

function frmResume_setVehicleDetails(){
  // licenseplate
  if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vehicle && 
      (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null ||
       (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType === null && ((CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc !== "") || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc === null || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc === "" || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc == voltmx.i18n.getLocalizedString("l_brand")))
      )
     ){
    var licenseplate = "";
    frmResume.licenseplate.lblLicenseplateHeader.text = voltmx.i18n.getLocalizedString("l_vehicle");
    frmResume.brand.lblHeader.text =  voltmx.i18n.getLocalizedString("l_brand");
    frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfoLicenseplate;
    if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined){
      if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumberFormatted != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumberFormatted !== ""){
        voltmx.print("### frmResume_setData - identNumberFormatted filled");
        licenseplate = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumberFormatted;
      }else{
        voltmx.print("### frmResume_setData - identNumberFormatted not filled");
        licenseplate = Utility_checkLicenseplate(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber, CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode, true);
      }
    }
    frmResume.licenseplate.lblLicensePlate.text = licenseplate;
    if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined){
      frmResume.brand.lblText.text = Utility_CapatilizeSentence(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc);
      if(frmResume.brand.lblText.text != null && frmResume.brand.lblText.text.length > 24){
        frmResume.brand.lblText.skin = lblFieldInfoSmall;
      }else{
        frmResume.brand.lblText.skin = lblFieldInfo;
        if(frmResume.brand.lblText.text != null && frmResume.location.lblText.text !== ""){
          frmResume.brand.lblText.skin = lblFieldInfo;
        }else{
          frmResume.brand.lblText.skin = lblFieldNotFilled;
          frmResume.brand.lblText.text = voltmx.i18n.getLocalizedString("l_brand");
        }
      }
    }
    if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].indRegCalled === true){
      frmResume.brand.btnLabel.setEnabled(false);
    } else {
      frmResume.brand.btnLabel.setEnabled(true);
    }
    if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber != null && (frmResume.brand.lblText.text === "" || frmResume.brand.lblText.text === null)){
      frmResume.brand.lblText.text = voltmx.i18n.getLocalizedString("l_brand");
      frmResume.brand.lblText.skin = lblFieldNotFilled;
      CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc = null;
    }
    if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined){
      frmResume.licenseplate.lblCountryCode.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense;
    }
    if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex] != null){
      frmResume.licenseplate.lblLicensePlatePrefix.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].prefix;
    } else {
      frmResume.licenseplate.lblLicensePlatePrefix.text = " ";
    }
    if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].dupNumberPlate != -1 && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].dupNumberPlate != null){
      frmResume.duplicate.lblText.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].dupNumberPlate.toString();
      frmResume.duplicate.lblText.skin = lblFieldInfo;
      frmResume.setvehicleinfo.duplicatepopup.txtInputText.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].dupNumberPlate.toString();
    }else{
      if(Global.vars.RegionDuplicatePopupSet === false){
        frmResume.duplicate.lblText.text = voltmx.i18n.getLocalizedString("l_nr");
        frmResume.duplicate.lblText.skin = lblFieldNotFilled;
        frmResume.setvehicleinfo.duplicatepopup.txtInputText.text = "";
      }
    }
    if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined){
      frmResume_setRegionCode(); 
    }
    if(frmResume.setvehicleinfo.brandpopup.lblText.text === voltmx.i18n.getLocalizedString("l_brand")){
      frmResume.setvehicleinfo.brandpopup.lblText.skin = lblFieldNotFilled;
    }else{
      frmResume.setvehicleinfo.brandpopup.lblText.skin = lblFieldInfo;
    }
  } else if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] != null && (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.vessel || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.commercialShip || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType == vehicleIdentType.recreationalBoat) && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null){
    frmResume.licenseplate.lblLicensePlate.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc;
    frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfo;
    if(frmResume.licenseplate.lblLicensePlate.text != null && frmResume.licenseplate.lblLicensePlate.text.length > 18){
      frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfoSmall;
    }
    if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] != null){
      frmResume.licenseplate.lblCountryCode.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense;
    }
    frmResume.licenseplate.lblLicenseplateHeader.text = voltmx.i18n.getLocalizedString("l_vessel");
    frmResume.licenseplate.width = "100%";
    frmResume.licenseplate.flcLicensePlate.width = '100%';
    frmResume_regioncode_setVisibility(false);
    frmResume_setvehicleinfo_regioncodepopup_setVisibility(false);
    frmResume_duplicate_setVisibility(false);
    frmResume_setvehicleinfo_duplicatepopup_setVisibility(false);
    if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber != null){
      frmResume.brand.lblHeader.text =  voltmx.i18n.getLocalizedString("pl_identification");
      frmResume.brand.lblText.text =  CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber;
      if(frmResume.brand.lblText.text.length > 24){
        frmResume.brand.lblText.skin = lblFieldInfoSmall;
      }
    }
    frmResume.brand.btnLabel.setEnabled(false);
  } else if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType != vehicleIdentType.vehicle && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null){
  	frmResume.licenseplate.lblLicensePlate.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc;
    frmResume.licenseplate.lblLicensePlate.skin = lblFieldInfo;
    frmResume.licenseplate.lblLicenseplateHeader.text = voltmx.i18n.getLocalizedString("l_kindOfVehicle");
    // code ala frmResume_setRegionCode
    frmResume.licenseplate.width = "100%";
    frmResume.licenseplate.flcLicensePlate.width = '100%';
   	frmResume_regioncode_setVisibility(false);
    frmResume_setvehicleinfo_regioncodepopup_setVisibility(false);
    frmResume_duplicate_setVisibility(false);
    //
    frmResume_setvehicleinfo_duplicatepopup_setVisibility(false);
	if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber != null){
      frmResume.brand.lblHeader.text =  voltmx.i18n.getLocalizedString("pl_identification");
      frmResume.brand.lblText.text =  CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber;
      if(frmResume.brand.lblText.text.length > 24){
        frmResume.brand.lblText.skin = lblFieldInfoSmall;
      }
    }
    frmResume.brand.btnLabel.setEnabled(false);
  }
}

function frmResume_onclick_btnCountryCode(){
  Global.vars.previousForm = "frmResume";
  Global.vars.btnCountryVehicleCode = false;
  frmVehicleCountries.show();
}

function frmResume_setRegionCode(){
  //set fields
  voltmx.print("### frmResume_setRegionCode");
  if(Global.vars.RegionDuplicatePopupSet === false){
    frmResume.setvehicleinfo.regioncodepopup.txtInputText.text = "";
    frmResume.regioncode.lblText.text = voltmx.i18n.getLocalizedString("l_code");
    frmResume.regioncode.lblText.skin = lblFieldNotFilled;
    frmResume.regioncode.skin = flcFieldEdgeRed;
  }
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense == "F" && Global.vars.regionCodeEnabled === true){
    voltmx.print("### frmResume_setRegionCode regionNumberPlate: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate);
  	frmResume_regioncode_setVisibility(true);
    frmResume_setvehicleinfo_regioncodepopup_setVisibility(true);
    frmResume_duplicate_setVisibility(false);
    frmResume_setvehicleinfo_duplicatepopup_setVisibility(false);
    frmResume.licenseplate.width = "48%";
    frmResume.licenseplate.flcLicensePlate.width = '82.3%';
    frmResume.regioncode.width = "48%";
    frmResume.regioncode.right = "0dp";
    frmResume.setvehicleinfo.regioncodepopup.width = "100%";
    if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate !== ""){
     	frmResume.setvehicleinfo.regioncodepopup.txtInputText.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate;
      	frmResume.regioncode.lblText.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate;
      	frmResume.regioncode.lblText.skin = lblFieldInfo;
    	frmResume.regioncode.skin = flcFieldEdge;
  	}
  }else if (Global.vars.enableParkiusMode === false && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense == "NL"){
    frmResume_regioncode_setVisibility(false);
    frmResume.regioncode.right = "26%";
    frmResume_setvehicleinfo_regioncodepopup_setVisibility(false);
    frmResume_duplicate_setVisibility(true);
    frmResume_setvehicleinfo_duplicatepopup_setVisibility(true);
    frmResume.licenseplate.width = "48%";
    frmResume.licenseplate.flcLicensePlate.width = '82.3%';
   	frmResume.duplicate.width = "48%";
    frmResume.duplicate.right = "0dp";
    frmResume.setvehicleinfo.duplicatepopup.width = "100%";
  }else{
    frmResume.licenseplate.width = "100%";
    frmResume.licenseplate.flcLicensePlate.width = '100%';
   	frmResume_regioncode_setVisibility(false);
    frmResume_setvehicleinfo_regioncodepopup_setVisibility(false);
    frmResume_duplicate_setVisibility(false);
    frmResume_setvehicleinfo_duplicatepopup_setVisibility(false);
  }
}

function frmResume_onclick_btnBack(){
  voltmx.print("### frmResume_onclick_btnBack: Global.vars.photosLoaded: " + Global.vars.photosLoaded);
  voltmx.print("### frmResume_onclick_btnBack: Global.vars.questionTypes.length: " + Global.vars.questionTypes.length);
  if (Global.vars.questionTypes.length === 0 &&  (CaseData.questions !== undefined && CaseData.questions != null)){
    Global.vars.questionTypes = CaseData.questions;
    voltmx.print("### frmResume_onclick_btnBack: Global.vars.questionTypes.length: " + Global.vars.questionTypes.length);
  }
  voltmx.print("### frmResume_onclick_btnBack: Global.vars.questionsClearedOnResume: " + Global.vars.questionsClearedOnResume);
  //#ifdef android
  frmResume.mapdisplay.mapLocation.clear();
  //#endif
  if(Global.vars.backToPreviousForm == "frmHistory") {
    voltmx.print("### frmResume_onclick_btnBack to frmHistory");
    Global.vars.claimedDoc = {};
    Global.vars.claimedDocID = null;
    Global.vars.selectedOutcome = null;
    CaseData_init();
    Global.vars.gCasePersons = {};
  	Global.vars.gCaseVehicles = CaseData_setNewvehicle();
    frmResume_clearMultimedia(); 
  	Global.vars.newPhotos = []; 
  	Global.vars.getPhotos = [];
  	Global.vars.photosLoaded = false;
    Global.vars.backToPreviousForm = null;
    frmResume.flcsLayout.setEnabled(true);
    frmResume.flcLayoutInput.setEnabled(true);
    frmHistory.show();
    CaseData_init();
  }else if(Global.vars.backToPreviousForm == "frmFollow") {
    voltmx.print("### frmResume_onclick_btnBack to frmFollow");
    Global.vars.claimedDoc = {};
    Global.vars.claimedDocID = null;
    Global.vars.selectedOutcome = null;
    Global.vars.gCasePersons = {};
  	Global.vars.gCaseVehicles = CaseData_setNewvehicle();
    frmResume_clearMultimedia(); 
  	Global.vars.newPhotos = []; 
  	Global.vars.getPhotos = [];
  	Global.vars.photosLoaded = false;
    Global.vars.backToPreviousForm = null;
    frmResume.flcsLayout.setEnabled(true);
    frmResume.flcLayoutInput.setEnabled(true);
    frmFollow.show();
    frmFollow_btnCancelClaim();
    CaseData_init();
  }else if(activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || 
            CaseData.processinfo.activeTaskType == "ValidateAlibi" || CaseData.processinfo.activeTaskType == "OverdrachtPolitie" || CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod"  || CaseData.processinfo.activeTaskType == "VersturenVerbod"){
    voltmx.print("### frmResume_onclick_btnBack to frmOpentask"); 
    Global.vars.claimedDoc = {};
    Global.vars.claimedDocID = null;
    Global.vars.selectedOutcome = null;
    CaseData_init();
    Global.vars.gCasePersons = {};
  	Global.vars.gCaseVehicles = CaseData_setNewvehicle();
    frmResume_clearMultimedia(); 
  	Global.vars.newPhotos = []; 
  	Global.vars.getPhotos = [];
  	Global.vars.photosLoaded = false;
    Global.vars.newTimeSet = false;
    Global.vars.newLocationSet = false;
    Global.vars.questionsClearedOnResume = false;
    frmResume.flcLayoutInput.setEnabled(true);
    frmOpenTasks.show();
    CaseData_init();
  }else if((CaseData.processinfo.activeTaskType == "CorrigerenKandidaat" || CaseData.processinfo.activeTaskType == "CorrigerenVerbod")){
    voltmx.print("### frmResume_onclick_btnBack to frmRegister"); 
    Global.vars.claimedDoc = {};
    Global.vars.claimedDocID = null;
    CaseData_init();
    Global.vars.gCasePersons = {};
  	Global.vars.gCaseVehicles = CaseData_setNewvehicle();
    frmResume_clearMultimedia(); 
  	Global.vars.newPhotos = []; 
  	Global.vars.getPhotos = [];
  	Global.vars.photosLoaded = false;
    frmRegister.show();
    CaseData_init();
  }else if(Global.vars.backToPreviousForm == "frmProhibitions") {
    voltmx.print("### frmResume_onclick_btnBack to frmProhibitions"); 
    Global.vars.claimedDoc = {};
    Global.vars.claimedDocID = null;
    CaseData_init();
    Global.vars.gCasePersons = {};
  	Global.vars.gCaseVehicles = CaseData_setNewvehicle();
    frmResume_clearMultimedia(); 
  	Global.vars.newPhotos = []; 
  	Global.vars.getPhotos = [];
  	Global.vars.photosLoaded = false;
    Global.vars.backToPreviousForm = null;
    frmResume.flcsLayout.setEnabled(true);
    frmResume.flcLayoutInput.setEnabled(true);
    frmProhibitions.show();
    CaseData_init();
  }else if(CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("DVV_") || CaseData.caseinfo.caseType.startsWith("DRV_") || CaseData.caseinfo.caseType.startsWith("RV_") || CaseData.caseinfo.caseType.startsWith("VV_") || CaseData.caseinfo.caseType.startsWith("KRV_") || CaseData.caseinfo.caseType.startsWith("KVV_") || CaseData.caseinfo.caseType.startsWith("SRV_") || CaseData.caseinfo.caseType.startsWith("SVV_"))){
    voltmx.print("### frmResume_onclick_btnBack to prohibitionDetails");
    Global.vars.previousForm = "frmProhibitionDetails";
  	frmProhibitionDetails.show();   
  }else if((CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("CTE_") || CaseData.caseinfo.caseType.startsWith("MLD_"))) && (Global.vars.questionTypes.length > 0 || Global.vars.questionsClearedOnResume === true)){
    voltmx.print("### frmResume_onclick_btnBack to questions");
    if(CaseData.questions !== undefined && CaseData.questions != null){
      voltmx.print("### frmResume_onclick_btnQuestions CaseData.questions: " + JSON.stringify(CaseData.questions));
      var previousdataFound = false;
      if(CaseData.questions.length > 0){
        for(var i in CaseData.questions){
          var v = CaseData.questions[i];
          if (v.cte_option_usage != null && v.cte_option_usage !== ""){
            Global.vars.questionTypeChooseEnabled = true;
          } else {
            Global.vars.questionTypeChooseEnabled = false;
          }  
          if(v.variables !== undefined && v.variables != null && v.variables.length > 0){
            Global.vars.selectedQuestionType = v;
            Global.vars.QuestionsSet = true;
            previousdataFound = true;
            break;
          }
        }
      }
//       if(previousdataFound === true){
//         if (Global.vars.questionTypeChooseEnabled === false){
//           Global.vars.previousForm = "frmQuestions";
//           frmResume_frmQuestions_show();
//         } else {
//           Global.vars.previousForm = "frmQuestionTypes";
//           frmQuestionTypes_getCaseTypeOptions();
//         }  
//         // 		frmQuestions.show();
//       }else{
//         voltmx.print("### frmResume_onclick_btnQuestions no previous data");
//         Utility_clearEnforcementObjectQuestions();
//         Global.vars.previousForm = "frmQuestionTypes";
//         frmQuestionTypes_getCaseTypeOptions();
      //       }
      Global.vars.previousForm = "frmHandleCharacteristic";
      frmHandleCharacteristic.show();
    }else{
      voltmx.print("### frmResume_onclick_btnQuestions no questions");
//      Utility_clearEnforcementObjectQuestions();
//       Global.vars.previousForm = "frmQuestionTypes";
//       frmQuestionTypes_getCaseTypeOptions();
      Global.vars.previousForm = "frmHandleCharacteristic";
      frmHandleCharacteristic.show();
    }
  }else if((CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("MLD_")) && Global.vars.questionTypes.length === 0){
    voltmx.print("### frmResume_onclick_btnBack to handlechracteristic");
    Global.vars.previousForm = "frmHandleCharacteristic";
  	frmHandleCharacteristic.show();
  }else if((CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("CONTROL_K")) && Global.vars.questionTypes.length === 0){
    voltmx.print("### frmResume_onclick_btnBack to handlechracteristic");
    var _unhandledTaskTypes = Utility_getIndexIfObjWithAttr(Global.vars.unhandledTaskTypes,"taskType",CaseData.processinfo.activeTaskType);
    if (_unhandledTaskTypes == -1){
      Global.vars.previousForm = "frmHandleCharacteristic";
      frmHandleCharacteristic.show();
    } else {
      Global.vars.previousForm = "frmHandle";
      frmHandle.show();
    }
   }else if(Global.vars.handleCharacteristicType != "OutcomeType" && Global.vars.handleCharacteristicType != "OutcomeTypeCategory"){
     voltmx.print("### frmResume_onclick_btnBack to offenceSelect"); 
     Global.vars.OffenceSelectPrevious = true;
     Global.vars.preserveData["caseID"] =  CaseData.caseinfo.externalId; 
     Global.vars.preserveData["offence"] = CaseData.offence;
     Global.vars.preserveData["option"] = CaseData.option;
     Global.vars.preserveData["questions"] = CaseData.questions;
     voltmx.print("### frmResume_onclick_btnBack to offenceSelect preserveData CaseData.offence: " + JSON.stringify(CaseData.offence)); 
     Global.vars.preserveData["offenceStatement"] = Utility_cloneObject(CaseData.offence);
     Global.vars.statementText = "";
     Global.vars.StatementNoPledge = "";
     Global.vars.OffenceCommunicatedNoStatement = "";
     for(var i in CaseData.text){
       var v = CaseData.text[i];
       if(v.type == 2){
         voltmx.print("#### Text: statement: " + v + " index: " + i);
         //verklaring
         Global.vars.statementText = v.value; //verklaring   	
       }else if(v.type == 20 && (v.value != null && v.value.length > 0 && voltmx.string.startsWith(v.value, "Cautie: "))){
         voltmx.print("#### Text: statement: " + v + " index: " + i);
         Global.vars.StatementNoPledge = v.value.replace("Cautie: ", ""); //Cautie startswith?
         //Geen Cautie verklaring
         //}else if(v.type == 20 && voltmx.string.startsWith(v.value, "Strafbaar feit niet medegedeeld: ")){
       }else if(v.type == 20){
         voltmx.print("#### Text: statement: " + v + " index: " + i);
         Global.vars.OffenceCommunicatedNoStatement = v.value; //Strafbaar feit niet medegedeeld startswith?
         //Strafbaar feit niet medegedeeld verklaring
       }
     }
     //preserve data
     Global.vars.preserveData["StatementNoPledge"] = Global.vars.StatementNoPledge;
     Global.vars.preserveData["OffenceCommunicatedNoStatement"]= Global.vars.OffenceCommunicatedNoStatement;
     Global.vars.preserveData["statementText"]= Global.vars.statementText;
     voltmx.print("### frmResume_onclick_btnBack to offenceSelect preserveData: " + JSON.stringify(Global.vars.preserveData)); 
     Global.vars.previousForm = "frmResume";
     if(CaseData.offence.offenceCode !== undefined && CaseData.offence.offenceCode != null && CaseData.offence.offenceCode == "H022" && Global.vars.gInstanceId == "NL0036" && CaseData.caseinfo.ticketType == 999){
       voltmx.print("### frmResume_onclick_btnBack only for offenceCode H022 for governance");
       CaseData.caseinfo.ticketType = null;
       CaseData.caseinfo.ticketTypeDescription = null;
       CaseData.caseinfo.ticketText = null;
       CaseData.caseinfo.enforcementType = null;
       CaseData.caseinfo.enforcementLevel = null;
     }
     frmOffenceSelect.lblSubHeader.text =voltmx.i18n.getLocalizedString("l_offenceCategory");
     frmOffenceSelect.show();
  }else{
     //leeg taakuitkomsten en het definitief maken van de zaak
//     CaseData.processinfo.tasks.sort(function(a, b) {
//       return (a.taskCreatedOn===null)-(b.taskCreatedOn===null) || -(a.taskCreatedOn>b.taskCreatedOn)||+(a.taskCreatedOn<b.taskCreatedOn);
//     });
    for(var i = CaseData.processinfo.tasks.length; i--;){
      var v = CaseData.processinfo.tasks[i];
      if(v.taskType == Global.vars.taskType){
        v.taskOutcome = null;
        v.taskOutcomeId = null;
        v.taskOutcomeDescription = null;
        v.taskCompletedOn = null;
        v.taskClaimedBy = null;
        v.taskClaimedByName = null;
        CaseData.caseinfo.indComplete = false;
      	CaseData.caseinfo.timeComplete = null;
        //Utility_cleanStatus(CaseData.statuscode);
        CaseData.statuscode = null;
        CaseData.processinfo.activeTaskType = Global.vars.taskType;
      }
      CaseData.processinfo.lastTaskProcessed = {};
      break;
    }
    //leeg APV
    CaseData.offence.apvCode = null;
    CaseData.offence.apvDesc = null;
    CaseData.offence.apvOffenceCode = null;
    CaseData.offence.apvOffenceDesc = null;
    CaseData.offence.apvNumber = null;
    //ga naar form
    frmHandleCharacteristic_showForm();
  }
  if(Global.vars.backToPreviousForm != "frmProhibitions") {
    frmResume_resetAllFields();
    frmResume_clearANPRPhotos();
  }
}


function frmResume_frmQuestions_show(){
  	//doe hier een database actie om de juiste variabelen op te halen
  	voltmx.print("### frmResume_frmQuestions_show");
  	voltmx.application.showLoadingScreen(lblLoader,
	                                    voltmx.i18n.getLocalizedString("l_loading"),
	                                    "center",
	                                    true,
	                                    true,
	                                    { enablemenukey : true, enablebackkey : true } );
  	try{
      voltmx.timer.schedule("goToQuestionsfromResume", frmResume_goToQuestionForm, 0.2, false);
    }catch(err){} 
}

function frmResume_goToQuestionForm(){
  try{
    voltmx.timer.cancel("goToQuestionsfromResume");
    Global.vars.previousForm = "frmResume";
    frmQuestions.show();
  }catch(err){}
}

function frmResume_clearANPRPhotos(){
  if(Global.vars.addANPRPhotos !== undefined && Global.vars.addANPRPhotos.length > 0 && Global.vars.showPhotosANPRLoaded === true){
    voltmx.print("### frmResume_clearANPRPhotosGlobal.vars.addANPRPhotos length: " + Global.vars.addANPRPhotos.length);
    Utility_removePhotosANPR();
    frmResume_clearMultimedia();
    frmPhotos.segPhotos.removeAll();
    Global.vars.showPhotosANPRLoaded = false;
  }
}

function frmResume_onclick_btnShowPhotos(){
  Global.vars.previousForm = "frmResume";
  frmPhotos.show();
  //frmResume_preSaveCase();
}

// function frmResume_onclick_btnPrint(){
//   voltmx.print("### frmResume_onclick_btnPrint  Global.vars.handleCharacteristicType: " +  Global.vars.handleCharacteristicType);

//   function updateCaseData(result){
//     voltmx.print("#### frmResume_onclick_btnPrint updateCaseData result: " + JSON.stringify(result));
//     Print.callback = frmResume_onclickBtnPrintCallback;
//     if(result !== undefined && result.response[0].externalCaseId !== undefined && result.response[0].externalCaseId != null && result.response[0].externalCaseId !== "" &&
//        result.response[0].caseId !== undefined && result.response[0].caseId != null && result.response[0].caseId !== ""){
//       var externalCaseID = result.response[0].externalCaseId;
//       var couchID = result.response[0].caseId;
//       CaseData.caseinfo.externalId = externalCaseID;
//       CaseData.caseinfo.id = couchID;
//       voltmx.print("### frmResume_onclick_btnPrint CaseData.caseinfo 2: " + JSON.stringify(CaseData.caseinfo));
//       //now show external ID
//       if(CaseData.caseinfo.externalId !== undefined && CaseData.caseinfo.externalId != null && CaseData.caseinfo.externalId !== ""){
//         frmResume_flcCaseNumber_setVisibility(true);
//         frmResume.lblCaseNumber.text = "Zaak ID: " + CaseData.caseinfo.externalId;
//       }
//       if (Global.vars.backToPreviousForm !== "frmHistory"){
//         if ((Global.vars.handleCharacteristicType == "OutcomeType" || Global.vars.handleCharacteristicType == "OutcomeTypeCategory") || (CaseData.caseinfo.caseType != null &&  CaseData.caseinfo.caseType.startsWith("MLD_"))){
//           frmResume_onclick_btnPrintReport();
//         } else {
//           voltmx.print("### frmResume_onclick_btnPrint frmResume_onclick_btnPrintReport");
//           voltmx.print("### frmResume_onclick_btnPrint CaseData: " + JSON.stringify(CaseData));
//           frmResume_onclick_btnPrintResume();
//         }
//       } else if (Global.vars.backToPreviousForm == "frmHistory"){
//         frmResume_onclick_btnPrintHistory();
//       }
//     }else{
//       if (Global.vars.backToPreviousForm !== "frmHistory"){
//         if ((Global.vars.handleCharacteristicType == "OutcomeType" || Global.vars.handleCharacteristicType == "OutcomeTypeCategory") || (CaseData.caseinfo.caseType != null &&  CaseData.caseinfo.caseType.startsWith("MLD_"))){
//           frmResume_onclick_btnPrintReport();
//         } else {
//           frmResume_onclick_btnPrintResume();
//         }
//       } else if (Global.vars.backToPreviousForm == "frmHistory"){
//         frmResume_onclick_btnPrintHistory();
//       }
//       voltmx.print("#### frmResume_onclick_btnPrint updateCaseData case is saved offline on device");
//     }
//   }
//   voltmx.print("### frmResume_onclick_btnPrint CaseData.caseinfo: " + JSON.stringify(CaseData.caseinfo));
//   if(CaseData.caseinfo.officerNumber === undefined || CaseData.caseinfo.officerNumber === null || CaseData.caseinfo.officerNumber === ""){
//     voltmx.print("### frmResume_onclick_btnPrint officer number is empty");
//     CaseData_setUserInformationToCaseInfo();   
//   }
//   voltmx.print("### frmResume_onclick_btnPrint Utility_saveUploadCaseData");
//   Utility_saveUploadCaseData(CaseData,updateCaseData,"frmResume");
// }

function frmResume_onclick_btnPrint(){
  voltmx.print("### frmResume_onclick_btnPrint  Global.vars.handleCharacteristicType: " +  Global.vars.handleCharacteristicType);
  voltmx.print("### frmResume_onclick_btnPrint CaseData.caseinfo: " + JSON.stringify(CaseData.caseinfo));
  if(CaseData.caseinfo.officerNumber === undefined || CaseData.caseinfo.officerNumber === null || CaseData.caseinfo.officerNumber === ""){
    voltmx.print("### frmResume_onclick_btnPrint officer number is empty");
    CaseData_setUserInformationToCaseInfo();   
  }
  voltmx.print("### frmResume_onclick_btnPrint Utility_saveUploadCaseData");
  if (Global.vars.backToPreviousForm !== "frmHistory"){
    voltmx.print("### frmResume_onclickBtnPrintCallback, CaseData.caseinfo 2: " + JSON.stringify(CaseData.caseinfo));
    if(CaseData.caseinfo.indComplete === false){
      //send
      btnPrintResume = true;
      CaseData.caseinfo.indPrinted = true;
      CaseData.caseinfo.timePrinted = Utility_getUTCJavascriptDate(null);
      frmResume_onclick_btnDone();
      Global.vars.uploadText = false;
    } else {
      if ((Global.vars.handleCharacteristicType == "OutcomeType" || Global.vars.handleCharacteristicType == "OutcomeTypeCategory") || (CaseData.caseinfo.caseType != null &&  CaseData.caseinfo.caseType.startsWith("MLD_"))){
        frmResume_onclick_btnPrintReport();
      } else {
        frmResume_onclick_btnPrintResume();
      }
    }
  } else if (Global.vars.backToPreviousForm == "frmHistory"){
    frmResume_onclick_btnPrintHistory();
  }
}

function frmResume_onclick_btnPrintReport(){
  voltmx.print("### frmResume_onclick_btnPrintReport");
  Print.callback = frmResume_onclickBtnPrintCallback;
  frmResume_showLoaderUploadCase();
  printReport_printTicket();
}

function frmResume_onclick_btnPrintResume(){
  voltmx.print("### frmResume_onclick_btnPrintResume");
  Print.callback = frmResume_onclickBtnPrintCallback;
  frmResume_showLoaderUploadCase();
  printLawEnforcement_printTicket();
}

function frmResume_onclick_btnPrintHistory(){
  voltmx.print("### frmResume_onclick_btnPrint");
  Print.callback = frmResume_onclickBtnPrintHistoryCallback;
  //frmResume_showLoaderUploadCase();
  if (CaseData.caseinfo.enforcementType == "S"){
    printLawEnforcement_printTicket();
  } else if (CaseData.caseinfo.enforcementType == "F") {
    printParking_printTicket();
  }
}

function frmResume_onclickBtnPrintCallback(){
	voltmx.print("### frmResume_onclickBtnPrintCallback: " + Print.printSuccessful);
	voltmx.application.dismissLoadingScreen();
	if(Print.printSuccessful === true){
		voltmx.print("### frmResume_onclickBtnPrintCallback Print Succesful, CaseData.caseinfo: " + JSON.stringify(CaseData.caseinfo));
	    // set printed indicator and date
		frmResume_RegisterCaseFinish();
	} else {
      	frmResume_dismissLoaderUploadCase();
		voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_prt0001"),
	               	  null,
	               	  "info",
	               	  voltmx.i18n.getLocalizedString("bt_ok"),
	               	  null,
	               	  voltmx.i18n.getLocalizedString("l_info"),
	              	  null);
	}
}

function frmResume_onclickBtnPrintHistoryCallback(){
	voltmx.print("### frmResume_onclickBtnPrintCallback: " + Print.printSuccessful);
	voltmx.application.dismissLoadingScreen();
	if(Print.printSuccessful === true){
	    // set printed indicator and date
	  frmResume_dismissLoaderUploadCase();
      frmResume_onclick_btnBack();
    } else {
      	frmResume_dismissLoaderUploadCase();
		voltmx.ui.Alert(voltmx.i18n.getLocalizedString("e_prt0001"),
	               	  null,
	               	  "info",
	               	  voltmx.i18n.getLocalizedString("bt_ok"),
	               	  null,
	               	  voltmx.i18n.getLocalizedString("l_info"),
	              	  null);
	}
}

function frmResume_onclick_btnVehicleBrand(){
  if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].indRegCalled !== true){
      Global.vars.previousForm = "frmResume";
    frmVehicleBrands.show();
  }
}

function frmResume_onclick_btnDone(){
  if(Global.vars.backToPreviousForm == "frmProhibitions") {
    voltmx.print("### frmResume_onclick_btnDone to frmProhibitions");
    Global.vars.claimedDoc = {};
    Global.vars.claimedDocID = null;
    CaseData_init();
    Global.vars.gCasePersons = {};
    Global.vars.gCaseVehicles = CaseData_setNewvehicle();
    frmResume_clearMultimedia();
    Global.vars.newPhotos = [];
    Global.vars.getPhotos = [];
    Global.vars.photosLoaded = false;
    Global.vars.backToPreviousForm = null;
    frmResume.flcsLayout.setEnabled(true);
    frmResume.flcLayoutInput.setEnabled(true);
    frmProhibitions.show();
    CaseData_init();
  }else{
  	// amountTax no longer needed
    if(CaseData.caseinfo.caseType !== "TICKET_F"){
      CaseData.offence.amountTax = "";
      CaseData.offence.amountTaxDisplay = "";
    }
    //
    if (Global.vars.keepLocationHousenumber === false && Utility_isEmptyEnforcementObject(CaseData.enforcementObject) === true && Global.vars.buildFor == "GEN"){
  	  CaseData.location.houseNumber = null;
  	}
    if(frmResume.offendertelephone.txtInputText.text !== undefined && frmResume.offendertelephone.txtInputText.text != null && frmResume.offendertelephone.txtInputText.text !== "" && CaseData.transportticket.offenderTelephone === null){
      CaseData.transportticket.offenderTelephone = frmResume.offendertelephone.txtInputText.text;
    }
    if(frmResume.offenderemail.txtInputText.text !== undefined && frmResume.offenderemail.txtInputText.text != null && frmResume.offenderemail.txtInputText.text !== "" && CaseData.transportticket.offenderEmail === null){
      CaseData.transportticket.offenderEmail = frmResume.offenderemail.txtInputText.text;
    }
    //check photos
    var numberExtraPhotos = 0;
    var documentType = "ExtraPhoto";
    for (var p=0; ((CaseData.multimedia) != null) && p < CaseData.multimedia.length; p++ ){
      var v = CaseData.multimedia[p];
      if((v.documentType == documentType)){
        if(CaseData.caseinfo.caseType == "CONTROL_K"){
          if (v.fileName != null && voltmx.string.startsWith(v.fileName, CaseData.processinfo.lastTaskProcessed.taskType) === true){
            numberExtraPhotos = numberExtraPhotos + 1;
          }
        } else {
          numberExtraPhotos = numberExtraPhotos + 1;
        }
      }
    }
    var minimumNumberOfPhotos = Global.vars.minimumNumberOfPhotos;
    var maximumNumberOfPhotos = Global.vars.maximumNumberOfPhotos;
    var validateNumberOfPhotos = false;
    for (var i = 0; i < Global.vars.validatePhotosForCaseType.length; i++) {
      var w =  Global.vars.validatePhotosForCaseType[i];
      voltmx.print("### frmResume_onclick_btnDone caseType: " + w);
      if (w === CaseData.caseinfo.caseType){
        validateNumberOfPhotos = true;
        break;
      } 
    }
    var validateMaxNumberOfPhotos = false;
    var _mandatoryForOutcome = -1;
    if(CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("CONTROL_K")){
      if (Global.vars.selectedOutcome != null){
        _mandatoryForOutcome = Utility_getIndexIfObjWithAttr(Global.vars.mandatoryForOutcome,"outcome",Global.vars.selectedOutcome.identification);
        if (_mandatoryForOutcome == -1){
          validateNumberOfPhotos = false;
          validateMaxNumberOfPhotos = true;
        }
      } else {
        validateNumberOfPhotos = false;
      }
    }
    voltmx.print("### frmResume_onclick_btnDone validateNumberOfPhotos: " + validateNumberOfPhotos);
    if(CaseData.offence.offenceCode !== undefined && CaseData.offence.offenceCode == "R397B" && (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV")){
      minimumNumberOfPhotos = 0;
      maximumNumberOfPhotos = 0;
      validateNumberOfPhotos = false;
    }
    var mandatoryOptions = 0;
    if(CaseData.offence.optionUsage != null){
      try{
        mandatoryOptions = CaseData.offence.optionUsage.split(',')[0];
      }catch(err){
        mandatoryOptions = 0;
      }
    }
    voltmx.print("### frmResume_onclick_btnDone mandatoryOptions: " + mandatoryOptions);
    voltmx.print("### frmResume_onclick_btnDone caseType: " + CaseData.caseinfo.caseType);
    mandatoryOptions = Number(mandatoryOptions);
    //check alerts
    voltmx.print("### frmResume_onclick_btnDone Global.vars.optionvariablesSet: " + Global.vars.optionvariablesSet);
    if(Global.vars.optionvariablesSet === false && frmResume.option.lblText.text !== "" && frmResume.option.lblText.text != null && frmResume.option.lblText.text !== voltmx.i18n.getLocalizedString("l_optionsVariables")){
      Global.vars.optionvariablesSet = true;
    }
    var alertMessage = "";
    var _noCautionPersonCaseType = Utility_getIndexIfObjWithAttr(Global.vars.noCautionPersonCaseType,"caseType",CaseData.caseinfo.caseType);
    voltmx.print("### frmResume_onclick_btnDone _noCautionPersonCaseType: " + _noCautionPersonCaseType);
    if(mandatoryOptions > 0 && Global.vars.optionvariablesSet === false && CaseData.caseinfo.enforcementLevel != "W"){
      alert(voltmx.i18n.getLocalizedString("l_mandatoryOptions")); //i18n
      frmResume_dismissLoaderUploadCase();
    }else if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense == "F" && Global.vars.regionCodeEnabled === true && Global.vars.handleCharacteristicType != "OutcomeType" && Global.vars.handleCharacteristicType != "OutcomeTypeCategory" && (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate === null || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate === "")){
      alert(voltmx.i18n.getLocalizedString("l_mandatoryRegionCodeFrance"));
      frmResume_dismissLoaderUploadCase();
    }else if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber == "01vv " && (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc === null || CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc === "") && Global.vars.handleCharacteristicType != "OutcomeType" && Global.vars.handleCharacteristicType != "OutcomeTypeCategory"){
      alert(voltmx.i18n.getLocalizedString("l_mandatoryBrand"));
      frmResume_dismissLoaderUploadCase();
    }else if(frmResume.statement.lblText.text === voltmx.i18n.getLocalizedString("l_statement") && CaseData.caseinfo.enforcementLevel != "W" && 
             CaseData.offence !== undefined && CaseData.offence.person === true && Global.vars.handleCharacteristicType != "OutcomeType" && 
             Global.vars.handleCharacteristicType != "OutcomeTypeCategory" && 
               (CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("CTE_") === false) && 
               (CaseData.caseinfo.caseType.startsWith("MLD_") === false) && 
              (CaseData.caseinfo.caseType.startsWith("DVV_") === false) &&
              (CaseData.caseinfo.caseType.startsWith("DRV_") === false) &&
              (CaseData.caseinfo.caseType.startsWith("RV_") === false) &&
              (CaseData.caseinfo.caseType.startsWith("VV_") === false) &&
              (CaseData.caseinfo.caseType.startsWith("KRV_") === false) &&
              (CaseData.caseinfo.caseType.startsWith("KVV_") === false) &&
              (CaseData.caseinfo.caseType.startsWith("SRV_") === false) &&
              (CaseData.caseinfo.caseType.startsWith("DVV_") === false))){
      alert(voltmx.i18n.getLocalizedString("l_mandatoryStatement"));
      frmResume_dismissLoaderUploadCase();
    }else if(frmResume.statement.lblText.text === voltmx.i18n.getLocalizedString("l_statement") && CaseData.offence != null && 
             CaseData.offence.person === true && Utility_isExtendReportCaseTypeCategory(CaseData.caseinfo.caseTypeCategory) === true &&
          _noCautionPersonCaseType == -1){
      alert(voltmx.i18n.getLocalizedString("l_mandatoryStatement"));
      frmResume_dismissLoaderUploadCase();
    }else if((frmResume.multimedia.isVisible === true && numberExtraPhotos === 0) && (
              (CaseData.caseinfo.caseType.startsWith("DVV_") === true) ||
              (CaseData.caseinfo.caseType.startsWith("DRV_") === true) ||
              (CaseData.caseinfo.caseType.startsWith("RV_") === true) ||
              (CaseData.caseinfo.caseType.startsWith("VV_") === true) ||
              (CaseData.caseinfo.caseType.startsWith("KRV_") === true) ||
              (CaseData.caseinfo.caseType.startsWith("KVV_") === true) ||
              (CaseData.caseinfo.caseType.startsWith("SRV_") === true) ||
              (CaseData.caseinfo.caseType.startsWith("DVV_") === true))){
      voltmx.ui.Alert("Weet u zeker dat u het verbod wilt versturen zonder een foto toe te voegen?",
                        frmResume_confirm_send,
                        "confirmation",
                        voltmx.i18n.getLocalizedString("bt_yes"),
                        voltmx.i18n.getLocalizedString("bt_no"),
                        "Info",
                        null);
      frmResume_dismissLoaderUploadCase();
    }else if (validateNumberOfPhotos === true && (numberExtraPhotos < minimumNumberOfPhotos  || (numberExtraPhotos > maximumNumberOfPhotos && maximumNumberOfPhotos > minimumNumberOfPhotos))){
      if (minimumNumberOfPhotos === 1 && maximumNumberOfPhotos === 0){
        alertMessage = voltmx.i18n.getLocalizedString("l_mandatoryPhoto");
      } else if (minimumNumberOfPhotos > 1 && maximumNumberOfPhotos === 0){
        alertMessage = voltmx.i18n.getLocalizedString("l_minPhotosMandatory");
        alertMessage = alertMessage.replace("#minimum#", minimumNumberOfPhotos);
      } else {
        alertMessage = voltmx.i18n.getLocalizedString("l_minmaxPhotosMandatory");
        alertMessage = alertMessage.replace("#minimum#", minimumNumberOfPhotos);
        alertMessage = alertMessage.replace("#maximum#", maximumNumberOfPhotos);
      }
      alert(alertMessage);
      frmResume_dismissLoaderUploadCase();
    }else if ((validateNumberOfPhotos === false && validateMaxNumberOfPhotos === true) && (numberExtraPhotos > maximumNumberOfPhotos && maximumNumberOfPhotos > minimumNumberOfPhotos)){
      alertMessage = voltmx.i18n.getLocalizedString("l_maxPhotosTaken");
      alertMessage = alertMessage.replace("#maximum#", maximumNumberOfPhotos);
      alert(alertMessage);
      frmResume_dismissLoaderUploadCase();
    }else{
      voltmx.print("#### frmResume_onclick_btnDone Try to send");
      frmResume_TryToSend();
    }
  }
}

function frmResume_confirm_send(response){
  voltmx.print("#### frmResume_confirm_send");
  if(response){
    voltmx.print("#### frmResume_confirm_send Try to send");
    frmResume_TryToSend();
  }
}

function frmResume_TryToSend(){
  voltmx.print("#### frmResume_TryToSend CaseData: " + JSON.stringify(CaseData));
  //check of er al uitkomsten zijn gezet (als je in de afhandellijst taskoutcome hebt gekozen)
  voltmx.print("### frmResume_TryToSend Global.vars.handleCharacteristicType: " + Global.vars.handleCharacteristicType);
  //set photoStatus to done before sending
  Global.vars.needToUploadPhotos = false;
  for(var k in CaseData.multimedia){
    var x = CaseData.multimedia[k];
    if(x.photoStatus == "draft"){
      x.photoStatus = "done";
    }
    //         if(x.uploaded === false){
    //           Global.vars.needToUploadPhotos = true;
    //         }
  }
  if(Global.vars.handleCharacteristicType != "OutcomeType" && Global.vars.handleCharacteristicType != "OutcomeTypeCategory"){
    //zet nu de default uitkomst
    voltmx.print("### frmResume_TryToSend Global.vars.defaultTaskOutcomeFrmResume: " + Global.vars.defaultTaskOutcomeFrmResume);
    var lTaskTypeClause = Utility_addTimelineToWhereClauseObjectSync("select id from mle_v_task_type_msv where identification = '" + CaseData.processinfo.activeTaskType + "'",CaseData.time.dateComponents);
    var wcs = "select * from mle_v_outcome_type_m where identification = '" + Global.vars.defaultTaskOutcomeFrmResume + "' and tte_id in (" + lTaskTypeClause + ")";
    if(CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("MLD_PV_")){
      wcs = "select * from mle_v_outcome_type_m where identification = '" + Global.vars.defTaskOutcomeFrmResumeBevinding + "' and tte_id in (" + lTaskTypeClause + ")";
    } else if(CaseData.caseinfo.caseType != null && CaseData.caseinfo.caseType.startsWith("MLD_TIK_")){
      wcs = "select * from mle_v_outcome_type_m where identification = '" + Global.vars.defTaskOutcomeFrmResumeTikVerbaal + "' and tte_id in (" + lTaskTypeClause + ")";
    } else if(CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("CTE_") || CaseData.caseinfo.caseType.startsWith("MLD_"))){
      wcs = "select * from mle_v_outcome_type_m where identification = '" + Global.vars.defTaskOutcomeFrmResumeHoreca + "' and tte_id in (" + lTaskTypeClause + ")";
    }
    if(CaseData.processinfo.activeTaskType == "CorrigerenKandidaat" && (CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("KRV_") || CaseData.caseinfo.caseType.startsWith("KVV_") || CaseData.caseinfo.caseType.startsWith("SRV_") || CaseData.caseinfo.caseType.startsWith("SVV_")))){
      wcs = "select * from mle_v_outcome_type_m where identification = '" + Global.vars.defTaskOutcomeCorrectCandidate + "' and tte_id in (" + lTaskTypeClause + ")";
    }else if(CaseData.processinfo.activeTaskType == "VersturenVerbod" && (CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("KRV_") || CaseData.caseinfo.caseType.startsWith("KVV_") || CaseData.caseinfo.caseType.startsWith("SRV_") || CaseData.caseinfo.caseType.startsWith("SVV_")))){
      wcs = "select * from mle_v_outcome_type_m where identification = '" + Global.vars.defTaskOutcomeSendProhibition + "' and tte_id in (" + lTaskTypeClause + ")";
    }if(CaseData.caseinfo.caseType != null && (CaseData.caseinfo.caseType.startsWith("DVV_") || CaseData.caseinfo.caseType.startsWith("DRV_"))){
      wcs = "select * from mle_v_outcome_type_m where identification = '" + Global.vars.defTaskOutcomeProhibitions + "' and tte_id in (" + lTaskTypeClause + ")";
    }
    if((activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi")){
      voltmx.print("### frmResume_TryToSend Global.vars.outcomeTypes: " + JSON.stringify(Global.vars.outcomeTypes));
      if(Global.vars.outcomeTypes.length === 0){
        wcs = "select * from mle_v_outcome_type_m where identification = 'appendTicketDone' and tte_id in (" + lTaskTypeClause + ")";
        wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
        wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
      }
    }
    voltmx.print("### frmResume_TryToSend find outcomtype wcs: " + wcs);
    function confirm_rightsNotRead(){
      var _unhandledTaskTypes = Utility_getIndexIfObjWithAttr(Global.vars.unhandledTaskTypes,"taskType",CaseData.processinfo.activeTaskType);
   	  var _caseType = " ";
      if (CaseData.caseinfo != null && CaseData.caseinfo.caseType != null){
        _caseType = CaseData.caseinfo.caseType;
      }
      voltmx.print("### frmResume_TryToSend confirm_rightsNotRead find outcomtype Global.vars.outcomeTypes: " + JSON.stringify(Global.vars.outcomeTypes));
      if((Global.vars.outcomeTypes.length > 1 && (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV")) && (CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod" || CaseData.processinfo.activeTaskType == "CorrigerenVerbod" || CaseData.processinfo.activeTaskType == "OverdrachtPolitie")){
        //get selected taskoutcome
        voltmx.print("### frmResume_TryToSend confirm_rightsNotRead set Taskoutcome more then 1");
        if(Global.vars.selectedOutcome != null){
          frmResume_setTaskoutcomeToActiveTask(Global.vars.selectedOutcome);
        }else{
          alert(voltmx.i18n.getLocalizedString("l_chooseTaskOutcome"));
        }
      }else if((Global.vars.outcomeTypes.length === 1 || Global.vars.buildFor == "NS") && (activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi" || CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod" || CaseData.processinfo.activeTaskType == "OverdrachtPolitie" 
                                                                                           || (_caseType.startsWith("MLD_") && Global.vars.backToPreviousForm == "frmFollow") || _unhandledTaskTypes !== -1)){
        //set taskoutcome
        voltmx.print("### frmResume_TryToSend confirm_rightsNotRead set Taskoutcome 1 or NS");
        frmResume_setTaskoutcomeToActiveTask(Global.vars.outcomeTypes[0]);
      }else if((Global.vars.outcomeTypes.length > 1 && Global.vars.buildFor !== "NS") && (activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi" || (_caseType.startsWith("MLD_") && Global.vars.backToPreviousForm == "frmFollow") || _unhandledTaskTypes !== -1)){
        //get selected taskoutcome
        voltmx.print("### frmResume_TryToSend confirm_rightsNotRead set Taskoutcome more then 1");
        if(Global.vars.selectedOutcome != null){
          frmResume_setTaskoutcomeToActiveTask(Global.vars.selectedOutcome);
        }else{
          alert(voltmx.i18n.getLocalizedString("l_chooseTaskOutcome"));//Kies een taak uitkomst
        }
      }else{
        voltmx.print("### frmResume_TryToSend confirm_rightsNotRead no taskoutcome search");
        KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmResume_getDefaultOutcomeForTaskScreenSuccessCallback, frmResume_getDefaultOutcomeForTaskScreenErrorCallback);
      }
    }
    if(CaseData.offence.rightsRead === false && Global.vars.cancelAfterRightsReadFalse === true && CaseData.offence.person === true){
      voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_rightsNotReadConfirmation"),
                    confirm_rightsNotRead,
                    "confirmation",
                    voltmx.i18n.getLocalizedString("bt_yes"),
                    voltmx.i18n.getLocalizedString("bt_no"),
                    "Info",
                    null);
    }else{
      confirm_rightsNotRead();
    }
  }else{
    Global.vars.processinfo_lastTaskProcessed.taskCompletedOn = Utility_getUTCJavascriptDate(null);
    CaseData.caseinfo.timeComplete = Global.vars.processinfo_lastTaskProcessed.taskCompletedOn;
    CaseData.caseinfo.indComplete = true;
    CaseData.processinfo.lastTaskProcessed = Global.vars.processinfo_lastTaskProcessed;
    Utility_setDeviceLocationToCase(frmResume_setDeviceLocationToCaseCallback);
  }
}


function frmResume_setTaskoutcomeToActiveTask(selectedTaskObject){
  voltmx.print("### frmResume_setTaskoutcomeToActiveTask selectedTaskObject: " + JSON.stringify(selectedTaskObject));
  if(selectedTaskObject !== undefined && selectedTaskObject != null){
//     CaseData.processinfo.tasks.sort(function(a, b) {
//       return (a.taskCreatedOn===null)-(b.taskCreatedOn===null) || -(a.taskCreatedOn>b.taskCreatedOn)||+(a.taskCreatedOn<b.taskCreatedOn);
//     });
    for(var i = CaseData.processinfo.tasks.length; i--;){
      var v = CaseData.processinfo.tasks[i];
      if(v.taskType == CaseData.processinfo.activeTaskType && (v.taskCompletedOn === "" || v.taskCompletedOn === null)){
        v.taskOutcome = selectedTaskObject.identification;
        v.taskOutcomeId = selectedTaskObject.id;
        v.taskOutcomeDescription = selectedTaskObject.description;
        v.taskCompletedOn = Utility_getUTCJavascriptDate(null);
        v.taskClaimedBy = Global.vars.gUsername;
        v.taskClaimedByName = CaseData.caseinfo.officerName;
        voltmx.print("### frmResume_setTaskoutcomeToActiveTask CaseData.processinfo.activeTaskType: " + CaseData.processinfo.activeTaskType);
        voltmx.print("### frmResume_setTaskoutcomeToActiveTask CaseData.prohibitions.policeOfficerEmail: " + CaseData.prohibitions.policeOfficerEmail);
        voltmx.print("### frmResume_setTaskoutcomeToActiveTask CaseData.prohibitions.policeOfficerMobile: " + CaseData.prohibitions.policeOfficerMobile);
        if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" && frmResume.policeofficeremail.txtInputText.text !== ""){
          if(v.taskOutcomeExplanation !== undefined && v.taskOutcomeExplanation != null){
            v.taskOutcomeExplanation = v.taskOutcomeExplanation + " " + frmResume.policeofficeremail.txtInputText.text;
          }else{
            v.taskOutcomeExplanation = CaseData.prohibitions.policeOfficerEmail;
          }
          if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" && frmResume.policeofficermobile.txtInputText.text !== ""){
            if(v.taskOutcomeExplanation !== undefined && v.taskOutcomeExplanation != null){
              v.taskOutcomeExplanation = v.taskOutcomeExplanation + " " + frmResume.policeofficermobile.txtInputText.text;
            }
          }
        }
        CaseData.caseinfo.indComplete = true;
  	  	CaseData.caseinfo.timeComplete = v.taskCompletedOn;
        CaseData.processinfo.lastTaskProcessed = v;
        break;
      }
    }
    voltmx.print("#### frmResume_setTaskoutcomeToActiveTask CaseData: " + JSON.stringify(CaseData));
    Utility_setDeviceLocationToCase(frmResume_setDeviceLocationToCaseCallback);
  }else{
    voltmx.application.dismissLoadingScreen();
    voltmx.print("#### frmResume_setTaskoutcomeToActiveTask not taskoutcome found");
    alert(voltmx.i18n.getLocalizedString("l_taskoutcomeNotFound"));
  }
}

function frmResume_confirm_rightsNotRead(response){
  if(response){
    var lTaskTypeClause = Utility_addTimelineToWhereClauseObjectSync("select id from mle_v_task_type_msv where identification = '" + CaseData.processinfo.activeTaskType + "'",CaseData.time.dateComponents);
    var wcs = "select * from mle_v_outcome_type_m where identification = '" + Global.vars.defaultTaskOutcomeRightsReadFalse + "' and tte_id in (" + lTaskTypeClause + ")";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmResume_getDefaultOutcomeForTaskScreenSuccessCallback, frmResume_getDefaultOutcomeForTaskScreenErrorCallback);
  }
}

function frmResume_getDefaultOutcomeForTaskScreenErrorCallback(error){
  voltmx.print("### frmResume_getDefaultOutcomeForTaskScreenErrorCallback error: " + JSON.stringify(error));
  voltmx.application.dismissLoadingScreen();
  alert("" + error);
}

function frmResume_getDefaultOutcomeForTaskScreenSuccessCallback(result){
  voltmx.print("### frmResume_getDefaultOutcomeForTaskScreenSuccessCallback result: " + JSON.stringify(result));
  if(result.length > 0){
//     CaseData.processinfo.tasks.sort(function(a, b) {
//       return (a.taskCreatedOn===null)-(b.taskCreatedOn===null) || -(a.taskCreatedOn>b.taskCreatedOn)||+(a.taskCreatedOn<b.taskCreatedOn);
//     });
    for(var i = CaseData.processinfo.tasks.length; i--;){
      var v = CaseData.processinfo.tasks[i];
      if(v.taskType == CaseData.processinfo.activeTaskType && (v.taskCompletedOn === "" || v.taskCompletedOn === null)){
        v.taskOutcome = result[0].identification;
        v.taskOutcomeId = result[0].id;
        v.taskOutcomeDescription = result[0].description;
        v.taskCompletedOn = Utility_getUTCJavascriptDate(null);
        v.taskClaimedBy = Global.vars.gUsername;
        v.taskClaimedByName = CaseData.caseinfo.officerName;
        //CaseData.processinfo.activeTaskType = "";
        CaseData.caseinfo.indComplete = true;
  	  	CaseData.caseinfo.timeComplete = v.taskCompletedOn;
        if(result[0].steidsucceeding !== undefined){
          //CaseData.statuscode = result[0].steidsucceeding;
          //Utility_setStatus(result[0].steidsucceeding);
        }
        CaseData.processinfo.lastTaskProcessed = v;
        break;
      }
    }
    voltmx.print("#### frmResume_getDefaultOutcomeForTaskScreenSuccessCallback CaseData: " + JSON.stringify(CaseData));
    Utility_setDeviceLocationToCase(frmResume_setDeviceLocationToCaseCallback);
  }else{
    voltmx.application.dismissLoadingScreen();
    alert(voltmx.i18n.getLocalizedString("l_taskoutcomeNotFound"));
  }
}

function frmResume_setDeviceLocationToCaseCallback(){
  voltmx.print("### frmResume_setDeviceLocationToCaseCallback Global.vars.appMode: " + Global.vars.appMode);
  voltmx.print("### frmResume_setDeviceLocationToCaseCallback");
  frmResume_showLoaderUploadCase();
  //set options variables if available
  if(Global.vars.optionvariablesSet === true){
    var loctextindex = null;
    for (var p=0; ((CaseData.text) != null) && p < CaseData.text.length; p++ ){
      var w = CaseData.text[p];
      if((w.type == 4 && voltmx.string.startsWith(w.value, "Beschrijving document(en): ") === false)){ //opmerking verbalisant
        voltmx.print("#### frmResume_setDeviceLocationToCaseCallback: present: " + w + " index: " + p);
        loctextindex = p;
        break;
      }
    }
    var laddrecord = CaseData_setNewtext();
    laddrecord.inserted = true;
    laddrecord.edited = true;
    laddrecord.type = 4; //opmerking verbalisant
    laddrecord.value = Global.vars.optionvariablesText;
    if (loctextindex != null) {
//       if(CaseData.text[loctextindex].value != null && CaseData.text[loctextindex].value !== ""){
//         laddrecord.value = Global.vars.optionvariablesText + "\r\n" + CaseData.text[loctextindex].value;
//       }
      CaseData.text.splice(loctextindex,1,laddrecord);
    }else{
      CaseData.text.splice(0,0,laddrecord);
    }
    voltmx.print("#### frmResume_setDeviceLocationToCaseCallback CaseData.text after: " + JSON.stringify(CaseData.text)); 
  }
  //save CaseData for frmRegister
  Global.vars.copyCaseData = JSON.parse(JSON.stringify({"person":CaseData.person,"vehicle":CaseData.vehicle,"time":CaseData.time,"location":CaseData.location,"text":CaseData.text, "setpastebutton":true, "personInputMethod":Global.vars.personInputMethod}));
  // RL-11633 START change vehicle indication from PS to 99 if Water offence
  if ((CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType == 'PS')
      && (CaseData.offence.offenceCode != null && voltmx.string.startsWith(CaseData.offence.offenceCode, "W"))){
    voltmx.print("### frmResume getVehicleTypeDescription pre: " + JSON.stringify(CaseData.vehicle[Global.vars.gCaseVehiclesIndex]));
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType = '99';
    var wcs = "select * from mle_v_vehicle_type_m where code = '" + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType + "'";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
    wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
    voltmx.print("### frmResume getVehicleTypeDescription wcs: " + JSON.stringify(wcs));
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, getVehicleTypeDescriptionSuccesCallback, getVehicleTypeDescriptionErrorCallback);

    function getVehicleTypeDescriptionErrorCallback(error){
      voltmx.print("### getVehicleTypeDescriptionErrorCallback: " + JSON.stringify(error));
      CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc = "Overige vaartuigen";
    }

    function getVehicleTypeDescriptionSuccesCallback(result){
      voltmx.print("### getVehicleTypeDescriptionSuccesCallback: " + JSON.stringify(result));
      if(result.length > 0){
        CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc = result[0].description;
      }
    }
    voltmx.print("### frmResume getVehicleTypeDescription post: " + JSON.stringify(CaseData.vehicle[Global.vars.gCaseVehiclesIndex]));
  }
  if ((CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType == '99')
      && (CaseData.offence.offenceCode != null && voltmx.string.startsWith(CaseData.offence.offenceCode, "W"))){
    if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber == null){
      CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense = null;
      CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode = null;
      CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseDesc = null;
    }
  }
  // RL-11633 END
  // part of RL-15110
  // delete statement if offence person eq false 
  Utility_resetOffencePerson();
  //
  frmResume_RegisterCase();
}

function frmResume_RegisterCase(){
  voltmx.print("#### frmResume_RegisterCase");
  if(Global.vars.gInstanceId === "NL0036" || Global.vars.gInstanceId === "RL0005"){
    voltmx.print("#### frmResume_RegisterCase ROTTERDAM SPECIFIEKE CODE MOET ER LATER UIT!!!!!!!");
    //try to dertermine CaseType and Category
    var caseType = "";
    if(CaseData.offence !== undefined && CaseData.offence.offenceCode !== undefined && CaseData.offence.offenceCode != null && CaseData.offence.offenceCode !== "" && (CaseData.caseinfo.caseTypeCategory === "mh" || CaseData.caseinfo.caseTypeCategory === "bsbh")){
      if(CaseData.caseinfo.caseTypeCategory === "mh"){ //voltmx.string.startsWith(CaseData.offence.offenceCode, "R")){
        caseType = "MH_" + CaseData.offence.offenceCode;
      }else if(CaseData.caseinfo.caseTypeCategory === "bsbh"){
        caseType = "BSBH_" + CaseData.offence.offenceCode;
      }
      voltmx.print("#### frmResume_RegisterCase Utility_setCaseTypeAndCategory");
      Utility_setCaseTypeAndCategory(caseType, frmResume_saveUploadMessage, frmResume_FindCaseTypeFailed);
    }else{
      voltmx.print("#### frmResume_RegisterCase Utility_saveUploadCaseData 1");
      Utility_saveUploadCaseData(CaseData, frmResume_RegisterCaseFinish);
    }
  }else{
    voltmx.print("#### frmResume_RegisterCase Utility_saveUploadCaseData 2");
    if (btnPrintResume === true){
      btnPrintResume = false;
      if ((Global.vars.handleCharacteristicType == "OutcomeType" || Global.vars.handleCharacteristicType == "OutcomeTypeCategory") || (CaseData.caseinfo.caseType != null &&  CaseData.caseinfo.caseType.startsWith("MLD_"))){
        Utility_saveUploadCaseData(CaseData, frmResume_onclick_btnPrintReport);
      } else {
        Utility_saveUploadCaseData(CaseData, frmResume_onclick_btnPrintResume);
      }
    } else {
      Utility_saveUploadCaseData(CaseData, frmResume_RegisterCaseFinish);
    }
  }
}

function frmResume_saveUploadMessage(){
  voltmx.print("### frmResume_saveUploadMessage");
  Utility_saveUploadMessage(CaseData, "startProces", frmResume_RegisterCaseFinish);
}

function frmResume_FindCaseTypeFailed(){
  voltmx.print("### frmResume_FindCaseTypeFailed");
  Utility_saveUploadCaseData(CaseData, frmResume_RegisterCaseFinish);
}

function frmResume_RegisterCaseFinish(){
  voltmx.print("### frmResume_RegisterCaseFinish");
  //
  if(Global.vars.appMode !== voltmx.i18n.getLocalizedString("l_trackDown")){
    Utility_doServerCalls();
  }
  frmResume_FinishUpload();
}

function frmResume_RegisterCaseFailed(){
  voltmx.print("### frmResume_RegisterCaseFailed");
  frmResume_dismissLoaderUploadCase();
  voltmx.application.dismissLoadingScreen();
  frmResume_FinishUpload();
}

function frmResume_RegisterCaseRetry(response){
  voltmx.print("### frmResume_RegisterCaseRetry");
  if(response){
    frmResume_RegisterCase();
  }
}

function frmResume_FinishUpload(){
  voltmx.print("### frmResume_FinishUpload");
  voltmx.runOnMainThread(frmResume_FinishUpload_mainThread,[]);
}


function frmResume_FinishUpload_mainThread(){
  voltmx.print("### frmResume_FinishUpload_mainThread Global.vars.appMode: " + Global.vars.appMode);
  frmResume.btnBack.setEnabled(false);
  try{
    voltmx.timer.cancel("FinishUpload");
  }catch(err){}
  //reset CaseData and go to beginning
  //init caseData
  //frmResume_deleteDocumentOffline();
  frmOffenceSelect_clearSearchText();
  //clear photos
  frmResume_clearMultimedia();
  //clear internal remark
  frmResume.internalremark.lblText.text = "";
  //
  frmResume_resetAllFields();
  frmResume_resetHistory();
  //#ifdef android
  frmResume.mapdisplay.mapLocation.clear();
  //#endif
  frmResume.flcLayoutInput.setEnabled(true);
  if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_trackDown")){
    voltmx.print("### frmResume_FinishUpload_mainThread frmTrackDown_resetApp");
  	// reset Vehicle Country to default;
//   	frmTrackDown_resetVehicleCountry();
	frmTrackDown_resetApp(false);
    voltmx.application.dismissLoadingScreen();
    //frmTrackDown.destroy();
    frmTrackDown.show();
  	frmResume.btnBack.setEnabled(true);
  } else if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer" || Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV"){
	voltmx.print("### frmResume_FinishUpload_mainThread frmRegister_resetApp");
  	frmRegister_resetApp(false); // om te zorgen dat er niet 2 keer gps bevraagd wordt op false zetten
    voltmx.application.dismissLoadingScreen();
    //frmRegister.destroy();
    frmRegister.show();
  	frmResume.btnBack.setEnabled(true);
  } else if(Global.vars.appMode == voltmx.i18n.getLocalizedString("appmode_registerconcept")){
	voltmx.print("### frmResume_FinishUpload_mainThread frmRegisterconcept_resetApp");
  	frmRegisterConcept_resetApp(false); // om te zorgen dat er niet 2 keer gps bevraagd wordt op false zetten
    voltmx.application.dismissLoadingScreen();
    frmRegisterConcept.destroy();
    frmRegisterConcept.show();
  	frmResume.btnBack.setEnabled(true);
  } else {
    voltmx.print("### frmResume_FinishUpload_mainThread else");
  	Global_resetApp();
    Utility_doServerCalls();
    voltmx.application.dismissLoadingScreen();
    //frmFollow.destroy();
    //frmFollow.show();
    frmOverviewTask.show();
    frmResume.btnBack.setEnabled(true);
  }
}

function frmResume_showSetText(kindoftext) {
  	voltmx.print("### frmResume_showSetText kindoftext: " + kindoftext);
    try {
      frmResume_settext2_flcCopy_setVisibility(false);
      frmResume_settext2_flcPaste_setVisibility(false);
      frmResume_settext2_flcCopyPaste_setVisibility(false);
      frmResume.settext2.textarea.lblTextHeader.text = kindoftext;
      frmResume.settext2.textarea.TextAreaText.text = "";
      if(kindoftext == frmResume.findings.lblHeader.text){
        frmResume.settext2.textarea.TextAreaText.text = frmResume.findings.lblText.text;
      }else if(kindoftext == frmResume.observation.lblHeader.text){
        frmResume.settext2.textarea.TextAreaText.text = frmResume.observation.lblText.text;
      }else if(kindoftext == frmResume.internalremark.lblHeader.text){
        frmResume.settext2.textarea.TextAreaText.text = frmResume.internalremark.lblText.text;
      }else if(kindoftext == frmResume.locationdescription.lblHeader.text){
        if(frmResume.locationdescription.lblText.text != null && frmResume.locationdescription.lblText.text !== "" && frmResume.locationdescription.lblText.text !== voltmx.i18n.getLocalizedString("pl_locationDescription")){
          frmResume.settext2.textarea.TextAreaText.text = frmResume.locationdescription.lblText.text;
        }
      }else if(kindoftext == frmResume.option.lblHeader.text){
        frmResume.settext2.textarea.TextAreaText.text = Global.vars.optionvariablesText;
        if(Global.vars.copyPasteCompleteOptionText === true){
          voltmx.print("### frmResume_showSetText options variables laat de knop kopieer en eventueel bewaar gehele tekst zien");
          voltmx.print("### frmResume_showSetText options variables Global.vars.copiedCompleteOptionText: " + Global.vars.copiedCompleteOptionText);
          frmResume_settext2_flcCopyPaste_setVisibility(true);
          frmResume_settext2_flcCopy_setVisibility(true);
          var pastetext = JSON.parse(Global.vars.copiedCompleteOptionText);
          if(Global.vars.copiedCompleteOptionText !== undefined && Global.vars.copiedCompleteOptionText != null && Global.vars.copiedCompleteOptionText !== "" && ((new Date(pastetext.date).toDateString()) == (new Date()).toDateString())){
            frmResume_settext2_flcPaste_setVisibility(true);
            frmResume.settext2.btnCopy.width = "92%";
            frmResume.settext2.flcCopy.width = "50%";
            frmResume.settext2.flcCopy.centerX = "25%";
          }else{
            frmResume_settext2_flcPaste_setVisibility(false);
            frmResume.settext2.btnCopy.width = "96%";
            frmResume.settext2.flcCopy.width = "100%";
            frmResume.settext2.flcCopy.centerX = "50%";
          }
        }
      }
      voltmx.print("### frmResume_showSetText: " + kindoftext);
      //deactivate footer and mainpage
      frmResume.flcMainPage.setEnabled(false);
	  voltmx.print("### flcMainPage disabled");
      frmResume_settext2_setVisibility(true);
      frmResume_showSetText_preAnim();
      frmResume_showSetText_animationStart();
    } catch (e) {
      voltmx.print("### frmResume_showSetText error: " + JSON.stringify(e));
    }
}

function frmResume_showSetText_preAnim() {
  try {
    voltmx.print("### frmResume_showSetText_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
//     frmResume.settext2.flcDetail.transform = trans1;
//     frmResume.settext2.imgPopupLogo1.transform = trans1;
//     frmResume.settext2.flcTextDetails.transform = trans1;
  } catch (e) {
    voltmx.print("### frmResume_showSetText_preAnim error: " + JSON.stringify(e));
  }
}

function frmResume_showSetText_arrangeWidgets() {
  try {
    voltmx.print("### frmResume_showSetText_arrangeWidgets");
    //popup fields
    frmResume.settext2.imgPopupLogo1.isVisible = false;
    frmResume.settext2.flcDetail.isVisible = false;
    frmResume.settext2.flcTextDetails.isVisible = false;
    frmResume.settext2.lbl1.isVisible = false;
    frmResume.settext2.flcFooterSetText.isVisible = false;
    frmResume_settext2_setVisibility(false);
    frmResume.settext2.flcFooterSetText.setEnabled(false);
    frmResume.settext2.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetText_preAnim error: " + JSON.stringify(e));
  }

}

function frmResume_showSetText_animationStart(eventobject) {
  try {
    voltmx.print("### frmResume_showSetText_animationStart");
    frmResume_settext2_setVisibility(true);
    frmResume.settext2.flcDetail.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmResume.settext2.flcDetail.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmResume_showSetText_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmResume_showSetText_animationStart error: " + JSON.stringify(e));
  }
}

function frmResume_showSetText_animLogo() {
  try {
    voltmx.print("### frmResume_showSetText_animLogo");
//     var trans = voltmx.ui.makeAffineTransform();
//     trans.scale(1.2, 1.2);
//     frmResume.settext2.imgPopupLogo1.animate(
//       voltmx.ui.createAnimation({
//         "100": {
//           "anchorPoint": {
//             "x": 0.5,
//             "y": 0.5
//           },
//           "stepConfig": {
//             "timingFunction": voltmx.anim.EASE
//           },
//           "transform": trans,
//         }
//       }), {
//         "delay": 0,
//         "iterationCount": 1,
//         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
//         "duration": 0.25
//       }, {
//         "animationEnd": function (){
//           frmResume_showSetText_animOtherWidgets(frmResume.settext2.flcTextDetails);
//           frmResume_showSetText_animOtherWidgets(frmResume.settext2.lbl1);
//           frmResume_showSetText_animLogoBack();
//         }
//       });
    frmResume_showSetText_animOtherWidgets(frmResume.settext2.flcTextDetails);
    frmResume_showSetText_animOtherWidgets(frmResume.settext2.lbl1);
    frmResume.settext2.imgPopupLogo1.isVisible = true;
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetText_animLogo error: " + JSON.stringify(e));
  }
}


function frmResume_showSetText_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmResume_showSetText_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmResume.settext2.flcTextDetails.isVisible = true;
    frmResume.settext2.lbl1.isVisible = true;
    frmResume.settext2.flcFooterSetText.isVisible = true;
    frmResume.settext2.flcFooterSetText.setEnabled(true);
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetText_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmResume_showSetText_animLogoBack() {
  try {
    voltmx.print("### frmResume_showSetText_animLogoBack");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmResume.settext2.imgPopupLogo1.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetText_animLogoBack error: " + JSON.stringify(e));
  }
}

function frmResume_hideSetText(){
  //activate footer and mainpage
  frmResume.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmResume_contentOffset();
  frmResume_settext2_setVisibility(false);
}

function frmResume_setTextDone(){
  var hideSetText = true;
  if(frmResume.settext2.textarea.lblTextHeader.text == frmResume.findings.lblHeader.text){
    frmResume.findings.lblText.text = frmResume.settext2.textarea.TextAreaText.text;
    voltmx.print("#### frmResume_setTextDone CaseData text before: " + JSON.stringify(CaseData.text));
    voltmx.print("#### frmResume_setTextDone Text: " + frmResume.findings.lblText.text);
    if(frmResume.findings.lblText.text != null && frmResume.findings.lblText.text !== ""){
      // add record to CaseData.text
      var loctextindex = null;
      for (var p=0; ((CaseData.text) != null) && p < CaseData.text.length; p++ ){
        var v = CaseData.text[p];
        if((v.type == 4 && voltmx.string.startsWith(v.value, "Beschrijving document(en): ") === false)){ //opmerking verbalisant
          voltmx.print("#### frmResume_setTextDone: Finding officer present: " + v + " index: " + p);
          loctextindex = p;
          break;
        }
      }
      var laddrecord = CaseData_setNewtext();
      laddrecord.inserted = true;
      laddrecord.edited = true;
      laddrecord.type = 4; //opmerking verbalisant
      laddrecord.value = frmResume.findings.lblText.text;
      if (loctextindex === null) {
        CaseData.text.splice(0,0,laddrecord);
      } else {
        CaseData.text.splice(loctextindex,1,laddrecord);
      }
      voltmx.print("#### frmResume_setTextDone findings.lblText CaseData.text after: " + JSON.stringify(CaseData.text));
    }
  }else if(frmResume.settext2.textarea.lblTextHeader.text == frmResume.observation.lblHeader.text){
    frmResume.observation.lblText.text = frmResume.settext2.textarea.TextAreaText.text;
    voltmx.print("#### frmResume_setTextDone CaseData text before: " + JSON.stringify(CaseData.text));
    voltmx.print("#### frmResume_setTextDone Text: " + frmResume.observation.lblText.text);
    if(frmResume.observation.lblText.text != null && frmResume.observation.lblText.text !== ""){
      // add record to CaseData.text
      var loctextindexObservation = null;
      for (var k=0; ((CaseData.text) != null) && k < CaseData.text.length; k++ ){
        var w = CaseData.text[k];
        if((w.type == 1)){ //Overtredings gevens
        //if((w.type == 1)){ //Overtredings gevens
          voltmx.print("#### frmResume_setTextDone observation.lblText Text: Notice officer present: " + w + " index: " + k);
          loctextindexObservation = k;
          break;
        }
      }
      var laddrecordObservation = CaseData_setNewtext();
      laddrecordObservation.inserted = true;
      laddrecordObservation.edited = true;
      laddrecordObservation.type = 1; //Overtredings gevens
      laddrecordObservation.value = frmResume.observation.lblText.text;
      if (loctextindexObservation === null) {
        CaseData.text.splice(0,0,laddrecordObservation);
      } else {
        CaseData.text.splice(loctextindexObservation,1,laddrecordObservation);
      }
      voltmx.print("#### frmResume_setTextDone observation.lblText CaseData.text after: " + JSON.stringify(CaseData.text));
    }
  }else if(frmResume.settext2.textarea.lblTextHeader.text == frmResume.internalremark.lblHeader.text){
    frmResume.internalremark.lblText.text = frmResume.settext2.textarea.TextAreaText.text;
    voltmx.print("#### frmResume_setTextDone internalremark: " + frmResume.internalremark.lblText.text);
    voltmx.print("#### frmResume_setTextDone internalremark Global.vars.taskType: " + Global.vars.taskType);
    voltmx.print("#### frmResume_setTextDone internalremark CaseData all tasks before sort: " + JSON.stringify(CaseData.processinfo.tasks));
//     CaseData.processinfo.tasks.sort(function(a, b) {
//       return (a.taskCreatedOn===null)-(b.taskCreatedOn===null) || -(a.taskCreatedOn>b.taskCreatedOn)||+(a.taskCreatedOn<b.taskCreatedOn);
//     });
    voltmx.print("#### frmResume_setTextDone internalremark tasks after sort: " + JSON.stringify(CaseData.processinfo.tasks));
    for(var j = CaseData.processinfo.tasks.length; j--;){
  	  var x = CaseData.processinfo.tasks[j];
      if(x.taskType == Global.vars.taskType){//&& (v.taskCompletedOn === "" || v.taskCompletedOn === null)
        voltmx.print("#### frmResume_setTextDone internalremark found task");
        x.taskOutcomeExplanation = frmResume.internalremark.lblText.text;
        CaseData.processinfo.lastTaskProcessed = x;
        break;
      }
    }
    voltmx.print("#### frmResume_setTextDone internalremark CaseData all tasks: " + JSON.stringify(CaseData.processinfo.tasks));
    voltmx.print("#### frmResume_setTextDone internalremark CaseData lasttask: " + JSON.stringify(CaseData.processinfo.lastTaskProcessed));
    frmResume_validateShowFooter();
  }else if(frmResume.settext2.textarea.lblTextHeader.text == frmResume.locationdescription.lblHeader.text){
    voltmx.print("#### frmResume_setTextDone locationdescription");
    frmResume.locationdescription.lblText.text = frmResume.settext2.textarea.TextAreaText.text;
    if(frmResume.locationdescription.lblText.text === null || frmResume.locationdescription.lblText.text === ""){
      frmResume.locationdescription.lblText.text = voltmx.i18n.getLocalizedString("pl_locationDescription");
      frmResume.locationdescription.lblText.skin = lblFieldNotFilled;
      CaseData.location.locationDescription = null;
    } else {
      CaseData.location.locationDescription = frmResume.locationdescription.lblText.text;
      frmResume.locationdescription.lblText.skin = lblFieldInfo;
    }
  }else if(frmResume.settext2.textarea.lblTextHeader.text == frmResume.option.lblHeader.text){
    voltmx.print("#### frmResume_setTextDone locationdescription");
    if (frmResume.settext2.textarea.TextAreaText.text.length === 0 ){
      hideSetText = false;	
    } else {
      Global.vars.optionvariablesText = frmResume.settext2.textarea.TextAreaText.text;
      voltmx.print("### frmResume_setTextDone CaseData.option 2: " + JSON.stringify(CaseData.option));
      if(CaseData.option !== undefined && CaseData.option.length > 0){
        for(var c in CaseData.option){
          var d = CaseData.option[c];
          voltmx.print("### frmResume_setTextDone CaseData option d: " + JSON.stringify(d));
          if(d.offencecode == CaseData.offence.offenceCode){
            d.filledText = Global.vars.optionvariablesText;
          }
          voltmx.print("### frmResume_setTextDone CaseData option after replace d: " + JSON.stringify(d));
        }
      }  
    }
    
  }
  if (hideSetText === true){
  	frmResume_hideSetText();
  } else {
  	alert ("Type een tekst in.");  
  }
}

function frmResume_clearTextAreaText(){
  frmResume.settext2.textarea.TextAreaText.text = "";
  frmResume.settext2.textarea.TextAreaText.setFocus(true);
}

function frmResume_onclick_btnCopy(){
  voltmx.print("### frmResume_onclick_btnCopy text: " + frmResume.settext2.textarea.TextAreaText.text);
  //Global.vars.copiedCompleteOptionText = frmResume.settext2.textarea.TextAreaText.text;
  var copytext = {text:frmResume.settext2.textarea.TextAreaText.text,date:new Date()};
  Global.vars.copiedCompleteOptionText = JSON.stringify(copytext);
  voltmx.store.setItem("copiedCompleteOptionText", Global.vars.copiedCompleteOptionText);
  if(Global.vars.copiedCompleteOptionText !== undefined && Global.vars.copiedCompleteOptionText != null && Global.vars.copiedCompleteOptionText !== ""){
    frmResume_settext2_flcPaste_setVisibility(true);
    frmResume.settext2.btnCopy.width = "92%";
    frmResume.settext2.flcCopy.width = "50%";
    frmResume.settext2.flcCopy.centerX = "25%";
  }else{
    frmResume_settext2_flcPaste_setVisibility(false);
    frmResume.settext2.btnCopy.width = "96%";
    frmResume.settext2.flcCopy.width = "100%";
    frmResume.settext2.flcCopy.centerX = "50%";
  }
}

function frmResume_onclick_btnPaste(){
  voltmx.print("### frmResume_onclick_btnPaste copied text: " + Global.vars.copiedCompleteOptionText);
  if(Global.vars.copiedCompleteOptionText != null){
    var pastetext = JSON.parse(Global.vars.copiedCompleteOptionText);
    frmResume.settext2.textarea.TextAreaText.text = pastetext.text;
  }
}

function frmResume_showSetVehicleInfo() {
    Global.vars.regionCodeEditable = false;
    if (Global.vars.regionCodeEnabled === true && frmResume.regioncode.isVisible === true){
      Global.vars.regionCodeEditable = true;
    }
    if (Global.vars.enableParkiusMode === true && Global.vars.regionCodeEditable === false){
		frmResume_onclick_btnVehicleBrand();
    } else {
      try {
      voltmx.print("### frmResume_showSetVehicleInfo");
      voltmx.print("### frmResume_showSetVehicleInfo Global.vars.gCaseVehiclesIndex: " + Global.vars.gCaseVehiclesIndex);
      voltmx.print("### frmResume_showSetVehicleInfo CaseData.vehicle: " + JSON.stringify(CaseData.vehicle));
      if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate != null){
      	frmResume.setvehicleinfo.regioncodepopup.txtInputText.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate;
      }else{
        frmResume.setvehicleinfo.regioncodepopup.txtInputText.text = "";
      }
      voltmx.print("### frmResume_showSetVehicleInfo 1");
      if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].dupNumberPlate !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].dupNumberPlate != null){
       	frmResume.setvehicleinfo.duplicatepopup.txtInputText.text = Number(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].dupNumberPlate).toString();
      }else{
        frmResume.setvehicleinfo.duplicatepopup.txtInputText.text = "";
      }
        voltmx.print("### frmResume_showSetVehicleInfo 2");
      if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc != null){
        frmResume.setvehicleinfo.brandpopup.lblText.text = Utility_CapatilizeSentence(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc);
      }else{
        frmResume.setvehicleinfo.brandpopup.lblText.text = frmResume.brand.lblText.text;
      }
      voltmx.print("### frmResume_showSetVehicleInfo 3");
      if(frmResume.setvehicleinfo.brandpopup.lblText.text === voltmx.i18n.getLocalizedString("l_brand")){
        frmResume.setvehicleinfo.brandpopup.lblText.skin = lblFieldNotFilled;
      }else{
        frmResume.setvehicleinfo.brandpopup.lblText.skin = lblFieldInfo;
      }
      voltmx.print("### frmResume_showSetVehicleInfo 4");
      if (CaseData.vehicle[Global.vars.gCaseVehiclesIndex].indRegCalled !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].indRegCalled === true){
        frmResume_setvehicleinfo_brandpopup_setVisibility(false);
      } else {
        frmResume_setvehicleinfo_brandpopup_setVisibility(true);
      }
      voltmx.print("### frmResume_showSetVehicleInfo 5");
  	  //deactivate footer and mainpage
      frmResume.flcMainPage.setEnabled(false);
	  voltmx.print("### flcMainPage disabled");
      frmResume.forceLayout();
      frmResume.setvehicleinfo.isVisible=true;
      frmResume_showSetVehicleInfo_preAnim();
      frmResume_showSetVehicleInfo_animationStart();
    } catch (e) {
      voltmx.print("### frmResume_showSetVehicleInfo error: " + JSON.stringify(e));
    }
  }    
}

function frmResume_showSetVehicleInfo_preAnim() {
  try {
    voltmx.print("### frmResume_showSetVehicleInfo_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
    //frmResume.setvehicleinfo.flcDetailVehicle.transform = trans1;
    //frmResume.setvehicleinfo.imgPopupLogo2.transform = trans1;
    //frmResume.setvehicleinfo.flcVehicleInfoDetails.transform = trans1;
  } catch (e) {
    voltmx.print("### frmResume_showSetVehicleInfo_preAnim error: " + JSON.stringify(e));
  }
}

function frmResume_showSetVehicleInfo_arrangeWidgets() {
  try {
    voltmx.print("### frmResume_showSetVehicleInfo_arrangeWidgets");
    //popup fields
    frmResume.setvehicleinfo.imgPopupLogo2.isVisible = false;
    frmResume.setvehicleinfo.flcDetailVehicle.isVisible = false;
    frmResume.setvehicleinfo.flcVehicleInfoDetails.isVisible = false;
    frmResume.setvehicleinfo.lbl2.isVisible = false;
    frmResume.setvehicleinfo.flcFooterSetVehicleInfo.isVisible = false;
    frmResume.setvehicleinfo.isVisible = false;
    frmResume.setvehicleinfo.flcFooterSetVehicleInfo.setEnabled(false);
    frmResume.setvehicleinfo.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetVehicleInfo_preAnim error: " + JSON.stringify(e));
  }

}

function frmResume_showSetVehicleInfo_animationStart(eventobject) {
  try {
    voltmx.print("### frmResume_showSetVehicleInfo_animationStart");
    frmResume.setvehicleinfo.isVisible = true;
    frmResume.setvehicleinfo.flcDetailVehicle.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmResume.setvehicleinfo.flcDetailVehicle.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmResume_showSetVehicleInfo_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmResume_showSetVehicleInfo_animationStart error: " + JSON.stringify(e));
  }
}

function frmResume_showSetVehicleInfo_animLogo() {
  try {
    voltmx.print("### frmResume_showSetVehicleInfo_animLogo");
//     var trans = voltmx.ui.makeAffineTransform();
//     trans.scale(1.2, 1.2);
//     frmResume.setvehicleinfo.imgPopupLogo2.animate(
//       voltmx.ui.createAnimation({
//         "100": {
//           "anchorPoint": {
//             "x": 0.5,
//             "y": 0.5
//           },
//           "stepConfig": {
//             "timingFunction": voltmx.anim.EASE
//           },
//           "transform": trans,
//         }
//       }), {
//         "delay": 0,
//         "iterationCount": 1,
//         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
//         "duration": 0.25
//       }, {
//         "animationEnd": function (){
//           frmResume_showSetVehicleInfo_animOtherWidgets(frmResume.setvehicleinfo.flcVehicleInfoDetails);
//           frmResume_showSetVehicleInfo_animOtherWidgets(frmResume.setvehicleinfo.lbl2);
//           frmResume_showSetVehicleInfo_animLogoBack();
//         }
//       });
    frmResume_showSetVehicleInfo_animOtherWidgets(frmResume.setvehicleinfo.flcVehicleInfoDetails);
    frmResume_showSetVehicleInfo_animOtherWidgets(frmResume.setvehicleinfo.lbl2);
    frmResume.setvehicleinfo.imgPopupLogo2.isVisible = true;
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetVehicleInfo_animLogo error: " + JSON.stringify(e));
  }
}


function frmResume_showSetVehicleInfo_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmResume_showSetVehicleInfo_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmResume.setvehicleinfo.flcVehicleInfoDetails.isVisible = true;
    frmResume.setvehicleinfo.lbl2.isVisible = true;
    frmResume.setvehicleinfo.flcFooterSetVehicleInfo.isVisible = true;
    frmResume.setvehicleinfo.flcFooterSetVehicleInfo.setEnabled(true);
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetVehicleInfo_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmResume_showSetVehicleInfo_animLogoBack() {
  try {
    voltmx.print("### frmResume_showSetVehicleInfo_animLogoBack");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmResume.setvehicleinfo.imgPopupLogo2.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetVehicleInfo_animLogoBack error: " + JSON.stringify(e));
  }
}

function frmResume_hideSetVehicleInfo(){
  //activate footer and mainpage
  frmResume.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmResume_setvehicleinfo_setVisibility(false);
  frmResume_contentOffset();
  Global.vars.RegionDuplicatePopupSet = false;
}

function frmResume_setVehicleInfoDone(){
  if(frmResume.setvehicleinfo.regioncodepopup.txtInputText.text != null && frmResume.setvehicleinfo.regioncodepopup.txtInputText.text !== ""){
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate = frmResume.setvehicleinfo.regioncodepopup.txtInputText.text;
    frmResume.regioncode.lblText.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate;
  	frmResume.regioncode.lblText.skin = lblFieldInfo;
  	frmResume.regioncode.skin = flcFieldEdge;
  } else {
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].regionNumberPlate = null;
    frmResume.regioncode.lblText.text = voltmx.i18n.getLocalizedString("l_code");
    frmResume.regioncode.lblText.skin = lblFieldNotFilled;
  	frmResume.regioncode.skin = flcFieldEdgeRed;
  }
  if(frmResume.setvehicleinfo.duplicatepopup.txtInputText.text != null && frmResume.setvehicleinfo.duplicatepopup.txtInputText.text !== ""){
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].dupNumberPlate = Number(frmResume.setvehicleinfo.duplicatepopup.txtInputText.text);
    frmResume.duplicate.lblText.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].dupNumberPlate.toString();
    frmResume.duplicate.lblText.skin = lblFieldInfo;
  } else {
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].dupNumberPlate = null;
    frmResume.duplicate.lblText.text = voltmx.i18n.getLocalizedString("l_nr");
    frmResume.duplicate.lblText.skin = lblFieldNotFilled;
  }
  if(frmResume.setvehicleinfo.brandpopup.lblText.text != null && frmResume.setvehicleinfo.brandpopup.lblText.text !== ""){// && frmResume.setvehicleinfo.brandpopup.lblText.text !== voltmx.i18n.getLocalizedString("l_unknown")){
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc = frmResume.setvehicleinfo.brandpopup.lblText.text;
    frmResume.brand.lblText.text = frmResume.setvehicleinfo.brandpopup.lblText.text;
    if(frmResume.brand.lblText.text != null && frmResume.brand.lblText.text.length > 24){
      frmResume.brand.lblText.skin = lblFieldInfoSmall;
    }else{
      if(frmResume.brand.lblText.text != null && frmResume.location.lblText.text !== ""){
        frmResume.brand.lblText.skin = lblFieldInfo;
      }else{
        frmResume.brand.lblText.skin = lblFieldNotFilled;
        frmResume.brand.lblText.text = voltmx.i18n.getLocalizedString("l_brand");
      }
    }
  }
  frmResume_validateShowFooter();
  frmResume_hideSetVehicleInfo();
}

function frmResume_onDoneSetRegionCodeAndDuplicate(){
  Global.vars.RegionDuplicatePopupSet = true;
}

function frmResume_resetAllFields(){
  	voltmx.print("### frmResume_resetAllFields");
    frmResume.furtherlocationindication.lblText.text = "Overig";
    frmResume.locationspecification.lblText.text = voltmx.i18n.getLocalizedString("pl_locationSpec");
    frmResume.locationdescription.lblText.text = voltmx.i18n.getLocalizedString("pl_locationDescription");
    frmResume.locationdescription.lblText.skin = lblFieldNotFilled;
  	frmResume_locationspecification_setVisibility(false);
    frmResume_locationdescription_setVisibility(false);
    frmResume.location.lblText.text = "";
    frmResume.licenseplate.lblLicensePlate.text = "";
    frmResume.brand.lblText.text = "";
    frmResume.licenseplate.lblCountryCode.text = "";
    frmResume.time.lblTime.text = "";
    frmResume.duplicate.lblText.text = voltmx.i18n.getLocalizedString("l_nr");
    frmResume.setvehicleinfo.duplicatepopup.txtInputText.text = "";
    frmResume.offenceinfo.lblOffenceCategory.text = "";
    frmResume.offenceinfo.lblOffenceCode.text = "";
    frmResume.sanctioninfo.lblTotalAmount.text = "";
    frmResume.sanctioninfo.lblTotalAmountCurrency.text = "";
    frmResume.onstreetsanctioninfo.lblAdminCostsAmount.text = "";
    frmResume.onstreetsanctioninfo.lblAdminCostsCurrency.text = "";
    frmResume.onstreetsanctioninfo.lblTotalAmount.text = "";
    frmResume.onstreetsanctioninfo.lblTotalAmountCurrency.text = "";
    frmResume.observation.lblText.text = "";
    frmResume.findings.lblText.text = "";
    frmResume.lblStatus.text = "";
    frmResume.setvehicleinfo.regioncodepopup.txtInputText.text = "";
    frmResume.regioncode.lblText.text = voltmx.i18n.getLocalizedString("l_code");
    frmResume.settext2.textarea.TextAreaText.text = "";
    frmResume.setvehicleinfo.brandpopup.lblText.text = "";
    frmResume.person.lblText.text = voltmx.i18n.getLocalizedString("Person");
    frmResume.statement.lblText.text = voltmx.i18n.getLocalizedString("l_statement");
    frmResume.option.lblText.text = voltmx.i18n.getLocalizedString("l_optionsVariables");
    frmResume.internalremark.lblText.text = "";
    frmResume.secondofficer.lblText.text = voltmx.i18n.getLocalizedString("l_secondOfficer");
    frmResume.enforcementobject.lblText.text = voltmx.i18n.getLocalizedString("l_enforcementObject");
    frmResume.questions.lblText.text = "Vragen";
    frmResume.taskoutcome.lblText.text = voltmx.i18n.getLocalizedString("l_outcome");
    frmResume.taskoutcome.lblText.skin = lblFieldNotFilled;
    frmResume_option_setVisibility(false);
    Global.vars.options = [];
    Global.vars.optionvariablesText = "";
    Global.vars.optionvariablesSet = false;
    Global.vars.selectedOption = {};
    frmResume_history_setVisibility(false);
    frmResume.imgSignature.base64 = null;
    //frmResume.imgSignature.src = "empty.png";
    frmResume_checkForSignatureImage();
    frmResume.lblCaseType.text = "";
    frmResume.lblCaseTypeCategory.text = "";
    frmResume.lblProhibitionTimeFrom.text = "";
  	frmResume.lblProhibitionTimeTo.text = "";
    frmResume.rtxStations.text = "";
  	frmResume.policeofficeremail.txtInputText.text = "";
  	frmResume.policeofficermobile.txtInputText.text = "";
  	frmResume.offenderemail.txtInputText.text = "";
  	frmResume.offendertelephone.txtInputText.text = "";
  	frmResume.lblClampNumber.text = voltmx.i18n.getLocalizedString("l_nr");
    frmResume.txtClampNumberPopup.text = "";
    frmResume.lblTimeArrived.text = "";
    frmResume_personAdress_resetFields();
}

function frmResume_showLoaderUploadCase(){
  try{
    //voltmx.timer.schedule("frmResume_killRegularLoaders", frmResume_killRegularLoaders, 0.1, true);
  }catch(err){}
  voltmx.application.showLoadingScreen(lblLoader,
                                          voltmx.i18n.getLocalizedString("l_loading") + "...",
                                          "center",
                                          false,
                                          true,
                                      { enablemenukey : true, enablebackkey : true } );
  frmResume_uploadcase_setVisibility(true);
  frmResume.flcMainPage.setEnabled(false);
  //frmResume_move_Car(false);
}

function frmResume_killRegularLoaders(){
  if(frmResume.uploadcase.isVisible === true && voltmx.application.getCurrentForm().id == "frmResume"){
    voltmx.application.dismissLoadingScreen();
  }else{
    try{
      voltmx.timer.cancel("frmResume_killRegularLoaders");
    }catch(err){}
  }
}

function frmResume_dismissLoaderUploadCase(){
  //frmResume_move_Car(true);
  frmResume_uploadcase_setVisibility(false);
  frmResume.flcMainPage.setEnabled(true);
  voltmx.application.dismissLoadingScreen();
}

function frmResume_move_Car(endAnimation){
  //start position
  frmResume.imgLoader.right = 17 + 'dp';
  //move stick and point to right
  var move1OptionsAnimation = voltmx.ui.createAnimation({
      "100": {
                  "left" : null,
        		  "right" : 93+'dp',
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
  var move1OptionsSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 1.0
                  };
  	if(endAnimation !== true){
  		frmResume.imgLoader.animate(move1OptionsAnimation,move1OptionsSetting,{"animationEnd" : frmResume_move_Car});
	}else{
      	frmResume.imgLoader.animate(move1OptionsAnimation,move1OptionsSetting,{"animationEnd" : null});
    }
}

function frmResume_onclick_btnPerson(){
  voltmx.print("#### frmResume_onclick_btnPerson");
//   if((Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") && Global.vars.buildFor == 'NS' && CaseData.person !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].edited === true){
//     var personinfo = JSON.parse(JSON.stringify(CaseData.person[Global.vars.gCasePersonsIndex]));
//     Global.vars.originalPersonDocumentInfo.idenDocType = personinfo.idenDocType;
//     Global.vars.originalPersonDocumentInfo.idenDocTypeDesc = personinfo.idenDocTypeDesc;
//     Global.vars.originalPersonDocumentInfo.countryIdenDoc = personinfo.countryIdenDoc;
//     Global.vars.originalPersonDocumentInfo.countryIdenDocDesc = personinfo.countryIdenDocDesc;
//     Global.vars.originalPersonDocumentInfo.documentNumber = personinfo.documentNumber;
//     Global.vars.originalPersonDocumentInfo.documentTypeCheckable = personinfo.documentTypeCheckable;
//     Global.vars.originalPersonDocumentInfo.documentNumberChecked = personinfo.documentNumberChecked;
//     Global.vars.originalPersonDocumentInfo.documentNumberValid = personinfo.documentNumberValid;
//     var documentDescription = "";
//     for (var p=0; ((CaseData.text) != null) && p < CaseData.text.length; p++ ){
//       var v = CaseData.text[p];
//       if((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))){ //beschrijving documenten
//         voltmx.print("#### frmResume_onclick_btnPerson: description document: " + v + " index: " + p);
//         documentDescription = v.value;
//         documentDescription = documentDescription.replace("Beschrijving document(en): ", "");
//         break;
//       }
//     }
//     Global.vars.originalPersonDocumentInfo.documentAdditionalDescription = documentDescription;
//     Global.vars.originalPersonDocumentInfo.formToGoBackTo = "frmResume";
//     Global.vars.gCasePersons = JSON.parse(JSON.stringify(CaseData.person[Global.vars.gCasePersonsIndex]));
//     frmPersonDocument.show();
// 	//frmResume_showSetDocumentDescription();
//   }else{
    if(CaseData.person === undefined){
      var XMLObjectDetail = [];
      CaseData["person"] = XMLObjectDetail;
      Global.vars.gCasePersons = CaseData_setNewperson();
      CaseData.offence.person = true;
  //       if (Utility_isEmptyEnforcementObject(CaseData.enforcementObject) === false){
        Global.vars.openedFromResume = true;
        Global.vars.openedFromResumePreviousForm = Global.vars.previousForm;
//       }
      frmPerson.show();
    }else{
      if(CaseData.person[Global.vars.gCasePersonsIndex] !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].edited === true){
        Global.vars.gCasePersons = CaseData.person[Global.vars.gCasePersonsIndex];
        frmResume_setDataPerson();
      }else{
        Global.vars.gCasePersons = CaseData_setNewperson();
        CaseData.offence.person = true;
  //       if (Utility_isEmptyEnforcementObject(CaseData.enforcementObject) === false){
        Global.vars.openedFromResume = true;
        Global.vars.openedFromResumePreviousForm = Global.vars.previousForm;
//       }
		frmPerson.show();
      }
    }
//  }
}

function frmResume_onclick_btnStatement(){
  voltmx.print("### frmResume_onclick_btnStatement");
  if(CaseData.offence.person === false || CaseData.offence.person === null){
    alert(voltmx.i18n.getLocalizedString("l_noPersonRegistered"));
  }else if((activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi" || CaseData.processinfo.activeTaskType == "OverdrachtPolitie" || CaseData.processinfo.activeTaskType == "CorrigerenKandidaat")){
    voltmx.print("### frmResume_onclick_btnStatement statement may not be edited on when activeTasktype is EditTicket");
  }else{
    Global.vars.previousForm = "frmResume";
    Global.vars.toStatementFromForm = "frmResume";
  	frmStatement.show();
  }
}

function frmResume_swipeToDeletePersons(widgetRef, gestureInfo, context){
  if(gestureInfo.swipeDirection == 1){
    frmResume_swipeToDeletePersonsShowButtons(widgetRef, gestureInfo, context);
  }else if(gestureInfo.swipeDirection == 2){
    frmResume_swipeToDeletePersonsCancelButtons(widgetRef, gestureInfo, context);
  }
}

function frmResume_swipeToDeletePersonsShowButtons(widgetRef, gestureInfo, context) {
    var animationdefToDelete = {
        0: {
            left: 0
        },
        100: {
            left: -25 + '%'
        }
    };
    var animDef = voltmx.ui.createAnimation(animationdefToDelete);
    var animationConfig = {
        duration: 0.6,
        fillMode: voltmx.anim.FILL_MODE_FORWARDS,
        "iterationCount": 1
    };
    function onAnimStart() {
        Global.vars.personDeleteButtons = false;
    }
    function onAnimEnd() {
        Global.vars.personDeleteButtons = true;
    }
    var callBack = {
        animationStart: onAnimStart,
        animationEnd: onAnimEnd
    };
    widgetRef.animate(animDef, animationConfig, callBack);
}

function frmResume_swipeToDeletePersonsCancelButtons(widgetRef, gestureInfo, context) {
    var animationdefToDelete = {
        0: {
            left: -25 + '%'
        },
        100: {
            left: 0
        }
    };
    var animDef = voltmx.ui.createAnimation(animationdefToDelete);
    var animationConfig = {
        duration: 0.6,
        fillMode: voltmx.anim.FILL_MODE_FORWARDS,
        "iterationCount": 1
    };
    function onAnimStart() {
        Global.vars.personDeleteButtons = true;
    }
    function onAnimEnd() {
      	try{
  			voltmx.timer.schedule("setCancelGlobal",  frmResume_swipeToDeletePersonsCancelButtons_setGlobal, 0.2, false);
    	}catch(err){}
    }
    var callBack = {
        animationStart: onAnimStart,
        animationEnd: onAnimEnd
    };
    widgetRef.animate(animDef, animationConfig, callBack);
}

function frmResume_swipeToDeletePersonsCancelButtons_setGlobal(){
  try{
    voltmx.timer.cancel("setCancelGlobal");
  }catch(err){}
  Global.vars.personDeleteButtons = false;
}

function frmResume_togglePersonPopup(){
  	if(frmResume.flcPersonPopup.bottom == -200+'%'){
      frmResume.flcsLayout.setEnabled(false);
  	  frmResume.flcHeader.setEnabled(false);
      var mainAnimation = voltmx.ui.createAnimation({
      "100": {
                  "bottom" : 0+'%',
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
      var mainSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 0.4
                  };
  		frmResume.flcPersonPopup.animate(mainAnimation,mainSetting);
      	frmResume_flcButtonLeft_setVisibility(false);
    }else{
      	 var mainAnimation = voltmx.ui.createAnimation({
      "100": {
                  "bottom" : -200+'%',
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
      var mainSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 0.4
                  };
  		frmResume.flcPersonPopup.animate(mainAnimation,mainSetting);
      	frmResume_flcButtonLeft_setVisibility(true);
      	frmResume.flcsLayout.setEnabled(true);
  	  	frmResume.flcHeader.setEnabled(true);
    }
}

function frmResume_setDataPerson(){
  //Set person data
  frmResume.segPersons.widgetDataMap = {
//    lblVehiclePopupLicensePlateHeader: ,
    lblPersonPopupSSN: "ssn",
//    lblVehiclePopupCountryHeader: ,
    lblPersonPopupGender: "genderDesc",
//    lblVehiclePopupBrandMakeHeader: ,
    lblPersonPopupName: "fullName",
//    lblVehiclePopupColorHeader: ,
    lblPersonPopupNationality: "nationalityDesc",
//    lblVehiclePopupKindOfVehicleHeader: ,
    lblPersonPopupBirthDate: "birthdateDesc",
    lblHeaderText : "lblHeaderText"
  };
  var person = [];
  for(var i in CaseData.person){
    var v = CaseData.person[i];
    var fullname = "";
  	if (v.middlename === null || v.middlename === undefined || v.middlename.length === 0){
        fullname = v.givenNames + " " + v.surname;
      }
    else {
      	fullname = (v.givenNames + " " + v.middlename).trim(" ") + " " + v.surname;
    }
    v.fullName = fullname;
    v.lblHeaderText = "Persoon";
  }
  voltmx.print("### frmResume_setDataPerson CaseData.person: " +JSON.stringify(CaseData.person));
  if(CaseData.person[0].index != null){
    frmResume_flcFooterPersonPopup_flcRemovePerson_setVisibility(false);
    frmResume.flcFooterPersonPopup.flcCancelPersonPopup.width = "100%";
    frmResume.flcFooterPersonPopup.flcCancelPersonPopup.centerX = "50%";
    frmResume.segPersons.setData(CaseData.person);
    if(Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV" || Global.vars.validatePersonDocument === false){
      frmResume_flcFooterPersonPopup_flcShowPersonDocumentDetails_setVisibility(true);
      frmResume.flcFooterPersonPopup.flcCancelPersonPopup.width = "50%";
      frmResume.flcFooterPersonPopup.flcCancelPersonPopup.centerX = "25%";
    }else{
      frmResume_flcFooterPersonPopup_flcShowPersonDocumentDetails_setVisibility(false);
      frmResume.flcFooterPersonPopup.flcCancelPersonPopup.width = "100%";
      frmResume.flcFooterPersonPopup.flcCancelPersonPopup.centerX = "50%";
    }
  }else{
    frmResume.segPersons.removeAll();
  }
  //Set legalentity data
//   frmResume.segLegalEntities.widgetDataMap = {
//     lblLegalEntitiesPopupCoC: "LegalEntityCocNumber",
//     lblLegalEntitiesPopupName: "LegalEntityName",
//     lblLegalEntitiesPopupAddress: "address",
//     lblHeaderText : "lblHeaderText"
//   };
//   for(var j in gCase.legalentities){
//     var w = gCase.legalentities[j];
//     var address = "";
//   	if (w.Addresses[0].StreetNumAdditn === null || w.Addresses[0].StreetNumAdditn === ""){
//         address = w.Addresses[0].Street + " " + w.Addresses[0].StreetNumber;
//       }
//     else {
//       	address = w.Addresses[0].Street + " " + w.Addresses[0].StreetNumber + w.Addresses[0].StreetNumAdditn;
//     }
//     w.address = address;
//     w.lblHeaderText = "Rechtspersoon";
//   }
//   voltmx.print("### frmResume_setDataPerson gCase.legalentities: " +JSON.stringify(gCase.legalentities));
//   if(gCase.legalentities[0].index != null){
//     frmResume.segLegalEntities.setData(gCase.legalentities);
//   }else{
    frmResume.segLegalEntities.removeAll();
//  }
  //Show popup
  frmResume_togglePersonPopup();
}

function frmResume_btnAddNewPerson(){
  frmResume_togglePersonPopup();
  if(CaseData.person[0].index === null){
     Global.vars.gCasePersonsIndex = 0;
     Global.vars.gCasePersons = CaseData_setNewperson();
  }else{
    Global.vars.gCasePersonsIndex = CaseData.person.length;
    Global.vars.gCasePersons = CaseData_setNewperson(Global.vars.gCasePersonsIndex);
    CaseData.person.push(Global.vars.gCasePersons);
  }
  Global.vars.previousForm = "frmResume";
  frmPerson.show();
}

function frmResume_removePerson(context){
  	var selecteditem = context.widgetInfo.selectedRowItems[0];
  	voltmx.print("### frmResume_removePerson index: " + selecteditem.index);
  	voltmx.print("### frmResume_removePerson selected" + selecteditem);
    var SI = context.sectionIndex;
    var RI = context.rowIndex;
    //Delete the row
    context.widgetInfo.removeAt(RI, SI);
  	Global.vars.personDeleteButtons = false;
  	for(var i in CaseData.person){
      var v = CaseData.person[i];
      if(v.index == selecteditem.index){
        CaseData.person.splice(i, 1);
        break;
      }
    }
  	frmResume_setPersonLegalEntityLabel();
}

function frmResume_btnRemovePerson(){
  //only for removing all persons
  frmResume.segPersons.removeAll();
  Global.vars.personDeleteButtons = false;
  for(var i in CaseData.person){
    var v = CaseData.person[i];
    CaseData.person.splice(i, 1);
  }
  frmResume_setPersonLegalEntityLabel();
}

function frmResume_setPersonLegalEntityLabel(){
  	voltmx.print("### frmResume_setPersonLegalEntityLabel");
  	//set data
//   	if(gCase.legalentities.length === 0){
//       gCase.legalentities = [CaseUtil_getNewLegalEntity(null)];
//     }
  	if(CaseData.person.length === 0){
      CaseData.person = [CaseData_setNewperson(null)];
      Global.vars.gCasePersons = CaseData.person[Global.vars.gCasePersonsIndex];
      CaseData.offence.person = false;
      frmResume.person.lblText.text = voltmx.i18n.getLocalizedString("Person");
      frmResume.person.lblText.skin = lblFieldNotFilled;
  	  frmResume.statement.lblText.text = voltmx.i18n.getLocalizedString("l_statement");
      frmResume.statement.lblText.skin = lblFieldNotFilled;
      frmResume_statement_setVisibility(false);
      frmStatement_clearGlobalsPledge();
      frmStatement_clearCaseDataPledge();
    }
  	//set labels
//   	if(CaseData.person.length > 1 || gCase.legalentities.length > 1){
//       frmHandleReport.person.lblText.text = "Meerdere";
//     }else if(gCase.persons.length == 1 && gCase.persons[0].index != null && gCase.legalentities.length == 1 && gCase.legalentities[0].index != null){
//       frmHandleReport.person.lblText.text = "Meerdere";
//     }else if(gCase.persons[0].index != null){
//       frmHandleReport.person.lblText.text = gCase.persons[0].Surname;
//     }else if(gCase.legalentities[0].index != null){
//       frmHandleReport.person.lblText.text = gCase.legalentities[0].LegalEntityName;
//     }else{

//    }
  	//toggle popup
//   	if(frmHandleReport.segLegalEntities.data === null){
//       if(frmHandleReport.segPersons.data.length === 0){
//         frmHandleReport_togglePersonPopup();
//       }
//     }else
    if(frmResume.segPersons.data === undefined){
      //if(frmResume.segLegalEntities.data.length === 0){
        frmResume_togglePersonPopup();
      //}
    }else{
      if(frmResume.segPersons.data === null || frmResume.segPersons.data.length === undefined || frmResume.segPersons.data.length === 0){ //&& frmResume.segLegalEntities.data.length === 0){
        frmResume_togglePersonPopup();
      }
    }
}

function frmResume_onclicksegPersons(context){
  	voltmx.print("### frmResume_onclicksegPersons");
  	voltmx.print("### frmResume_onclicksegPersons context: " + JSON.stringify(context));
  	var selecteditem = context.widgetInfo.selectedRowItems[0];
 	voltmx.print("### frmResume_onclicksegPersons index: " + selecteditem.index);
  	voltmx.print("### frmResume_onclicksegPersons selected" + selecteditem);
  	gSelectedPersonIndex = selecteditem.index;
	try{
  		voltmx.timer.schedule("OpenPerson", frmResume_openPerson, 0.7, false);
    }catch(err){}
}

function frmResume_openPerson(){
  //alert(gSelectedVehicleIndex);
  try{
    voltmx.timer.cancel("OpenPerson");
  }catch(err){}
  if(Global.vars.personDeleteButtons === false){
    voltmx.application.showLoadingScreen(lblLoader,
                                          voltmx.i18n.getLocalizedString("l_loading") + "...",
                                          "center",
                                          false,
                                          true,
                                      { enablemenukey : true, enablebackkey : true } );
    for(var i in CaseData.person){
      var v = CaseData.person[i];
      if(v.index == gSelectedPersonIndex){
        Global.vars.gCasePersons = v;
        frmResume_togglePersonPopup();
    	Global.vars.openedFromResume = true;
        Global.vars.openedFromResumePreviousForm = Global.vars.previousForm;
		Global.vars.previousForm = "frmResume";
        frmPersonResult.show();
        break;
      }
    }
  }
}

//Parallex effect
var lastTopScroll = 0;

function frmResume_onScrolling() {
      try {
        voltmx.print("### Start frmResume_onScrolling");
        var scrollingPosition = frmResume.flcsLayout.contentOffsetMeasured;
        var topScroll = scrollingPosition.y;
        voltmx.print("### frmResume_onScrolling topScroll: " + topScroll);
        voltmx.print("### frmResume_onScrolling contentOffsetMeasured: " + frmResume.flcsLayout.contentOffsetMeasured);
        var down = false;
        if(topScroll > lastTopScroll){
          voltmx.print("### frmResume_onScrolling going up");
          lastTopScroll = topScroll;
          down = false;
        }else if(topScroll < lastTopScroll){
          voltmx.print("### frmResume_onScrolling going down");
          lastTopScroll = topScroll;
          down = true;
        }
        //#ifdef android
  		if (topScroll < 0 && down === true) {
          var scrollFactor = (((-1) * topScroll) / 100);
          var popImage = voltmx.ui.makeAffineTransform();
          popImage.scale(1 + scrollFactor, 1 + scrollFactor);
          frmResume.mapdisplay.animate(
            voltmx.ui.createAnimation({
              100 : {
                "stepConfig" : {
                  "timingFunction" : voltmx.anim.EASE
                },
                "transform" : popImage
              }
            }), {
              delay : 0,
              fillMode : voltmx.anim.FILL_MODE_FORWARDS,
              duration : 0.1
            }, {
              animationEnd : function () {}
            });
        } else if (topScroll > 0 || topScroll === 0) {
          voltmx.print("### frmResume_onScrolling set map to 44");
          frmResume.mapdisplay.top = 44 + (0 - topScroll * 0.3);
          frmResume.mapdisplay.parent.forceLayout();
          frmResume.flcsLayout.parent.forceLayout();
          if(topScroll === 0){
            frmResume_onScrollingEnd();
          }
        }
        //#endif
        voltmx.print("### frmResume_onScrolling End onScrollingFunction");
      } catch (e) {
        voltmx.print("### frmResume_onScrolling Error onScrollingFunction " + JSON.stringify(e));
      }
}

function frmResume_onScrollingEnd() {
      try {
        voltmx.print("### frmResume_onScrollingEnd Start onScrollingEndFunction");
        var scrollingPos = frmResume.flcsLayout.contentOffsetMeasured;
        var scrollingPosY = scrollingPos.y;
        voltmx.print("### frmResume_onScrollingEnd Start onScrollingEndFunction scrollingPosY: " + scrollingPosY);
        //if (scrollingPosY === 0) {
        //#ifdef android
        var defaultImage = voltmx.ui.makeAffineTransform();
        defaultImage.scale(1, 1);
        frmResume.mapdisplay.animate(
          voltmx.ui.createAnimation({
            100 : {
              "stepConfig" : {
                "timingFunction" : voltmx.anim.EASE
              },
              "transform" : defaultImage
            }
          }), {
            delay : 0,
            fillMode : voltmx.anim.FILL_MODE_FORWARDS,
            duration : 0.1
          }, {
            animationEnd : function () {}

          });
        frmResume.mapdisplay.parent.forceLayout();
        //#endif
          voltmx.print("### frmResume_onScrollingEnd End onScrollingEndFunction");
        //}
      } catch (e) {
        voltmx.print("### frmResume_onScrollingEnd Error onScrollingEndFunction " + JSON.stringify(e));
      }
}

//end of parallex effect

function frmResume_checkOption(){
  if(Utility_isEmptyObject(Global.vars.selectedOption) === false){
    if(Global.vars.selectedOption.offencecode !== CaseData.offence.offenceCode){
      voltmx.print("### frmResume_checkOption offenceCode: " + Global.vars.selectedOption.offencecode);
      Utility_clearOptions();
    }
  }else{
    voltmx.print("### frmResume_checkOption Global.vars.selectedOption is empty object: " + JSON.stringify(Global.vars.selectedOption));
    Utility_clearOptions();
  }
}

function frmResume_onclick_btnOptionVariables(){
  voltmx.print("### frmResume_onclick_btnOptionVariables");
  
// a. Optie 1 als nog niks is ingevuld en er is geen eerder vastgelegde reden van wetenschap is het dus gewoon invullen (geen Global)
// b. Optie 2 alleen inkijken reden van wetenschap en Nieuwe kunnen invullen Global.vars.readCompleteOptionText = true (dit hangt dan weer af of Global.vars.optionvariablesText gevuld is)
// c. Optie 3 is aanpassen van de gehele tekst Global.vars.seeAndEditCompleteOptionTextEditTicket = true met als extra mogelijkheid bewaar op klembord Global.vars.copyPasteCompleteOptionText = true (extra knop bewaar tekst op klembord)
// d. Optie 4 kunnen overnemen van eerder ingevulde reden van wetenschap en aanpassen Global.vars.fillAndEditOptionData = true
// e. Optie 5 plakken van gekopieerde in optie 3 complete tekst (extra knop: inzien en wijzigen van geplakte tekst - afhankelijk of er ook tekst geplakt is) Global.vars.copyPasteCompleteOptionText = true
  
  //iedereen heeft optie 1
  //voor iedereen geld optie 3 als activeTaskType EditTicket is
  var segDataFilledOptions = null;
  var previousdataFound = false;
  
  //check if editticket
  if((activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi")){
    voltmx.print("### frmResume_onclick_btnOptionVariables set editTicket globals");
    previousdataFound = true;
    Global.vars.seeAndEditCompleteOptionTextEditTicket = true; //optie 3
  }else{
    Global.vars.seeAndEditCompleteOptionTextEditTicket = false;
  }
  if(Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV"){
    voltmx.print("### frmResume_onclick_btnOptionVariables set NS globals");
    Global.vars.readCompleteOptionText = false; //optie 2
    Global.vars.fillAndEditOptionData = false;
    Global.vars.copyPasteCompleteOptionText = false;
  }else if(Global.vars.fillAndEditOptionDataEnabled === true){
    voltmx.print("### frmResume_onclick_btnOptionVariables set fillAndEditOptionData globals");
    Global.vars.readCompleteOptionText = false;
    Global.vars.fillAndEditOptionData = true; //optie 4
    Global.vars.copyPasteCompleteOptionText = true; //optie 5
  }else if(Global.vars.buildFor == "GEN"){//voor nu even als RWS
    voltmx.print("### frmResume_onclick_btnOptionVariables set GEN globals");
    Global.vars.readCompleteOptionText = false;
    Global.vars.fillAndEditOptionData = false;
    Global.vars.copyPasteCompleteOptionText = false;
  } 
  //Check of er bestaande data is
  if(CaseData.offence.offenceCode != null && Global.vars.fillAndEditOptionData === true){
    segDataFilledOptions = Utility_getfilledOptionVariablesForOffence(CaseData.offence.offenceCode);
    if(segDataFilledOptions !== undefined && segDataFilledOptions != null && segDataFilledOptions.length > 0){
      previousdataFound = true;
    }
  }
  
  voltmx.print("### frmResume_onclick_btnOptionVariables previousdataFound: " + previousdataFound);
  voltmx.print("### frmResume_onclick_btnOptionVariables Global.vars.optionvariablesText: " + Global.vars.optionvariablesText);
  
  if(Global.vars.optionvariablesText !== "" && Global.vars.seeAndEditCompleteOptionTextEditTicket === false && Global.vars.readCompleteOptionText === true){
    ///Als je NS bent en de Global.vars.optionvariablesText - bestaande complete tekst - gevuld is kun je alleen bekijken of een nieuwe maken (en tasktype is niet EditTicket)
    voltmx.print("### frmResume_onclick_btnOptionVariables readCompleteOptionText (optie 2)");
    voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_editExistingOptionsVariables"),
                  frmResume_confirm_LookAtOptionText,
                  "confirmation",
                  voltmx.i18n.getLocalizedString("bt_view"), //response true
                  voltmx.i18n.getLocalizedString("bt_new"),
                  "Info",
                  null);
  }else if(previousdataFound === false){
    voltmx.print("### frmResume_onclick_btnOptionVariables no previousdata of geen tekst in Global.vars.optionvariablesText (Optie 1)");
    //geen data dus laat opties zien optie 1
    frmResume_checkOption();
    Global.vars.previousForm = "frmResume";
    Global.vars.toOptionsFromForm = "frmResume";
    frmOptions.show();
  }else if(Global.vars.seeAndEditCompleteOptionTextEditTicket === true){
    //Als het tasktype EditTicket is laat dan de te wijzigen tekst zien optie 3
    voltmx.print("### frmResume_onclick_btnOptionVariables previousdata is true  seeAndEditCompleteOptionText (optie 3)");
    frmResume_showSetText(frmResume.option.lblHeader.text);
    if(Global.vars.copyPasteCompleteOptionText === true){
      voltmx.print("### frmResume_onclick_btnOptionVariables laat de knop kopieer en bewaar gehele tekst zien");
      //wordt geregeld in show settext
    }else{
      voltmx.print("### frmResume_onclick_btnOptionVariables laat de knop kopieer en bewaar gehele tekst NIET zien");
      //wordt geregeld in show settext
    }
  }else if(previousdataFound === true && Global.vars.seeAndEditCompleteOptionTextEditTicket === false && Global.vars.fillAndEditOptionData === true){
    //Als het taaktype geen Editticket is en er is vooraf gevulede data dan kun je de optie overnemen optie 4
    //show popup met de over te nemen optie en variabelen - dit stuurt je weer naar het optiesvariabelen/reden van wetenschap scherm
    voltmx.print("### frmResume_onclick_btnOptionVariables segDataFilledOptions filled (Optie 4)");
    Utility_setOptionVariablesGlobals();
//     if(CaseData.option !== undefined && CaseData.option.length > 0){
//       for(var c in CaseData.option){
//         var d = CaseData.option[c];
//         d = Utility_transformKeys(d);
//         voltmx.print("### frmResume_onclick_btnOptionVariables CaseData option d: " + JSON.stringify(d));
//         if(d.offencecode == CaseData.offence.offenceCode){
//           Global.vars.selectedOption = d;
//           Global.vars.optionvariablesSet = true;
//           Global.vars.optionvariablesText = d.filledText;
//         }
//       }
//     }
    if(Global.vars.optionvariablesSet === true){
      voltmx.print("### frmResume_onclick_btnOptionVariables segDataFilledOptions filled (Optie 4) met bestaande optie laat knop zien die optie 3 ook doet");
      frmResume_flcShowExistingOptionCompleteText_setVisibility(true);
      frmResume.imgPopupLogoSavedOptions.centerY = "25%";
    }else{
      frmResume_flcShowExistingOptionCompleteText_setVisibility(false);
      frmResume.imgPopupLogoSavedOptions.centerY = "30%";
    }
    frmResume.segSavedOptions.setData(segDataFilledOptions);
    frmResume.flcMainPage.setEnabled(false);
    frmResume_flcSavedOptions_setVisibility(true);
  }else{
    voltmx.print("### frmResume_onclick_btnOptionVariables no valid options");
  }
}

function frmResume_onclick_segSavedOptions(){
  voltmx.print("### frmResume_onclick_segSavedOptions");
  var selectedItem = frmResume.segSavedOptions.selectedItems[0];
  var index = Number(selectedItem.index);
  CaseData.option = [Global.vars.filledOptionVariables[index]];
  Global.vars.previousForm = "frmResume";
  Global.vars.toOptionsFromForm = "frmResume";
  frmResume_hideSavedOptions();
  Global.vars.options = CaseData.option;
  voltmx.print("### frmResume_onclick_segSavedOptions frmOption_goToVariablesForm");
  frmOption_goToVariablesForm(CaseData.option[0]);
}

function frmResume_onclick_btnNewOption(){
  Utility_clearOptions();
  Global.vars.previousForm = "frmResume";
  Global.vars.toOptionsFromForm = "frmResume";
  frmResume_hideSavedOptions();
  frmOptions.show();
}

function frmResume_onclick_btnShowExistingOptionCompleteText(){
  frmResume_hideSavedOptions();
  frmResume_showSetText(frmResume.option.lblHeader.text);
}

function frmResume_hideSavedOptions(){
  frmResume.flcMainPage.setEnabled(true);
  frmResume_flcSavedOptions_setVisibility(false);
}

function frmResume_confirm_EditOptionText(response){
  if(response){
    frmResume_showSetText(frmResume.option.lblHeader.text);
  }else{
    frmResume_checkOption();
    Global.vars.previousForm = "frmResume";
    Global.vars.toOptionsFromForm = "frmResume";
    frmOptions.show();
  }
}

function frmResume_confirm_LookAtOptionText(response){
  if(response){
    frmResume_showOptionsVariables();
  }else{
    Utility_clearOptions();
    Global.vars.previousForm = "frmResume";
    Global.vars.toOptionsFromForm = "frmResume";
    frmOptions.show();
  }
}

function frmResume_showOptionsVariables() {
  	voltmx.print("### frmResume_showOptionsVariables");
    try {
        frmResume.lblValueOptionsVariables.text = Global.vars.optionvariablesText;
        //deactivate footer and mainpage
        frmResume.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmResume_showOptionsVariables_preAnim();
        frmResume_showOptionsVariables_animationStart();
    } catch (e) {
      voltmx.print("### frmResume_showOptionsVariables error: " + JSON.stringify(e));
    }
}

function frmResume_showOptionsVariables_preAnim() {
  try {
    voltmx.print("### frmResume_showOptionsVariables_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
    //frmResume.flcDetailOptionVariables.transform = trans1;
    //frmResume.imgPopupLogoOptionVariables.transform = trans1;
    //frmResume.flcTextDetailsOptionsVariables.transform = trans1;
  } catch (e) {
    voltmx.print("### frmResume_showOptionsVariables_preAnim error: " + JSON.stringify(e));
  }
}

function frmResume_showOptionsVariables_arrangeWidgets() {
  try {
    voltmx.print("### frmResume_showOptionsVariables_arrangeWidgets");
    //popup fields
    frmResume.imgPopupLogoOptionVariables.isVisible = false;
    frmResume.flcDetailOptionVariables.isVisible = false;
    frmResume.flcTextDetailsOptionsVariables.isVisible = false;
    frmResume.lblLineOptionsVariables.isVisible = false;
    frmResume.flcFooterOptionsVariables.isVisible = false;
    frmResume_showOptionsVariables_setVisibility(false);
    frmResume.flcFooterOptionsVariables.setEnabled(false);
    frmResume.showOptionsVariables.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showOptionsVariables_preAnim error: " + JSON.stringify(e));
  }

}

function frmResume_showOptionsVariables_animationStart(eventobject) {
  try {
    voltmx.print("### frmResume_showOptionsVariables_animationStart");
    frmResume_showOptionsVariables_setVisibility(true);
    frmResume.flcDetailOptionVariables.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmResume.flcDetailOptionVariables.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmResume_showOptionsVariables_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmResume_showOptionsVariables_animationStart error: " + JSON.stringify(e));
  }
}

function frmResume_showOptionsVariables_animLogo() {
  try {
    voltmx.print("### frmResume_showOptionsVariables_animLogo");
//     var trans = voltmx.ui.makeAffineTransform();
//     trans.scale(1.2, 1.2);
//     frmResume.imgPopupLogoOptionVariables.animate(
//       voltmx.ui.createAnimation({
//         "100": {
//           "anchorPoint": {
//             "x": 0.5,
//             "y": 0.5
//           },
//           "stepConfig": {
//             "timingFunction": voltmx.anim.EASE
//           },
//           "transform": trans,
//         }
//       }), {
//         "delay": 0,
//         "iterationCount": 1,
//         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
//         "duration": 0.25
//       }, {
//         "animationEnd": function (){
//           frmResume_showOptionsVariables_animOtherWidgets(frmResume.flcDetailOptionVariables);
//           frmResume_showOptionsVariables_animOtherWidgets(frmResume.lblLineOptionsVariables);
//           frmResume_showOptionsVariables_animOtherWidgets(frmResume.flcTextDetailsOptionsVariables);
//           frmResume_showOptionsVariables_animLogoBack();
//         }
//       });
    frmResume_showOptionsVariables_animOtherWidgets(frmResume.flcDetailOptionVariables);
    frmResume_showOptionsVariables_animOtherWidgets(frmResume.lblLineOptionsVariables);
    frmResume_showOptionsVariables_animOtherWidgets(frmResume.flcTextDetailsOptionsVariables);
    frmResume.imgPopupLogoOptionVariables.isVisible = true;
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showOptionsVariables_animLogo error: " + JSON.stringify(e));
  }
}


function frmResume_showOptionsVariables_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmResume_showOptionsVariables_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmResume.flcDetailOptionVariables.isVisible = true;
    frmResume.lblLineOptionsVariables.isVisible = true;
    frmResume.flcFooterOptionsVariables.isVisible = true;
    frmResume.flcFooterOptionsVariables.setEnabled(true);
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showOptionsVariables_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmResume_showOptionsVariables_animLogoBack() {
  try {
    voltmx.print("### frmResume_showOptionsVariables_animLogoBack");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmResume.imgPopupLogoOptionVariables.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showOptionsVariables_animLogoBack error: " + JSON.stringify(e));
  }
}

function frmResume_hideShowOptionVariables(){
  frmResume.lblValueOptionsVariables.text = "";
  frmResume.flcMainPage.setEnabled(true);
  frmResume_showOptionsVariables_setVisibility(false);
}

function frmResume_showHandleCasePopup(){
    //setGaussianBlur(frmResume.flcMainPage, frmResume.imgBlured);
  	frmResume.flcMainPage.setEnabled(false);
    var choices = [
      				//{lblItem: voltmx.i18n.getLocalizedString("l_save_data"), caseData:CaseData},
      				{lblItem: voltmx.i18n.getLocalizedString("l_saveAndExit"), caseData:CaseData}
                  ];
    frmResume.segCaseHandle.setData(choices);
    frmResume.flcHandleCasePopup.bottom = "0%";
    //move popup options
    var scanOptionsAnimation = voltmx.ui.createAnimation({
      "100": {
                  "bottom" : 0+'%',
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
      var scanOptionsSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 0.2
                  };

    frmResume.flcCases.animate(scanOptionsAnimation,scanOptionsSetting);
}

function frmResume_cancelHandleCasePopup(){
    frmResume.flcMainPage.setEnabled(true);
    //move popup options
    var scanOptionsAnimation = voltmx.ui.createAnimation({
      "100": {
                  "bottom" : -39+'%',
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
      var scanOptionsSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 0.4
                  };

    frmResume.flcCases.animate(scanOptionsAnimation,scanOptionsSetting);
  	frmResume.flcHandleCasePopup.bottom = "-100%";
}

function frmResume_preSaveCase(){
  function updateCaseData(result){
    voltmx.print("#### frmResume_preSaveCase updateCaseData result: " + JSON.stringify(result));
    if(result !== undefined && result.response[0].externalCaseId !== undefined && result.response[0].externalCaseId != null && result.response[0].externalCaseId !== "" &&
       result.response[0].caseId !== undefined && result.response[0].caseId != null && result.response[0].caseId !== ""){
      var externalCaseID = result.response[0].externalCaseId;
      var couchID = result.response[0].caseId;
      CaseData.caseinfo.externalId = externalCaseID;
      CaseData.caseinfo.id = couchID;
      //now show external ID
      if(CaseData.caseinfo.externalId !== undefined && CaseData.caseinfo.externalId != null && CaseData.caseinfo.externalId !== ""){
        frmResume_flcCaseNumber_setVisibility(true);
        frmResume.lblCaseNumber.text = "Zaak ID: " + CaseData.caseinfo.externalId;
      }
    }else{
      voltmx.print("#### frmResume_preSaveCase updateCaseData case is saved offline on device");
    }
  }
  if(Global.vars.saveCaseEnabled === true && (activeTaskTypeInOpenTaskTypesToQuery === false && CaseData.processinfo.activeTaskType != "ValidateDocument" && 
                                              CaseData.processinfo.activeTaskType != "ValidateAlibi" && CaseData.processinfo.activeTaskType != "AanvullenDirectVerbod" && 
                                              CaseData.processinfo.activeTaskType != "OverdrachtPolitie" && CaseData.processinfo.activeTaskType != "CorrigerenKandidaat") && 
     Global.vars.backToPreviousForm !== "frmHistory" && CaseData.processinfo.activeTaskType != "OpvolgenControleOpStraat")
  {
    voltmx.print("#### frmResume_preSaveCase");
    Utility_saveUploadCaseData(CaseData,updateCaseData,"frmResume");
  }
}

function frmResume_onclickSegCaseHandle(){
  voltmx.print("### frmResume_onclickSegCaseTypes selecteditem: " + JSON.stringify(frmResume.segCaseHandle.selectedItems));
  var selectedCase = frmResume.segCaseHandle.selectedItems[0];
   if(selectedCase != null && selectedCase !== ""){
    var name = null;
    Global.vars.previousForm = "frmResume";
    if(selectedCase.lblItem == voltmx.i18n.getLocalizedString("l_save_data")){
      voltmx.print("### frmResume_onclickSegCaseTypes l_save_data clicked");
      if(Global.vars.claimedDocID != null && Global.vars.claimedDocID !== ""){
        name = Global.vars.claimedDocID;
      }
      function updateCaseData(result){
        voltmx.print("#### frmResume_onclickSegCaseTypes updateCaseData result: " + JSON.stringify(result));
        if(result !== undefined && result.response[0].externalCaseId !== undefined && result.response[0].externalCaseId != null && result.response[0].externalCaseId !== "" &&
          result.response[0].caseId !== undefined && result.response[0].caseId != null && result.response[0].caseId !== ""){
          var externalCaseID = result.response[0].externalCaseId;
          var couchID = result.response[0].caseId;
          CaseData.caseinfo.externalId = externalCaseID;
          CaseData.caseinfo.id = couchID;
          //now show external ID
          if(CaseData.caseinfo.externalId !== undefined && CaseData.caseinfo.externalId != null && CaseData.caseinfo.externalId !== ""){
            frmResume_flcCaseNumber_setVisibility(true);
            frmResume.lblCaseNumber.text = "Zaak ID: " + CaseData.caseinfo.externalId;
          }
        }else{
          voltmx.print("#### frmResume_onclickSegCaseTypes updateCaseData case is saved offline on device");
        }
      }
      Utility_saveUploadCaseData(CaseData,updateCaseData,"frmResume");
      frmResume_cancelHandleCasePopup();
    }else if(selectedCase.lblItem == voltmx.i18n.getLocalizedString("l_saveAndExit")){
      voltmx.print("### frmResume_onclickSegCaseTypes l_save_data and exit clicked");
      Utility_saveUploadCaseData(CaseData,frmResume_saveCaseCallback,"frmResume");
    }
   }
}
  
function frmResume_saveCaseCallback(){
  frmResume_cancelHandleCasePopup();
  frmResume_resetAllFields();
  frmResume_clearMultimedia();
  Global.vars.chosenTaskOutcome = "";
  Global.vars.selectedOutcome = null;
  if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_trackDown")){
    // reset Vehicle Country to default;
//   	frmTrackDown_resetVehicleCountry();
	frmTrackDown_resetApp(false);
    voltmx.application.dismissLoadingScreen();
    frmTrackDown.show();
  } else if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer"){
	frmRegister_resetApp();
    voltmx.application.dismissLoadingScreen();
    frmRegister.show();
  } else {
    Global_resetApp();
    Utility_doServerCalls();
    voltmx.application.dismissLoadingScreen();
    //frmFollow.show();
    frmOverviewTask.show();
  }
}

function frmResume_showSetDocumentDescription() {
  	voltmx.print("### frmResume_showSetDocumentDescription");
    try {
      if(CaseData.person[Global.vars.gCasePersonsIndex].idenDocType.toString().startsWith("99")){
        frmResume.lblTextHeader.text = "Beschrijving document(en)";
        frmResume.lblValueHeaderDocumentDescription.text = "Document";
        frmResume.lblValue.text = CaseData.person[Global.vars.gCasePersonsIndex].idenDocTypeDesc;
        var currentDocumentDescription = "";
        for (var p=0; ((CaseData.text) != null) && p < CaseData.text.length; p++ ){
          var v = CaseData.text[p];
          if((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))){ //beschrijving documenten
            currentDocumentDescription = v.value.replace("Beschrijving document(en): ","");
          }
        }
        frmResume.TextAreaText.text = currentDocumentDescription;
        frmResume.TextAreaText.maxTextLength = 200;
        //deactivate footer and mainpage
        frmResume.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmResume_setdocumentdescription_setVisibility(true);
        frmResume_showSetDocumentDescription_preAnim();
        frmResume_showSetDocumentDescription_animationStart();
      }
    } catch (e) {
      voltmx.print("### frmResume_showSetDocumentDescription error: " + JSON.stringify(e));
    }
}

function frmResume_showSetDocumentDescription_preAnim() {
  try {
    voltmx.print("### frmResume_showSetDocumentDescription_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
    //frmResume.setdocumentdescription.flcDetailDocumentDescription.transform = trans1;
    //frmResume.setdocumentdescription.imgPopupLogoDocumentDescription.transform = trans1;
    //frmResume.setdocumentdescription.flcDetailDocumentDescription.flcTextDetailsDocumentDescription.transform = trans1;
  } catch (e) {
    voltmx.print("### frmResume_showSetDocumentDescription_preAnim error: " + JSON.stringify(e));
  }
}

function frmResume_showSetDocumentDescription_arrangeWidgets() {
  try {
    voltmx.print("### frmResume_showSetDocumentDescription_arrangeWidgets");
    //popup fields
    frmResume.setdocumentdescription.imgPopupLogoDocumentDescription.isVisible = false;
    frmResume.setdocumentdescription.flcDetailDocumentDescription.isVisible = false;
    frmResume.setdocumentdescription.flcDetailDocumentDescription.flcTextDetailsDocumentDescription.isVisible = false;
    frmResume.setdocumentdescription.flcDetailDocumentDescription.lbl0.isVisible = false;
    frmResume.setdocumentdescription.flcDetailDocumentDescription.flcFooterDocumentDescription.isVisible = false;
    frmResume_setdocumentdescription_setVisibility(false);
    frmResume.setdocumentdescription.flcDetailDocumentDescription.flcFooterDocumentDescription.setEnabled(false);
    frmResume.setdocumentdescription.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetDocumentDescription_preAnim error: " + JSON.stringify(e));
  }

}

function frmResume_showSetDocumentDescription_animationStart(eventobject) {
  try {
    voltmx.print("### frmResume_showSetDocumentDescription_animationStart");
    frmResume_setdocumentdescription_setVisibility(true);
    frmResume.setdocumentdescription.flcDetailDocumentDescription.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmResume.setdocumentdescription.flcDetailDocumentDescription.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmResume_showSetDocumentDescription_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmResume_showSetDocumentDescription_animationStart error: " + JSON.stringify(e));
  }
}

function frmResume_showSetDocumentDescription_animLogo() {
  try {
    voltmx.print("### frmResume_showSetDocumentDescription_animLogo");
//     var trans = voltmx.ui.makeAffineTransform();
//     trans.scale(1.2, 1.2);
//     frmResume.setdocumentdescription.imgPopupLogoDocumentDescription.animate(
//       voltmx.ui.createAnimation({
//         "100": {
//           "anchorPoint": {
//             "x": 0.5,
//             "y": 0.5
//           },
//           "stepConfig": {
//             "timingFunction": voltmx.anim.EASE
//           },
//           "transform": trans,
//         }
//       }), {
//         "delay": 0,
//         "iterationCount": 1,
//         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
//         "duration": 0.25
//       }, {
//         "animationEnd": function (){
//           frmResume_showSetDocumentDescription_animOtherWidgets(frmResume.flcTextDetailsDocumentDescription);
//           frmResume_showSetDocumentDescription_animOtherWidgets(frmResume.lbl0);
//           frmResume_showSetDocumentDescription_animLogoBack();
//         }
//       });
    frmResume_showSetDocumentDescription_animOtherWidgets(frmResume.flcTextDetailsDocumentDescription);
    frmResume_showSetDocumentDescription_animOtherWidgets(frmResume.lbl0);
    frmResume.setdocumentdescription.imgPopupLogoDocumentDescription.isVisible = true;
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetDocumentDescription_animLogo error: " + JSON.stringify(e));
  }
}


function frmResume_showSetDocumentDescription_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmResume_showSetDocumentDescription_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmResume.flcTextDetailsDocumentDescription.isVisible = true;
    frmResume.lbl0.isVisible = true;
    frmResume.flcFooterDocumentDescription.isVisible = true;
    frmResume.flcFooterDocumentDescription.setEnabled(true);
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetDocumentDescription_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmResume_showSetDocumentDescription_animLogoBack() {
  try {
    voltmx.print("### frmResume_showSetDocumentDescription_animLogoBack");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmResume.setdocumentdescription.imgPopupLogoDocumentDescription.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetDocumentDescription_animLogoBack error: " + JSON.stringify(e));
  }
}

function frmResume_hideSetDocumentDescription(){
  //activate footer and mainpage
  frmResume.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmResume_setdocumentdescription_setVisibility(false);
  frmResume_contentOffset();
}

function frmResume_SetDocumentDescriptionDone(){
  	if(frmResume.TextAreaText.text != null && frmResume.TextAreaText.text !== "" && frmResume.TextAreaText.text !== "Beschrijving document(en)"){
      // add record to CaseData.text
      var loctextindex = null;
      for (var p=0; ((CaseData.text) != null) && p < CaseData.text.length; p++ ){
        var v = CaseData.text[p];
        if((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))){ //beschrijving documenten
          voltmx.print("#### frmResume_SetDocumentDescriptionDone: " + v + " index: " + p);
          loctextindex = p;
          break;
        }
      }
      var laddrecord = CaseData_setNewtext();
      laddrecord.inserted = true;
      laddrecord.edited = true;
      laddrecord.type = 3; //beschrijving documenten
      laddrecord.value = "Beschrijving document(en): " + frmResume.TextAreaText.text;
      if (loctextindex === null) {
        CaseData.text.splice(0,0,laddrecord);
      } else {
        CaseData.text.splice(loctextindex,1,laddrecord);
      }
      voltmx.print("#### frmResume_SetDocumentDescriptionDone CaseData.text after: " + JSON.stringify(CaseData.text));
    }
  	frmResume_hideSetDocumentDescription();
}

function frmResume_clearTextAreaText_SetDocumentDescription(){
  frmResume.TextAreaText.text = "";
  frmResume.TextAreaText.setFocus(true);
}

function frmResume_showSearchUser(officer) {
  Global.vars.gOfficerToSelect = officer;
  if (Global.vars.userSelectEnabled === true){
    Global.vars.previousForm = "frmResume";
    frmSelectUser.show();
  } else {
    frmResume_showSearchUserPopup();
  }
}

function frmResume_showSearchCase() {
    frmSelectCase.show();
}

function frmResume_showSearchUserPopup() {
	voltmx.print("### frmResume_showSearchUser");
    try {
      if (Global.vars.gOfficerToSelect === "second"){
        if(CaseData.caseinfo.secondOfficerNumber != null && CaseData.caseinfo.secondOfficerNumber !== ""){
          frmResume.searchuser.textalphanumeric.txtInputText.text = CaseData.caseinfo.secondOfficerNumber;
        }else{
          frmResume.searchuser.textalphanumeric.txtInputText.text = "";
        }
      } else if (Global.vars.gOfficerToSelect === "third"){
        if(CaseData.caseinfo.thirdOfficerNumber != null && CaseData.caseinfo.thirdOfficerNumber !== ""){
          frmResume.searchuser.textalphanumeric.txtInputText.text = CaseData.caseinfo.thirdOfficerNumber;
        } else {
          frmResume.searchuser.textalphanumeric.txtInputText.text = "";
        }
      }
      if((Global.vars.buildFor !== "NS" && Global.vars.buildFor !== "OV") || (CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber != null)){
        frmResume_searchuser_flcInfo_setVisibility(false);
        frmResume.searchuser.textalphanumeric.top = "37dp";
      }else{
        frmResume_searchuser_flcInfo_setVisibility(true);
        frmResume.searchuser.textalphanumeric.top = "12dp";
      }
      //deactivate footer and mainpage
      frmResume.flcMainPage.setEnabled(false);
      voltmx.print("### flcMainPage disabled");
      frmResume_searchuser_setVisibility(true);
      frmResume_showSearchUser_preAnim();
      frmResume_showSearchUser_animationStart();
    } catch (e) {
      voltmx.print("### frmResume_showSearchUser error: " + JSON.stringify(e));
    }
}

function frmResume_showSearchUser_preAnim() {
  try {
    voltmx.print("### frmResume_showSearchUser_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
    //frmResume.searchuser.flcDetail.transform = trans1;
    //frmResume.searchuser.imgPopupLogo1.transform = trans1;
    //frmResume.searchuser.flcTextDetails.transform = trans1;
  } catch (e) {
    voltmx.print("### frmResume_showSearchUser_preAnim error: " + JSON.stringify(e));
  }
}

function frmResume_showSearchUser_arrangeWidgets() {
  try {
    voltmx.print("### frmResume_showSearchUser_arrangeWidgets");
    //popup fields
    frmResume.searchuser.imgPopupLogo1.isVisible = false;
    frmResume.searchuser.flcDetail.isVisible = false;
    frmResume.searchuser.flcTextDetails.isVisible = false;
    frmResume.searchuser.lbl1.isVisible = false;
    frmResume.searchuser.flcFooterSetText.isVisible = false;
    frmResume_searchuser_setVisibility(false);
    frmResume.searchuser.flcFooterSetText.setEnabled(false);
    frmResume.searchuser.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSearchUser_preAnim error: " + JSON.stringify(e));
  }

}

function frmResume_showSearchUser_animationStart(eventobject) {
  try {
    voltmx.print("### frmResume_showSearchUser_animationStart");
    frmResume_searchuser_setVisibility(true);
    frmResume.searchuser.flcDetail.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmResume.searchuser.flcDetail.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmResume_showSearchUser_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmResume_showSearchUser_animationStart error: " + JSON.stringify(e));
  }
}

function frmResume_showSearchUser_animLogo() {
  try {
    voltmx.print("### frmResume_showSearchUser_animLogo");
//     var trans = voltmx.ui.makeAffineTransform();
//     trans.scale(1.2, 1.2);
//     frmResume.searchuser.imgPopupLogo1.animate(
//       voltmx.ui.createAnimation({
//         "100": {
//           "anchorPoint": {
//             "x": 0.5,
//             "y": 0.5
//           },
//           "stepConfig": {
//             "timingFunction": voltmx.anim.EASE
//           },
//           "transform": trans,
//         }
//       }), {
//         "delay": 0,
//         "iterationCount": 1,
//         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
//         "duration": 0.25
//       }, {
//         "animationEnd": function (){
//           frmResume_showSearchUser_animOtherWidgets(frmResume.searchuser.flcTextDetails);
//           frmResume_showSearchUser_animOtherWidgets(frmResume.searchuser.lbl1);
//           frmResume_showSearchUser_animLogoBack();
//         }
//       });
    frmResume_showSearchUser_animOtherWidgets(frmResume.searchuser.flcTextDetails);
    frmResume_showSearchUser_animOtherWidgets(frmResume.searchuser.lbl1);
    frmResume.searchuser.imgPopupLogo1.isVisible = true;
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSearchUser_animLogo error: " + JSON.stringify(e));
  }
}


function frmResume_showSearchUser_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmResume_showSearchUser_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmResume.searchuser.flcTextDetails.isVisible = true;
    frmResume.searchuser.lbl1.isVisible = true;
    frmResume.searchuser.flcFooterSetText.isVisible = true;
    frmResume.searchuser.flcFooterSetText.setEnabled(true);
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSearchUser_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmResume_showSearchUser_animLogoBack() {
  try {
    voltmx.print("### frmResume_showSearchUser_animLogoBack");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmResume.searchuser.imgPopupLogo1.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSearchUser_animLogoBack error: " + JSON.stringify(e));
  }
}

function frmResume_hideSearchUser(){
  //activate footer and mainpage
  frmResume.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmResume_searchuser_setVisibility(false);
}

function frmResume_SearchUserSearch(){
  	voltmx.print("### frmResume_SearchUserSearch");
  	if(frmResume.searchuser.textalphanumeric.txtInputText.text != null && frmResume.searchuser.textalphanumeric.txtInputText.text !== ""){
      var officernumber = frmResume.searchuser.textalphanumeric.txtInputText.text;
      officernumber = officernumber.toUpperCase();
      if(officernumber !== CaseData.caseinfo.officerNumber){
        voltmx.print("### frmResume_SearchUserSearch officernumber: " + officernumber);
        if(Global.vars.indmunicipalityrestricted === false){
          service_GetUserByOfficerName(officernumber, "", frmResume_SearchUserCallback, frmResume_SearchUserErrorcallback);
        }else{
          	if(CaseData.location !== undefined && CaseData.location.municipalityCode !== undefined && CaseData.location.municipalityCode != null){
         		service_GetUserByOfficerName(officernumber, CaseData.location.municipalityCode, frmResume_SearchUserCallback, frmResume_SearchUserErrorcallback); 
            }else{
              	alert(voltmx.i18n.getLocalizedString("l_officerNotFoundMuni"));//"Verbalisant niet gevonden vanwege ontbrekende gemeente");
        		voltmx.application.dismissLoadingScreen();
            }
        }
      }else{
        alert(voltmx.i18n.getLocalizedString("l_cannotRegisterYourself"));
      }
    }
}

function frmResume_SearchUserCallback(result){
  voltmx.print("### frmResume_SearchUserCallback: " + JSON.stringify(result));
  if(result.opstatus === 0 && result.httpStatusCode == 200){
    var response = result.response;
    voltmx.print("### frmResume_SearchUserCallback: " + JSON.stringify(response));	
    if(response != null){
      if ( response.length == 1){
        var v = response[0];
        voltmx.print("### frmResume_SearchUserCallback v: " + JSON.stringify(v));	
        if(v.username !== Global.vars.gUsername && v.team_code !== "100" && v.officer_number != null){
          voltmx.print("### frmResume_SearchUserCallback: " + JSON.stringify(v.username));
          if ((Global.vars.gOfficerToSelect === "third" && CaseData.caseinfo.secondOfficerUserName === v.username) || (Global.vars.gOfficerToSelect === "second" && CaseData.caseinfo.thirdOfficerUserName === v.username)){
            voltmx.print("### frmResume_SearchUserCallback: not in the list already a 2nd or 3rd officer");
            alert(voltmx.i18n.getLocalizedString("l_officerNotFound"));
            voltmx.application.dismissLoadingScreen();
          } else {
            if (Global.vars.gOfficerToSelect === "second"){
              CaseData.caseinfo.secondOfficerNumber = v.officer_number;
              CaseData.caseinfo.secondOfficerName = v.full_name;
              CaseData.caseinfo.secondOfficerUserName = v.username;
              if (v.officer_identification != null){
                CaseData.caseinfo.secondOfficerIdentification = v.officer_identification;
              } else {
                CaseData.caseinfo.secondOfficerIdentification = null;
              }
              if (v.oath_type != null){
                CaseData.caseinfo.secondOfficerOath = v.oath_type;
              } else {
                CaseData.caseinfo.secondOfficerOath = null;
              }
              if (v.document_number != null){
                CaseData.caseinfo.secondOfficerDocumentNumber = v.document_number;
              } else {
                CaseData.caseinfo.secondOfficerDocumentNumber = null;
              }
              frmResume.secondofficer.lblText.text = CaseData.caseinfo.secondOfficerName;
              frmResume.secondofficer.lblText.skin = lblFieldInfo;
            } else if (Global.vars.gOfficerToSelect === "third"){
              CaseData.caseinfo.thirdOfficerNumber = v.officer_number;
              CaseData.caseinfo.thirdOfficerName = v.full_name;
              CaseData.caseinfo.thirdOfficerUserName = v.username;
              if (v.officer_identification != null){
                CaseData.caseinfo.thirdOfficerIdentification = v.officer_identification;
              } else {
                CaseData.caseinfo.thirdOfficerIdentification = null;
              }
              if (v.oath_type != null){
                CaseData.caseinfo.thirdOfficerOath = v.oath_type;
              } else {
                CaseData.caseinfo.thirdOfficerOath = null;
              }
              if (v.document_number != null){
                CaseData.caseinfo.thirdOfficerDocumentNumber = v.document_number;
              } else {
                CaseData.caseinfo.thirdOfficerDocumentNumber = null;
              }
              frmResume.thirdofficer.lblText.text = CaseData.caseinfo.thirdOfficerName;
              frmResume.thirdofficer.lblText.skin = lblFieldInfo;
            }
            frmResume_hideSearchUser();
            voltmx.application.dismissLoadingScreen();
          }
        } else {
          alert(voltmx.i18n.getLocalizedString("l_officerNotFound"));
          voltmx.application.dismissLoadingScreen();
        }
      } else if ( response.length > 1){
        alert(voltmx.i18n.getLocalizedString("l_moreThenOnePersonFound"));
        voltmx.application.dismissLoadingScreen();
      } else {
        alert(voltmx.i18n.getLocalizedString("l_officerNotFound"));
        voltmx.application.dismissLoadingScreen();
      }
    }
  }else{
    alert(voltmx.i18n.getLocalizedString("l_officerNotFound"));
    voltmx.application.dismissLoadingScreen();
  }
}

function frmResume_SearchUserErrorcallback(error){
  voltmx.print("### frmResume_SearchUserErrorcallback: " + JSON.stringify(error));
  voltmx.application.dismissLoadingScreen();
  alert(voltmx.i18n.getLocalizedString("l_officerNotFound"));
}

function frmResume_clearSearchUser(){
  voltmx.print("### frmResume_clearSearchUser");
  frmResume.searchuser.textalphanumeric.txtInputText.text = "";
  frmResume.searchuser.textalphanumeric.setFocus(true);
}

function frmResume_clearSecondOfficer(){
  voltmx.print("### frmResume_clearSecondOfficer");
  frmResume.secondofficer.lblText.text = voltmx.i18n.getLocalizedString("l_secondOfficer");
  frmResume.secondofficer.lblText.skin = lblFieldNotFilled;
  CaseData.caseinfo.secondOfficerName = null;
  CaseData.caseinfo.secondOfficerNumber = null;
  CaseData.caseinfo.secondOfficerUserName = null;
  CaseData.caseinfo.secondOfficerOath = null;
  CaseData.caseinfo.secondOfficerIdentification = null;
  CaseData.caseinfo.secondOfficerDocumentNumber = null;
  CaseData.offence.execByPartner = false; 
  Global.vars.execByPartner = null;
}

function frmResume_clearThirdOfficer(){
  voltmx.print("### frmResume_clearThirdOfficer");
  if (CaseData.caseinfo.caseTypeCategory === "dpv"){
    frmResume.thirdofficer.lblText.text = voltmx.i18n.getLocalizedString("l_assessor");
  } else {
    frmResume.thirdofficer.lblText.text = voltmx.i18n.getLocalizedString("l_thirdOfficer");
  }
  frmResume.thirdofficer.lblText.skin = lblFieldNotFilled;
  CaseData.caseinfo.thirdOfficerName = null;
  CaseData.caseinfo.thirdOfficerNumber = null;
  CaseData.caseinfo.thirdOfficerUserName = null;
  CaseData.caseinfo.thirdOfficerOath = null;
  CaseData.caseinfo.thirdOfficerIdentification = null;
  CaseData.caseinfo.thirdOfficerDocumentNumber = null;
//  CaseData.offence.execByPartner = false; 
//  Global.vars.execByPartner = null;
}

function frmResume_clearRelatedCase(){
  voltmx.print("### frmResume_clearRelatedCase");
  frmResume.relatedcase.lblText.text = voltmx.i18n.getLocalizedString("l_relatedcase");
  frmResume.relatedcase.lblText.skin = lblFieldNotFilled;
  CaseData.caseinfo.relatedId = null;
  CaseData.caseinfo.relatedExternalId = null;
}

function frmResume_showTransportticketPopup(){
  frmResume.flcMainPage.setEnabled(false);
  frmResume_flcTransportticketPopup_setVisibility(true);
}

function frmResume_hideTransportticketPopup(){
  frmResume.flcMainPage.setEnabled(true);
  frmResume_flcTransportticketPopup_setVisibility(false);
}

function frmResume_callWarningService(){
  if(CaseData.offence.person === true && (Global.vars.gInstanceId == "RL0001" || Global.vars.gInstanceId == "NL0036" || Global.vars.useDemo === true)){
    var period = 30;
    for(var i=0; i<Global.vars.ticketTypes.length; i++){
      var v = Global.vars.ticketTypes[i];
      if(CaseData.caseinfo.ticketType == v.id && v.check_period !== undefined && v.check_period != null){
        period = Number(v.check_period);
        if(v.max_occurences !== undefined && v.max_occurences != null){
        	Global.vars.maxoccurences = v.max_occurences;
        }
      }
    }
  	service_GetWarning(CaseData.caseinfo.caseType, CaseData.caseinfo.ticketType, CaseData.person[Global.vars.gCasePersonsIndex].ssn, CaseData.offence.offenceCode, period ,frmResume_WarningSuccesCallback, frmResume_WarningErrorcallback);
  }
}

function frmResume_WarningErrorcallback(error){
  voltmx.print("### frmResume_WarningErrorcallback error: " + JSON.stringify(error));
  Global.vars.maxWarningsReached = false;
}

function frmResume_WarningSuccesCallback(result){
  voltmx.print("### frmResume_WarningSuccesCallback result: " + JSON.stringify(result));
  Global.vars.maxWarningsReached = false;
  if(result.opstatus === 0 && result.httpStatusCode == 200){
    if(result.response !==undefined && result.response.length > 0 && result.response[0] !== undefined && result.response[0].totalHits !== undefined && result.response[0].totalHits !== 0){
      voltmx.print("### frmResume_WarningSuccesCallback totalHits: " + result.response[0].totalHits);
      frmResume.lblHeaderWarnings.text = result.response[0].totalHits + " " + CaseData.caseinfo.ticketTypeDescription + " geschreven op feit: " + result.response[0].hits[0].offenceCode;
      frmResume.segWarnings.removeAll();
      var warnings = [];
      for(var i in result.response[0].hits){
        var v = result.response[0].hits[i];
        var addrecord = {
                      lbl1 : Utility_getLocalizedDateTimeString(new Date(v.utcDateTime), false)  + " - " + v.officerNumber
                      };
        warnings.push(addrecord);
      }
      voltmx.print("### frmResume_WarningSuccesCallback warnings: " + JSON.stringify(warnings));
      frmResume.segWarnings.setData(warnings);
      voltmx.print("### frmResume_WarningSuccesCallback number totalHits: " + Number(result.response[0].totalHits));
//       if(Number(result.response[0].totalHits) > 0){
//         voltmx.print("### frmResume_WarningSuccesCallback show popup");
//         frmResume_showWarnings();
//       }
      if(Global.vars.maxoccurences !== undefined && Global.vars.maxoccurences != null &&
         Global.vars.maxoccurences !== 0 && (Number(result.response[0].totalHits) > Number(Global.vars.maxoccurences))){
        voltmx.print("### frmResume_WarningSuccesCallback warnings exceed maxoccurences");
        frmResume.lblHeaderWarnings.text = result.response[0].totalHits + " keer " + CaseData.caseinfo.ticketTypeDescription + " geschreven op feit: " + result.response[0].hits[0].offenceCode;
        frmResume.lblMessageWarning.text = "Het maximum is overschreden kies een ander bontype!";
        Global.vars.maxWarningsReached = true;
        frmResume_validateShowFooter();
        voltmx.print("### frmResume_WarningSuccesCallback warnings exceed maxoccurences show the popup");
        frmResume_showWarnings();
        //kies procesverbaal automatisch
        voltmx.print("### frmResume_WarningSuccesCallback choose tickettype procesverbaal!!!");//NOG DOEN
//         Utility_getDefaultTicketType(Global.vars.enforcementLevel, CaseData.caseinfo.enforcementType);
//         try{
//           voltmx.timer.schedule("setDefaultTicketTypeOnForm",frmResume_setDefaultTicketTypeOnForm,1,false);
//         }catch(e){}
      }
    }else{
      voltmx.print("### frmResume_WarningSuccesCallback No warnings");
    }
  }
}

function frmResume_setDefaultTicketTypeOnForm(){
  voltmx.print("### frmResume_setDefaultTicketTypeOnForm");
  try{
    voltmx.timer.cancel("setDefaultTicketTypeOnForm");
  }catch(e){}
  frmResume_onselectTicketType();
}

function frmResume_showWarnings() {
    try {
      	voltmx.print("### frmResume_showWarnings flcMainPage disabled");
        frmResume.flcMainPage.setEnabled(false);
        frmResume.forceLayout();
        frmResume_flcWarnings_setVisibility(true);
        frmResume_showWarnings_preAnim();
        frmResume_showWarnings_animationStart();
    } catch (e) {
      voltmx.print("### frmResume_showWarnings error: " + JSON.stringify(e));
    }
}

function frmResume_showWarnings_preAnim() {
  try {
    voltmx.print("### frmResume_showWarnings_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
    //frmResume.flcWarningDetails.transform = trans1;
    //frmResume.imgPopupLogoWarnings.transform = trans1;
  } catch (e) {
    voltmx.print("### frmResume_showWarnings_preAnim error: " + JSON.stringify(e));
  }
}

function frmResume_showWarnings_arrangeWidgets() {
  try {
    voltmx.print("### frmResume_showWarnings_arrangeWidgets");
    //popup fields
    frmResume.imgPopupLogoWarnings.isVisible = false;
    frmResume.flcWarnings.forceLayout();
    frmResume.flcWarningDetails.isVisible = false;
    frmResume.lblLineWarnings.isVisible = false;
    frmResume.flcFooterWarnings.isVisible = false;
    frmResume_flcWarnings_setVisibility(false);
  } catch (e) {
    voltmx.print("### frmResume_showWarnings_arrangeWidgets error: " + JSON.stringify(e));
  }

}

function frmResume_showWarnings_animationStart(eventobject) {
  try {
    voltmx.print("### frmResume_showWarnings_animationStart");
    frmResume_flcWarnings_setVisibility(true);
    frmResume.flcWarningDetails.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmResume.flcWarningDetails.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmResume_Warnings_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmResume_showWarnings_animationStart error: " + JSON.stringify(e));
  }
}

function frmResume_Warnings_animLogo() {
  try {
    voltmx.print("### frmResume_Warnings_animLogo");
//     var trans = voltmx.ui.makeAffineTransform();
//     trans.scale(1.2, 1.2);
//     frmResume.imgPopupLogoWarnings.animate(
//       voltmx.ui.createAnimation({
//         "100": {
//           "anchorPoint": {
//             "x": 0.5,
//             "y": 0.5
//           },
//           "stepConfig": {
//             "timingFunction": voltmx.anim.EASE
//           },
//           "transform": trans,
//         }
//       }), {
//         "delay": 0,
//         "iterationCount": 1,
//         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
//         "duration": 0.25
//       }, {
//         "animationEnd": function (){
//           frmResume_showWarnings_animOtherWidgets(frmResume.flcWarningDetails);
//           frmResume_showWarnings_animOtherWidgets(frmResume.lblLineWarnings);
//           frmResume_showWarnings_animOtherWidgets(frmResume.flcFooterWarnings);
//         }
//       });
    frmResume_showWarnings_animOtherWidgets(frmResume.flcWarningDetails);
    frmResume_showWarnings_animOtherWidgets(frmResume.lblLineWarnings);
    frmResume_showWarnings_animOtherWidgets(frmResume.flcFooterWarnings);
    frmResume.imgPopupLogoWarnings.isVisible = true;
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_Warnings_animLogo error: " + JSON.stringify(e));
  }
}


function frmResume_showWarnings_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmResume_showWarnings_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmResume.flcWarningDetails.isVisible = true;
    frmResume.lblLineWarnings.isVisible = true;
    frmResume.flcFooterWarnings.isVisible = true;
    frmResume.flcFooterWarnings.setEnabled(true);
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showWarnings_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmResume_hideShowWarnings(){
  //activate footer and mainpage
  frmResume.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmResume_flcWarnings_setVisibility(false);
}

function frmResume_populateTicketType(){
  	var tickettypesList = [];
  	voltmx.print("### frmResume_populateTicketType CaseData.caseinfo.enforcementType: " + CaseData.caseinfo.enforcementType);
  	voltmx.print("### frmResume_populateTicketType CaseData.caseinfo.ticketType: " + CaseData.caseinfo.ticketType);
  	if(Global.vars.ticketTypes !== undefined && Global.vars.ticketTypes != null){
      for(var i=0; i<Global.vars.ticketTypes.length; i++){
        var v = Global.vars.ticketTypes[i];
        voltmx.print("### frmResume_populateTicketType tickettypesList: " + JSON.stringify(v));
        if (CaseData.caseinfo.enforcementType == v.enforcement_type){
          if (v.enforcement_level == "Y"){
            //if (CaseData.person[Global.vars.gCasePersonsIndex] !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].age < 16){
            if (CaseData.person[Global.vars.gCasePersonsIndex] !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].age < Global.vars.maxHaltAge){
              tickettypesList.push({key : v.id.toString(), 
                                value : v.description
                               });
            }
          } else if (v.enforcement_level == "G"){
              if(CaseData.offence.offenceCode !== undefined && CaseData.offence.offenceCode != null && CaseData.offence.offenceCode == "H022"){
                voltmx.print("### frmResume_populateTicketType G only for offenceCode H022");
                tickettypesList.push({key : v.id.toString(), 
                                  value : v.description
                                 });
              }
          } else{
            _validate = false;
            if (
              (v.description == "Toonrecht" || 
               v.description == "Alibi") && 
              (CaseData.processinfo.activeTaskType == "ValidateDocument" || 
               CaseData.processinfo.activeTaskType == "ValidateAlibi")
            ){
              _validate = true;
            }
            if ((frmHandleCharacteristic.lblSubHeader.text === Global.vars.UVB.description && Utility_stringToBoolean(Global.vars.UVB.UVB) === true) || _validate){
              //Combibon is UVB
              tickettypesList.push({key : v.id.toString(), 
                                value : v.description
                               });
            }else{
              //Gewone Combibon - laat hier Alibi en Toonrecht weg
              if(v.description !== "Toonrecht" && v.description !== "Alibi"){
                tickettypesList.push({key : v.id.toString(), 
                                value : v.description
                               });
              } 
            }
          }
        }
      }
      //Governance NL0022
      if ((frmHandleCharacteristic.lblSubHeader.text === Global.vars.governance.description && Utility_stringToBoolean(Global.vars.governance.governance) === true)){
        if(CaseData.offence.offenceCode !== undefined && CaseData.offence.offenceCode != null && CaseData.offence.offenceCode == "H022" && Global.vars.gInstanceId == "NL0036"){
          voltmx.print("### frmResume_populateTicketType G only for offenceCode H022 for governance");
          var description = "Mutatie";
          var id = 999;
          var exists = false;
          CaseData.caseinfo.ticketType = id;
          CaseData.caseinfo.ticketTypeDescription = "Mutatie";
          for(var a in tickettypesList){
            var b = tickettypesList[a];
            if(b.description == "Mutatie"){
              exists = true;
              voltmx.print("### frmResume_populateTicketType already exists");
            }
          }
          if(exists === false){
            tickettypesList.push({key : id.toString(), 
                                  value : description
                                 });
          }
        }
      }
      //Einde governace NL0022
      voltmx.print("### frmResume_populateTicketType tickettypesList: " + JSON.stringify(tickettypesList));
      frmResume.tickettype.lbxList.masterDataMap = [tickettypesList, "key", "value"];
      if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_followUp") && tickettypesList.length == 1){
      	frmResume_tickettype_setVisibility(false);
      }	else if (tickettypesList.length == 1){
        frmResume.tickettype.setEnabled(false);
        frmResume_tickettype_imgRight_setVisibility(false);
      } else {
        frmResume.tickettype.setEnabled(true);
        frmResume_tickettype_imgRight_setVisibility(true);
      }
      frmResume_setSelectedTicketType_from_caseInfo();
    }else{
      alert(voltmx.i18n.getLocalizedString("l_noTicketTypes"));
    }
}

function frmResume_setSelectedTicketType_from_caseInfo(){
  voltmx.print("### frmResume_setSelectedTicketType_from_caseInfo");
  voltmx.print("### frmResume_setSelectedTicketType_from_caseInfo ticketType: " + CaseData.caseinfo.ticketType);
  Global.vars.maxWarningsReached = false;
  if(CaseData.caseinfo.ticketType !== undefined && CaseData.caseinfo.ticketType != null){
    frmResume.tickettype.lbxList.selectedKey = CaseData.caseinfo.ticketType.toString();
    voltmx.print("### frmResume_setSelectedTicketType_from_caseInfo tickettype CaseData.caseinfo.ticketType: " + CaseData.caseinfo.ticketType);
    if(CaseData.caseinfo.enforcementLevel === "W" || CaseData.caseinfo.enforcementLevel === "G"){
      //frmResume_callWarningService();
      //tarief zetten
      if(CaseData.caseinfo.enforcementLevel === "G"){
        frmResume_setGovernMentalTarif();
      }else{
        frmResume_setOriginalOffenceTarif();
      }
    }else{
      frmResume_setOriginalOffenceTarif();
    }
  }
}

function frmResume_onselectTicketType(){
  Global.vars.maxWarningsReached = false;
  frmResume_validateShowFooter();
  frmResume.contentSize = {height : "100%", width : "100%"};
  frmResume.contentOffset = {"x":"0px", "y":"0px"};
  var tickettypeID = Number(frmResume.tickettype.lbxList.selectedKey);
  // NEW
  frmResume_getTicketType(tickettypeID);
  //#ifdef iphone	
  frmResume_contentOffset();
  //#endif
  frmResume.tickettype.lbxList.setFocus(false);
}

function frmResume_getTicketType(ticketTypeId){
  	voltmx.print("### frmResume_getTicketType for ticketTypeId: " + ticketTypeId);
  	if ((Utility_stringToBoolean(Global.vars.governance.governance) === true) && CaseData.offence.offenceCode == "H022" && Global.vars.gInstanceId == "NL0036" && ticketTypeId == 999){
      CaseData.caseinfo.ticketType = 999;
      CaseData.caseinfo.ticketTypeDescription = "Mutatie";
      //CaseData.caseinfo.ticketText = result[0].tickettext;
      CaseData.caseinfo.enforcementType = "S";
      CaseData.caseinfo.enforcementLevel = "G";      
    }else if(ticketTypeId != null){
      	var wcs = "select * from mle_v_ticket_type_m where id = '" + ticketTypeId + "'";
  		wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
  		wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
		voltmx.print("### frmResume_getTicketType wcs" + wcs);
		KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmResume_getTicketTypeSuccessCallback, frmResume_getTicketTypeErrorCallback);
    }
}

function frmResume_getTicketTypeSuccessCallback(result){
  voltmx.print("### frmResume_getTicketTypeSuccessCallback: " + JSON.stringify(result));
  if(result.length > 0){
    CaseData.caseinfo.ticketType = result[0].id;
    CaseData.caseinfo.ticketTypeDescription = result[0].description;
    CaseData.caseinfo.ticketText = result[0].ticket_text;
    CaseData.caseinfo.enforcementType = result[0].enforcement_type;
    CaseData.caseinfo.enforcementLevel = result[0].enforcement_level;
    if((CaseData.caseinfo.enforcementLevel === "W" || CaseData.caseinfo.enforcementLevel === "G") && frmResume.flcWarnings.isVisible === false){
//       if(result[0].checkperiod !== undefined && result[0].checkperiod != null){
//         frmResume_callWarningService(result[0].checkperiod);
//       }else{
//         frmResume_callWarningService();
//       }
      //tarief zetten
      if(CaseData.caseinfo.enforcementLevel === "G"){
        frmResume_setGovernMentalTarif();
      }else{
        frmResume_setOriginalOffenceTarif();
      }
    }else{
      frmResume_setOriginalOffenceTarif();
    }
    if (CaseData.caseinfo.enforcementLevel === "W" && Global.vars.hideItemsForWarning === true){
      frmResume_sanctioninfo_setVisibility(false);
      frmResume_statement_setVisibility(false);
      frmResume_option_setVisibility(false);
    } else {
      frmResume_sanctioninfo_setVisibility(true);
      frmResume_statement_setVisibility(true);
      frmResume_option_setVisibility(true);
    }
	// NEW
  	frmResume_validateShowFooter();
   }else{
    CaseData.caseinfo.ticketType = null;
    CaseData.caseinfo.ticketTypeDescription = null;
    CaseData.caseinfo.ticketText = null;
    CaseData.caseinfo.enforcementType = null;
    CaseData.caseinfo.enforcementLevel = null;
  }
  voltmx.print("### frmResume_getTicketTypeSuccessCallback Global.vars.offenceCities" + JSON.stringify(CaseData.caseinfo));
}

function frmResume_setGovernMentalTarif(){
  voltmx.print("### frmResume_setGovernMentalTarif amount: " + Global.vars.governmentalEnforcementAmount);
  if(CaseData.offence.amount !== voltmx.i18n.getLocalizedString("l_notariff_star")){
  	CaseData.offence.amount = Number(Global.vars.governmentalEnforcementAmount).toFixed(2);
  	CaseData.offence.amountDisplay = CaseData.offence.amount.formatCurrency(); 
  }else{
    CaseData.offence.amountDisplay = CaseData.offence.amount;
  }
  CaseData.offence.amountExtra = "";
  CaseData.offence.amountExtraDisplay = "";
  if(CaseData.offence.amount != null){
    frmResume.sanctioninfo.lblTotalAmount.text = CaseData.offence.amount.replace(".", ",");
    if(CaseData.offence.amount !== voltmx.i18n.getLocalizedString("l_notariff_star")){
    	frmResume.sanctioninfo.lblTotalAmount.text = frmResume.sanctioninfo.lblTotalAmount.text.toLocaleString();
    }
  }
  if(CaseData.offence.amountDisplay != null){
    frmResume.sanctioninfo.lblTotalAmountCurrency.text = CaseData.offence.amountDisplay.replace(/[^a-zA-Z]+/g, '');
  }
}

function frmResume_setOriginalOffenceTarif(){
  voltmx.print("### frmResume_setOriginalOffenceTarif amount: " + Global.vars.originalOffenceAmounts);
  if(Global.vars.originalOffenceAmounts.amount !== voltmx.i18n.getLocalizedString("l_notariff_star")){
  	CaseData.offence.amount = Number(Global.vars.originalOffenceAmounts.amount).toFixed(2);
  }else{
    CaseData.offence.amount = Global.vars.originalOffenceAmounts.amount;
  }
  CaseData.offence.amountDisplay = Global.vars.originalOffenceAmounts.amountDisplay; 
  CaseData.offence.amountExtra = Global.vars.originalOffenceAmounts.amountExtra;
  CaseData.offence.amountExtraDisplay = Global.vars.originalOffenceAmounts.amountExtraDisplay;
  if(CaseData.offence.amount != null){
    frmResume.sanctioninfo.lblTotalAmount.text = CaseData.offence.amount.replace(".", ",");
    if(CaseData.offence.amount !== voltmx.i18n.getLocalizedString("l_notariff_star")){
    	frmResume.sanctioninfo.lblTotalAmount.text = frmResume.sanctioninfo.lblTotalAmount.text.toLocaleString();
    }
  }
  if(CaseData.offence.amountDisplay != null){
    frmResume.sanctioninfo.lblTotalAmountCurrency.text = CaseData.offence.amountDisplay.replace(/[^a-zA-Z]+/g, '');
  }
}

function frmResume_getTicketTypeErrorCallback(error){
  voltmx.print("### frmResume_getTicketTypeErrorCallback" + JSON.stringify(error));
}

// function frmResume_populateChooseTaskOutcome(){
//   	var taskOutcomeList = [];
// 	for(var i=0; i<Global.vars.outcomeTypes.length; i++){
// 		taskOutcomeList.push({key : Global.vars.outcomeTypes[i].identification, 
// 							  value : Global.vars.outcomeTypes[i].description
// 							 });
//     }
//     voltmx.print("### frmResume_populateChooseTaskOutcome taskOutcomeList: " + JSON.stringify(taskOutcomeList));
// 	frmResume.lbxChooseTaskOutcome.masterDataMap = [taskOutcomeList, "key", "value"];
//   	frmResume.lbxChooseTaskOutcome.selectedKey = null;
//   	frmResume.flcChooseTaskOutcome.skin = flcFieldEdgeRed;
// }

// function frmResume_onselectChooseTaskOutcome(){
//   voltmx.print("### frmResume_onselectChooseTaskOutcome selectedKey: " + frmResume.lbxChooseTaskOutcome.selectedKey);
//   frmResume.flcChooseTaskOutcome.skin = flcFieldEdge;
//   frmResume.flcHeader.setFocus(true);
// }

function frmResume_setTaskOutcomeOnForm(){
  voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter Global.vars.outcomeTypes: " + JSON.stringify(Global.vars.outcomeTypes));
  var showFooter = true;
  if((Global.vars.outcomeTypes.length > 1 && (Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV")) && (CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod" || CaseData.processinfo.activeTaskType == "CorrigerenVerbod" || CaseData.processinfo.activeTaskType == "OverdrachtPolitie")){
    //get selected taskoutcome
    var _notMandatoryForOutcome = -1;
    if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" && 
       Global.vars.selectedOutcome !== undefined && Global.vars.selectedOutcome != null 
       && Global.vars.selectedOutcome.identification !== undefined && Global.vars.selectedOutcome.identification != null){
      _notMandatoryForOutcome = Utility_getIndexIfObjWithAttr(Global.vars.notMandatoryForOutcome,"outcome",Global.vars.selectedOutcome.identification);
    }
    voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter set Taskoutcome more then 1");
    if(Global.vars.selectedOutcome === null){
      voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter no taskoutcome chosen");
      showFooter = false;
    }
    if((frmResume.policeofficeremail.txtInputText.text === null || frmResume.policeofficeremail.txtInputText.text === "" || CaseData.prohibitions.policeOfficerEmail === null || CaseData.prohibitions.policeOfficerEmail === "") && CaseData.processinfo.activeTaskType == "OverdrachtPolitie"){
      voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter: policeofficer email is leeg");
      //
      if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" && Global.vars.selectedOutcome !== undefined && 
         Global.vars.selectedOutcome != null && Global.vars.selectedOutcome.identification !== undefined && 
         Global.vars.selectedOutcome.identification != null && _notMandatoryForOutcome !== -1){
        voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter: policeofficer email is leeg maar taakuitkomst is nietOverTeDragen");
        //Zet checks uit
        frmResume.policeofficeremail.skin = flcFieldEdge; 
      }else{
        voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter: policeofficer email is leeg 2");
        showFooter = false;
        frmResume.policeofficeremail.skin = flcFieldEdgeRed;
      }   
    }else{
      if(CaseData.prohibitions.policeOfficerEmail !== undefined && CaseData.prohibitions.policeOfficerEmail != null && voltmx.string.isValidEmail(CaseData.prohibitions.policeOfficerEmail) === false){
        showFooter = false;
        voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter: policeofficer email is not valid");
        frmResume.policeofficeremail.skin = flcFieldEdgeRed;
      }else if(CaseData.prohibitions.policeOfficerEmail !== undefined && CaseData.prohibitions.policeOfficerEmail != null && voltmx.string.isValidEmail(CaseData.prohibitions.policeOfficerEmail) === true){
        var isEmailInDomain = false;
        for(var a in Global.vars.prohibitionEmailDomain){
          var domainMail = Global.vars.prohibitionEmailDomain[a].domain;
          if(CaseData.prohibitions.policeOfficerEmail.endsWith("@" + domainMail) === true){
            isEmailInDomain = true;
            break;
          }
        }
        if(isEmailInDomain){
          voltmx.print("### frmResume_setTaskOutcomeOnForm: policeofficer email in domain");
          frmResume.policeofficeremail.skin = flcFieldEdge; 
        }else{
          voltmx.print("### frmResume_setTaskOutcomeOnForm: policeofficer email NOT in domain");
          showFooter = false;
          frmResume.policeofficeremail.skin = flcFieldEdgeRed;
        }
      }else{
        frmResume.policeofficeremail.skin = flcFieldEdge; 
      }
    }
    if((frmResume.policeofficermobile.txtInputText.text === null || frmResume.policeofficermobile.txtInputText.text === "" || CaseData.prohibitions.policeOfficerMobile === null || CaseData.prohibitions.policeOfficerMobile === "") && CaseData.processinfo.activeTaskType == "OverdrachtPolitie"){
      voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter: policeoficer mobile is leeg");
      //
      if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" && Global.vars.selectedOutcome !== undefined && 
         Global.vars.selectedOutcome != null && Global.vars.selectedOutcome.identification !== undefined && 
         Global.vars.selectedOutcome.identification != null && _notMandatoryForOutcome !== -1){
        frmResume.policeofficermobile.skin = flcFieldEdge;
      }else{
        showFooter = false;
        frmResume.policeofficermobile.skin = flcFieldEdgeRed;
      }
    }else if(CaseData.processinfo.activeTaskType == "OverdrachtPolitie" && (Global.vars.selectedOutcome === undefined || 
                                                                            Global.vars.selectedOutcome === null || Global.vars.selectedOutcome.identification === undefined || 
                                                                            Global.vars.selectedOutcome.identification === null || Global.vars.selectedOutcome.identification != "nietOverTeDragen")){
      var str = CaseData.prohibitions.policeOfficerMobile;
      var is10Digits = (/^\d{10}$/.test(str));
      if(is10Digits === false){
        voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter: policeoficer mobile niet 10 cijfers");
        showFooter = false;
        frmResume.policeofficermobile.skin = flcFieldEdgeRed;
      }else{
        if(str.startsWith("06") === false){
          voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter: policeoficer mobile niet 06");
          showFooter = false;
          frmResume.policeofficermobile.skin = flcFieldEdgeRed;
        }else{
          frmResume.policeofficermobile.skin = flcFieldEdge;
        }
      }
    }
  }else if((Global.vars.outcomeTypes.length > 1 && Global.vars.buildFor !== "NS") && (activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi")){
    //get selected taskoutcome
    voltmx.print("### frmResume_setTaskOutcomeOnForm validateShowFooter set Taskoutcome more then 1 not NS");
    if(Global.vars.selectedOutcome === null){
      showFooter = false;
    }
  }
  if(showFooter === true){
    frmResume_flcFooterMain_setVisibility(true);
    frmResume.flcsLayout.bottom = '117dp';
  }else{
    frmResume_flcFooterMain_setVisibility(false);
    frmResume.flcsLayout.bottom = '44dp';
  }
  voltmx.print("### frmResume_setTaskOutcomeOnForm Global.vars.selectedOutcome: " + JSON.stringify(Global.vars.selectedOutcome));
 // if((Global.vars.outcomeTypes.length === 1 || Global.vars.buildFor == "NS") && (activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || CaseData.processinfo.activeTaskType == "ValidateAlibi" || CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod" || CaseData.processinfo.activeTaskType == "OverdrachtPolitie")){
  var _unhandledTaskTypes = Utility_getIndexIfObjWithAttr(Global.vars.unhandledTaskTypes,"taskType",CaseData.processinfo.activeTaskType);
  var _caseType = " ";
  if (CaseData.caseinfo != null && CaseData.caseinfo.caseType != null){
    _caseType = CaseData.caseinfo.caseType;
  }
  if((Global.vars.outcomeTypes.length === 1) && (activeTaskTypeInOpenTaskTypesToQuery === true || CaseData.processinfo.activeTaskType == "ValidateDocument" || 
                                                 CaseData.processinfo.activeTaskType == "ValidateAlibi" || CaseData.processinfo.activeTaskType == "AanvullenDirectVerbod" || 
                                                 CaseData.processinfo.activeTaskType == "OverdrachtPolitie" || (_caseType.startsWith("MLD_") && Global.vars.backToPreviousForm == "frmFollow") || _unhandledTaskTypes !== -1)){
    frmResume_taskoutcome_setVisibility(false);
    frmResume.taskoutcome.skin = flcFieldEdge;
    frmResume.taskoutcome.lblText.text =  voltmx.i18n.getLocalizedString("l_outcome");
    frmResume.taskoutcome.lblText.skin = lblFieldInfo;
    frmResume.btnDone.text = Global.vars.outcomeTypes[0].name;
    voltmx.print("### frmResume_setData Global.vars.outcomeTypes: " + JSON.stringify(Global.vars.outcomeTypes));
    if (Global.vars.outcomeTypes[0].ind_comment_required === 1){
      Global.vars.indCommentrequired = true;
    } else{
      Global.vars.indCommentrequired = false;
    }
    var _resumeDisabledForOutcome = -1;
    _resumeDisabledForOutcome = Utility_getIndexIfObjWithAttr(Global.vars.resumeDisabledForOutcome,"outcome",Global.vars.outcomeTypes[0].identification);
    if (_resumeDisabledForOutcome !== -1 && Global.vars.indCommentrequired == false){
      frmResume.flcLayoutInput.setEnabled(false);
    }
    frmResume_validateShowFooter();
  } else {
    frmResume.btnDone.text = voltmx.i18n.getLocalizedString("bt_send");
  	frmResume_taskoutcome_setVisibility(true);
    if(Utility_isEmptyObject(Global.vars.selectedOutcome) === false && Global.vars.selectedOutcome != null){
      frmResume.taskoutcome.skin = flcFieldEdge;
      frmResume.taskoutcome.lblText.text = Global.vars.selectedOutcome.description;
      frmResume.taskoutcome.lblText.skin = lblFieldInfo;
      if (Global.vars.selectedOutcome.ind_comment_required === 1){
        Global.vars.indCommentrequired = true;
      } else{
        Global.vars.indCommentrequired = false;
      }
      frmResume_validateShowFooter();
    }else{
      frmResume.taskoutcome.skin = flcFieldEdgeRed;
      frmResume.taskoutcome.lblText.text = voltmx.i18n.getLocalizedString("l_outcome");
      frmResume.taskoutcome.lblText.skin = lblFieldNotFilled;
      if ( _unhandledTaskTypes !== -1){
        frmResume_validateShowFooter();
      }
    }
  }
  voltmx.print("### frmResume_setTaskOutcomeOnForm 2: " + frmResume.btnDone.text);
}

function frmResume_onclick_btnSetTaskOutcome(){
  voltmx.print("### frmResume_onclickChooseTaskOutcome");
  Global.vars.previousForm = "frmResume";
  if(frmResume.policeofficeremail.isVisible === true && frmResume.policeofficeremail.txtInputText.text === ""){
    Global.vars.prohibitionsFillPoliceOfficerEmailFromCase = false;
  }else{
    Global.vars.prohibitionsFillPoliceOfficerEmailFromCase = true;
  }
  if(frmResume.policeofficermobile.isVisible === true && frmResume.policeofficermobile.txtInputText.text === ""){
    Global.vars.prohibitionsFillPoliceOfficerMobileFromCase = false;
  }else{
    Global.vars.prohibitionsFillPoliceOfficerMobileFromCase = true;
  }
  frmTaskOutcomes.show();
}

function frmResume_clearMultimedia(){
  frmResume_multimedia_flcPhoto1_setVisibility(false);
  frmResume_multimedia_flcPhoto2_setVisibility(false);
  frmResume_multimedia_flcPhoto3_setVisibility(false);
  frmResume_multimedia_lblMorePhotos_setVisibility(false);
  frmResume_multimedia_flcNoMedia_setVisibility(true);
}

function frmResume_historyCheck(){
  frmResume.segHistoryCases.widgetDataMap = {lblDate:"caseDate", lblCaseID:"externalId", lblStatus:"statusDescription", lblValue:"offenceDescription", lblPerson:"person", lblOffence:"offenceCode", lblTicketType:"ticketTypeDesc", lblLetterCode:"letterCode", flcColorDot:"colorDot", flcPersonName:"flcPersonName", flcVehicle:"flcVehicle", lblCountryCode:"countryLicense", lblVehiclePlate:"licensePlate",lblBrand:"brand", lblLocationHistory:"location"};
  voltmx.print("### frmResume_historyCheck");
  if(CaseData.person[Global.vars.gCasePersonsIndex] !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].ssn != null && CaseData.person[Global.vars.gCasePersonsIndex].ssn !== ""){
  	service_GetHistory(frmResume_getHistorySuccesCallback, frmResume_getHistoryErrorCallback, CaseData.person[Global.vars.gCasePersonsIndex].ssn, null, null);
  } else {
    frmResume_resetHistory();
  }
}

function frmResume_getHistorySuccesCallback(result){
  var history = result.result[0].hits;
  voltmx.print("#### frmResume_getHistorySuccesCallback result aantal: " + history.hits.length);
  var segmentData = [];
  frmResume.segHistoryCases.removeAll();
  frmResume.segHistoryCases.widgetDataMap = {lblDate:"caseDate", lblCaseID:"externalId", lblStatus:"statusDescription", lblValue:"offenceDescription", lblPerson:"person", lblOffence:"offenceCode", lblTicketType:"ticketTypeDesc", lblLetterCode:"letterCode", flcColorDot:"colorDot", flcPersonName:"flcPersonName", flcVehicle:"flcVehicle", lblCountryCode:"countryLicense", lblVehiclePlate:"licensePlate",lblBrand:"brand", lblLocationHistory:"location", lblExtraInformation:"extrainformation"};
  var fiscalCounter = 0;
  var parkingCounter = 0;
  var combibonCounter = 0;
  var showHistory = false;
  var d = new Date();
  var curr_date = d.getDate();
  var curr_month = d.getMonth();
  var curr_year = d.getFullYear();
  var currentDay = new Date(curr_year, curr_month, curr_date, 0, 0, 0, 0);
  for(var i = 0; i < history.hits.length; i++){
    voltmx.print("#### frmResume_getHistorySuccesCallback casesResponse v: " + JSON.stringify(history.hits[i]));
    var doc = history.hits[i]._source.doc;
    var showInHistory = false;
    voltmx.print("#### frmResume_getHistorySuccesCallback casesResponse v: " + JSON.stringify(v));
    var v = doc.case;
	//
    if (v.status !== "CreateCase"){
      var historyDate = null;
      if (showHistory === false){
        historyDate = new Date(v.time.utcDateTime);
        if(Global.vars.showHistoryPopup === true){
          showHistory = (historyDate >= currentDay); 
        }
        voltmx.print("#### frmResume_getHistorySuccesCallback show currentDay: " + showHistory + " " + v.time.utcDateTime);
      }
      // caseinfo
      v.caseDate = Utility_getLocalizedDateTimeString(new Date(v.time.utcDateTime),false);
      v.couchID = doc._id;
      v.externalId = v.caseinfo.externalId.toString();
      if(Global.vars.buildFor !== "NS"){
        v.statusDescription = v.status;
        if (v.processinfo !== undefined && v.processinfo.statuses !== undefined){
          for (var j = 0; j < v.processinfo.statuses.length; j++){
            var statusRec = v.processinfo.statuses[j];
            if (statusRec.status === v.status){
              v.statusDescription = statusRec.statusDescription;
              break;
            }
          }
        }
      }else{
        v.statusHeader = "";
        v.statusDescription = "";
      }
      if ( Global.vars.showInHistoryPopup.length > 0){
        if (v.processinfo !== undefined && v.processinfo.statuses !== undefined){
          if (v.processinfo.statuses.length > 0){
            var statusRec2 = v.processinfo.statuses[v.processinfo.statuses.length -1];
            for (var ii = 0; ii < Global.vars.showInHistoryPopup.length; ii++) {
              var ww =  Global.vars.showInHistoryPopup[ii];
              voltmx.print("### frmRegister_getHistorySuccesCallback global status: " + ww);
              voltmx.print("### frmRegister_getHistorySuccesCallback case status: " + statusRec2.status);
              if (ww === statusRec2.status){
                showInHistory = true;
                break;
              } 
            }
          }
        }
      } else {
        showInHistory = true;
      }
      // ticketinfo
      v.letterCode = "";
      v.ticketTypeDesc = Utility_returnTicketTypeDesc(v.caseinfo.enforcementType, v.caseinfo.enforcementLevel);
      v.colorDot = {"skin" : flcMarkings};
      if (showInHistory === true){
        if(v.caseinfo.caseType == "TICKET_M"){
          combibonCounter++;
          v.letterCode = "C";
          if (voltmx.string.startsWith(Global.vars.gInstanceId,"BE") === true){
            v.letterCode = "G";
          }
          v.colorDot = {"skin" : flcMarkingsLawEnforceRed};
        }else if(v.caseinfo.caseType == "TICKET_MP"){
          combibonCounter++;
          v.letterCode = "C";
          if (voltmx.string.startsWith(Global.vars.gInstanceId,"BE") === true){
            v.letterCode = "G";
          }
          v.colorDot = {"skin" : flcMarkingsLawEnforceRed};
        }else if(v.caseinfo.caseType == "TICKET_F"){
          fiscalCounter++;
          v.letterCode = "F";
          if(voltmx.string.startsWith(Global.vars.gInstanceId,"BE") === true){
            v.letterCode = "R";
          }
          v.colorDot = {"skin" : flcMarkingsParkingBlue};
        }else if(v.caseinfo.caseType == "TICKET_V"){
          parkingCounter++;
          v.letterCode = "P";
          if(voltmx.string.startsWith(Global.vars.gInstanceId,"BE") === true){
            v.letterCode = "P";
          }
          v.colorDot = {"skin" : flcMarkingsParkingBlue};
        }
        // vehicle
        if (doc.case.vehicle !== undefined && v.vehicle[0].identNumber !== undefined && v.vehicle[0].identNumber != null){
          v.flcVehicle = {"isVisible":true};
          v.licensePlate = v.vehicle[0].identNumberFormatted === undefined ? (v.vehicle[0].identNumber === undefined ? "" : v.vehicle[0].identNumber) : v.vehicle[0].identNumberFormatted;
          v.countryLicense = v.vehicle[0].countryLicense;
          v.brand = v.vehicle[0].brandDesc;
        } else {
          v.flcVehicle = {"isVisible":false};
          v.licensePlate = "";
          v.countryLicense = "";
          v.brand = "";
        }
        // person
        if (doc.case.person !== undefined && v.person[0].surname !== undefined && v.person[0].surname != null && v.person[0].surname.length > 0){
          v.flcPersonName = {"isVisible":true};
          var initials = "";
          var middlename = "";
          var surname = "";
          if (v.person[0].initials === undefined || v.person[0].initials === null || v.person[0].initials.length === 0){
            initials = "";
          } else {
            initials = v.person[0].initials;
          }
          if (v.person[0].middlename === undefined || v.person[0].middlename === null || v.person[0].middlename.length === 0){
            middlename = "";
          } else {
            middlename = v.person[0].middlename;
          }
          if (v.person[0].surname === undefined || v.person[0].surname === null || v.person[0].surname.length === 0){
            surname = "";
          } else {
            surname = v.person[0].surname;
          }
          v.person = ((initials + " " + middlename).trim(" ") + " " + surname).trim(" ");
        } else {
          v.flcPersonName = {"isVisible":false};
          v.person = "";
        }
        // offence
        if (v.offence.offenceDescription !== undefined && v.offence.offenceDescription != null && v.offence.offenceDescription !== ""){
          v.offenceDescription = v.offence.offenceDescription;
        }else{
          v.offenceDescription = "";
        }
        v.offenceCode = v.offence.offenceCode;
        // location
        v.location = frmHistory_Setlocation(v.location.street, v.location.city);

        if(v.text !== undefined && v.text != null && v.text.length > 0 && Global.vars.disableExtraInfo === false){
          v.extrainformation =  voltmx.i18n.getLocalizedString("l_extraInformation");
        } else {
          v.extrainformation = "";
        }
        // push
        segmentData.push(v);
      }
    }  
  }
  voltmx.print("#### frmResume_getHistorySuccesCallback segmentData: " + JSON.stringify(segmentData));
  if (segmentData.length > 0){
  	frmResume.segHistoryCases.setData(segmentData);
  	frmResume.lblHeaderHistory.text = voltmx.i18n.getLocalizedString("l_previousFines") + " (" + segmentData.length + ")";
    frmResume.history.lblHeader.text = voltmx.i18n.getLocalizedString("l_previousFines") + " (" + segmentData.length + ")";
    var counterText = "";
    var combibon = "Combibon";
    var nha = "NHA";
    var parking = "Parking";
    if (voltmx.string.startsWith(Global.vars.gInstanceId,"BE") === true){
      combibon = "GAS";
      nha = "Retributie";
      parking = "Parking";
    }
    if (fiscalCounter > 0){
      counterText = nha + " (" + fiscalCounter + ")";
    }
    if (parkingCounter > 0){
      if (counterText.length > 0){
        counterText = counterText + " / " + parking + " (" + parkingCounter + ")";
      } else {
        counterText = parking + " (" + parkingCounter + ")";
      }
    }
    if (combibonCounter > 0){
      if (counterText.length > 0){
        counterText = counterText + " / " + combibon + " (" + combibonCounter + ")";
      } else {
        counterText = combibon + " (" + combibonCounter + ")";
      }
    }
    frmResume.history.lblText.text =  counterText;
    frmResume_history_setVisibility(true);
    if (showHistory === true){
      frmResume_showHistory();
    }
  } else {  
    frmResume_resetHistory();
  }
  voltmx.application.dismissLoadingScreen();
}

function frmResume_resetHistory(){
   	frmResume.lblHeaderHistory.text = voltmx.i18n.getLocalizedString("l_previousFines");
    frmResume.history.lblHeader.text = voltmx.i18n.getLocalizedString("l_previousFines");
    frmResume.history.lblText.text = "Geen zaken";
    frmResume.segHistoryCases.removeAll();
  	frmResume_history_setVisibility(false);
	frmResume_flcHistory_setVisibility(false);
}

function frmResume_getHistoryErrorCallback(error){
  voltmx.print("#### frmResume_getHistoryErrorCallback error: " + JSON.stringify(error));
  voltmx.application.dismissLoadingScreen();
}

function frmResume_showHistory(){
  //activate footer and mainpage
  frmResume.flcMainPage.setEnabled(false);
  voltmx.print("### flcMainPage enabled");
  frmResume_flcHistory_setVisibility(true);
}

function frmResume_hideHistory(){
  //activate footer and mainpage
  if (frmResume.segHistoricCaseText.isVisible === true){
    frmResume_segHistoricCaseText_setVisibility(false);
    frmResume_segHistoryCases_setVisibility(true);
    frmResume.btnCancelHistoricCase.text = voltmx.i18n.getLocalizedString("l_close");
  } else {
    frmResume.flcMainPage.setEnabled(true);
    voltmx.print("### flcMainPage enabled");
    frmResume_flcHistory_setVisibility(false);
  }
}

function frmResume_onclick_segHistoricCase(){
  var selectedCase = frmResume.segHistoryCases.selectedItems[0];
  voltmx.print("#### frmResume_onclick_segHistoricCase selectedCase: " + JSON.stringify(selectedCase));
  if(selectedCase.text !== undefined && selectedCase.text != null && selectedCase.text.length > 0){
    var segmentTextData = [];
    frmResume.segHistoricCaseText.removeAll();
    frmResume.segHistoricCaseText.widgetDataMap = {lblValueHeader:"textType",  lblValue:"textDescription"};
    for(var i = 0; i < selectedCase.text.length; i++){
      var v = selectedCase.text[i];
      voltmx.print("#### frmResume_onclick_segHistoricCase caseText v: " + JSON.stringify(v));
      // offence
      if (v.type == 4){
        v.textType = voltmx.i18n.getLocalizedString("l_optionsVariables");
        v.orderSequence = "3_";
      } else if (v.type == 2){
        v.textType = voltmx.i18n.getLocalizedString("l_ownStatement");
        v.orderSequence = "2_";
      } else if (v.type == 1){
        v.textType = voltmx.i18n.getLocalizedString("l_observation");
        v.orderSequence = "1_";
      } else {
        v.textType = voltmx.i18n.getLocalizedString("l_description");
        v.orderSequence = "4_";
      }
      if (v.value !== undefined && v.value != null && v.value !== ""){
        voltmx.print("#### #### frmResume_onclick_segHistoricCase value " + v.value);
        v.textDescription = v.value;
//         if (v.type !== 2){// GEEN VERKLARING VERDACHTE
          segmentTextData.push(v);
//         }
      }
    }
    voltmx.print("#### #### frmResume_onclick_segHistoricCase segmentTextData: " + JSON.stringify(segmentTextData));
    if (segmentTextData.length > 0){
      segmentTextData.sort((a, b) => a.orderSequence.localeCompare(b.orderSequence, undefined, { numeric: true, sensitivity: 'base' }));
		
      frmResume.segHistoricCaseText.setData(segmentTextData);
      frmResume.btnCancelHistoricCase.text = voltmx.i18n.getLocalizedString("bt_back");
      frmResume_segHistoricCaseText_setVisibility(true);
	  frmResume_segHistoryCases_setVisibility(false);
	}
    voltmx.application.dismissLoadingScreen();
  } else {
    voltmx.application.dismissLoadingScreen();  
  }
}

function frmResume_showPersonDocumentDetails(){
  //if((Global.vars.appMode == voltmx.i18n.getLocalizedString("l_register") || Global.vars.appMode == "Registreer") && ((Global.vars.buildFor == 'NS' || Global.vars.buildFor == 'OV') || Global.vars.validatePersonDocument === false) && CaseData.person !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].edited === true){
  if(((Global.vars.buildFor == "NS" || Global.vars.buildFor == "OV") || Global.vars.validatePersonDocument === false) && CaseData.person !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].edited === true){
    var personinfo = JSON.parse(JSON.stringify(CaseData.person[Global.vars.gCasePersonsIndex]));
    Global.vars.originalPersonDocumentInfo.idenDocType = personinfo.idenDocType;
    Global.vars.originalPersonDocumentInfo.idenDocTypeDesc = personinfo.idenDocTypeDesc;
    Global.vars.originalPersonDocumentInfo.countryIdenDoc = personinfo.countryIdenDoc;
    Global.vars.originalPersonDocumentInfo.countryIdenDocDesc = personinfo.countryIdenDocDesc;
    Global.vars.originalPersonDocumentInfo.documentNumber = personinfo.documentNumber;
    Global.vars.originalPersonDocumentInfo.documentTypeCheckable = personinfo.documentTypeCheckable;
    Global.vars.originalPersonDocumentInfo.documentNumberChecked = personinfo.documentNumberChecked;
    Global.vars.originalPersonDocumentInfo.documentNumberValid = personinfo.documentNumberValid;
    var documentDescription = "";
    for (var p=0; ((CaseData.text) != null) && p < CaseData.text.length; p++ ){
      var v = CaseData.text[p];
      if((v.type == 3 && voltmx.string.startsWith(v.value, "Beschrijving document(en): "))){ //beschrijving documenten
        voltmx.print("#### frmResume_onclick_btnPerson: description document: " + v + " index: " + p);
        documentDescription = v.value;
        documentDescription = documentDescription.replace("Beschrijving document(en): ", "");
        break;
      }
    }
    Global.vars.originalPersonDocumentInfo.documentAdditionalDescription = documentDescription;
    Global.vars.originalPersonDocumentInfo.formToGoBackTo = "frmResume";
    Global.vars.gCasePersons = JSON.parse(JSON.stringify(CaseData.person[Global.vars.gCasePersonsIndex]));
    frmPersonDocument.show();
	//frmResume_showSetDocumentDescription();
  }
}

function frmResume_setEnforcementObjectDetailsOnForm(){
  voltmx.print("### frmResume_setEnforcementObjectDetailsOnForm");
  if(CaseData.enforcementObject !== undefined && CaseData.enforcementObject != null){
    voltmx.print("### frmResume_setEnforcementObjectDetailsOnForm CaseData.enforcementObject: " + JSON.stringify(CaseData.enforcementObject));
    var name = "";
    if(CaseData.enforcementObject.name !== undefined && CaseData.enforcementObject.name != null && CaseData.enforcementObject.name !== ""){
      name = CaseData.enforcementObject.name;
    }
    voltmx.print("### frmResume_setEnforcementObjectDetailsOnForm name: " + name);
  	if(name !== ""){
      voltmx.print("### frmResume_setEnforcementObjectDetailsOnForm if");
      frmResume.enforcementobject.lblText.text = name;
      frmResume.enforcementobject.lblText.skin = lblFieldInfo;
      frmResume_enforcementobject_setVisibility(true); //RKA
      frmResume_setEnforcementQuestionDetailsOnForm();
  	}else{
      voltmx.print("### frmResume_setEnforcementObjectDetailsOnForm else");
      voltmx.print("### frmResume_setEnforcementObjectDetailsOnForm name: " + name);
      frmResume_enforcementobject_setVisibility(false); //RKA
      frmResume_clearEnforcementObject();
    }
  }else{
    voltmx.print("### frmResume_setEnforcementObjectDetailsOnForm else frmResume_clearEnforcementObject");
    frmResume_clearEnforcementObject();
  }
}

function frmResume_clearEnforcementObject(){
  voltmx.print("### frmResume_clearEnforcementObject");
  //clear field
  frmResume.enforcementobject.lblText.text = voltmx.i18n.getLocalizedString("l_enforcementObject");
  frmResume.enforcementobject.lblText.skin = lblFieldNotFilled;
}

function frmResume_setEnforcementQuestionDetailsOnForm(){
  voltmx.print("### frmResume_setEnforcementQuestionDetailsOnForm");
  voltmx.print("### frmResume_setEnforcementQuestionDetailsOnForm CaseData.questions: " + JSON.stringify(CaseData.questions));
  if(CaseData.questions !== undefined && CaseData.questions != null){
    var questionType = "";
    for(var i in CaseData.questions){
      var v = CaseData.questions[i];
      if(v.option_description !== undefined && v.option_description != null && v.option_description !== ""){
        questionType = v.option_description + ", ingevuld";
        break;
      }
    }
    if(questionType !== ""){
      frmResume.questions.lblText.text = questionType;
      frmResume.questions.lblText.skin = lblFieldInfo;
      frmResume_questions_flcDelete_setVisibility(true);
    }else{
      frmResume_clearQuestions();
    }
  }else{
    frmResume_clearQuestions();
  }
}

function frmResume_questionsSet(questionsset){
  voltmx.print("### frmResume_questionsSet: " + questionsset);
  voltmx.print("### frmResume_setEnforcementQuestionDetailsOnForm CaseData.questions: " + JSON.stringify(CaseData.questions));
  if(CaseData.questions !== undefined && CaseData.questions != null && questionsset === true){
    var questionType = "";
    for(var i in CaseData.questions){
      var v = CaseData.questions[i];
      if(v.option_description !== undefined && v.option_description != null && v.option_description !== ""){
        questionType = v.option_description + ", ingevuld";
        break;
      }
    }
    if(questionType !== ""){
      frmResume.questions.lblText.text = questionType;
      frmResume.questions.lblText.skin = lblFieldInfo;
      frmResume_questions_flcDelete_setVisibility(true);
    }else{
      frmResume.questions.lblText.text = voltmx.i18n.getLocalizedString("l_questions");
      frmResume.questions.lblText.skin = lblFieldNotFilled;
      frmResume_questions_flcDelete_setVisibility(false);
    }
  }else{
      frmResume.questions.lblText.text = voltmx.i18n.getLocalizedString("l_questions");
      frmResume.questions.lblText.skin = lblFieldNotFilled;
      frmResume_questions_flcDelete_setVisibility(false);
  }
}

function frmResume_clearQuestions(){
  voltmx.print("### frmResume_clearQuestions");
  //clear field
  frmResume.questions.lblText.text = voltmx.i18n.getLocalizedString("l_questions");
  frmResume.questions.lblText.skin = lblFieldNotFilled;
  //Utility_clearEnforcementObjectQuestions();
  CaseData.questions = [];
  Global.vars.QuestionsSet = false;
  frmResume_questions_flcDelete_setVisibility(false);
  if (CaseData.caseinfo.caseType !== undefined && CaseData.caseinfo.caseType != null && Utility_questionsMandatory(Global.vars.questionTypesUsage) === false ){
    Global.vars.questionsClearedOnResume = false;//RKO
  } else {
    Global.vars.questionsClearedOnResume = true;
  }
  frmResume_validateShowFooter();
}

function frmResume_onclick_btnRemoveDuplicate(){
  voltmx.print("### frmResume_onclick_btnRemoveDuplicate");
  frmResume.setvehicleinfo.duplicatepopup.txtInputText.text = "";
  frmResume.setvehicleinfo.duplicatepopup.txtInputText.setFocus(true);
}

function frmResume_onclick_btnRemoveRegion(){
  voltmx.print("### frmResume_onclick_btnRemoveRegion");
  frmResume.setvehicleinfo.regioncodepopup.txtInputText.text = "";
  frmResume.setvehicleinfo.regioncodepopup.skin = flcFieldEdgeRed;
  frmResume.setvehicleinfo.regioncodepopup.txtInputText.setFocus(true);
}

function frmResume_ontextChange_regioncodepopup(){
  voltmx.print("### frmResume_ontextChange_regioncodepopup");
  if (frmResume.setvehicleinfo.regioncodepopup.txtInputText.text != null && frmResume.setvehicleinfo.regioncodepopup.txtInputText.text.length > 0){
    frmResume.setvehicleinfo.regioncodepopup.skin = flcFieldEdge;
  } else {
    frmResume.setvehicleinfo.regioncodepopup.skin = flcFieldEdgeRed;
  }
}

function frmResume_onclick_btnQuestions(){
  //Check of er bestaande data is
  voltmx.print("### frmResume_onclick_btnQuestions");
  if(CaseData.questions !== undefined && CaseData.questions != null){
    voltmx.print("### frmResume_onclick_btnQuestions CaseData.questions: " + JSON.stringify(CaseData.questions));
    var previousdataFound = false;
    if(CaseData.questions.length > 0){
      for(var i in CaseData.questions){
        var v = CaseData.questions[i];
        if (v.cte_option_usage != null && v.cte_option_usage !== ""){
          Global.vars.questionTypeChooseEnabled = true;
        } else {
          Global.vars.questionTypeChooseEnabled = false;
        }  
        if(v.variables !== undefined && v.variables != null && v.variables.length > 0){
          Global.vars.selectedQuestionType = v;
          Global.vars.QuestionsSet = true;
          previousdataFound = true;
          break;
        }
      }
    }
    
    if(previousdataFound === true){
      if (Global.vars.questionTypeChooseEnabled === false){
        // get CaseTypes
        frmResume_getCaseTypeOptions();
      } else {
        Global.vars.openedFromResume = true;
        Global.vars.openedFromResumePreviousForm = Global.vars.previousForm;
        frmQuestionTypes_getCaseTypeOptions();
      }  
	}else{
      voltmx.print("### frmResume_onclick_btnQuestions no previous data");
      //Utility_clearEnforcementObjectQuestions();
      Global.vars.openedFromResume = true;
      Global.vars.openedFromResumePreviousForm = Global.vars.previousForm;
      Global.vars.previousForm = "frmResume";
      frmQuestionTypes_getCaseTypeOptions();
    }
  }else{
    voltmx.print("### frmResume_onclick_btnQuestions no questions");
    //Utility_clearEnforcementObjectQuestions();
    Global.vars.openedFromResume = true;
    Global.vars.openedFromResumePreviousForm = Global.vars.previousForm;
    Global.vars.previousForm = "frmResume";
    frmQuestionTypes_getCaseTypeOptions();
  }
}

function frmResume_getCaseTypeOptions(){    
    frmQuestionTypes.segOptions.removeAll();
	var wcs = "select * from mle_v_case_type_option_msv where cte_identification = '" + CaseData.caseinfo.caseType + "'";
    wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
  	wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
//     var options = {};
//     options["whereConditionAsAString"] = wcs;
    voltmx.print("### frmResume_postshow wcs: " + wcs);
//    Global.vars.syncObjects.caseTypeOptionObj.get(options, frmResume_caseTypeOption_succescallback, frmResume_caseTypeOption_errorcallback);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmResume_caseTypeOption_succescallback, frmResume_caseTypeOption_errorcallback);
}

function frmResume_caseTypeOption_errorcallback (error){
  Global.vars.questionTypes = [];
  voltmx.print("frmResume_caseTypeOption_errorcallback unable to objectSync_onGetAllFail retrieve records from db"+ error.code);
  alert(voltmx.i18n.getLocalizedString("l_listNotAvailable"));
}

function frmResume_caseTypeOption_succescallback(result){
  	voltmx.print("#### frmResume_caseTypeOption_succescallback result" + JSON.stringify(result));
  	Global.vars.questionTypes = [];
  	if(result !== undefined && result != null && result.length > 0){
      for(var i in result){
        var v = result[i];
        v.imgRight = "arrowrightmini.png";
        v.lbl1 = v.option_description;
        Global.vars.questionTypes.push(v);
      }
      voltmx.print("#### frmResume_caseTypeOption_succescallback Global.vars.questionTypes" + JSON.stringify(Global.vars.questionTypes));
      frmResume_frmQuestions_show();//frmQuestions.show();	
    } else {
      alert(voltmx.i18n.getLocalizedString("l_listNotAvailable"));
    }
}



function frmResume_showSetSignature(){
  //activate footer and mainpage
  voltmx.print("### frmResume_showSetSignature: " + frmResume.signaturecapture.getSignatureFromDevice());
  frmResume.setsignature.left = "0dp";
  frmResume.flcMainPage.setEnabled(false);
  voltmx.print("### frmResume_showSetSignature flcMainPage enabled");
//    frmResume.signaturecapture.onClickClear();
  Global.vars.signatureSet = false;
  Global.vars.onClickSignatureCancelButton = false;
  frmResume_setsignature_setVisibility(true);
}

function frmResume_hideSetSignature(){
  //activate footer and mainpage
  frmResume.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmResume_setsignature_setVisibility(false);
}

function frmResume_cancelSetSignature(){
  voltmx.print("### frmResume_cancelSetSignature");
  Global.vars.signatureSet = false;
  Global.vars.onClickSignatureCancelButton = false;
}

function frmResume_onclick_btnDoneSetSignature(){
  voltmx.print("### frmResume_onclick_btnDoneSetSignature");
}

function frmResume_saveSignatureSucces(response){
  voltmx.print("### frmResume_saveSignatureSucces: " + frmResume.signaturecapture.getSignatureFromDevice());
  voltmx.print("### frmResume_saveSignatureSucces signatureSet: " + Global.vars.signatureSet);
  voltmx.print("### frmResume_saveSignatureSucces onClickSignatureCancelButton: " + Global.vars.onClickSignatureCancelButton);
  if (Global.vars.onClickSignatureCancelButton === true){
// do nothing
  } else {
    if (Global.vars.signatureSet === true){
      frmResume.imgSignature.base64 = frmResume.signaturecapture.getSignatureFromDevice();
      CaseData.prohibitions.signature = frmResume.signaturecapture.getSignatureFromDevice();
    } else {
      CaseData.prohibitions.signature = null;
      frmResume.imgSignature.base64 = null;
      frmResume.imgSignature.src = "empty.png";
    }
  }
  Global.vars.signatureSet = false;
  Global.vars.onClickSignatureCancelButton = false;
  frmResume_hideSetSignature();
  frmResume_checkForSignatureImage();
}

function frmResume_checkForSignatureImage(){
  if(frmResume.imgSignature.base64 !== undefined && frmResume.imgSignature.base64 != null && frmResume.imgSignature.base64.length > 0 && frmResume.imgSignature.src != "empty.png"){
    frmResume_lblEmptySignature_setVisibility(false, frmResume_validateShowFooter);
  }else{
    frmResume_lblEmptySignature_setVisibility(true, frmResume_validateShowFooter);
  }
}


function frmResume_saveSignatureError(error){
  voltmx.print("### frmResume_saveSignatureError" + error);
}

function frmResume_saveSignatureErrorCallback(error){
  voltmx.print("### frmResume_saveSignatureErrorCallback" + error);
}

function frmResume_saveSignatureCheckValidity(bool){
  voltmx.print("### frmResume_saveSignatureCheckValidity" + bool);
}

function frmResume_getProhibitionsPDF(){
  voltmx.print("### frmResume_getProhibitionsPDF Global.vars.attachmentsLoaded" + Global.vars.attachmentsLoaded);
  if (Global.vars.attachmentsLoaded === false){
    voltmx.print("### frmResume_getProhibitionsPDF Global.vars.claimedDocID" + Global.vars.claimedDocID);
  	Utility_getOnlineAttachments(Global.vars.claimedDocID, frmResume_prohibitionsPDF);
  } else {
    frmResume_prohibitionsPDF();
  }
}

function frmResume_prohibitionsPDF(){
  //PDF heet of VerblijfsverbodPolitie of ReisverbodPolitie (check casetype voor welke je moet hebben dan van de preset bijv KVV_ kijken of het VV is of RV)
  voltmx.print("### frmResume_prohibitionsPDF CaseData.caseinfo.caseType: " +CaseData.caseinfo.caseType);
  var caseType = CaseData.caseinfo.caseType.split("_");
  voltmx.print("### frmResume_prohibitionsPDF caseType after split: " + JSON.stringify(caseType));
  var caseTypePrefix = caseType[0];
  voltmx.print("### frmResume_prohibitionsPDF caseType after caseTypePrefix: " + caseTypePrefix);
  if(caseTypePrefix.endsWith("RV")){
    Global.vars.PDFProhibitionsName = "ReisverbodPolitie";
  }else if(caseTypePrefix.endsWith("VV")){
    Global.vars.PDFProhibitionsName = "VerblijfsverbodPolitie";
  }
  voltmx.print("### frmResume_prohibitionsPDF Global.vars.PDFProhibitionsName" + Global.vars.PDFProhibitionsName);

  /* For Android we use different approach to view PDF then on iOS 
  	- on Android we use FFI startPDFViewer defined in function Utility_startPDFViewer
    - on iOS we use voltmx.ui.Browser to view PDF in webView
    
    We duplicated the code here because code could not be entirly used for both systems
  */
  
  //#ifdef android
  var externalStoragePermission = null;
  var skipExternalStoragePermission = false;
  var deviceOSVersion = voltmx.os.deviceInfo().version;
  if (deviceOSVersion != null && voltmx.os.toNumber(deviceOSVersion) >= 13){
    skipExternalStoragePermission = true;
  } else {
    externalStoragePermission = voltmx.application.checkPermission(voltmx.os.RESOURCE_EXTERNAL_STORAGE);
  }
  voltmx.print("### frmResume_prohibitionsPDF skipExternalStoragePermission: " + skipExternalStoragePermission);
  if(skipExternalStoragePermission === false && externalStoragePermission.status == voltmx.application.PERMISSION_DENIED) {
    voltmx.application.requestPermission(voltmx.os.RESOURCE_EXTERNAL_STORAGE,frmResume_prohibitionsPDF);
  }
  else if(skipExternalStoragePermission === true || externalStoragePermission.status == voltmx.application.PERMISSION_GRANTED){
    function getAttchmentCallback(result) {
      if(result.opstatus === 0 && result.httpStatusCode == 200){
        if(result.response !== undefined && result.response.length > 0){
          var base64pdf = result.response[0].base64;
          voltmx.print("### frmResume_prohibitionsPDF getAttachment pdfAsBase64: " + JSON.stringify(base64pdf));
          var rb = voltmx.convertToRawBytes(base64pdf); 
          var path =""; 
          path = voltmx.io.FileSystem.getDataDirectoryPath();
          var pathToFile = path +"/"+ Global.vars.PDFProhibitionsName + ".pdf";
          voltmx.print("### frmResume_prohibitionsPDF getAttachment path: " + path);
          if(skipExternalStoragePermission === true || voltmx.io.FileSystem.isExternalStorageAvailable()){ 
            var myFileName = new voltmx.io.File(pathToFile).createFile();
            try {
              var writing = new voltmx.io.File(pathToFile).write(rb);
              if (writing !== null) {
                voltmx.print("### frmResume_prohibitionsPDF writing can be done on Non Existing Files");
                var fileLocation = pathToFile;
                Utility_startPDFViewer(fileLocation);
                voltmx.application.dismissLoadingScreen();
              } else {
                voltmx.print("### frmResume_prohibitionsPDF writing on nonExisting file returns null");
                voltmx.application.dismissLoadingScreen();
              }
            } catch (err) {
              voltmx.print("can't try write on NonExistingFile, causes Error");
              voltmx.application.dismissLoadingScreen();
            }
          }
        }else{
          alert("PDF niet gevonden");
          voltmx.application.dismissLoadingScreen();
        }
      }else{
        alert("PDF niet gevonden");
        voltmx.application.dismissLoadingScreen();
      }
    }
    function getAttchmentErrorCallback(err) {
      voltmx.print("### frmResume_prohibitionsPDF getAttachment err: " + JSON.stringify(err));
      alert("PDF niet gevonden");
      voltmx.application.dismissLoadingScreen();
    }
    var claimedDocID = Global.vars.claimedDocID;
    voltmx.print("### frmResume_prohibitionsPDF get pdf");
    var _attname = "init";
    var _filename = Global.vars.PDFProhibitionsName + ".pdf";
    for (var q=0; ((Global.vars.onlineAttachments) != null) && q < Global.vars.onlineAttachments.length; q++ ){
      voltmx.print("### frmResume_prohibitionsPDF Global.vars.onlineAttachments: " + JSON.stringify(Global.vars.onlineAttachments));
      var z = Global.vars.onlineAttachments[q];
      if((z.fileName == _filename)){
        _attname = z.attachmentId;
        break;
      }
    }
    voltmx.print("### frmResume_prohibitionsPDF get pdf _filename: " + _filename);
    voltmx.print("### frmResume_prohibitionsPDF get pdf _attname: " + _attname);
    if (_attname != "init"){
      voltmx.print("### frmResume_prohibitionsPDF get pdf service_getAttachment");
      service_getAttachment(Global.vars.claimedDocID, _attname, getAttchmentCallback, getAttchmentErrorCallback);//Legacy voor ophalen pdf, later een keer aanpassen
    } else {
      alert("PDF niet gevonden");
      voltmx.application.dismissLoadingScreen();
    }
  }
  //#else
  function closePDF() {
    if (frmResume.pdfWebView) {
      frmResume.remove(frmResume.pdfWebView);  // Removes WebView
      frmResume.remove(frmResume.btnClosePDF);  // Removes WebView
      frmResume.forceLayout();  // Refresh the UI
    }
  }	

  function displayPDF(pdfPath) {
    var webView = new voltmx.ui.Browser({
      "id": "pdfWebView",
      "top": "0dp",
      "left": "0dp",
      "width": "100%",
      "height": "100%",
      "enableZoom": true
    }, {}, {});

    webView.requestURLConfig = { URL: pdfPath, requestMethod: constants.BROWSER_REQUEST_METHOD_GET };

    var closeButton = new voltmx.ui.Button({
      "id": "btnClosePDF",
      "text": voltmx.i18n.getLocalizedString("l_closePDF"),
      "top": "90%",
      "left": "3%",
      "width": "94%",
      "height": "49dp",
      "skin": "butFooter",
      "focusSkin": "butFooter",
      "onClick": closePDF
    }, {}, {});

    frmResume.add(webView); // Add WebView to the form
    frmResume.add(closeButton);
  }	

  function getAttchmentCallback(result) {
    if(result.opstatus === 0 && result.httpStatusCode == 200){
      if(result.response !== undefined && result.response.length > 0){
        var base64pdf = result.response[0].base64;
        voltmx.print("### frmResume_prohibitionsPDF getAttachment pdfAsBase64: " + JSON.stringify(base64pdf));
        var rb = voltmx.convertToRawBytes(base64pdf); 
        var path =""; 
        path = voltmx.io.FileSystem.getDataDirectoryPath();
        var pathToFile = path +"/"+ Global.vars.PDFProhibitionsName + ".pdf";
        voltmx.print("### frmResume_prohibitionsPDF getAttachment path: " + path);
        var myFileName = new voltmx.io.File(pathToFile).createFile();
        try {
          var writing = new voltmx.io.File(pathToFile).write(rb);
          if (writing !== null) {
            voltmx.print("### frmResume_prohibitionsPDF writing can be done on Non Existing Files");
            var fileLocation = pathToFile;
            displayPDF(fileLocation);
            voltmx.application.dismissLoadingScreen();
          } else {
            voltmx.print("### frmResume_prohibitionsPDF writing on nonExisting file returns null");
            voltmx.application.dismissLoadingScreen();
          }
        } catch (err) {
          voltmx.print("can't try write on NonExistingFile, causes Error: " + JSON.stringify(err));
          voltmx.application.dismissLoadingScreen();
        }
      }else{
        alert("PDF niet gevonden");
        voltmx.application.dismissLoadingScreen();
      }
    }else{
      alert("PDF niet gevonden");
      voltmx.application.dismissLoadingScreen();
    }
  }
  function getAttchmentErrorCallback(err) {
    voltmx.print("### frmResume_prohibitionsPDF getAttachment err: " + JSON.stringify(err));
    alert("PDF niet gevonden");
    voltmx.application.dismissLoadingScreen();
  }
  var claimedDocID = Global.vars.claimedDocID;
  voltmx.print("### frmResume_prohibitionsPDF get pdf");
  var _attname = "init";
  var _filename = Global.vars.PDFProhibitionsName + ".pdf";
  for (var q=0; ((Global.vars.onlineAttachments) != null) && q < Global.vars.onlineAttachments.length; q++ ){
    voltmx.print("### frmResume_prohibitionsPDF Global.vars.onlineAttachments: " + JSON.stringify(Global.vars.onlineAttachments));
    var z = Global.vars.onlineAttachments[q];
    if((z.fileName == _filename)){
      _attname = z.attachmentId;
      break;
    }
  }
  voltmx.print("### frmResume_prohibitionsPDF get pdf _filename: " + _filename);
  voltmx.print("### frmResume_prohibitionsPDF get pdf _attname: " + _attname);
  if (_attname != "init"){
    voltmx.print("### frmResume_prohibitionsPDF get pdf service_getAttachment");
    service_getAttachment(Global.vars.claimedDocID, _attname, getAttchmentCallback, getAttchmentErrorCallback);//Legacy voor ophalen pdf, later een keer aanpassen
  } else {
    alert("PDF niet gevonden");
    voltmx.application.dismissLoadingScreen();
  }
  //#endif
}

function frmResume_onDone_policeofficeremail(){
  if(frmResume.policeofficeremail.txtInputText.text !== undefined && frmResume.policeofficeremail.txtInputText.text != null && frmResume.policeofficeremail.txtInputText.text !== ""){
    //lower case
    frmResume.policeofficeremail.txtInputText.text = frmResume.policeofficeremail.txtInputText.text.toLowerCase();
    //trim spaces
    frmResume.policeofficeremail.txtInputText.text = frmResume.policeofficeremail.txtInputText.text.replace(/\s/g, '');
    //check email domain and enrich
    //check if email
    voltmx.print("### frmResume_onDone_policeofficeremail frmResume.policeofficeremail.txtInputText.text: " + frmResume.policeofficeremail.txtInputText.text);
    if (frmResume.policeofficeremail.txtInputText.text !== undefined && frmResume.policeofficeremail.txtInputText.text != null && voltmx.string.isValidEmail(frmResume.policeofficeremail.txtInputText.text) === false){
      //check for @
      if(frmResume.policeofficeremail.txtInputText.text.includes("@") === true){
        //now replace everything with correct domain
        var splitEmail = frmResume.policeofficeremail.txtInputText.text.split("@");
        frmResume.policeofficeremail.txtInputText.text = splitEmail[0] + "@" + Global.vars.prohibitionEmailDomain[0].domain;
        //recheck
        frmResume_onDone_policeofficeremail();
      }else{
        frmResume.policeofficeremail.txtInputText.text = frmResume.policeofficeremail.txtInputText.text + "@" + Global.vars.prohibitionEmailDomain[0].domain;
        //recheck
        frmResume_onDone_policeofficeremail();
      }
      //alert("Dit is geen geldig email adres");
    }else{
      var splitEmail3 = frmResume.policeofficeremail.txtInputText.text.split("@");      
      if(frmResume.policeofficeremail.txtInputText.text !== undefined && frmResume.policeofficeremail.txtInputText.text != null && splitEmail3[0].includes(".") === true){
        //&& frmResume.policeofficeremail.txtInputText.text.endsWith("@" + Global.vars.prohibitionEmailDomain) === true && splitEmail3[0].includes(".") === true){
        var isEmailInDomain = false;
        for(var a in Global.vars.prohibitionEmailDomain){
          var domainMail = Global.vars.prohibitionEmailDomain[a].domain;
          if(frmResume.policeofficeremail.txtInputText.text.endsWith("@" + domainMail) === true){
            isEmailInDomain = true;
            break;
          }
        }
        if(isEmailInDomain){
          voltmx.print("### frmResume_onDone_policeofficeremail: policeofficer email in domain");
          voltmx.print("### frmResume_onDone_policeofficeremail: geldig email");
          CaseData.prohibitions.policeOfficerEmail = frmResume.policeofficeremail.txtInputText.text;
          frmResume_validateShowFooter();
        }else{
          voltmx.print("### frmResume_onDone_policeofficeremail: policeofficer email NOT in domain");
          var alertDomains = "";
          for(var b in Global.vars.prohibitionEmailDomain){
            var mailDomain = Global.vars.prohibitionEmailDomain[b].domain;
            if(alertDomains === ""){
              alertDomains = mailDomain;
            }else{
              alertDomains = alertDomains + ", " + mailDomain;
            }
          }
          alert("Dit email adres heeft geen geldig domein, gebruik een van deze domeinen: " + alertDomains);
          frmResume_validateShowFooter();
        }
      }else{
        alert("U bent verplicht een emailadres met een . erin te gebruiken voor de @ bijv: <EMAIL>");
        //remove domain
        var domain = frmResume.policeofficeremail.txtInputText.text.substring(frmResume.policeofficeremail.txtInputText.text.lastIndexOf("@") +1);
        frmResume.policeofficeremail.txtInputText.text = frmResume.policeofficeremail.txtInputText.text.replace("@" + domain, "");
        frmResume_validateShowFooter();
      }
    }
  }
}

function frmResume_clearpoliceofficeremail(){
  frmResume.policeofficeremail.txtInputText.text = "";
  frmResume.policeofficeremail.txtInputText.setFocus(true);
  frmResume_validateShowFooter();
}

function frmResume_onDone_offenderemail(){
  if(frmResume.offenderemail.txtInputText.text !== undefined && frmResume.offenderemail.txtInputText.text != null && frmResume.offenderemail.txtInputText.text !== ""){
    //lower case
    frmResume.offenderemail.txtInputText.text = frmResume.offenderemail.txtInputText.text.toLowerCase();
    //trim spaces
    frmResume.offenderemail.txtInputText.text = frmResume.offenderemail.txtInputText.text.replace(/\s/g, '');
    //check email domain and enrich
    //check if email
    voltmx.print("### frmResume_onDone_offenderemail frmResume.offenderemail.txtInputText.text: " + frmResume.offenderemail.txtInputText.text);
    if (frmResume.offenderemail.txtInputText.text !== undefined && frmResume.offenderemail.txtInputText.text != null && voltmx.string.isValidEmail(frmResume.offenderemail.txtInputText.text) === false){
      alert("Dit is geen geldig email adres");
    }else{
      //VALID
      voltmx.print("### frmResume_onDone_offenderemail: geldig email");
      CaseData.transportticket.offenderEmail = frmResume.offenderemail.txtInputText.text;
    }
  }
}

function frmResume_clearoffenderemail(){
  frmResume.offenderemail.txtInputText.text = "";
  frmResume.offenderemail.txtInputText.setFocus(true);
  //frmResume_validateShowFooter();
}


function frmResume_onDone_policeofficermobile(){
  if(frmResume.policeofficermobile.txtInputText.text !== undefined && frmResume.policeofficermobile.txtInputText.text != null && frmResume.policeofficermobile.txtInputText.text !== ""){
    //lower case
    frmResume.policeofficermobile.txtInputText.text = frmResume.policeofficermobile.txtInputText.text.toLowerCase();
    //trim spaces
    frmResume.policeofficermobile.txtInputText.text = frmResume.policeofficermobile.txtInputText.text.replace(/\s/g, '');
    var str = frmResume.policeofficermobile.txtInputText.text;
    var is10Digits = (/^\d{10}$/.test(str));
	if(is10Digits === false){
      alert("Dit nummer is geen 10 cijfers");
    }else{
      if(str.startsWith("06") === false){
        alert("Nummer start niet met 06");
      }else{
        CaseData.prohibitions.policeOfficerMobile = frmResume.policeofficermobile.txtInputText.text;
        frmResume_validateShowFooter();
      }
    }
  }
}

function frmResume_clearpoliceofficermobile(){
  frmResume.policeofficermobile.txtInputText.text = "";
  frmResume.policeofficermobile.txtInputText.setFocus(true);
  frmResume_validateShowFooter();
}

function frmResume_onDone_offendertelephone(){
  if(frmResume.offendertelephone.txtInputText.text !== undefined && frmResume.offendertelephone.txtInputText.text != null && frmResume.offendertelephone.txtInputText.text !== ""){
    //lower case
    frmResume.offendertelephone.txtInputText.text = frmResume.offendertelephone.txtInputText.text.toLowerCase();
    //trim spaces
    frmResume.offendertelephone.txtInputText.text = frmResume.offendertelephone.txtInputText.text.replace(/\s/g, '');
    var str = frmResume.offendertelephone.txtInputText.text;
    var is10Digits = (/^\d{10}$/.test(str));
	if(is10Digits === false){
      alert("Dit nummer is geen 10 cijfers");
    }else{
      if(str.startsWith("0") === false){
        alert("Nummer start niet met 0");
      }else{
        CaseData.transportticket.offenderTelephone = frmResume.offendertelephone.txtInputText.text;
      }
   }
  }
}

function frmResume_clearoffendertelephone(){
  frmResume.offendertelephone.txtInputText.text = "";
  frmResume.offendertelephone.txtInputText.setFocus(true);
}

function frmResume_setProhibitionHanded(context){
  voltmx.print("### frmResume_setProhibitionHanded context: " + JSON.stringify(context));
  if(context.id !== undefined && context.id == "segControlBtnNo"){
    frmResume.prohibitionhanded.segControlBtnNo.skin = subHeaderSegControlBtnSelectedNormal;
    frmResume.prohibitionhanded.segControlBtnYes.skin = subHeaderSegControlBtnNotSelectedNormal;
    if(CaseData.prohibitions.prohibitionHanded !== undefined){
      CaseData.prohibitions.prohibitionHanded = false;
    }else{
      CaseData.prohibitions["prohibitionHanded"] = false;
    }
  }else if(context.id !== undefined && context.id == "segControlBtnYes"){
    frmResume.prohibitionhanded.segControlBtnNo.skin = subHeaderSegControlBtnNotSelectedNormal;
    frmResume.prohibitionhanded.segControlBtnYes.skin = subHeaderSegControlBtnSelectedNormal;
    if(CaseData.prohibitions.prohibitionHanded !== undefined){
      CaseData.prohibitions.prohibitionHanded = true;
    }else{
      CaseData.prohibitions["prohibitionHanded"] = true;
    }
  }
  frmResume_validateShowFooter();
}

function frmResume_showPersonAdress(){
  frmResume_personAdress_resetFields();
  if (CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressType == addressType.emptyDataGBA.value){
    voltmx.print("### frmResume_showPersonAdress context: Geheim adres");
    frmResume_flcPersonAdress_setVisibility(false);
  }else if (CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressType == addressType.withoutFixedStay.value || CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressType == addressType.departedDestinationUnknown.value){
    voltmx.print("### frmResume_showPersonAdress context: " + CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressTypeDesc);
    frmResume_flcPersonAdress_setVisibility(false);
  }else{
    frmResume_flcPersonAdress_setVisibility(true);
    if(CaseData.person[Global.vars.gCasePersonsIndex] !== undefined){
      var name = "";
      if(CaseData.person[Global.vars.gCasePersonsIndex].initials !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].initials != null && CaseData.person[Global.vars.gCasePersonsIndex].initials !== ""){
        name = CaseData.person[Global.vars.gCasePersonsIndex].initials;
      }
      if(CaseData.person[Global.vars.gCasePersonsIndex].middlename !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].middlename != null && CaseData.person[Global.vars.gCasePersonsIndex].middlename !== ""){
        name = name + " " + CaseData.person[Global.vars.gCasePersonsIndex].middlename;
      }
      if(CaseData.person[Global.vars.gCasePersonsIndex].surname !== undefined && CaseData.person[Global.vars.gCasePersonsIndex].surname != null && CaseData.person[Global.vars.gCasePersonsIndex].surname !== ""){
        name = name + " " + CaseData.person[Global.vars.gCasePersonsIndex].surname;
      }
      frmResume.lblName.text = name;
    }
    frmResume.lblCountryAdress.text = CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].country;
    if(CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].countryCode == Global.vars.CountryCode){      
      frmResume_flcStreet_setVisibility(true);
      frmResume_flcZipPlace_setVisibility(true);
      frmResume_flcAddressline1_setVisibility(false);
      frmResume_flcAddressline2_setVisibility(false);
      frmResume_flcAddressline3_setVisibility(false);
      //set data
      var zipcode = Utility_checkZipcode(CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].zipcode);
      if(zipcode === false){
        zipcode = CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].zipcode;
      }
      frmResume.lblStreet.text = CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].street + " " + CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].streetNumber + " " + CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].streetNumAdditn;
      frmResume.lblZipCode.text = zipcode + " " + CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].city;
      if(CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].postOfficeBox != null){
        frmResume.lblMailbox.text = CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].postOfficeBox;
        frmResume_flcMailbox_setVisibility(true);
        frmResume_flcStreet_setVisibility(false);
      }else{
        frmResume_flcMailbox_setVisibility(false);
        frmResume_flcStreet_setVisibility(true);
      }
      if(CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].replyNumber === null){
        voltmx.print("#### hide Reply");
        frmResume_flcReplyNumber_setVisibility(false);	
      }else{
        frmResume_flcReplyNumber_setVisibility(true);
        frmResume.lblReplyNumber.text = CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].replyNumber;
      }
    }else{
      frmResume_flcZipPlace_setVisibility(false);
      frmResume_flcStreet_setVisibility(false);
      frmResume_flcMailbox_setVisibility(false);
      frmResume_flcReplyNumber_setVisibility(false);
      if(CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressLine1 === null || CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressLine1 === ""){
        frmResume_flcAddressline1_setVisibility(false);
      }else{
        frmResume_flcAddressline1_setVisibility(true);
        frmResume.lblAddressline1.text = CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressLine1;
      }
      if(CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressLine2 === null || CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressLine2 === ""){
        frmResume_flcAddressline2_setVisibility(false);
      }else{
        frmResume_flcAddressline2_setVisibility(true);
        frmResume.lblAddressline2.text = CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressLine2;
      }
      if(CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressLine3 === null || CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressLine3 === ""){
        frmResume_flcAddressline3_setVisibility(false);
      }else{
        frmResume_flcAddressline3_setVisibility(true);
        frmResume.lblAddressline3.text = CaseData.person[Global.vars.gCasePersonsIndex].addresses[0].addressLine3;
      }
    }
  }
}

function frmResume_personAdress_resetFields(){
    frmResume.lblCountryAdress.text = "";
    frmResume.lblZipCode.text = "";
    frmResume.lblStreet.text = "";
    frmResume.lblMailbox.text = "";
   	frmResume.lblReplyNumber.text = "";
   	frmResume.lblAddressline1.text = "";
	frmResume.lblAddressline2.text = "";
  	frmResume.lblAddressline3.text = "";
  	frmResume_flcPersonAdress_setVisibility(false);
}

function frmResume_showSetClampNumber() {
      try {
        voltmx.print("### frmResume_showSetClampNumber");
        if(CaseData.caseinfo.clampNumber != null && CaseData.caseinfo.clampNumber !== ""){
          frmResume.txtClampNumberPopup.text = CaseData.caseinfo.clampNumber.replace(/\D+/g, "");
          frmResume.lblClampNumber.text = CaseData.caseinfo.clampNumber;
          frmResume.lblClampNumber.skin = lblFieldInfo;
        }
        //deactivate footer and mainpage
        frmResume.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmResume.forceLayout();
        frmResume_flcSetClampNumber_setVisibility(true);
        frmResume_showSetClampNumber_preAnim();
        frmResume_showSetClampNumber_animationStart();
      } catch (e) {
        voltmx.print("### frmResume_showSetClampNumber error: " + JSON.stringify(e));
      }
}

function frmResume_showSetClampNumber_preAnim() {
  try {
    voltmx.print("### frmResume_showSetClampNumber_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
  } catch (e) {
    voltmx.print("### frmResume_showSetClampNumber_preAnim error: " + JSON.stringify(e));
  }
}

function frmResume_showSetClampNumber_animationStart(eventobject) {
  try {
    voltmx.print("### frmResume_showSetClampNumber_animationStart");
    frmResume_flcSetClampNumber_setVisibility(true);
    frmResume_flcClampDetail_setVisibility(true);
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmResume.flcClampDetail.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmResume_showSetClampNumber_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmResume_showSetClampNumber_animationStart error: " + JSON.stringify(e));
  }
}

function frmResume_showSetClampNumber_animLogo() {
  try {
    voltmx.print("### frmResume_showSetClampNumber_animLogo");
    frmResume_showSetClampNumber_animOtherWidgets(frmResume.flcClampDetails);
    frmResume_showSetClampNumber_animOtherWidgets(frmResume.lblClampLine);
    frmResume_imgPopupClampLogo_setVisibility(true);
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetClampNumber_animLogo error: " + JSON.stringify(e));
  }
}


function frmResume_showSetClampNumber_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmResume_showSetClampNumber_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5, 
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
//     frmResume.flcClampDetails.isVisible = true;
//     frmResume.lblClampLine.isVisible = true;
//     frmResume.flcFooterClamp.isVisible = true;
    frmResume.flcFooterClamp.setEnabled(true);
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetClampNumber_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmResume_showSetClampNumber_animLogoBack() {
  try {
    voltmx.print("### frmResume_showSetClampNumber_animLogoBack");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmResume.imgPopupClampLogo.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmResume.forceLayout();
  } catch (e) {
    voltmx.print("### frmResume_showSetClampNumber_animLogoBack error: " + JSON.stringify(e));
  }
}

function frmResume_hideSetClampNumber(){
  //activate footer and mainpage
  frmResume.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmResume_flcSetClampNumber_setVisibility(false);
  //Global.vars.RegionDuplicatePopupSet = false;
}

function frmResume_setClampNumberDone(){
  voltmx.print("### frmResume_setClampNumberDone frmResume.txtClampNumberPopup.text: " + frmResume.txtClampNumberPopup.text);
  if(frmResume.txtClampNumberPopup.text != null && frmResume.txtClampNumberPopup.text !== ""){
    var wcs = "select * from exn_v_clamp_m where code = '" + frmResume.txtClampNumberPopup.text +"'";
    voltmx.print("### frmResume_setClampNumberDone wcs: " + wcs);
  	wcs = Utility_addTimelineToWhereClauseObjectSync(wcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs,frmResume_getClampuccessCallback,frmResume_getClamperrorCallback);
  } else {
    CaseData.caseinfo.clampNumber = "";
    frmResume.lblClampNumber.text = voltmx.i18n.getLocalizedString("l_nr");
    frmResume.lblClampNumber.skin = lblFieldNotFilled;
    frmResume.txtClampNumberPopup.text = "";
    frmResume_validateShowFooter();
  	frmResume_hideSetClampNumber();
  }
}
  
function frmResume_getClamperrorCallback(error){
  voltmx.print("### frmResume_getDefaultOutcomeForTaskScreenErrorCallback error: " + JSON.stringify(error));
  voltmx.application.dismissLoadingScreen();
  alert("" + error);
  frmResume_validateShowFooter();
  frmResume_hideSetClampNumber();
}

function frmResume_getClampuccessCallback(result){
  voltmx.print("### frmResume_getDefaultOutcomeForTaskScreenSuccessCallback result: " + JSON.stringify(result));
  voltmx.application.dismissLoadingScreen();
  if(result.length > 0){
    CaseData.caseinfo.clampNumber = result[0].description;
    frmResume.lblClampNumber.text = CaseData.caseinfo.clampNumber;
    frmResume.lblClampNumber.skin = lblFieldInfo;
    frmResume_validateShowFooter();
  	frmResume_hideSetClampNumber();
  }else{
    alert(voltmx.i18n.getLocalizedString("l_clampNumberNotFound"));
//     CaseData.caseinfo.clampNumber = "";
//     frmResume.lblClampNumber.text = voltmx.i18n.getLocalizedString("l_nr");
//     frmResume.lblClampNumber.skin = lblFieldNotFilled;
    frmResume.txtClampNumberPopup.text = "";
  	frmResume.txtClampNumberPopup.setFocus(true);
  }
}

function frmResume_clearTextClampNumber(){
  frmResume.txtClampNumberPopup.text = "";
  frmResume.txtClampNumberPopup.setFocus(true);
}

