function frmFollow_contentOffset(){
  frmFollow.contentSize = {height : "100%", width : "100%"};
  frmFollow.contentOffset = {"x":"0px", "y":"0px"};
}

function frmFollow_editplate_flcEditPrefixText_setVisibility(boolean){
  voltmx.print("### frmFollow_editplate.flcEditPrefixText_setVisibility");
  function editplate_flcEditPrefixText_setVisibility(){
    voltmx.print("### frmFollow_editplate.flcEditPrefixText_setVisibility editplate.flcEditPrefixText_setVisibility: " + boolean);
  	frmFollow.editplate.flcEditPrefixText.setVisibility(boolean);
  }
  voltmx.runOnMainThread(editplate_flcEditPrefixText_setVisibility, []);
}

function frmFollow_flcLayout_flcKindOfVehicleBrand_kindofvehicle_setVisibility(boolean){
  voltmx.print("### frmFollow_flcLayout.flcKindOfVehicleBrand.kindofvehicle_setVisibility");
  function flcLayout_flcKindOfVehicleBrand_kindofvehicle_setVisibility(){
    voltmx.print("### frmFollow_flcLayout.flcKindOfVehicleBrand.kindofvehicle_setVisibility flcLayout.flcKindOfVehicleBrand.kindofvehicle_setVisibility: " + boolean);
  	frmFollow.flcLayout.flcKindOfVehicleBrand.kindofvehicle.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcLayout_flcKindOfVehicleBrand_kindofvehicle_setVisibility, []);
}

function frmFollow_flcTimeArea_flcLocationArea_setVisibility(boolean){
  voltmx.print("### frmFollow_flcTimeArea.flcLocationArea_setVisibility");
  function flcTimeArea_flcLocationArea_setVisibility(){
    voltmx.print("### frmFollow_flcTimeArea.flcLocationArea_setVisibility flcTimeArea.flcLocationArea_setVisibility: " + boolean);
  	frmFollow.flcTimeArea.flcLocationArea.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcTimeArea_flcLocationArea_setVisibility, []);
}

function frmFollow_editplate_flcLicenseplateImage_setVisibility(boolean){
  voltmx.print("### frmFollow_editplate.flcLicenseplateImage_setVisibility");
  function editplate_flcLicenseplateImage_setVisibility(){
    voltmx.print("### frmFollow_editplate.flcLicenseplateImage_setVisibility editplate.flcLicenseplateImage_setVisibility: " + boolean);
  	frmFollow.editplate.flcLicenseplateImage.setVisibility(boolean);
  }
  voltmx.runOnMainThread(editplate_flcLicenseplateImage_setVisibility, []);
}

function frmFollow_flcGetNewCaseList_flcRetrieveCases_setVisibility(boolean){
  voltmx.print("### frmFollow_flcGetNewCaseList.flcRetrieveCases_setVisibility");
  function flcGetNewCaseList_flcRetrieveCases_setVisibility(){
    voltmx.print("### frmFollow_flcGetNewCaseList.flcRetrieveCases_setVisibility flcGetNewCaseList.flcRetrieveCases_setVisibility: " + boolean);
  	frmFollow.flcGetNewCaseList.flcRetrieveCases.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcGetNewCaseList_flcRetrieveCases_setVisibility, []);
}

function frmFollow_flcGetNewCaseList_flcCaseSegment_setVisibility(boolean){
  voltmx.print("### frmFollow_flcGetNewCaseList.flcCaseSegment_setVisibility");
  function flcGetNewCaseList_flcCaseSegment_setVisibility(){
    voltmx.print("### frmFollow_flcGetNewCaseList.flcCaseSegment_setVisibility flcGetNewCaseList.flcCaseSegment_setVisibility: " + boolean);
  	frmFollow.flcGetNewCaseList.flcCaseSegment.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcGetNewCaseList_flcCaseSegment_setVisibility, []);
}

function frmFollow_flcGetNewCaseList_flcCaseMap_setVisibility(boolean){
  voltmx.print("### frmFollow_flcGetNewCaseList.flcCaseMap_setVisibility");
  function flcGetNewCaseList_flcCaseMap_setVisibility(){
    voltmx.print("### frmFollow_flcGetNewCaseList.flcCaseMap_setVisibility flcGetNewCaseList.flcCaseMap_setVisibility: " + boolean);
  	frmFollow.flcGetNewCaseList.flcCaseMap.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcGetNewCaseList_flcCaseMap_setVisibility, []);
}

function frmFollow_btnNext_setVisibility(boolean){
  voltmx.print("### frmFollow_btnNext_setVisibility");
  function btnNext_setVisibility(){
    voltmx.print("### frmFollow_btnNext_setVisibility btnNext_setVisibility: " + boolean);
  	frmFollow.btnNext.setVisibility(boolean);
  }
  voltmx.runOnMainThread(btnNext_setVisibility, []);
}

function frmFollow_lblLicenseplatePrefix_setVisibility(boolean){
  voltmx.print("### frmFollow_lblLicenseplatePrefix_setVisibility");
  function lblLicenseplatePrefix_setVisibility(){
    voltmx.print("### frmFollow_lblLicenseplatePrefix_setVisibility lblLicenseplatePrefix_setVisibility: " + boolean);
  	frmFollow.lblLicenseplatePrefix.setVisibility(boolean);
  }
  voltmx.runOnMainThread(lblLicenseplatePrefix_setVisibility, []);
}

function frmFollow_mapCases_setVisibility(boolean){
  voltmx.print("### frmFollow_mapCases_setVisibility");
  function mapCases_setVisibility(){
    voltmx.print("### frmFollow_mapCases_setVisibility mapCases_setVisibility: " + boolean);
  	//frmFollow.mapCases.setVisibility(boolean);
  }
  voltmx.runOnMainThread(mapCases_setVisibility, []);
}

function frmFollow_flcRecheck_setVisibility(boolean){
  voltmx.print("### frmFollow_flcRecheck_setVisibility");
  function flcRecheck_setVisibility(){
    voltmx.print("### frmFollow_flcRecheck_setVisibility flcRecheck_setVisibility: " + boolean);
  	frmFollow.flcRecheck.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcRecheck_setVisibility, []);
}

function frmFollow_flcQueueTimer_setVisibility(boolean){
  voltmx.print("### frmFollow_flcQueueTimer_setVisibility");
  function flcQueueTimer_setVisibility(){
    voltmx.print("### frmFollow_flcQueueTimer_setVisibility flcQueueTimer_setVisibility: " + boolean);
  	frmFollow.flcQueueTimer.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcQueueTimer_setVisibility, []);
}

function frmFollow_flcCaseMap_setVisibility(boolean){
  voltmx.print("### frmFollow_flcCaseMap_setVisibility");
  function flcCaseMap_setVisibility(){
    voltmx.print("### frmFollow_flcCaseMap_setVisibility flcCaseMap_setVisibility: " + boolean);
  	frmFollow.flcCaseMap.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcCaseMap_setVisibility, []);
}

function frmFollow_flcRetrieveCases_setVisibility(boolean){
  voltmx.print("### frmFollow_flcRetrieveCases_setVisibility");
  function flcRetrieveCases_setVisibility(){
    voltmx.print("### frmFollow_flcRetrieveCases_setVisibility flcRetrieveCases_setVisibility: " + boolean);
  	frmFollow.flcRetrieveCases.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcRetrieveCases_setVisibility, []);
}

function frmFollow_flcCaseSegment_setVisibility(boolean){
  voltmx.print("### frmFollow_flcCaseSegment_setVisibility");
  function flcCaseSegment_setVisibility(){
    voltmx.print("### frmFollow_flcCaseSegment_setVisibility flcCaseSegment_setVisibility: " + boolean);
  	frmFollow.flcCaseSegment.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcCaseSegment_setVisibility, []);
}

function frmFollow_flcFooterCases_setVisibility(boolean){
  voltmx.print("### frmFollow_flcFooterCases_setVisibility");
  function flcFooterCases_setVisibility(){
    voltmx.print("### frmFollow_flcFooterCases_setVisibility flcFooterCases_setVisibility: " + boolean);
  	frmFollow.flcFooterCases.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcFooterCases_setVisibility, []);
}

function frmFollow_flcCounter_setVisibility(boolean){
  voltmx.print("### frmFollow_flcCounter_setVisibility");
  function flcCounter_setVisibility(){
    voltmx.print("### frmFollow_flcCounter_setVisibility flcCounter_setVisibility: " + boolean);
  	frmFollow.flcCounter.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcCounter_setVisibility, []);
}

function frmFollow_editplate_setVisibility(boolean){
  voltmx.print("### frmFollow_editplate_setVisibility");
  function editplate_setVisibility(){
    voltmx.print("### frmFollow_editplate_setVisibility editplate_setVisibility: " + boolean);
  	frmFollow.editplate.setVisibility(boolean);
  }
  voltmx.runOnMainThread(editplate_setVisibility, []);
}

function frmFollow_flcMenuButton_setVisibility(boolean){
  voltmx.print("### frmFollow_flcMenuButton_setVisibility");
  function flcMenuButton_setVisibility(){
    voltmx.print("### frmFollow_flcMenuButton_setVisibility flcMenuButton_setVisibility: " + boolean);
  	frmFollow.flcMenuButton.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcMenuButton_setVisibility, []);
}

function frmFollow_btnCancelClaim_setVisibility(boolean){
  voltmx.print("### frmFollow_btnCancelClaim_setVisibility");
  function btnCancelClaim_setVisibility(){
    voltmx.print("### frmFollow_btnCancelClaim_setVisibility btnCancelClaim_setVisibility: " + boolean);
  	frmFollow.btnCancelClaim.setVisibility(boolean);
  }
  voltmx.runOnMainThread(btnCancelClaim_setVisibility, []);
}

function frmFollow_btnLocation_setVisibility(boolean){
  voltmx.print("### frmFollow_btnLocation_setVisibility");
  function btnLocation_setVisibility(){
    voltmx.print("### frmFollow_btnLocation_setVisibility btnLocation_setVisibility: " + boolean);
  	frmFollow.btnLocation.setVisibility(boolean);
  }
  voltmx.runOnMainThread(btnLocation_setVisibility, []);
}

function frmFollow_btnTime_setVisibility(boolean){
  voltmx.print("### frmFollow_btnTime_setVisibility");
  function btnTime_setVisibility(){
    voltmx.print("### frmFollow_btnTime_setVisibility btnTime_setVisibility: " + boolean);
  	frmFollow.btnTime.setVisibility(boolean);
  }
  voltmx.runOnMainThread(btnTime_setVisibility, []);
}

function frmFollow_flcResult_setVisibility(boolean){
  voltmx.print("### frmFollow_flcResult_setVisibility");
  function flcResult_setVisibility(){
    voltmx.print("### frmFollow_flcResult_setVisibility flcResult_setVisibility: " + boolean);
  	frmFollow.flcResult.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcResult_setVisibility, []);
}

function frmFollow_flcLicenseplateImageIn_setVisibility(boolean){
  voltmx.print("### frmFollow_flcLicenseplateImageIn_setVisibility");
  function flcLicenseplateImageIn_setVisibility(){
    voltmx.print("### frmFollow_flcLicenseplateImageIn_setVisibility flcLicenseplateImageIn_setVisibility: " + boolean);
  	frmFollow.flcLicenseplateImageIn.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcLicenseplateImageIn_setVisibility, []);
}

function frmFollow_flcKindOfVehicleBrand_setVisibility(boolean){
  voltmx.print("### frmFollow_flcKindOfVehicleBrand_setVisibility");
  function flcKindOfVehicleBrand_setVisibility(){
    voltmx.print("### frmFollow_flcKindOfVehicleBrand_setVisibility flcKindOfVehicleBrand_setVisibility: " + boolean);
  	frmFollow.flcKindOfVehicleBrand.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcKindOfVehicleBrand_setVisibility, []);
}

function frmFollow_flcGetNewCaseList_setVisibility(boolean){
  voltmx.print("### frmFollow_flcGetNewCaseList_setVisibility");
  function flcGetNewCaseList_setVisibility(){
    voltmx.print("### frmFollow_flcGetNewCaseList_setVisibility flcGetNewCaseList_setVisibility: " + boolean);
  	frmFollow.flcGetNewCaseList.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcGetNewCaseList_setVisibility, []);
}

function frmFollow_kindofvehicle_setVisibility(boolean){
  voltmx.print("### frmFollow_kindofvehicle_setVisibility");
  function kindofvehicle_setVisibility(){
    voltmx.print("### frmFollow_kindofvehicle_setVisibility kindofvehicle_setVisibility: " + boolean);
  	frmFollow.kindofvehicle.setVisibility(boolean);
  }
  voltmx.runOnMainThread(kindofvehicle_setVisibility, []);
}

function frmFollow_brand_setVisibility(boolean){
  voltmx.print("### frmFollow_brand_setVisibility");
  function brand_setVisibility(){
    voltmx.print("### frmFollow_brand_setVisibility brand_setVisibility: " + boolean);
  	frmFollow.brand.setVisibility(boolean);
  }
  voltmx.runOnMainThread(brand_setVisibility, []);
}

function frmFollow_brandtype_setVisibility(boolean){
  voltmx.print("### frmFollow_brandtype_setVisibility");
  function brandtype_setVisibility(){
    voltmx.print("### frmFollow_brandtype_setVisibility brandtype_setVisibility: " + boolean);
  	frmFollow.brandtype.setVisibility(boolean);
  }
  voltmx.runOnMainThread(brandtype_setVisibility, []);
}

function frmFollow_color_setVisibility(boolean){
  voltmx.print("### frmFollow_color_setVisibility");
  function color_setVisibility(){
    voltmx.print("### frmFollow_color_setVisibility color_setVisibility: " + boolean);
  	frmFollow.color.setVisibility(boolean);
  }
  voltmx.runOnMainThread(color_setVisibility, []);
}

function frmFollow_btnNoViolation_setVisibility(boolean){
  voltmx.print("### frmFollow_btnNoViolation_setVisibility");
  function btnNoViolation_setVisibility(){
    voltmx.print("### frmFollow_btnNoViolation_setVisibility btnNoViolation_setVisibility: " + boolean);
  	frmFollow.btnNoViolation.setVisibility(boolean);
  }
  voltmx.runOnMainThread(btnNoViolation_setVisibility, []);
}

function frmFollow_btnNoAutorization_setVisibility(boolean){
  voltmx.print("### frmFollow_btnNoAutorization_setVisibility");
  function btnNoAutorization_setVisibility(){
    voltmx.print("### frmFollow_btnNoAutorization_setVisibility btnNoAutorization_setVisibility: " + boolean);
  	frmFollow.btnNoAutorization.setVisibility(boolean);
  }
  voltmx.runOnMainThread(btnNoAutorization_setVisibility, []);
}

function frmFollow_flcFooterMain_setVisibility(boolean){
  voltmx.print("### frmFollow_flcFooterMain_setVisibility");
  function flcFooterMain_setVisibility(){
    voltmx.print("### frmFollow_flcFooterMain_setVisibility flcFooterMain_setVisibility: " + boolean);
  	frmFollow.flcFooterMain.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcFooterMain_setVisibility, []);
}

function frmFollow_imgStatusRight_setVisibility(boolean){
  voltmx.print("### frmFollow_imgStatusRight_setVisibility");
  function imgStatusRight_setVisibility(){
    voltmx.print("### frmFollow_imgStatusRight_setVisibility imgStatusRight_setVisibility: " + boolean);
  	frmFollow.imgStatusRight.setVisibility(boolean);
  }
  voltmx.runOnMainThread(imgStatusRight_setVisibility, []);
}

function frmFollow_flcHistoricCases_setVisibility(boolean){
  voltmx.print("### frmFollow_flcHistoricCases_setVisibility");
  function flcHistoricCases_setVisibility(){
    voltmx.print("### frmFollow_flcHistoricCases_setVisibility flcHistoricCases_setVisibility: " + boolean);
  	frmFollow.flcHistoricCases.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcHistoricCases_setVisibility, []);
}

function frmFollow_flcParkingCheckResult_setVisibility(boolean){
  voltmx.print("### frmFollow_flcParkingCheckResult_setVisibility");
  function flcParkingCheckResult_setVisibility(){
    voltmx.print("### frmFollow_flcParkingCheckResult_setVisibility flcParkingCheckResult_setVisibility: " + boolean);
  	frmFollow.flcParkingCheckResult.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcParkingCheckResult_setVisibility, []);
}

function frmFollow_flcSpinner_setVisibility(boolean){
  voltmx.print("### frmFollow_flcSpinner_setVisibility");
  function flcSpinner_setVisibility(){
    voltmx.print("### frmFollow_flcSpinner_setVisibility flcSpinner_setVisibility: " + boolean);
  	frmFollow.flcSpinner.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcSpinner_setVisibility, []);
}

function frmFollow_btnRetrieveCases_setVisibility(boolean){
  voltmx.print("### frmFollow_btnRetrieveCases_setVisibility");
  function btnRetrieveCases_setVisibility(){
    voltmx.print("### frmFollow_btnRetrieveCases_setVisibility btnRetrieveCases_setVisibility: " + boolean);
  	frmFollow.btnRetrieveCases.setVisibility(boolean);
  }
  voltmx.runOnMainThread(btnRetrieveCases_setVisibility, []);
}

function frmFollow_flcUpLoadCase_setVisibility(boolean){
  voltmx.print("### frmFollow_flcUpLoadCase_setVisibility");
  function flcUpLoadCase_setVisibility(){
    voltmx.print("### frmFollow_flcUpLoadCase_setVisibility flcUpLoadCase_setVisibility: " + boolean);
  	frmFollow.flcUpLoadCase.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcUpLoadCase_setVisibility, []);
}

function frmFollow_flcShowTaskTimeLine_setVisibility(boolean){
  voltmx.print("### frmFollow_flcShowTaskTimeLine_setVisibility");
  function flcShowTaskTimeLine_setVisibility(){
    voltmx.print("### frmFollow_flcShowTaskTimeLine_setVisibility flcShowTaskTimeLine_setVisibility: " + boolean);
  	frmFollow.flcShowTaskTimeLine.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcShowTaskTimeLine_setVisibility, []);
 }

function frmFollow_flcShowParkingChecks_setVisibility(boolean){
  voltmx.print("### frmFollow_flcShowParkingChecks_setVisibility");
  function flcShowParkingChecks_setVisibility(){
    voltmx.print("### frmFollow_flcShowParkingChecks_setVisibility flcShowParkingChecks_setVisibility: " + boolean);
  	frmFollow.flcShowParkingChecks.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcShowParkingChecks_setVisibility, []);
}

function frmFollow_flcShowRegions_setVisibility(boolean){
  voltmx.print("### frmFollow_flcShowRegions_setVisibility");
  function flcShowRegions_setVisibility(){
    voltmx.print("### frmFollow_flcShowRegions_setVisibility flcShowRegions_setVisibility: " + boolean);
  	frmFollow.flcShowRegions.setVisibility(boolean);
  }
  voltmx.runOnMainThread(flcShowRegions_setVisibility, []);
}

function frmFollow_onHide(){
  	voltmx.print("### frmFollow_onHide start");
	//frmFollow.mapCases.clear();
  	frmFollow_mapCases_setVisibility(false);
  	voltmx.print("### frmFollow_onHide end");
}


function frmFollow_init(){
  voltmx.print("### frmFollow_init");
  frmFollow.onDeviceBack = Global_onDeviceBack;
  // set swipe gesture handler
//   var swipeSettings = {fingers:1,swipedistance:75,swipevelocity:75};
//   //var swipeGesture = frmFollow.menu2.setGestureRecognizer(2,swipeSettings,frmFollow_handleGesture);
//   if (Global.vars.disableSwipeMenuSettings === false){
//     var swipeGesture2 = frmFollow.flcMenuSettings.setGestureRecognizer(2,swipeSettings,frmFollow_handleGesture);
//   }
//   var swipeGesture3 = frmFollow.flcMainPage.setGestureRecognizer(2,swipeSettings,frmFollow_handleGesture);
  frmFollow.lblSearchQuery.text = "";
  frmFollow.editplate.btnRemoveText.onClick = frmFollow_onclick_btnRemoveText;
  frmFollow.lblNumberOfCases.text = "";
  frmFollow_btnNext_setVisibility(false);
  frmFollow.segPhotos.removeAll();
  frmFollow_setModus();
  frmFollow.segRegions.widgetDataMap = {lbl1 : "name"};
  frmFollow.segCaseTypes.widgetDataMap = {lbl1 : "name"};
  frmFollow.segCases.widgetDataMap = {
  					lblSecHeader : "lblSecHeader",
    				lblLocationHeader : "lblLocationHeader",
    				lblValue : "lblValue",
    				lblValueHeader : "lblValueHeader",
    				lblDate : "lblDate",
    				lblDateHeader : "lblDateHeader",
    				imgMarker : "imgMarker",
    				imgLocation : "imgLocation",
    				lblLetterCode:"letterCode", 
                    flcColorDot:"colorDot",
                    lblCountryCode : "lblCountryCode"
					};
  frmFollow.segHistoricCases.widgetDataMap = {
  					//lblVehicleHeader : "lblVehicleHeader",
    				//lblLicenseplateHeader : "lblLicenseplateHeader",
    				lblLicenseplate : "lblLicenseplate",
    				//lblCountryHeader : "lblCountryHeader",
    				lblCountry : "lblCountry",
    				//lblAdressHeader : "lblAdressHeader",
    				lblAdress1 : "lblAdress1",
    				//lblDateTimeHeader : "lblDateTimeHeader",
    				lblDateTime : "lblDateTime"
					};
  frmFollow.segTaskTimeLine.widgetDataMap = {
    				rtxDate : "date",
    				rtxTime : "time",
    				rtxTaskType : "taskType",
    				rtxUser : "taskClaimedByName",
    				rtxTaskOutcome : "taskOutcomeDescription",
    				rtxRemark : "taskOutcomeExplanation"
  					};
  frmFollow.segParkingChecks.widgetDataMap = {lbl1 : "name",
                                                  lbl2 : "result",
                                                  imgLeft : "image"};
  frmFollow.segParkingCheckDetail.widgetDataMap = {lbl1 : "key",
                                                  lbl2 : "value",
                                                  imgLeft : "imageLeft",
                                                  imgRight : "imageRight"};
  // AE0001
  if (Global.vars.licensePlatePrefixEnabled === true){
  	frmFollow_lblLicenseplatePrefix_setVisibility(true);
    frmFollow_editplate_flcEditPrefixText_setVisibility(true);
  	frmFollow.editplate.flcEditLicensePlateText.left = "2dp";
	frmFollow.editplate.flcEditLicensePlateText.width = "71%";
  } else {
    frmFollow_lblLicenseplatePrefix_setVisibility(false);
    frmFollow_editplate_flcEditPrefixText_setVisibility(false);
 	frmFollow.editplate.flcEditLicensePlateText.left = "0dp";
	frmFollow.editplate.flcEditLicensePlateText.width = "89%";
  }
  //
  voltmx.print("### frmFollow_init Global.vars.gFullName: " + Global.vars.gFullName);
}

function frmFollow_exitCallback(response){
  voltmx.application.dismissLoadingScreen();
  voltmx.print("#### frmFollow_exitCallback");
  voltmx.print("### frmFollow_exitCallback success logout response : " + JSON.stringify(response));
  if(Global.vars.connectedCloud != null){
    frmFollow_toggleMenu();
    service_LogoutAndAssignToCloud(frmFollow_exitLogOutCallback);
  }else{
    frmFollow_toggleMenu();
    frmFollow_restartApp();
  }
}

function frmFollow_exitErrorCallback(error){
  voltmx.application.dismissLoadingScreen();
  voltmx.print("#### frmFollow_exitErrorCallback");
  voltmx.print("### frmFollow_exitErrorCallback Error in foundry logout : " + JSON.stringify(error));
}

function frmFollow_exitButton(){
  voltmx.print("### frmFollow_exitButton");
  Global_logoutIdentity(frmFollow_exitCallback,frmFollow_exitErrorCallback);
}

function frmFollow_exitLogOutCallback(result){
 voltmx.print("### frmFollow_exitLogOutCallback result: " + JSON.stringify(result));
  if(result.opstatus === 0 && result.httpStatusCode == 200){
    voltmx.print("### frmFollow_exitLogOutCallback done");
    frmFollow_restartApp();
  } else {
    voltmx.print("### frmFollow_exitLogOutCallback failed");
    frmFollow_restartApp();
  }
}

function frmFollow_restartApp(){
	voltmx.print("### frmFollow_restartApp restart");
    Global_resetApp();
    frmStart.show();
}


function frmFollow_handleGesture(myWidget, gestureInfo){
//  	voltmx.print("#### frmFollow_handleGesture: " + gestureInfo.swipeDirection + " frmFollow.flcMainPage.left: " + frmFollow.flcMainPage.left + " frmFollow.flcMenuSettings.centerY: "+frmFollow.flcMenuSettings.centerY);
//   	voltmx.print("#### frmFollow_handleGesture myWidget: " + JSON.stringify(myWidget));
// 	voltmx.print("#### frmFollow_handleGesture gestureinfo: " + JSON.stringify(gestureInfo));
//   	if (gestureInfo.swipeDirection == 1 && (""+frmFollow.flcMainPage.left) == (Global.vars.deviceInfo.deviceWidth*0.85) +"px" && (""+frmFollow.flcMenuSettings.centerY) == "150%"){
// 		frmFollow_toggleMenu();
// 	} else if (gestureInfo.swipeDirection == 2 && (""+frmFollow.flcMainPage.left) == "0px" && (""+frmFollow.flcMenuSettings.centerY) == "150%"){
// 		//frmFollow_toggleMenu();
// 	} else if (gestureInfo.swipeDirection == 3 && (""+frmFollow.flcMenuSettings.centerY) == "150%" && (""+frmFollow.flcMainPage.left) == (Global.vars.deviceInfo.deviceWidth*0.85) +"px"){//637.5
// 		frmFollow_toggleMenuSettings();
// 	} else if (gestureInfo.swipeDirection == 4 && (""+frmFollow.flcMenuSettings.centerY) == "50%" && (""+frmFollow.flcMainPage.left) == (Global.vars.deviceInfo.deviceWidth*0.85) +"px"){
// 		frmFollow_toggleMenuSettings();
// 	}
}

function frmFollow_preShow(){      
  	Analytics_logScreenView("follow");
	voltmx.print("### frmFollow_preShow ###");
  	if(voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true){
      voltmx.print("### frmFollow_preShow iOS destroy ###");
      //frmNHA.destroy();
      //frmHandle.destroy();
      //frmResume.destroy();
//       frmHistory.destroy();
//       frmOpenTasks.destroy();
    }
  	//Utility_preloadMaps("frmFollow");
  	Global.vars.appMode = voltmx.i18n.getLocalizedString("l_followUp");
  	//Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_followUp"), true);
  	//frmFollow.menusetting.setDataMenuSetting(Global.vars.menusettingdata);
  	//frmFollow_setMenuCounter();
  	if(Global.vars.backFromMenuSettings === false){
      frmFollow.flcMainPage.left = "0px";
      frmFollow_toggleMenu(true);
    }
  	Global.vars.btnCountryVehicleCode = false;
  	//frmFollow.lblTeam.text = Global.vars.selectedTeamDescription;
  	frmFollow.lblLocationArea.text = voltmx.i18n.getLocalizedString("l_area");
  	//set area
  	if(CaseData.location.area != null){
      frmFollow.lblLocationArea.text = CaseData.location.area;
    }
  	GPS_watchMypositionCancel();
  	//disable map for memory issues
  	frmFollow_mapCases_setVisibility(false);
    voltmx.print("### frmFollow_preShow set location: " + JSON.stringify(CaseData.location));
  	if(CaseData.location.street != null){
      var _housenumber = "";
      var _houseletter = "";
      var _housenumberaddition = "";
      if((CaseData.location.houseNumber != null && CaseData.location.houseNumber !== "null")){
        voltmx.print("### frmFollow_preShow set housenumber");
        _housenumber = CaseData.location.houseNumber;
      }
      if((CaseData.location.houseLetter != null && CaseData.location.houseLetter !== "null")){
        voltmx.print("### frmFollow_preShow set houseletter");
        _houseletter = CaseData.location.houseLetter;
      }
      if((CaseData.location.houseNumberAddition != null && CaseData.location.houseNumberAddition !== "null")){
        voltmx.print("### frmFollow_preShow set housenumberaddition");
        _housenumberaddition = CaseData.location.houseNumberAddition;
      }
      var _streetaddition = (_housenumber+ " " + (_houseletter + " " + _housenumberaddition).trim()).trim();
      voltmx.print("### frmFollow_preShow _streetaddition: " + _streetaddition);
      if((CaseData.location.street != null && CaseData.location.street !== "null")){
        voltmx.print("### frmHandle_btnDoneSetHousenumber 1 housenumber -1");
        frmFollow.lblLocation.text = (CaseData.location.street + " " + _streetaddition).trim() + ", " + CaseData.location.city; 
      }else{
        frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
      }
      if(frmFollow.lblLocation.text!= null && frmFollow.lblLocation.text.length > 24){
        frmFollow.lblLocation.skin = lblFieldInfoSmall;
      }else{
        frmFollow.lblLocation.skin = lblFieldInfo;
      }
    }
    voltmx.print("#### frmFollow_preShow kindofvehicle");
  	if (Global.vars.enableParkiusMode === true){
      frmFollow_flcLayout_flcKindOfVehicleBrand_kindofvehicle_setVisibility(false);
    }
  	voltmx.print("### frmFollow_preShow Global.vars.previousForm: " + Global.vars.previousForm);
    if(Global.vars.previousForm === null){
      try{
        if(frmResume.uploadcase.isVisible === true){
          frmResume_dismissLoaderUploadCase();
        }
        if(frmNHA.uploadcase.isVisible === true){
          frmNHA_dismissLoaderUploadCase();
        }
      }catch(e){}
      voltmx.print("### frmFollow_preShow frmFollow_RetrieveUnclaimedCases");
      frmFollow_RetrieveUnclaimedCases();
    }
  	Utility_checkGPS();
  	if(Global.vars.isGPSEnabled === false){
      voltmx.print("### frmFollow_preShow gps is not enabled");
    }
  //	GPS_getCurrentLocation(frmFollow_gpsCallbackInit);
  	voltmx.runOnMainThread(GPS_getCurrentLocation, [frmFollow_gpsCallbackInit]);
  //bepaal bij klemmen of het een duits kenteken is om aan te passen
  	if((CaseData.caseinfo.caseType == "CONTROL_K") && frmFollow.editplate.lblCountryCodeEditLicensePlate.text !== "D"){
      frmFollow.editplate.txtLicensePlate.setEnabled(false);
    }else{
      frmFollow.editplate.txtLicensePlate.setEnabled(true);
    }
  	if (Global.vars.followBtnRecheckEnabled === true){
      frmFollow_flcRecheck_setVisibility(true);
    } else {
      frmFollow_flcRecheck_setVisibility(false);
      frmFollow.flcRemove.centerX = "50%";
    }
}

function frmFollow_setMenuCounter(){
  var countMenuActions = Number(Global.vars.numberOfOutboxfiles) + Number(Global.vars.numberOfConceptfiles) + Number(Global.vars.numberOfConceptfilesOnDevice) + Number(Global.vars.numberOfopenTasks);
  if(countMenuActions > 0 && Global.vars.showMenuCounter === true){
    frmFollow_flcCounter_setVisibility(true);
    frmFollow.lblCounter.text = countMenuActions.toString();
    if(Number(Global.vars.numberOfopenTasks) > 0){
      //set red color to menucounters
      frmFollow.flcCounter.skin = flcRoundRed;
    }else{
      frmFollow.flcCounter.skin = flcRoundBlue;
    }
  }else{
    frmFollow_flcCounter_setVisibility(false);
  }
}

function frmFollow_setRetrieveCasesTimer(){
  try {
    voltmx.timer.cancel("setRetrieveCasesTimer");
  }catch(err){}
  var form = voltmx.application.getCurrentForm().id;
  if(form == "frmFollow"){
    frmFollow_RetrieveUnclaimedCases();
  }
}

function frmFollow_gpsCallbackInit(){
  	voltmx.print("### frmFollow_gpsCallbackInit");
  	Global.vars.gLatitudeLast = Global.vars.gLatitude;
	Global.vars.gLongitudeLast = Global.vars.gLongitude;
}

function frmFollow_postShow(){
	voltmx.print("### frmFollow_postShow() ###");
  	Global.vars.backFromMenuSettings = false;
	//drag
	frmFollow.flcMainPage.onTouchStart = null;
  	frmFollow.flcMainPage.onTouchMove = null;
  	frmFollow.flcMainPage.onTouchEnd = null;
  	try{
    	voltmx.timer.schedule("setMainTouch",frmFollow_setMainTouch,0.3,false);
	}catch(err){}
}

function frmFollow_setRecheckDateTime(){
	//Set valid dates
	var startdate = new Date();
	startdate.setDate(startdate.getDate()-Global.vars.daysToLookBack);
	voltmx.print("#### frmFollow_setRecheckDateTime startdate: " + startdate); //now.format("m/dd/yy");
	//format the  start date
	var form_date = startdate.getDate();
	var form_month = startdate.getMonth();
	form_month++;
	var form_year = startdate.getFullYear();
	var valid_startdate = [form_date,form_month,form_year];
	voltmx.print("#### frmFollow_setRecheckDateTime valid startdate: " + valid_startdate);
	//format the current date
	var d = new Date();
  	var caseDate = d;
	var curr_date = d.getDate();
	var curr_month = d.getMonth();
	curr_month++;
	var curr_year = d.getFullYear();
	var curr_hour = d.getHours();
	var curr_min = d.getMinutes();
  	var curr_sec = d.getSeconds();
	var enddate = [curr_date,curr_month,curr_year,curr_hour,curr_min,curr_sec];
  	var caseDateComponents = enddate;
  	//Chgeck if casedate already exists
  	if(CaseData.time.utcDateTime != null){
      caseDate = new Date(CaseData.time.utcDateTime);
      voltmx.print("### frmFollow_setRecheckDateTime CaseTime UTC: " + caseDate);
      var case_date = caseDate.getDate();
      var case_month = caseDate.getMonth();
      case_month++;
      var case_year = caseDate.getFullYear();
      var case_hour = caseDate.getHours();
      var case_min = caseDate.getMinutes();
      var case_sec = caseDate.getSeconds();
      caseDateComponents = [case_date,case_month,case_year,case_hour,case_min,case_sec];
    }
	//End set
	voltmx.print("### frmFollow_setRecheckDateTime enddate: " + enddate);
  	voltmx.print("### frmFollow_setRecheckDateTime caseDateComponents: " + caseDateComponents);
  	Global.vars.newTimeSet = true;
  	CaseData_setDateTime(caseDateComponents, frmFollow_CaseData_setDateTimeCallback);
}

function frmFollow_CaseData_setDateTimeCallback(){
  voltmx.print("### frmFollow_CaseData_setDateTimeCallback Time set to Case: " + JSON.stringify(CaseData.time));
  CaseData.time.savedTime = true;
}

//Drag page
function frmFollow_dragMainOnStart (source, x , y){
	voltmx.print("### dragMainOnStart");
	Global.vars.init_x = frmFollow.flcMainPage.left;
}

function frmFollow_dragMainOnMove (source ,x , y){
  	voltmx.print("### dragMainOnMove");
  	if(frmFollow.flcGetNewCaseList.flcCaseSegment.isVisible === true || frmFollow.flcMenuButton.isVisible === false){
      Global.vars.cannotMove = true;
    }else{
      Global.vars.cannotMove = false;
    }
  	Global.vars.headerTouchCounter = Global.vars.headerTouchCounter+1;
  	var nowLeft = frmFollow.flcMainPage.left;
  	Global.vars.newLeft = (parseInt(nowLeft.replace("px",""))+(x*Global.vars.P_P_DP));
  	if(Global.vars.headerTouchCounter == 1){
    	Global.vars.firstLeft = Global.vars.newLeft;
  	}
  	//heen
  	var drag = null;
  	if(Global.vars.firstLeft != null && Global.vars.newLeft - Global.vars.firstLeft > 50 && Global.vars.cannotMove === false){//verhogen??
    	voltmx.print("### dragMainOnMove Left");
    	frmFollow.flcMainPage.left = Global.vars.newLeft - Global.vars.firstLeft+"px";
    	voltmx.print("### dragMainOnMove Left iPhone: " + frmFollow.flcMainPage.left);
    	drag = "1";
  	}else if(Global.vars.firstLeft != null && (Global.vars.newLeft - Global.vars.firstLeft) <= 50 && parseInt(frmFollow.flcMainPage.left.replace("px","")) < 628 && parseInt(frmFollow.flcMainPage.left.replace("px","")) > 0  && Global.vars.cannotMove === false){
    	frmFollow.flcMainPage.left = Global.vars.newLeft - Global.vars.firstLeft+"px";
    	if(parseInt(frmFollow.flcMainPage.left.replace("px","")) < 0){
      		voltmx.print("### dragMainOnMove");
      		frmFollow.flcMainPage.left = 0+"px";
    	}
    	drag = "2";
  	}else{
    	voltmx.print("### dragMainOnMove no drag");
    	drag = "no";
  	}
  	//terug
  	if (Global.vars.newLeft <= parseInt(Global.vars.init_x.replace("px",""))){
  		voltmx.print("### dragMainOnMove back");
      	if (Global.vars.gDeviceInfo.name == "iPhone"){
      		voltmx.print("### dragMainOnMove back iPhone: " + Global.vars.newLeft);
        	frmFollow.flcMainPage.left = Global.vars.newLeft+"px";
      	}else{
      		frmFollow.flcMainPage.animate(
        	voltmx.ui.createAnimation({
          		100:{
      			"left": Global.vars.newLeft +"px",
      			"stepConfig": {}}
      		}),{"fillMode": voltmx.anim.FILL_MODE_BOTH,"duration": 0},{});
        }
    }
}

function frmFollow_setMainTouch(){
  	voltmx.print("### setMainTouch Left");
  	frmFollow.flcMainPage.onTouchStart = frmFollow_dragMainOnStart;
  	frmFollow.flcMainPage.onTouchMove = frmFollow_dragMainOnMove;
  	frmFollow.flcMainPage.onTouchEnd = frmFollow_dragMainOnStop;
  	try {
  		voltmx.timer.cancel("setMainTouch");
  	}catch(err){}
}

// Once stopped dragging on the MainView, this will be called
function frmFollow_dragMainOnStop (source ,x , y){
	voltmx.print("### dragMainOnStop");
  	if(parseInt(frmFollow.flcMainPage.left.replace("px","")) < (Global.vars.gDeviceInfo.deviceWidth/2)){
    	voltmx.print("### dragMainOnStop close Menu");
       	frmFollow.flcLayout.setEnabled(true);
      	frmFollow.flcGetNewCaseList.setEnabled(true);
  		voltmx.print("### Page enabled");
  		frmFollow.flcFooterMain.setEnabled(true);
      	voltmx.print("### Footer enabled");
       	frmFollow.menu2.setEnabled(false);
       	voltmx.print("### Menu disabled");
       	frmFollow.flcMainPage.animate(
        voltmx.ui.createAnimation({
          100:{
      		"left": 0+"px",
      		"stepConfig": {"timingFunction": voltmx.anim.EASIN_IN_OUT}}
      	}),{"fillMode": voltmx.anim.FILL_MODE_FORWARDS,"duration": 0.20,"delay" : 0.05},{});
      	frmFollow.flcMainPage.onTouchStart = null;
      	frmFollow.flcMainPage.onTouchMove = null;
      	frmFollow.flcMainPage.onTouchEnd = null;
      	try{
             voltmx.timer.schedule("setMainTouch",frmFollow_setMainTouch,0.3,false);
        }catch(err){}
	}else{
      	voltmx.print("### dragMainOnStop open Menu");
      	frmFollow.flcLayout.setEnabled(false);
      	frmFollow.flcGetNewCaseList.setEnabled(false);
  		voltmx.print("### Page disabled");
  		frmFollow.flcFooterMain.setEnabled(false);
      	voltmx.print("### Footer disabled");
      	frmFollow.menu2.setEnabled(true);
      	voltmx.print("### Menu enabled");
      	frmFollow.flcMainPage.animate(
        voltmx.ui.createAnimation({
          100:{
      		"left": (Global.vars.gDeviceInfo.deviceWidth*0.85) +"px",
      		"stepConfig": {"timingFunction": voltmx.anim.EASIN_IN_OUT}}
      	}),{"fillMode": voltmx.anim.FILL_MODE_FORWARDS,"duration": 0.20,"delay" : 0.05},{});
    }
  	Global.vars.headerTouchCounter = 0;
  	Global.vars.firstTouchMain = false;
}

function frmFollow_onTouchStart_doNotMove(){
  voltmx.print("### frmFollow_onTouchStart_doNotMove no movement");
  Global.vars.cannotMove = true;
}

function frmFollow_onTouchEnd_doNotMove(){
  voltmx.print("### frmFollow_onTouchEnd_doNotMove movement allowed");
  Global.vars.cannotMove = false;
}
//end of drag

function frmFollow_showEditPlate() {
  voltmx.print("### frmFollow_showEditPlate Global.vars.disableRecheck: " + Global.vars.disableRecheck);
  if (Global.vars.disableRecheck === true){
    alert(voltmx.i18n.getLocalizedString("l_noChangeAvailable"));
  } else {
      try {
      	//set correct data
  		frmFollow_setPlateData();
        frmFollow.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmFollow.forceLayout();
        frmFollow_editplate_setVisibility(true);
        frmFollow_showEditPlate_preAnim();
        frmFollow_showEditPlate_animationStart();
    } catch (e) {
      voltmx.print("### frmFollow_showEditPlate error: " + JSON.stringify(e));
    }

  }
}

function frmFollow_showEditPlate_preAnim() {
  try {
    voltmx.print("### frmFollow_showEditPlate_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
    //frmFollow.flcDetailEditPlate.transform = trans1;
    //frmFollow.imgPopupLogoEditPlate.transform = trans1;
  } catch (e) {
    voltmx.print("### frmFollow_showEditPlate_preAnim error: " + JSON.stringify(e));
  }
}

function frmFollow_showEditPlate_arrangeWidgets() {
  try {
    voltmx.print("### frmFollow_showEditPlate_arrangeWidgets");
    //popup fields
    frmFollow.imgPopupLogoEditPlate.isVisible = false;
    frmFollow.editplate.forceLayout();
    frmFollow.flcDetailEditPlate.isVisible = false;
    frmFollow.lblLineEditPlate.isVisible = false;
    frmFollow.flcFooterShowEditPlate.isVisible = false;
    frmFollow_editplate_setVisibility(false);
  } catch (e) {
    voltmx.print("### frmFollow_showEditPlate_arrangeWidgets error: " + JSON.stringify(e));
  }

}

function frmFollow_showEditPlate_animationStart(eventobject) {
  try {
    voltmx.print("### frmFollow_showEditPlate_animationStart");
    frmFollow_editplate_setVisibility(true);
    frmFollow.flcDetailEditPlate.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmFollow.flcDetailEditPlate.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmFollow_showEditPlate_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmFollow_showEditPlate_animationStart error: " + JSON.stringify(e));
  }
}

function frmFollow_showEditPlate_animLogo() {
  try {
    voltmx.print("### frmFollow_showEditPlate_animLogo");
    frmFollow_showEditPlate_animOtherWidgets(frmFollow.lblLineEditPlate);
    frmFollow.imgPopupLogoEditPlate.isVisible = true;
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showEditPlate_animLogo error: " + JSON.stringify(e));
  }
}

function frmFollow_showEditPlate_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmFollow_showEditPlate_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmFollow.flcEditPlateDetails.isVisible = true;
    frmFollow.lblLineEditPlate.isVisible = true;
    frmFollow.flcFooterShowEditPlate.isVisible = true;
    frmFollow.flcFooterShowEditPlate.setEnabled(true);
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showEditPlate_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmFollow_showEditPlate_animLogoBack() {
  try {
    voltmx.print("### frmFollow_showEditPlate_animLogo");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmFollow.imgPopupLogoEditPlate.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showEditPlate_animLogo error: " + JSON.stringify(e));
  }
}

function frmFollow_hideShowEditPlate(){
  //activate footer and mainpage
  frmFollow.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmFollow_editplate_setVisibility(false);
  frmFollow_contentOffset();
}

function frmFollow_setPlateData(){
  frmFollow.editplate.txtLicensePlate.text = frmFollow.lblLicenseplate.text;
  if(frmFollow.lblLicenseplate.text == voltmx.i18n.getLocalizedString("l_licenseplate")){
    frmFollow.editplate.txtLicensePlate.text = "";
  }
  if(frmFollow.lblLicenseplate.text == voltmx.i18n.getLocalizedString("l_plateNotValid")){
    frmFollow.editplate.txtLicensePlate.text = "";
  }
  frmFollow.editplate.lblCountryCodeEditLicensePlate.text = frmFollow.lblCountryCode.text;
  frmFollow.editplate.txtPrefix.text = frmFollow.lblLicenseplatePrefix.text;
  //bepaal bij klemmen of het een duits kenteken is om aan te passen
  if((CaseData.caseinfo.caseType == "CONTROL_K") && frmFollow.editplate.lblCountryCodeEditLicensePlate.text !== "D"){
    frmFollow.editplate.txtLicensePlate.setEnabled(false);
  }else{
    frmFollow.editplate.txtLicensePlate.setEnabled(true);
  }
}

function frmFollow_toggleFilters(){
	if ((""+frmFollow.flcFilters.left) == "-100%"){
      var mainAnimation = voltmx.ui.createAnimation({
      "100": {
                  "left" : 0+'%',
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
      var mainSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 0.4
                  };
      frmFollow.flcFilters.animate(mainAnimation,mainSetting);
      frmFollow_flcMenuButton_setVisibility(false);
      frmFollow_btnCancelClaim_setVisibility(false);
      frmFollow_btnNext_setVisibility(false);
  }else{
  	frmFollow_hideFilters();
  }
}

function frmFollow_hideFilters(){
  var mainAnimation = voltmx.ui.createAnimation({
    "100": {
      			"left" : -100+'%',
            	"stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
           }
  	});
  	var mainSetting = {"delay" : 0,
                 "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                 "duration": 0.4
                };
  	frmFollow.flcFilters.animate(mainAnimation,mainSetting);
  	frmFollow_flcMenuButton_setVisibility(true);
  	frmFollow_btnCancelClaim_setVisibility(false);
  	if(frmFollow.lblLocation.text == voltmx.i18n.getLocalizedString("l_location") || frmFollow.lblLicenseplate.text == voltmx.i18n.getLocalizedString("l_licenseplate")){
      frmFollow_btnNext_setVisibility(false);
    }else{
      frmFollow_btnNext_setVisibility(true);
    }
}

function frmFollow_setFilters(){
  var selected = frmFollow.segFilters.selectedItems;
  voltmx.print("### frmFollow_setFilters selected: " + JSON.stringify(selected));
  frmFollow.segFilters.selectedIndices = null;
  frmFollow.imgFilter.src = "butfilter.png";
  gSetFilters = [];
  if(frmFollow.flcFilters.left == 0+'%'){
   	frmFollow_toggleFilters();
  }
}

function frmFollow_removeFilters(){
  frmFollow.imgFilter.src = "butfilter.png";
  frmFollow.segFilters.selectedIndices = null;
  gSetFilters = [];
  frmFollow_hideFilters();
}

function frmFollow_toggleMenu(close){
	frmFollow.contentSize = {height:"100%", width:"100%"};
	if(close !== undefined && close === true){
      //hide menu so it can not be clicked
      frmFollow.flcLayout.setEnabled(true);
      frmFollow.flcGetNewCaseList.setEnabled(true);
      voltmx.print("### Page enabled");
      frmFollow.flcFooterMain.setEnabled(true);
      voltmx.print("### Footer enabled");
      frmFollow.menu2.setEnabled(false);
      voltmx.print("### Menu disabled");
      var mainAnimation = voltmx.ui.createAnimation({
      "100": {
                  "left" : 0+'px',
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
      var mainSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 0.4
                  };

      frmFollow.flcMainPage.animate(mainAnimation,mainSetting);
      if (Global.vars.useQueueLengthTimer == "no" && Global.vars.queueInUse === true){
        Global.vars.queueInUse = false;
        frmFollow_RetrieveUnclaimedCases();
      }
	}else if ((""+frmFollow.flcMainPage.left) == "0px"){
      //show menu so it can be clicked
		frmFollow.flcLayout.setEnabled(false);
      	frmFollow.flcGetNewCaseList.setEnabled(false);
	  	voltmx.print("### Page disabled");
	  	frmFollow.flcFooterMain.setEnabled(false);
	    voltmx.print("### Footer disabled");
		frmFollow.menu2.setEnabled(true);
		voltmx.print("### Menu enabled");
      var mainAnimation = voltmx.ui.createAnimation({
      "100": {
                  "left": (Global.vars.deviceInfo.deviceWidth*0.85) +"px",
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
      var mainSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 0.4
                  };

    frmFollow.flcMainPage.animate(mainAnimation,mainSetting);
  }else{
  	//hide menu so it can not be clicked
	frmFollow.flcLayout.setEnabled(true);
    frmFollow.flcGetNewCaseList.setEnabled(true);
  	voltmx.print("### Page enabled");
  	frmFollow.flcFooterMain.setEnabled(true);
    voltmx.print("### Footer enabled");
	frmFollow.menu2.setEnabled(false);
	voltmx.print("### Menu disabled");
  	var mainAnimation = voltmx.ui.createAnimation({
    "100": {
      			"left" : 0+'px',
            	"stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
           }
  	});
  	var mainSetting = {"delay" : 0,
                 "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                 "duration": 0.4
                };

  	frmFollow.flcMainPage.animate(mainAnimation,mainSetting);
  }
}

function frmFollow_toggleMenuSettings(){
  voltmx.print("### frmFollow_toggleMenuSettings frmFollow.flcMenuSettings.centerY: " + frmFollow.flcMenuSettings.centerY);
  if ((""+frmFollow.flcMenuSettings.centerY) == 150+"%"){
    var menuAnimation = voltmx.ui.createAnimation({
    "100": {
      			"centerY" : 50+'%',
            	"stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
           }
  	});
    var menuSetting = {"delay" : 0,
                 "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                 "duration": 0.4
                };

  	frmFollow.flcMenuSettings.animate(menuAnimation,menuSetting);
    var devicename = voltmx.os.deviceInfo().name;
	if(devicename == "iPhone"){
		try{
			voltmx.timer.schedule("BlackBackground",frmFollow_changeBackgroundBlack, 0.2, false);
		}catch(e){}
	}
  }else{
    var menuAnimation = voltmx.ui.createAnimation({
    "100": {
      			"centerY" : 150+'%',
            	"stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
           }
  	});
    var menuSetting = {"delay" : 0,
                 "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                 "duration": 0.4
                };

  	frmFollow.flcMenuSettings.animate(menuAnimation,menuSetting);
    frmFollow.skin = frmBackground;
  }
}

function frmFollow_changeBackgroundBlack(){
  frmFollow.skin = frmBlack;
}

function frmFollow_setModus(){
  voltmx.print("### frmFollow_setModus: " + Global.vars.appMode);
  if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_followUp")){
    //header
    frmFollow.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_followUp");
    //zet knoppen uit
    frmFollow_btnLocation_setVisibility(false);
    frmFollow_btnTime_setVisibility(false);
//     if(CaseData.caseinfo.caseType == "CONTROL_K"){
//       	frmFollow.flcFooterMain.setVisibility(false);
//     }else{
//       	frmFollow.flcFooterMain.setVisibility(true);
//     }
//    frmFollow_flcTimeArea_flcLocationArea_setVisibility(false);
//     frmFollow.flcTimeArea.flcTime.width = '100%';
    //gevuld scherm service aanroep
    if(CaseData.processinfo.activeTaskType != null){
      frmFollow_flcResult_setVisibility(true);
      frmFollow_flcLicenseplateImageIn_setVisibility(true);
      if(Global.vars.licenseplateCountryCode == 6030){
        frmFollow_flcKindOfVehicleBrand_setVisibility(true);
      }else{
        frmFollow_flcKindOfVehicleBrand_setVisibility(false);
      }
      //Zet volgende aan
      frmFollow_btnNext_setVisibility(true);
      //zet scherm haal zaak op uit
      frmFollow_flcGetNewCaseList_setVisibility(false);
      frmFollow.flcLayout.setEnabled(true);
      frmFollow_flcMenuButton_setVisibility(false);
      frmFollow_btnCancelClaim_setVisibility(true);
  	}else{
      frmFollow.lblTime.text = voltmx.i18n.getLocalizedString("l_time");
      //show load case
      //zet scherm haal zaak op uit
      frmFollow_flcGetNewCaseList_setVisibility(false);
      frmFollow.flcLayout.setEnabled(true);
      frmFollow_flcMenuButton_setVisibility(false);
      frmFollow_btnCancelClaim_setVisibility(true);
      //Zorg dat scherm eerst leeg is
      frmFollow_flcResult_setVisibility(false);
  	  frmFollow_flcLicenseplateImageIn_setVisibility(false);
      frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
      frmFollow.lblLocation.skin = lblFieldInfo;
      frmFollow.lblLicenseplate.text = voltmx.i18n.getLocalizedString("l_licenseplate");
      frmFollow.lblLicenseplatePrefix.text = "";
    }
  }
  if(frmFollow.lblLicenseplate.text == voltmx.i18n.getLocalizedString("l_licenseplate")){
    frmFollow.lblLicenseplate.skin = lblFieldInfoLicenseplate;
  }else{
    frmFollow.lblLicenseplate.skin = lblBlack350Licenseplate;
  }
}

function frmFollow_populateFromExistingCase(){
  voltmx.print("### frmFollow_populateFromExistingCase");
  //Clear form
  frmFollow_resetForm();
  //Time
  frmFollow_setRecheckDateTime();
  frmFollow.lblTime.text = CaseData.time.localeShortDate;
  //Set claimed by user indication
  voltmx.print("### frmFollow_populateFromExistingCase 1");
  CaseData_setUserInformationToCaseInfo();
  CaseData.caseinfo.indClaimedByUser = true;
  CaseData.caseinfo.timeClaimedByUser = Utility_getUTCJavascriptDate(null);
  Global.vars.lastTaskProcessedPrevious = CaseData.processinfo.lastTaskProcessed;
  voltmx.print("### frmFollow_populateFromExistingCase 2");
  CaseData.caseinfo.updateUser = Global.vars.gUsername;
  voltmx.print("### frmFollow_populateFromExistingCase 3");
  //reset Global.vars.handleCharacteristicType
  Global.vars.handleCharacteristicType = "Handlingtype";
//   frmHandleCharacteristic.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_kindOfHandling");
//   frmHandleCharacteristic.segSearch.removeAll();
  var houseNumberAddition = "";
  if(CaseData.location.houseNumberAddition != null){
    houseNumberAddition = " " + CaseData.location.houseNumberAddition;
  }
  //Location
  var _housenumber = "";
  var _houseletter = "";
  var _housenumberaddition = "";
  if((CaseData.location.houseNumber != null && CaseData.location.houseNumber !== "null")){
    voltmx.print("### frmFollow_preShow set housenumber");
    _housenumber = CaseData.location.houseNumber;
  }
  if((CaseData.location.houseLetter != null && CaseData.location.houseLetter !== "null")){
    voltmx.print("### frmFollow_preShow set houseletter");
    _houseletter = CaseData.location.houseLetter;
  }
  if((CaseData.location.houseNumberAddition != null && CaseData.location.houseNumberAddition !== "null")){
    voltmx.print("### frmFollow_preShow set housenumberaddition");
    _housenumberaddition = CaseData.location.houseNumberAddition;
  }
  var _streetaddition = (_housenumber+ " " + (_houseletter + " " + _housenumberaddition).trim()).trim();
  voltmx.print("### frmFollow_preShow _streetaddition: " + _streetaddition);
  if((CaseData.location.street != null && CaseData.location.street !== "null")){
    voltmx.print("### frmHandle_btnDoneSetHousenumber 1 housenumber -1");
    frmFollow.lblLocation.text = (CaseData.location.street + " " + _streetaddition).trim() + ", " + CaseData.location.city; 
  }else{
    frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
  }
  if(frmFollow.lblLocation.text!= null && frmFollow.lblLocation.text.length > 24){
    frmFollow.lblLocation.skin = lblFieldInfoSmall;
  }else{
    frmFollow.lblLocation.skin = lblFieldInfo;
  }
  voltmx.print("### frmFollow_populateFromExistingCase 4");
  //Vehicle
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode === null){
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode = 0;
  }
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumberFormatted != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumberFormatted !== ""){
  	frmFollow.lblLicenseplate.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumberFormatted;
  }else{
    frmFollow.lblLicenseplate.text = Utility_checkLicenseplate(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber, CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode, true);
  }
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].prefix !== undefined && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].prefix != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].prefix !== ""){
  	frmFollow.lblLicenseplatePrefix.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].prefix;
  } else {
  	frmFollow.lblLicenseplatePrefix.text = "";
  }
  frmFollow.editplate.txtLicensePlate.text = frmFollow.lblLicenseplate.text;
  frmFollow.editplate.txtPrefix.text = frmFollow.lblLicenseplatePrefix.text;
  frmFollow.lblLicenseplate.skin = lblBlack350Licenseplate;
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense === null){
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense = "";
  }
  frmFollow.lblCountryCode.text = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense;
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseDesc === null){
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseDesc = "";
  }
  //fill countrycode globals
//   Global.vars.licenseplateCountryCode = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode;
//   Global.vars.licenseplateCountryModule = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense;
//   Global.vars.licenseplateCountryDesc = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseDesc;
//  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode == 6030){
  frmFollow_flcKindOfVehicleBrand_setVisibility(true);
  voltmx.print("### frmFollow_populateFromExistingCase 5");
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc === null){
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc = "";
    if (Global.vars.setDefaultVehicleTypeGroup === true && (CaseData.caseinfo.caseType == "CONTROL_F" || CaseData.caseinfo.caseType == "CONTROL_L")){
      CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup = "P";
      frmFollow_findAndSetDefaultvehicleType();
    }
  }else if(CaseData.caseinfo.caseType == "CONTROL_F" || CaseData.caseinfo.caseType == "CONTROL_L"){
    frmFollow_findAndSetDefaultvehicleType();
  }
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc === null){
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc = "";
  }
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandTypeDesc === null){
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandTypeDesc = "";
  }
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].color === null){
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].color = "";
  }
  voltmx.print("### frmFollow_populateFromExistingCase 6");

  frmFollow.kindofvehicle.lblText.text = Utility_CapatilizeSentence(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc);
  if(frmFollow.kindofvehicle.lblText.text != null && frmFollow.kindofvehicle.lblText.text.length > 24){
    frmFollow.kindofvehicle.lblText.skin = lblFieldInfoSmall;
  }else{
    frmFollow.kindofvehicle.lblText.skin = lblFieldInfo;
  }
  if(frmFollow.kindofvehicle.lblText.text === ""){
    frmFollow_kindofvehicle_setVisibility(false);
  } else {
    frmFollow_kindofvehicle_setVisibility(true);
  }
  voltmx.print("### frmFollow_populateFromExistingCase 7");
  frmFollow.brand.lblText.text = Utility_CapatilizeSentence(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandDesc);
  if(frmFollow.brand.lblText.text != null && frmFollow.brand.lblText.text.length > 24){
    frmFollow.brand.lblText.skin = lblFieldInfoSmall;
  }else{
    frmFollow.brand.lblText.skin = lblFieldInfo;
  }
  voltmx.print("### frmFollow_populateFromExistingCase 10");
  if(frmFollow.brand.lblText.text === ""){
    frmFollow_brand_setVisibility(false);
  } else {
    frmFollow_brand_setVisibility(true);
  	if(frmFollow.brand.lblText.text != null){
      frmFollow.brand.lblText.skin = lblFieldInfo;
    }else{
      frmFollow.brand.lblText.skin = lblFieldNotFilled;
    }
  }
  voltmx.print("### frmFollow_populateFromExistingCase 8b");
  frmFollow.brandtype.lblText.text = Utility_CapatilizeSentence(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].brandTypeDesc);
  voltmx.print("### frmFollow_populateFromExistingCase 9b");
  if(frmFollow.brandtype.lblText.text != null && frmFollow.brandtype.lblText.text.length > 24){
    frmFollow.brandtype.lblText.skin = lblFieldInfoSmall;
  }else{
    if(frmFollow.brandtype.lblText.text != null){
      frmFollow.brandtype.lblText.skin = lblFieldInfo;
    }else{
      frmFollow.brandtype.lblText.skin = lblFieldNotFilled;
    }
  }
  voltmx.print("### frmFollow_populateFromExistingCase 10b");
  if(frmFollow.brandtype.lblText.text === ""){
    frmFollow_brandtype_setVisibility(false);
  } else {
    frmFollow_brandtype_setVisibility(true);
  }
  voltmx.print("### frmFollow_populateFromExistingCase 8c");
  frmFollow.color.lblText.text = Utility_CapatilizeSentence(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].colorDesc);
  voltmx.print("### frmFollow_populateFromExistingCase 9c");
  if(frmFollow.color.lblText.text != null && frmFollow.color.lblText.text.length > 24){
    frmFollow.color.lblText.skin = lblFieldInfoSmall;
  }else{
    if(frmFollow.color.lblText.text != null){
      frmFollow.color.lblText.skin = lblFieldInfo;
    }else{
      frmFollow.color.lblText.skin = lblFieldNotFilled;
    }
  }
  voltmx.print("### frmFollow_populateFromExistingCase 10c");
  if(frmFollow.color.lblText.text === ""){
    frmFollow_color_setVisibility(false);
  } else {
    frmFollow_color_setVisibility(true);
  }

  frmFollow.flcKindOfVehicleBrand.forceLayout();
//   }else{
//     frmFollow_flcKindOfVehicleBrand_setVisibility(false);
//   }
  Global.vars.vehicleTypeGroupOriginal.vehicleTypeGroup = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup;
  Global.vars.vehicleTypeGroupOriginal.vehicleTypeGroupDesc = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupDesc;
  Global.vars.vehicleTypeGroupOriginal.vehicleTypeGroupId = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupId;
  //set vehicle to global
  Global.vars.gCaseVehicles = CaseData.vehicle[Global.vars.gCaseVehiclesIndex];
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumberFormatted != null && CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumberFormatted !== ""){
    voltmx.print("### frmFollow_populateFromExistingCase CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumberFormatted already filled");
  }else{
    voltmx.print("### frmFollow_populateFromExistingCase CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumberFormatted not filled");
   	Global.vars.gCaseVehicles.identNumberFormatted = Utility_checkLicenseplate(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber, CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode, true);
  }
  //status
  Utility_getCaseTypeDescriptionFrmFollow(CaseData.caseinfo.caseType); //opzoeken omschrijving van dit zaaktype
  //set Status color and image
  //change color
  frmFollow.lblStatus.skin = lblFieldInfo;
  //replace alarm bell
  frmFollow.imgStatusLeft.src = "alarm.png";
  frmFollow.lblStatus.text = "";
  frmFollow_flcResult_setVisibility(true);
  frmFollow_flcLicenseplateImageIn_setVisibility(true);
  voltmx.print("### frmFollow_populateFromExistingCase Global.vars.noViolation: "  + Global.vars.noViolation);
  //Zet volgende aan
  if(Global.vars.noViolation === false){
    frmFollow_btnNext_setVisibility(true);
    frmFollow_btnNoViolation_setVisibility(false);
    //zet status text op basis van zaaktype
    if(CaseData.caseinfo.caseType == "CONTROL_F"){
      frmFollow.lblStatus.text = voltmx.i18n.getLocalizedString("l_statusControl_F");
    }else if(CaseData.caseinfo.caseType == "CONTROL_L"){
      frmFollow.lblStatus.text = voltmx.i18n.getLocalizedString("l_statusControl_L");
    }else if(CaseData.caseinfo.caseType == "CONTROL_K"){
      frmFollow.lblStatus.text = voltmx.i18n.getLocalizedString("l_statusControl_K");
    }else if(CaseData.caseinfo.caseType == "CONTROL_O"){
      frmFollow.lblStatus.text = voltmx.i18n.getLocalizedString("l_statusControl_O");
    }else{
      frmFollow.lblStatus.text = voltmx.i18n.getLocalizedString("l_statusClaimed");
    }
//     for(var j in CaseData.processinfo.tasks){
//       var w = CaseData.processinfo.tasks[j];
//       if(w.taskType == "OpvolgenControleInBackoffice"){
//         frmFollow_getTaskOutcomeDescription(w.taskOutcome);
//       }
//     }
    //check if officer has rights to handle caseType
    //CaseData.caseinfo.caseType
    var authorized = false;
    voltmx.print("### frmFollow_populateFromExistingCase Global.vars.authorizedCaseTypes: " + JSON.stringify(Global.vars.authorizedCaseTypes));
    for(var o in Global.vars.authorizedCaseTypes){
      if(Global.vars.authorizedCaseTypes[o].identification == CaseData.caseinfo.caseType){
        authorized = true;
      }
    }
    voltmx.print("### frmFollow_populateFromExistingCase authorized to handle case: " + authorized);
    if(authorized === false){
      frmFollow_btnNext_setVisibility(false);
      frmFollow_btnNoAutorization_setVisibility(true);//this button is same as cancel claim
    }else{
	  frmFollow_btnNext_setVisibility(true);
      frmFollow_btnNoAutorization_setVisibility(false);
      if(CaseData.caseinfo.caseType == "CONTROL_F"){
        voltmx.print("### search history");
  		//service_LicenseplateHistory("88pxxt", "2018-04-13", "ticket_f", "opvolgenafgehandeldfiscaal", frmFollow_LicenseplateHistorySuccesCallback, frmFollow_LicenseplateHistoryErrorCallback);
      //	service_LicenseplateHistory(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber, CaseData.time.caseDate, "ticket_f", "opvolgenafgehandeldfiscaal", frmFollow_LicenseplateHistorySuccesCallback, frmFollow_LicenseplateHistoryErrorCallback);
      }
    }
  }else{
    frmFollow.lblStatus.text = voltmx.i18n.getLocalizedString("l_noViolation");
    frmFollow_btnNext_setVisibility(false);
    frmFollow_btnNoViolation_setVisibility(true);
    frmFollow.imgStatusLeft.src = "validgreen.png";
  }
  //set footer buttons
  if(CaseData.caseinfo.caseType == "CONTROL_K"){
    frmFollow_flcFooterMain_setVisibility(false);
  }else{
    frmFollow_flcFooterMain_setVisibility(true);
  }
  if(CaseData.processinfo.tasks.length > 0){
    var showTaskImage = false;
    for(var i in CaseData.processinfo.tasks){
      	var v = CaseData.processinfo.tasks[i];
        if(v.taskOutcomeExplanation != null && v.taskOutcomeExplanation !== "n/a" && v.taskOutcomeExplanation !== ""){
			showTaskImage = true;
          	break;
        }
    }
    if(showTaskImage === true){
      frmFollow.imgStatusRight.src = "task.png";
      frmFollow_imgStatusRight_setVisibility(true);
    }else{
      frmFollow_imgStatusRight_setVisibility(false);
    }
  }else{
    frmFollow_imgStatusRight_setVisibility(false);
  }
  //stop loading screen
  voltmx.print("#### frmFollow_populateFromExistingCase parking.serviceResult: " + JSON.stringify(CaseData.parking.serviceResult));
  if (CaseData.parking.serviceResult !== undefined && CaseData.parking.serviceResult != null){
  	frmFollow_setParkingCheckResult();
  } else {
  	voltmx.application.dismissLoadingScreen();
  	frmFollow_dismissLoaderUploadCase();
  }
  frmFollow_flcGetNewCaseList_setVisibility(false);
  frmFollow_flcGetNewCaseList_flcRetrieveCases_setVisibility(false);
  voltmx.print("### frmFollow_populateFromExistingCase end");
}

function frmFollow_LicenseplateHistorySuccesCallback(result){
  voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback result: " + JSON.stringify(result));
  if(result.opstatus === 0 && result.httpStatusCode == 200){
    if(result.numberOfCases > 0){
      voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback licensePlateHistoryResponse latest date: " + result.licensePlateHistoryResponse[0]._source.doc.case.time.localeShortDate);
      voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback licensePlateHistoryResponse latest latitude: " + result.licensePlateHistoryResponse[0]._source.doc.case.location.latitude);
      voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback licensePlateHistoryResponse latest longitude: " + result.licensePlateHistoryResponse[0]._source.doc.case.location.longitude);
      var Adress1 = "";
      if(result.licensePlateHistoryResponse[0]._source.doc.case.location.houseNumber != null){
          	voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback licensePlateHistoryResponse latest location: " + result.licensePlateHistoryResponse[0]._source.doc.case.location.street + " " + result.licensePlateHistoryResponse[0]._source.doc.case.location.houseNumber + ", " + result.licensePlateHistoryResponse[0]._source.doc.case.location.city, true);
     		Adress1 = result.licensePlateHistoryResponse[0]._source.doc.case.location.street + " " + result.licensePlateHistoryResponse[0]._source.doc.case.location.houseNumber;
      }else{
          	voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback licensePlateHistoryResponse latest location: " + result.licensePlateHistoryResponse[0]._source.doc.case.location.street);
        	Adress1 = result.licensePlateHistoryResponse[0]._source.doc.case.location.street;
      }
      //TEST VALUES
      //CaseData.location.latitude = 52.362156;//TEST
      //CaseData.location.longitude = 4.8374777;//TEST
      //check if current case within range of historic case
      var location = {lat:result.licensePlateHistoryResponse[0]._source.doc.case.location.latitude, lon:result.licensePlateHistoryResponse[0]._source.doc.case.location.longitude};
      var withinRange = voltmx.map.containsLocation(
        voltmx.map.SHAPE_TYPE_CIRCLE,location,
        {
          locations : [{lat:CaseData.location.latitude, lon:CaseData.location.longitude}],
          radius : Number(Global.vars.licensplateHistoryRadius) //in meters //instantieparamter to number Global.vars.licensplateHistoryRadius
        }
      );
      var licenseplate = result.licensePlateHistoryResponse[0]._source.doc.case.vehicle[0].identNumberFormatted;
      if(result.licensePlateHistoryResponse[0]._source.doc.case.vehicle[0].identNumberFormatted != null && result.licensePlateHistoryResponse[0]._source.doc.case.vehicle[0].identNumberFormatted !== ""){
        voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback identNumberFormatted already filled");
      }else{
        voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback identNumberFormatted not filled");
        licenseplate = Utility_checkLicenseplate(result.licensePlateHistoryResponse[0]._source.doc.case.vehicle[0].identNumber, result.licensePlateHistoryResponse[0]._source.doc.case.vehicle[0].countryLicenseCode, true);
      }
      voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback historic case within range of current case: " + withinRange);
      frmFollow.segHistoricCases.removeAll();
      var historicPlates = [];
      var addrecord = {
  					//lblVehicleHeader : "lblVehicleHeader",
    				//lblLicenseplateHeader : "lblLicenseplateHeader",
    				lblLicenseplate : licenseplate,
    				//lblCountryHeader : "lblCountryHeader",
    				lblCountry : result.licensePlateHistoryResponse[0]._source.doc.case.vehicle[0].countryLicense,
    				//lblAdressHeader : "lblAdressHeader",
    				lblAdress1 : Adress1,
    				//lblDateTimeHeader : "lblDateTimeHeader",
    				lblDateTime : result.licensePlateHistoryResponse[0]._source.doc.case.time.localeShortDate
					};
      historicPlates.push(addrecord);
      frmFollow.segHistoricCases.setData(historicPlates);
      voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback historic case within range of current case: " + JSON.stringify(frmFollow.segHistoricCases.data));
      Global.vars.historicCaseData = result.licensePlateHistoryResponse[0]._source.doc.case;
      frmFollow_showHistoricCases();
    }else{
      voltmx.print("### frmFollow_LicenseplateHistorySuccesCallback No historic cases");
    }
  }
}

function frmFollow_LicenseplateHistoryErrorCallback(error){
  voltmx.print("### frmFollow_LicenseplateHistoryErrorCallback result: " + JSON.stringify(error));
}

function frmFollow_showHistoricCases() {
    try {
        frmFollow.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmFollow.forceLayout();
        frmFollow.flcHistoricCases.isVisible=true;
        frmFollow_showHistoricCases_preAnim();
        frmFollow_showHistoricCases_animationStart();
    } catch (e) {
      voltmx.print("### frmFollow_showHistoricCases error: " + JSON.stringify(e));
    }
}

function frmFollow_showHistoricCases_preAnim() {
  try {
    voltmx.print("### frmFollow_showHistoricCases_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
    //frmFollow.flcHistoricCasesDetails.transform = trans1;
    //frmFollow.imgPopupLogoHistoricCases.transform = trans1;
    //frmFollow.flcRegionDetails.transform = trans1;
  } catch (e) {
    voltmx.print("### frmFollow_showRegionInfo_preAnim error: " + JSON.stringify(e));
  }
}

function frmFollow_showHistoricCases_arrangeWidgets() {
  try {
    voltmx.print("### frmFollow_showRegionInfo_arrangeWidgets");
    //popup fields
    frmFollow.imgPopupLogoHistoricCases.isVisible = false;
    frmFollow.flcHistoricCases.forceLayout();
    frmFollow.flcHistoricCasesDetails.isVisible = false;
    frmFollow.lblLineHistoricCases.isVisible = false;
    frmFollow.flcFooterHistoricCases.isVisible = false;
    frmFollow.flcHistoricCases.isVisible = false;
  } catch (e) {
    voltmx.print("### frmFollow_showRegionInfo_preAnim error: " + JSON.stringify(e));
  }

}

function frmFollow_showHistoricCases_animationStart(eventobject) {
  try {
    voltmx.print("### frmFollow_showHistoricCases_animationStart");
    frmFollow.flcHistoricCases.isVisible = true;
    frmFollow.flcHistoricCasesDetails.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmFollow.flcHistoricCasesDetails.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmFollow_HistoricCases_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmFollow_showHistoricCases_animationStart error: " + JSON.stringify(e));
  }
}

function frmFollow_HistoricCases_animLogo() {
  try {
    voltmx.print("### frmFollow_HistoricCases_animLogo");
//     var trans = voltmx.ui.makeAffineTransform();
//     trans.scale(1.2, 1.2);
//     frmFollow.imgPopupLogoHistoricCases.animate(
//       voltmx.ui.createAnimation({
//         "100": {
//           "anchorPoint": {
//             "x": 0.5,
//             "y": 0.5
//           },
//           "stepConfig": {
//             "timingFunction": voltmx.anim.EASE
//           },
//           "transform": trans,
//         }
//       }), {
//         "delay": 0,
//         "iterationCount": 1,
//         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
//         "duration": 0.25
//       }, {
//         "animationEnd": function (){
//           frmFollow_showHistoricCases_animOtherWidgets(frmFollow.flcHistoricCasesDetails);
//           frmFollow_showHistoricCases_animOtherWidgets(frmFollow.lblLineHistoricCases);
//           frmFollow_HistoricCases_animLogoBack();
//         }
//       });
    frmFollow_showHistoricCases_animOtherWidgets(frmFollow.flcHistoricCasesDetails);
    frmFollow_showHistoricCases_animOtherWidgets(frmFollow.lblLineHistoricCases);
    frmFollow.imgPopupLogoHistoricCases.isVisible = true;
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_HistoricCases_animLogo error: " + JSON.stringify(e));
  }
}


function frmFollow_showHistoricCases_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmFollow_showHistoricCases_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmFollow.flcHistoricCasesDetails.isVisible = true;
    frmFollow.lblLineHistoricCases.isVisible = true;
    frmFollow.flcFooterHistoricCases.isVisible = true;
    frmFollow.flcFooterHistoricCases.setEnabled(true);
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showHistoricCases_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmFollow_HistoricCases_animLogoBack() {
  try {
    voltmx.print("### frmFollow_HistoricCases_animLogoBack");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmFollow.imgPopupLogoHistoricCases.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_HistoricCases_animLogoBack error: " + JSON.stringify(e));
  }
}

function frmFollow_hideShowHistoricCases(){
  //activate footer and mainpage
  frmFollow.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmFollow_flcHistoricCases_setVisibility(false);
}

function frmFollow_btnHistoricCasesWriteTicket(){
  //activate footer and mainpage
  frmFollow_hideShowHistoricCases();
  Utility_defaultTaskOutcome(Global.vars.defaultTaskOutcomeFrmNHA, CaseData.processinfo.lastTaskProcessed.taskType, frmFollow_btnHistoricCasesWriteTicket_defaultTaskOutcomecallback);
}
  
function frmFollow_btnHistoricCasesWriteTicket_defaultTaskOutcomecallback(result){
     if(result[0].steidsucceeding !== undefined){
          //Utility_setStatus(result[0].steidsucceeding);
        voltmx.print("### frmFollow_btnHistoricCasesWriteTicket_defaultTaskOutcomecallback");
        //replace CaseData with old case
        var newCase = CaseData;
        CaseData = Global.vars.historicCaseData;
        //replace certain fields with new case data
        CaseData.statuscode = result[0].steidsucceeding;
        //
        CaseData.status = newCase.status;
        CaseData.processinfo.assignment = newCase.processinfo.assignment;
        CaseData.processinfo.statuses = newCase.processinfo.statuses;
        voltmx.print("### frmFollow_btnHistoricCasesWriteTicket_defaultTaskOutcomecallback assignment: " + JSON.stringify(CaseData.processinfo.assignment));
        CaseData_setUserInformationToCaseInfo();
        CaseData.processinfo.lastTaskProcessed.taskCompletedOn = Utility_getUTCJavascriptDate(null);
        CaseData.processinfo.lastTaskProcessed.taskClaimedBy = Global.vars.gUsername;
        CaseData.processinfo.lastTaskProcessed.taskClaimedByName = CaseData.caseinfo.officerName;
        //ook voor tasks waar last task proccessed gelijk is
        for(var i in CaseData.processinfo.tasks){
            var v = CaseData.processinfo.tasks[i];
            if(v.taskType == CaseData.processinfo.lastTaskProcessed.taskType){
              v.taskCompletedOn = Utility_getUTCJavascriptDate(null);
              v.taskClaimedBy = Global.vars.gUsername;
              v.taskClaimedByName = CaseData.caseinfo.officerName;
              break;
            }
        }
        voltmx.print("### frmFollow_btnHistoricCasesWriteTicket_defaultTaskOutcomecallback tasks: " + JSON.stringify(CaseData.processinfo.tasks));
        //
        //CaseData.caseinfo.timeClaimedByUser = Utility_getUTCJavascriptDate(null);
        CaseData.caseinfo.timeComplete = Utility_getUTCJavascriptDate(null);
        CaseData.caseinfo.timeUserOnLocation = Utility_getUTCJavascriptDate(null);
       	Utility_addStatusOnLocation();
        CaseData.caseinfo.indPrinted = null;
        CaseData.caseinfo.timePrinted = null;
        CaseData.caseinfo.id = newCase.caseinfo.id;
        //Doc ID
        Global.vars.claimedDocID = newCase.caseinfo.id;
        CaseData.caseinfo.externalId = newCase.caseinfo.externalId;
        CaseData.caseinfo.scanUnit = newCase.caseinfo.scanUnit;
        CaseData.caseinfo.creationTime = newCase.caseinfo.creationTime;
        CaseData.time = newCase.time;
        CaseData.location = newCase.location;
        CaseData.multimedia = newCase.multimedia;
        CaseData.text = newCase.text;
        CaseData.indArchived = newCase.indArchived;
        CaseData.metainfo = newCase.metainfo;
        CaseData.vehicle[0].vehicleRegCheckDate = CaseData.time.utcDateTime;
        voltmx.print("### frmFollow_btnHistoricCasesWriteTicket_defaultTaskOutcomecallback time: " + JSON.stringify(CaseData.time));
        Utility_setDeviceLocationToCase(frmFollow_setDeviceLocationToHistoricCaseWriteTicket);
     } else {
       voltmx.print("### frmFollow_btnHistoricCasesWriteTicket_defaultTaskOutcomecallback geen resultaat");
       alert(voltmx.i18n.getLocalizedString("l_noOutcome"));
     }
 }


function frmFollow_setDeviceLocationToHistoricCaseWriteTicket(){
  voltmx.print("### frmFollow_setDeviceLocationToHistoricCaseWriteTicket CaseData: " + JSON.stringify(CaseData));
  frmFollow_showLoaderUploadCase(voltmx.i18n.getLocalizedString("l_send"));
  frmFollow_RegisterCase();
}

function frmFollow_findAndSetDefaultvehicleType(){
  voltmx.print("### frmFollow_findAndSetDefaultvehicleType for vehicleTypeGroup: " + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup);
  var wcs = "select * from mle_v_vehicle_type_group_m where code = '" + CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroup + "'";
  wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
  wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
//   var options = {};
//   options["whereConditionAsAString"] = wcs;
  voltmx.print("### frmFollow_findAndSetDefaultvehicleType wcs: " + JSON.stringify(wcs));
//   Global.vars.ObjServiceVehicleObject.appTypeGroupObj.get(options, frmFollow_findAndSetDefaultvehicleTypeSuccessCallback, frmFollow_findAndSetDefaultvehicleTypeErrorCallback);
  KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmFollow_findAndSetDefaultvehicleTypeSuccessCallback, frmFollow_findAndSetDefaultvehicleTypeErrorCallback);
}

function frmFollow_findAndSetDefaultvehicleTypeSuccessCallback(result){
  voltmx.print("### frmFollow_findAndSetDefaultvehicleTypeSuccessCallback result: " + JSON.stringify(result));
  if(result.length > 0 && CaseData.vehicle[Global.vars.gCaseVehiclesIndex] !== undefined){
    //CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType = result[0].vtecodedefault;
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleType = result[0].vte_code_default;
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeDesc = result[0].description;
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].vehicleTypeGroupId = result[0].id;
  }
}

function frmFollow_findAndSetDefaultvehicleTypeErrorCallback(error){
  voltmx.print("### frmFollow_getTaskOutcomeDescriptionErrorCallback error: " + JSON.stringify(error));
}

function frmFollow_getTaskOutcomeDescription(outcome){
  voltmx.print("### frmFollow_getTaskOutcomeDescription for outcome: " + outcome);
  var wcs = "select * from mle_v_outcome_type_m where identification = '" + outcome + "'";
  wcs = Utility_addTimelineToWhereClauseObjectSyncObjectSync(wcs, CaseData.time.dateComponents);
  wcs = Utility_addLanguageToWhereClauseObjectSyncObjectSync(wcs);
  KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmFollow_getTaskOutcomeDescriptionSuccessCallback, frmFollow_getTaskOutcomeDescriptionErrorCallback);
}

function frmFollow_getTaskOutcomeDescriptionSuccessCallback(result){
  voltmx.print("### frmFollow_getTaskOutcomeDescriptionSuccessCallback result: " + JSON.stringify(result));
  if(result.length > 0){
    frmFollow.lblStatus.text = result[0].description;
  }
}

function frmFollow_getTaskOutcomeDescriptionErrorCallback(error){
  voltmx.print("### frmFollow_getTaskOutcomeDescriptionErrorCallback error: " + JSON.stringify(error));
}

function frmFollow_setLicensePlatePhotoCallback(base64){
  voltmx.print("### frmFollow_setLicensePlatePhotoCallback base64: " + base64);
}

function frmFollow_setLicensePlateCutoutCallback(base64){
  voltmx.print("### frmFollow_setLicensePlatePhotoCallback base64: " + base64);
  //frmFollow.imgPlate.base64 = base64;
  frmFollow_flcLicenseplateImageIn_setVisibility(true);
}

function frmFollow_resetForm(){
  voltmx.print("### frmFollow_resetForm");
  //set new time
  frmFollow.lblTime.text = voltmx.i18n.getLocalizedString("l_time");
  frmFollow_flcParkingCheckResult_setVisibility(false);
	//reset area and location
  frmFollow.lblLocationArea.text = voltmx.i18n.getLocalizedString("l_area");
  frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
  // reset licenseplate
  frmFollow.lblLicenseplate.text = voltmx.i18n.getLocalizedString("l_licenseplate");
  frmFollow.lblLicenseplatePrefix.text = "";
  // reset kind of vehicle and brand
  frmFollow.kindofvehicle.lblText.text = voltmx.i18n.getLocalizedString("l_kindOfVehicle");
  frmFollow.kindofvehicle.lblText.skin = lblFieldNotFilled;
  frmFollow.brand.lblText.text = voltmx.i18n.getLocalizedString("l_brand");
  frmFollow.brand.lblText.skin = lblFieldNotFilled;
  frmFollow.brandtype.lblText.text = voltmx.i18n.getLocalizedString("l_brandtype");
  frmFollow.brandtype.lblText.skin = lblFieldNotFilled;
  frmFollow.color.lblText.text = voltmx.i18n.getLocalizedString("l_color");
  frmFollow.color.lblText.skin = lblFieldNotFilled;
  if(Global.vars.licenseplateCountryCode == 6030){
  	frmFollow_flcKindOfVehicleBrand_setVisibility(true);
  }else{
    frmFollow_flcKindOfVehicleBrand_setVisibility(false);
  }
  frmFollow_brandtype_setVisibility(false);
  frmFollow_color_setVisibility(false);
  voltmx.print("### frmFollow_resetForm frmFollow_flcGetNewCaseList.flcRetrieveCases_setVisibility");
  frmFollow_flcGetNewCaseList_setVisibility(true);
  frmFollow_flcGetNewCaseList_flcRetrieveCases_setVisibility(true);
}

function frmFollow_resetAfterVehicleCountryCodechange(){
  voltmx.print("### frmFollow_resetAfterVehicleCountryCodechange");
  // reset kind of vehicle and brand
  frmFollow_resetVehicleShowInfo();
  //set modus
  frmFollow_setModus();
}

function frmFollow_recheckCase(lat, lon, CaseType){
  voltmx.print("### frmFollow_recheckCase");
  voltmx.application.dismissLoadingScreen();
  frmFollow_showLoaderUploadCase(voltmx.i18n.getLocalizedString("l_recheck")+"...");
  if(Global.vars.claimedDocID == "DEMO"){
    try{
      voltmx.timer.schedule("CloseDemo", frmFollow_dismissDemo, 3, false);
    }catch(err){}
  }else{
    var licenseplate = frmFollow.editplate.txtLicensePlate.text;//.replace(/[^A-zÀ-ÿ0-9]/gi,'');
	if(licenseplate === null || licenseplate === ""){
      licenseplate = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber;
    }
    if(lat === "" || lat === 0.0){
      lat = null;
    }
    if(lon === "" || lon === 0.0){
      lon = null;
    }
    var licensePlateCountry = CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense;
    voltmx.print("### frmFollow_recheckCase licenseplate: " + licenseplate);
    voltmx.print("### frmFollow_recheckCase lat: " + lat);
    voltmx.print("### frmFollow_recheckCase lon: " + lon);
    voltmx.print("### frmFollow_recheckCase CaseType: " + CaseType);
    voltmx.print("### frmFollow_recheckCase licensePlateCountry" + licensePlateCountry);
    service_ARSRecheckCase(CaseData.caseinfo.externalId, licenseplate, licensePlateCountry ,lat, lon, CaseType, frmFollow_recheckCaseCallback, frmFollow_recheckCaseerrorcallback);
  }
}

function frmFollow_dismissDemo(){
  //frmFollow_showDemoData();//TESTDATA
  voltmx.print("### frmFollow_dismissDemo");
  if(frmResume.uploadcase.isVisible === true){
    frmResume_dismissLoaderUploadCase();
  }
  if(frmNHA.uploadcase.isVisible === true){
    frmNHA_dismissLoaderUploadCase();
  }
  if(frmFollow.flcUpLoadCase.isVisible === true){
    frmFollow_dismissLoaderUploadCase();
  }if(frmClamp.flcUpLoadCase.isVisible === true){
    frmClamp_dismissLoaderUploadCase();
  }
  try{
    voltmx.timer.cancel("CloseDemo");
  }catch(err){}
  Global_resetApp();
  frmFollow.show();
  voltmx.application.dismissLoadingScreen();
}

function frmFollow_recheckCaseCallback(result){
  if(result.opstatus === 0 && result.httpStatusCode == 200){
     voltmx.print("### frmFollow_recheckCaseCallback result: " + JSON.stringify(result));
     //alles ok ga verder
     var claimedID = Global.vars.claimedDocID;
     if(result.ok == "true"){
       if(result.code == "NoViolation"){
         voltmx.print("### frmFollow_recheckCaseCallback noViolation");
         Global.vars.noViolation = true;
       }else{
         Global.vars.noViolation = false;
       }
       try {
          Global_resetApp();
          voltmx.application.dismissLoadingScreen();
          Global.vars.claimedDocID = claimedID;
          try{
            voltmx.print("### frmFollow_postShow Global.vars.previousForm: " + Global.vars.previousForm);
            if(Global.vars.previousForm === null){
              frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
              frmFollow.lblLocation.skin = lblFieldInfo;
            }
          }catch(e){
            frmFollow_dismissLoaderUploadCase();
          }
          try{
         	Utility_getOnlineAttachments(Global.vars.claimedDocID, frmFollow_RetrieveAndAssignedCaseARS);
          }catch(e){
            frmFollow_dismissLoaderUploadCase();
          }
       }catch(e){
         frmFollow_dismissLoaderUploadCase();
       }
     }else{
       	voltmx.print("### frmFollow_recheckCaseCallback result: " + JSON.stringify(result));
       	voltmx.print("### frmFollow_recheckCaseCallback NOT OK");
       	frmFollow_SP_ErrorHandling(result);
        voltmx.application.dismissLoadingScreen();
       	frmFollow_dismissLoaderUploadCase();
        Global_resetApp();
        try{
          voltmx.print("### frmFollow_recheckCaseCallback Global.vars.previousForm: " + Global.vars.previousForm);
          if(Global.vars.previousForm === null){
            frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
            frmFollow.lblLocation.skin = lblFieldInfo;
            voltmx.print("### frmFollow_recheckCaseCallback 1 frmFollow_RetrieveUnclaimedCases");
            frmFollow_RetrieveUnclaimedCases();
          }
        }catch(e){
          frmFollow_dismissLoaderUploadCase();
        }
     }
  }else{
    voltmx.print("### frmFollow_recheckCaseCallback result error: " + JSON.stringify(result));
    voltmx.application.dismissLoadingScreen();
    frmFollow_SP_ErrorHandling(result);
    voltmx.application.dismissLoadingScreen();
    frmFollow_dismissLoaderUploadCase();
    Global_resetApp();
    try{
      voltmx.print("### frmFollow_recheckCaseCallback Global.vars.previousForm: " + Global.vars.previousForm);
      if(Global.vars.previousForm === null){
        frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
        frmFollow.lblLocation.skin = lblFieldInfo;
        voltmx.print("### frmFollow_recheckCaseCallback 2 frmFollow_RetrieveUnclaimedCases");
        frmFollow_RetrieveUnclaimedCases();
      }
    }catch(e){
      frmFollow_dismissLoaderUploadCase();
    }
  }
}

function frmFollow_SP_ErrorHandling(result, errortext){
   	var errorDescription = "";
  	if (result === undefined || result === null){
    	alert(errortext + " (Geen resultaat ontvangen)");
    } else {
      if(result.code == "ERR_SP_00"){
        errorDescription = voltmx.i18n.getLocalizedString("ERR_SP_00");
      }else if(result.code == "ERR_SP_01"){
        errorDescription = voltmx.i18n.getLocalizedString("ERR_SP_01");
      }else if(result.code == "ERR_SP_02"){
        errorDescription = voltmx.i18n.getLocalizedString("ERR_SP_02");
      }else if(result.code == "ERR_SP_03"){
        errorDescription = voltmx.i18n.getLocalizedString("ERR_SP_03");
      }else if(result.code == "ERR_SP_04"){
        errorDescription = voltmx.i18n.getLocalizedString("ERR_SP_04");
      }else if(result.code == "ERR_SP_05"){
        errorDescription = voltmx.i18n.getLocalizedString("ERR_SP_05") + " " + result.description;
      }else if(result.code == "ERR_SP_06"){
        errorDescription = voltmx.i18n.getLocalizedString("ERR_SP_06");
      }else if(result.code == "500"){
        errorDescription = voltmx.i18n.getLocalizedString("l_serviceErrorRetrieveList");
      }else if(result.code == "TimeoutException"){
        errorDescription = voltmx.i18n.getLocalizedString("l_timeOutException");
      }else if(result.code == "DocumentNotFoundException"){
        errorDescription = voltmx.i18n.getLocalizedString("l_documentNotFoundException");
      }else if(result.code == "DocumentAlreadyProcessedException"){
        //errorDescription = voltmx.i18n.getLocalizedString("l_documentAlreadyProcessedException");
      }else{
        errorDescription = result.description;
      }
      if(errortext === undefined || errortext === null || errortext === ""){
        alert(result.code + ": " + errorDescription);
      }else{
        alert(errortext + " (" + result.code + ": " + errorDescription + ")");
      }
    } 
}

function frmFollow_recheckCaseerrorcallback(error){
  voltmx.print("### frmFollow_recheckCaseerrorcallback error: " + JSON.stringify(error));
  voltmx.application.dismissLoadingScreen();
  if(error.opstatus === 8005){
    voltmx.print("### frmFollow_recheckCaseerrorcallback Empty response: " + JSON.stringify(error));
  }else{
    if(Global.vars.mfRetry == 1){
      service_ARSRecheckCase(Global.vars.pupNoConnectionCallback.recall);
    }else{
      frmFollow_dismissLoaderUploadCase();
      Utility_getOnlineAttachments(Global.vars.claimedDocID, frmFollow_RetrieveAndAssignedCaseARS);
      alert(voltmx.i18n.getLocalizedString("i_fsc0001"));
      Global.vars.mfRetry = 0;
    }
  }
}

function frmFollow_resetVehicleShowInfo(){
  	voltmx.print("### frmFollow_resetVehicleShowInfo: " + Global.vars.gCaseVehicles.countryLicenseCode);
    if(Global.vars.gCaseVehicles.countryLicenseCode == 6030){
      frmFollow_flcKindOfVehicleBrand_setVisibility(true);
    }else{
      frmFollow_flcKindOfVehicleBrand_setVisibility(false);
    }
  	frmFollow_flcResult_setVisibility(true);
}

function frmFollow_resetImages(){
  //remove images
  frmFollow.imgPlate.src = "empty.png";
  frmFollow.editplate.imgPlatePopup.src = "empty.png";
  frmFollow.segPhotos.removeAll();
  frmFollow_flcLicenseplateImageIn_setVisibility(false);
}

function frmFollow_onclick_btnRemoveText(){
  voltmx.print("### frmFollow_onclick_btnRemoveText");
  frmFollow.editplate.imgPlatePopup.src = "empty.png";
  frmFollow_editplate_flcLicenseplateImage_setVisibility(false);
  frmFollow.editplate.txtLicensePlate.text = "";
  frmFollow.editplate.txtLicensePlate.setFocus(true);
}


function frmFollow_onclick_butRemove(){
  voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_wantToRemoveCase"),
                          frmFollow_confirm_removeCase,
                          "confirmation",
                          voltmx.i18n.getLocalizedString("bt_yes"),
                          voltmx.i18n.getLocalizedString("bt_no"),
                          "Info",
                          null);
}

function frmFollow_confirm_removeCase(response){
   if(response){
      voltmx.application.showLoadingScreen(lblLoader,
	                                    "Uploaden zaak...",
	                                    "center",
	                                    false,
	                                    true,
                                    { enablemenukey : true, enablebackkey : true } );
     Utility_setDeviceLocationToCase(frmFollow_setDeviceLocationToCaseCallback);
  }
}

function frmFollow_onclick_butValid(){
  voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_giveValidParkingRight"),
                          frmFollow_confirm_validPlate,
                          "confirmation",
                          voltmx.i18n.getLocalizedString("bt_yes"),
                          voltmx.i18n.getLocalizedString("bt_no"),
                          "Info",
                          null);
}

function frmFollow_confirm_validPlate(response){
   if(response){
    alert(voltmx.i18n.getLocalizedString("l_hasValidParkingRight"));
  }
}

function frmFollow_onclick_btnNoviolation(){
  voltmx.print("### frmFollow_onclick_btnNoviolation");
  voltmx.application.showLoadingScreen(lblLoader,
	                                    "Uploaden zaak...",
	                                    "center",
	                                    false,
	                                    true,
                                    { enablemenukey : true, enablebackkey : true } );
  Utility_setDeviceLocationToCase(frmFollow_setDeviceLocationToCaseNoviolationCallback);
}

function frmFollow_setDeviceLocationToCaseNoviolationCallback(){
  voltmx.print("### frmFollow_setDeviceLocationToCaseNoviolationCallback");
  frmFollow_getDefaultNoviolationOutcomeForTaskScreen();
}

function frmFollow_getDefaultNoviolationOutcomeForTaskScreen(){
  voltmx.print("### frmFollow_getDefaultNoviolationOutcomeForTaskScreen Global.vars.defaultTaskOutcomeFrmFollowNoViolation: " + Global.vars.defaultTaskOutcomeFrmFollowNoViolation);
  var lTaskTypeClause = Utility_addTimelineToWhereClauseObjectSync("select id from mle_v_task_type_msv where identification = '" + CaseData.processinfo.activeTaskType + "'",CaseData.time.dateComponents);
  var wcs = "select * from mle_v_outcome_type_m where identification = '" + Global.vars.defaultTaskOutcomeFrmFollowNoViolation + "' and tte_id in (" + lTaskTypeClause + ")";
  wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
  wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
  KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmFollow_getDefaultNoviolationOutcomeForTaskScreenSuccessCallback, frmFollow_getDefaultNoviolationOutcomeForTaskScreenErrorCallback);
}

function frmFollow_getDefaultNoviolationOutcomeForTaskScreenErrorCallback(error){
  voltmx.print("### frmFollow_getDefaultNoviolationOutcomeForTaskScreenErrorCallback error: " + JSON.stringify(error));
  voltmx.application.dismissLoadingScreen();
  alert(voltmx.i18n.getLocalizedString("l_removalFailed") + error);
}

function frmFollow_getDefaultNoviolationOutcomeForTaskScreenSuccessCallback(result){
  voltmx.print("### frmFollow_getDefaultNoviolationOutcomeForTaskScreenSuccessCallback result: " + JSON.stringify(result));
  if(result.length > 0){
    for(var i in CaseData.processinfo.tasks){
      var v = CaseData.processinfo.tasks[i];
      if(v.taskType == CaseData.processinfo.activeTaskType && (v.taskCompletedOn === "" || v.taskCompletedOn === null)){
        v.taskOutcome = result[0].identification;
        v.taskOutcomeId = result[0].id;
        v.taskCompletedOn = Utility_getUTCJavascriptDate(null);
        v.taskOutcomeDescription = result[0].description;
        v.taskClaimedBy = Global.vars.gUsername;
        v.taskClaimedByName = CaseData.caseinfo.officerName;
        //CaseData.processinfo.activeTaskType = "";
        CaseData.caseinfo.indComplete = true;
  	  	CaseData.caseinfo.timeComplete = v.taskCompletedOn;
        if(result[0].steidsucceeding !== undefined){
          CaseData.statuscode = result[0].steidsucceeding;
          //Utility_setStatus(result[0].steidsucceeding);
        }
        CaseData.processinfo.lastTaskProcessed = v;
        break;
      }
    }
    voltmx.print("#### frmFollow_getDefaultNoviolationOutcomeForTaskScreenSuccessCallback CaseData: " + JSON.stringify(CaseData));
    frmFollow_showLoaderUploadCase(voltmx.i18n.getLocalizedString("l_send"));
    frmFollow_RegisterCase();
  }else{
    voltmx.print("#### frmFollow_getDefaultNoviolationOutcomeForTaskScreenSuccessCallback geen uitkomsten");
    voltmx.application.dismissLoadingScreen();
    alert(voltmx.i18n.getLocalizedString("l_noOutcome"));
  }
}

function frmFollow_btnDoneEdit(){
  voltmx.print("### frmFollow_btnDoneEdit");
  frmFollow_licenseplateToUpper();
  var plate = frmFollow.editplate.txtLicensePlate.text;
  var recheckCase = false;
  var plateChanged = false;
  if(frmFollow.lblCountryCode.text !== frmFollow.editplate.lblCountryCodeEditLicensePlate.text){
    voltmx.print("### frmFollow_btnDoneEdit reset after country code change");
//     Global.vars.licenseplateCountryCode = Global.vars.lastChosenLicensePlateCountry.licenseplateCountryCode;
//     Global.vars.licenseplateCountryDesc = Global.vars.lastChosenLicensePlateCountry.licenseplateCountryDesc;
//     Global.vars.licenseplateCountryModule = Global.vars.lastChosenLicensePlateCountry.licenseplateCountryModule;
    //set to case
    Global.vars.gCaseVehicles.countryLicense = Global.vars.lastChosenLicensePlateCountry.licenseplateCountryModule;
    Global.vars.gCaseVehicles.countryLicenseCode = Global.vars.lastChosenLicensePlateCountry.licenseplateCountryCode;
    Global.vars.gCaseVehicles.countryLicenseDesc = Global.vars.lastChosenLicensePlateCountry.licenseplateCountryDesc;
    frmFollow_resetAfterVehicleCountryCodechange();
//     if(Global.vars.gCaseVehicles.countryLicenseCode == 6030){
//       voltmx.print("### frmFollow_btnDoneEdit land veranderd naar Nederland");
//       recheckCase = true;
//     }
  }
  if(frmFollow.lblLicenseplate.text !== frmFollow.editplate.txtLicensePlate.text){
    recheckCase = true;
    plateChanged = true;
    if(frmFollow.lblLicenseplate.text.replace(/[^A-zÀ-ÿ0-9]/gi,'') != frmFollow.editplate.txtLicensePlate.text.replace(/[^A-zÀ-ÿ0-9]/gi,'') && Global.vars.gCaseVehicles.countryLicenseCode == 9089){
      voltmx.print("### Duits gewijzigd kenteken");
      plateChanged = true;
      recheckCase = true;
    }else if(frmFollow.lblLicenseplate.text.replace(/[^A-zÀ-ÿ0-9]/gi,'') == frmFollow.editplate.txtLicensePlate.text.replace(/[^A-zÀ-ÿ0-9]/gi,'') && Global.vars.gCaseVehicles.countryLicenseCode == 9089){
      voltmx.print("### Duits kenteken alleen streepjes toegevoegd , geen recheck case nodig");
      Global.vars.gCaseVehicles.identNumberFormatted = frmFollow.lblLicenseplate.text;
      recheckCase = false;
      plateChanged = false;
    }
    voltmx.print("### license plate has changed");
    voltmx.print("### frmFollow_btnDoneEdit Global.vars.gCaseVehicles.countryLicenseCode: " + Global.vars.gCaseVehicles.countryLicenseCode);
    plate = Utility_checkLicenseplate(plate, Global.vars.gCaseVehicles.countryLicenseCode, false);
    voltmx.print("### frmFollow_btnDoneEdit plate: " + plate);
    //Check licenseplate
    if(plateChanged === true && (CaseData.caseinfo.caseType == "CONTROL_K")){
      alert(voltmx.i18n.getLocalizedString("l_doNotChangePlate"));
    }else{
     	if(plate !== false){
          //for manual hide these fields
          //set licenseplate
          frmFollow.editplate.txtLicensePlate.text = plate;
          frmFollow.lblLicenseplate.skin = lblBlack350Licenseplate;
          frmFollow.lblLicenseplate.text = frmFollow.editplate.txtLicensePlate.text;
          //zet plaat in formatted en niet fomatted
          Global.vars.gCaseVehicles.identNumber = plate.replace(/[^A-zÀ-ÿ0-9]/gi,'');
          Global.vars.gCaseVehicles.identNumberFormatted = plate;
          if(frmFollow.lblLocation.text == voltmx.i18n.getLocalizedString("l_location")){
            frmFollow_btnNext_setVisibility(false);
          }else{
            frmFollow_btnNext_setVisibility(true);
          }
          voltmx.application.dismissLoadingScreen();
        }else{
          alert(voltmx.i18n.getLocalizedString("l_plateNotValid"));
          voltmx.application.dismissLoadingScreen();
        }
    }
  }
  voltmx.print("### frmFollow_btnDoneEdit licenseplatePrefixEnabled: " + Global.vars.licensePlatePrefixEnabled);
  if (Global.vars.licensePlatePrefixEnabled === true){
    voltmx.print("### frmFollow_btnDoneEdit lblLicenseplatePrefix: " + frmFollow.lblLicenseplatePrefix.text);
  	voltmx.print("### frmFollow_btnDoneEdit editplate.txtPrefix: " + frmFollow.editplate.txtPrefix.text);
  	if(frmFollow.lblLicenseplatePrefix.text !== frmFollow.editplate.txtPrefix.text){
      voltmx.print("### frmFollow_btnDoneEdit editplate 1");
      if (Global.vars.recheckTotalPlateInfo === true){
        voltmx.print("### frmFollow_btnDoneEdit editplate 2");
      	recheckCase = true;
      }  
      voltmx.print("### frmFollow_btnDoneEdit editplate 3");
      Global.vars.gCaseVehicles.prefix = frmFollow.lblLicenseplatePrefix.text = frmFollow.editplate.txtPrefix.text;
    }
  }  
  voltmx.print("### frmFollow_btnDoneEdit licenseplatePrefixEnabled: " + Global.vars.licensePlatePrefixEnabled);
  if (frmFollow.lblCountryCode.text !== frmFollow.editplate.lblCountryCodeEditLicensePlate.text){
    if (Global.vars.recheckTotalPlateInfo === true){
      recheckCase = true;
    }  
   	frmFollow.lblCountryCode.text = frmFollow.editplate.lblCountryCodeEditLicensePlate.text;
  }
    
  
  if(recheckCase === true && plate !== false && CaseData.caseinfo.caseType !== "CONTROL_K"){
    //licenseplate has changed
    frmFollow_resetVehicleShowInfo();
    CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense = frmFollow.editplate.lblCountryCodeEditLicensePlate.text;
    if (Global.vars.useRecheckServiceARS === false){
      // zaak wordt afgesloten en opnieuw aangeboden met gewijzigd kenteken
      Utility_setDeviceLocationToCase(frmFollow_setDeviceLocationToCaseOtherPlate);    
    } else {
	  frmFollow_recheckCase(null, null, CaseData.caseinfo.caseType);
    }
  }
  frmFollow_hideShowEditPlate();
}

function frmFollow_onclick_btnNext(){
  voltmx.print("### frmFollow_onclick_btnNext frmFollow.lblLocation.text: " + frmFollow.lblLocation.text);
  voltmx.print("### frmFollow_onclick_btnNext frmFollow.lblLicenseplate.text: " + frmFollow.lblLicenseplate.text);
  voltmx.print("### frmFollow_onclick_btnNext Global.vars.gCaseVehicles.countryLicenseCode: " + Global.vars.gCaseVehicles.countryLicenseCode);
  //frmFollow_hideEditPlate();
  //check if data is filled
  var datafilled = true;
  var germanCarFormatted = true;
  var plateCountryUnknown = false;
  if(frmFollow.lblLocation.text == voltmx.i18n.getLocalizedString("l_location")){
    datafilled = false;
  }
  if(frmFollow.lblLicenseplate.text == voltmx.i18n.getLocalizedString("l_licenseplate")){
	datafilled = false;
  }
  if(frmFollow.lblLicenseplate.text.indexOf('-') == -1 && (Global.vars.gCaseVehicles.countryLicenseCode == 9089 && Global.vars.germanSeparatorMandatory === true)){
    germanCarFormatted = false;
    datafilled = false;
  }
  if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense.toLowerCase() == Global.vars.unknownVehicleCountryLicense.module.toLowerCase()){
    plateCountryUnknown = true;
    datafilled = false;
  }
  //Global.vars.gCaseVehicles
  voltmx.print("### frmFollow_onclick_btnNext Global.vars.gCaseVehicles: " + JSON.stringify(Global.vars.gCaseVehicles));
  voltmx.print("### frmFollow_onclick_btnNext CaseData.vehicle[Global.vars.gCaseVehiclesIndex]: " + JSON.stringify(CaseData.vehicle[Global.vars.gCaseVehiclesIndex]));
  CaseData.vehicle[Global.vars.gCaseVehiclesIndex] = Global.vars.gCaseVehicles;
  //Go to next form
  voltmx.print("### frmFollow_onclick_btnNext datafilled: " + datafilled);
  voltmx.print("### frmFollow_onclick_btnNext germanCarFormatted: " + germanCarFormatted);
  if(datafilled){
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
//   	if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_trackDown")){
//     	frmHandleCharacteristic.show();
//     }else{
      	frmHandle.show();
//    }
  }else{
    if(germanCarFormatted === false){
      	alert(voltmx.i18n.getLocalizedString("l_germanPlateDash"));
    }else if(plateCountryUnknown === true){
          voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_countryPlateUnknownChange"),
                                  frmFollow_confirm_countryUnknown,
                                  "confirmation",
                                  voltmx.i18n.getLocalizedString("bt_yes"),
                                  voltmx.i18n.getLocalizedString("bt_no"),
                                  "Info",
                                  null);
    }else{
      	alert(voltmx.i18n.getLocalizedString("l_not_all_fields_filled"));
    }
  }
}

function frmFollow_confirm_countryUnknown(response){
  if(response){
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
  	if(Global.vars.appMode == voltmx.i18n.getLocalizedString("l_trackDown")){
    	frmHandleCharacteristic.show();
    }else{
      	frmHandle.show();
    }
  }
}

function frmFollow_onclick_btnLocation(){
  frmHandle.show();
}

function frmFollow_togglePlateImage(){
	//map 73% 100%
  	//move up flcSensorInfo
  	if(frmFollow.flcPlateImage.bottom == -500+'dp'){
      var mainAnimation = voltmx.ui.createAnimation({
      "100": {
                  "bottom" : 0+'%',
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
      var mainSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 0.4
                  };
  		frmFollow.flcPlateImage.animate(mainAnimation,mainSetting);
      	frmFollow.flcMainPage.setEnabled(false);
      	frmFollow_loadImages();
    }else{
      	 var mainAnimation = voltmx.ui.createAnimation({
      "100": {
                  "bottom" : -500+'dp',
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
      var mainSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 0.4
                  };
  	  frmFollow.flcPlateImage.animate(mainAnimation,mainSetting);
      frmFollow.flcMainPage.setEnabled(true);
    }
}

function frmFollow_loadImages(){
  	voltmx.print("### frmFollow_loadImages");
  	voltmx.application.showLoadingScreen(lblLoader,
	                                    voltmx.i18n.getLocalizedString("l_loadPhoto"),
	                                    "center",
	                                    false,
	                                    true,
                                    { enablemenukey : true, enablebackkey : true } );
  	
    voltmx.print("### frmFollow_loadImages Global.vars.photosLoaded " + Global.vars.photosLoaded);
  	voltmx.print("### frmFollow_loadImages Global.vars.attachmentsLoaded " + Global.vars.attachmentsLoaded);
  	if(Global.vars.attachmentsLoaded === false){
      Utility_getOnlineAttachments(Global.vars.claimedDocID, frmFollow_loadImages);
  	} else if(Global.vars.photosLoaded === false){
      var image = {imgPhoto: {base64:""}};
      Global.vars.getPhotos = [];
      for(var i in Global.vars.onlineAttachments){
        var v = Global.vars.onlineAttachments[i];
        if((v.contentType == 'image/png' || v.contentType == 'image/jpeg') && v.documentType != "SmallMap"){
          var filename = v.fileName;
          var attachmentId = v.attachmentId;
          Global.vars.getPhotos.push(attachmentId);
        }
      }
      Global.vars.segPhotosData = [];
      frmFollow_getImages();
    }else{
      voltmx.application.dismissLoadingScreen();
    }
}

function frmFollow_getImages(){
  voltmx.print("### frmFollow_getImages");
  var getPhoto = null;
  var image = {imgPhoto: {base64:""}};
  function service_getAttachmentCallback(result){
    if(result.opstatus === 0 && result.httpStatusCode == 200){
      if(result.response !== undefined && result.response.length > 0){
        var imageBase64 = result.response[0].base64;
        // handle result
        voltmx.print("### frmFollow_getImages service_getAttachmentCallback result: " + JSON.stringify(result));
        //voltmx.print("### frmFollow_getImages getAttachment imageBase64: " + JSON.stringify(imageBase64));
        if (Global.vars.photoMetadataVisible === true){
			image = {imgPhoto: {base64: imageBase64}, lblPhotoHeader: getPhoto.substr(0, getPhoto.indexOf('.'))};
        } else {
    		image = {imgPhoto: {base64: imageBase64}};
        }
        var addimage = true;
        for(var j in Global.vars.segPhotosData){
          var w = Global.vars.segPhotosData[j];
          if(w.imgPhoto === image.imgPhoto){
            addimage = false;
          }
        }
        if(addimage === true){
          voltmx.print("### frmFollow_getImages push foto");
          Global.vars.segPhotosData.push(image);
          //frmFollow.segPhotos.addDataAt(image, 0, 0);
          frmFollow.segPhotos.setData(Global.vars.segPhotosData);
          frmFollow.segPhotos.selectedRowIndex = [0,0];
        }
        frmFollow_getImages();
      }else{
        frmFollow_getImages();
        voltmx.application.dismissLoadingScreen();
      }
    }else{
      alert(voltmx.i18n.getLocalizedString("e_ser0001"));
      voltmx.application.dismissLoadingScreen();
    }
  }
  function service_getAttachmentErrorCallback(err) {
    voltmx.print("### frmFollow_getImages getAttachment err: " + JSON.stringify(err));
    voltmx.application.dismissLoadingScreen();
  }
  voltmx.print("### frmFollow_getImages Global.vars.getPhotos.length: " + Global.vars.getPhotos.length);
  if(Global.vars.getPhotos.length > 0){
    getPhoto = Global.vars.getPhotos.pop();
    voltmx.print("### frmFollow_getImages Photo: " + getPhoto);
    if (getPhoto !== undefined && getPhoto != null){
      service_getAttachment(Global.vars.claimedDocID, getPhoto, service_getAttachmentCallback, service_getAttachmentErrorCallback);
    } else {
      frmFollow_getImages();
    }
  }else{
    Global.vars.photosLoaded = true;
    voltmx.application.dismissLoadingScreen();
    voltmx.print("### frmFollow_getImages getAttachment setData: " + JSON.stringify(frmFollow.segPhotos.data));
  }
}

function frmFollow_hidePhotoData(){
    var mainAnimation = voltmx.ui.createAnimation({
      "100": {
                  "bottom" : -500+'dp',
                  "stepConfig": {"timingFunction" : voltmx.anim.EASE_IN_OUT}
             }
      });
    var mainSetting = {"delay" : 0,
                   "fillMode" : voltmx.anim.FILL_MODE_FORWARDS,
                   "duration": 0.4
                  };
  	frmFollow.flcPlateImage.animate(mainAnimation,mainSetting);
  	frmFollow.flcMainPage.setEnabled(true);
}

function frmFollow_onclick_btnCountryCode(){
  voltmx.print("### frmFollow_onclick_btnCountryCode");
  Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
  Global.vars.btnCountryVehicleCode = false;
  frmVehicleCountries.show();
}

function frmFollow_onclick_btnCountryVehicleCode(){
  voltmx.print("### frmFollow_onclick_btnCountryVehicleCode");
  Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
  Global.vars.btnCountryVehicleCode = true;
  frmVehicleCountries.show();
}

function frmFollow_onclick_btnLocationArea(){
  Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
  frmArea.show();
}

function frmFollow_setDeviceLocationToCaseCallback(){
  voltmx.print("### frmFollow_setDeviceLocationToCaseCallback");
  frmFollow_getDefaultOutcomeForTaskScreen();
}

function frmFollow_setDeviceLocationToCaseOtherPlate(){
  voltmx.print("### frmFollow_setDeviceLocationToCaseCallback");
  frmFollow_getDefaultOutcomeForTaskScreen(Global.vars.defaultTaskOutcomeOtherPlate);
}

function frmFollow_setDeviceLocationToCaseOtherLocation(){
  voltmx.print("### frmFollow_setDeviceLocationToCaseCallback");
  frmFollow_getDefaultOutcomeForTaskScreen(Global.vars.defaultTaskOutcomeOtherLocation);
}

function frmFollow_getDefaultOutcomeForTaskScreen(defaultOutcomeOther){
  var _defaultOutcomeOther = defaultOutcomeOther === undefined ? Global.vars.defaultTaskOutcomeFrmFollow : defaultOutcomeOther;
  voltmx.print("### frmFollow_getDefaultOutcomeForTaskScreen Global.vars.defaultTaskOutcomeFrmFollow: " + Global.vars.defaultTaskOutcomeFrmFollow);
  var lTaskTypeClause = Utility_addTimelineToWhereClauseObjectSync("select id from mle_v_task_type_msv where identification = '" + CaseData.processinfo.activeTaskType + "'",CaseData.time.dateComponents);
  var wcs = "select * from mle_v_outcome_type_m where identification = '" + _defaultOutcomeOther + "' and tte_id in (" + lTaskTypeClause + ")";
  wcs = Utility_addTimelineToWhereClauseObjectSync(wcs, CaseData.time.dateComponents);
  wcs = Utility_addLanguageToWhereClauseObjectSync(wcs);
  KNYMobileFabric.OfflineObjects.executeSelectQuery(wcs, frmFollow_getDefaultOutcomeForTaskScreenSuccessCallback, frmFollow_getDefaultOutcomeForTaskScreenErrorCallback);
}

function frmFollow_getDefaultOutcomeForTaskScreenErrorCallback(error){
  voltmx.print("### frmFollow_getDefaultOutcomeForTaskScreenErrorCallback error: " + JSON.stringify(error));
  voltmx.application.dismissLoadingScreen();
  alert(voltmx.i18n.getLocalizedString("l_removalFailed") + error);
}

function frmFollow_getDefaultOutcomeForTaskScreenSuccessCallback(result){
  voltmx.print("### frmFollow_getDefaultOutcomeForTaskScreenSuccessCallback result: " + JSON.stringify(result));
  if(result.length > 0){
    for(var i in CaseData.processinfo.tasks){
    var v = CaseData.processinfo.tasks[i];
      if(v.taskType == CaseData.processinfo.activeTaskType && (v.taskCompletedOn === "" || v.taskCompletedOn === null)){
        v.taskOutcome = result[0].identification;
        v.taskOutcomeId = result[0].id;
        v.taskCompletedOn = Utility_getUTCJavascriptDate(null);
        v.taskOutcomeDescription = result[0].description;
        v.taskClaimedBy = Global.vars.gUsername;
        v.taskClaimedByName = CaseData.caseinfo.officerName;
        //CaseData.processinfo.activeTaskType = "";
        CaseData.caseinfo.indComplete = true;
  	  	CaseData.caseinfo.timeComplete = v.taskCompletedOn;
        if(result[0].ste_id_succeeding !== undefined){
          CaseData.statuscode = result[0].ste_id_succeeding;
          //Utility_setStatus(result[0].steidsucceeding);
        }
        CaseData.processinfo.lastTaskProcessed = v;
        CaseData.caseinfo.caseTypeCategory = Global.vars.claimedDoc.case.caseinfo.caseTypeCategory;
        CaseData.caseinfo.caseTypeCategoryId = Global.vars.claimedDoc.case.caseinfo.caseTypeCategoryId;
        if (Global.vars.claimedDoc.case.caseinfo.caseTypeCategoryDescription !== undefined){
          CaseData.caseinfo.caseTypeCategoryDescription = Global.vars.claimedDoc.case.caseinfo.caseTypeCategoryDescription;
      	}
      	CaseData.caseinfo.caseType = Global.vars.claimedDoc.case.caseinfo.caseType;
        CaseData.caseinfo.caseTypeId = Global.vars.claimedDoc.case.caseinfo.caseTypeId;
        if (Global.vars.claimedDoc.case.caseinfo.caseTypeDescription !== undefined){
          CaseData.caseinfo.caseTypeDescription = Global.vars.claimedDoc.case.caseinfo.caseTypeDescription;
      	}
      	CaseData.location.zone = Global.vars.claimedDoc.case.location.zone;
      	CaseData.location.zoneCode = Global.vars.claimedDoc.case.location.zoneCode;
        break;
      }
    }
    voltmx.print("#### frmFollow_getDefaultOutcomeForTaskScreenSuccessCallback CaseData: " + JSON.stringify(CaseData));
    frmFollow_showLoaderUploadCase(voltmx.i18n.getLocalizedString("l_send"));
    frmFollow_RegisterCase();
  }else{
    voltmx.print("#### frmFollow_getDefaultOutcomeForTaskScreenSuccessCallback geen uitkomsten");
    voltmx.application.dismissLoadingScreen();
    alert(voltmx.i18n.getLocalizedString("l_noOutcome"));
  }
}

function frmFollow_RegisterCase(){
  voltmx.print("#### frmFollow_RegisterCase");
  voltmx.print("#### frmFollow_RegisterCase Utility_saveUploadCaseData");
  Utility_saveUploadCaseData(CaseData, frmFollow_RegisterCaseFinish);
}

function frmFollow_RegisterCaseFinish(){
  voltmx.print("### frmFollow_RegisterCaseFinish");
  frmFollow_Finish();
}

function frmFollow_Finish(){
  	//
  	voltmx.print("#### frmFollow_Finish");
    try{
  	voltmx.timer.schedule("FinishUpload", frmFollow_FinishUpload, 1.5, false);
  }catch(err){}
}

function frmFollow_FinishUpload(){
    try{
      voltmx.timer.cancel("FinishUpload");
    }catch(err){}
  	//reset CaseData and go to beginning
    //init caseData
    frmFollow.lblSearchQuery.text = voltmx.i18n.getLocalizedString("l_shift");
    Global_resetApp();
    voltmx.application.dismissLoadingScreen();
    try{
      voltmx.print("### frmFollow_postShow Global.vars.previousForm: " + Global.vars.previousForm);
      if(Global.vars.previousForm === null){
        frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
        frmFollow.lblLocation.skin = lblFieldInfo;
        frmFollow_dismissLoaderUploadCase();
        voltmx.print("### frmFollow_FinishUpload frmFollow_RetrieveUnclaimedCases");
        frmOverviewTask.show();
      }
    }catch(e){}
}

function frmFollow_RetrieveUnclaimedCases(){
  try {
    voltmx.timer.cancel("setRetrieveCasesTimer");
  }catch(err){}
  voltmx.print("### frmFollow_RetrieveUnclaimedCases disabled");
}

function frmFollow_getCaseTypeDescription_lblsearchQuery(result){
  voltmx.print("### frmFollow_getCaseTypeDescription_lblsearchQuery: " + JSON.stringify(result));
  var description = "";
  if(result.length > 0){
    description = result[0].description;
    voltmx.print("### frmFollow_getCaseTypeDescription_lblsearchQuery description: " + description);
    frmFollow.lblSearchQuery.text = frmFollow.lblSearchQuery.text + " - " + description;
  }
}

function frmFollow_getCaseTypedescriptions(selectorCaseTypes){
  for(var n in selectorCaseTypes){
    var y = selectorCaseTypes[n];
    var wcs = "select * from mle_v_case_type_m where identification = '" + y + "'";
    var caseTypesWcs = Utility_addTimelineToWhereClauseObjectSync(wcs);
    caseTypesWcs = Utility_addLanguageToWhereClauseObjectSync(caseTypesWcs);
    KNYMobileFabric.OfflineObjects.executeSelectQuery(caseTypesWcs,frmFollow_setCaseTypesToPopup_getCaseTypeDescriptions_successCallback,frmFollow_setCaseTypesToPopup_getCaseTypeDescriptions_errorCallback);
  }

}

function frmFollow_setCaseTypesToPopup_getCaseTypeDescriptions_successCallback(result){
   voltmx.print("### setCaseTypesToPopup_getCaseTypeDescription_successCallback: " + JSON.stringify(result));
   if(result.length > 0){
     Global.vars.setCaseTypesToPopup.push({name:result[0].description});
   }
   voltmx.print("### setCaseTypesToPopup_getCaseTypeDescription_successCallback: " + JSON.stringify(Global.vars.setCaseTypesToPopup));
   if(Global.vars.setCaseTypesToPopup.length > 0){
     frmFollow.segCaseTypes.setData(Global.vars.setCaseTypesToPopup);
   }else{
     var noCaseTypes = [{name:voltmx.i18n.getLocalizedString("l_noCaseTypesFound")}];
     frmFollow.segCaseTypes.setData(noCaseTypes);
   }

}

function frmFollow_setCaseTypesToPopup_getCaseTypeDescriptions_errorCallback(error){
  voltmx.print("### setCaseTypesToPopup_getCaseTypeDescription_errorCallback: " + JSON.stringify(error));
}

function frmFollow_removeSpinner(){
  voltmx.print("### frmFollow_removeSpinner");
  // NB only works if there's a list filled already
  if(frmFollow.segCases.data !== undefined && frmFollow.segCases.data != null){
    frmFollow_flcSpinner_setVisibility(false);
  }
}

function frmFollow_checkCouchIndex(){
  if(Global.vars.couchIndexesFound === true){
    frmFollow_btnRetrieveCases();
  }else{
    frmFollow_btnRetrieveCases_setVisibility(true);
    voltmx.application.dismissLoadingScreen();
  }
}

function frmFollow_onclick_segCases(){
  	voltmx.print("### frmFollow_onclick_segCases item: " + JSON.stringify(frmFollow.segCases.selectedItems));
  	if(Global.vars.demoModus === true){
      frmFollow_showDemoData();
    }else{
      if(frmFollow.segCases.selectedItems[0].docID !== undefined){
        try {
          voltmx.timer.cancel("setRetrieveCasesTimer");
        }catch(err1){}
        voltmx.print("### frmFollow_onclick_segCases docID: " + JSON.stringify(frmFollow.segCases.selectedItems[0].docID));
        voltmx.print("### frmFollow_onclick_segCases case: " + JSON.stringify(frmFollow.segCases.selectedItems[0].case));
        //Global.vars.claimedDocID = frmFollow.segCases.selectedItems[0].docID;
        Global.vars.alreadyClaimed = false;
        if(frmFollow.segCases.selectedItems[0].case.status == "OpvolgenPending" && frmFollow.segCases.selectedItems[0].case.processinfo.assignment.assignee === Global.vars.gUsername){
          Global.vars.alreadyClaimed = true;
        }
        //var arsCaseID = frmFollow.segCases.selectedItems[0].case.caseinfo.externalId;
        //service_RetrieveAndAssignCaseARS(arsCaseID, frmFollow_RetrieveAndAssignedCaseARSCallback, frmFollow_RetrieveAndAssignedCaseARSErrorCallback);
        frmFollow_retrieveAndAssignCase(frmFollow.segCases.selectedItems[0].docID, frmFollow.segCases.selectedItems[0].case.caseinfo.externalId, frmFollow.segCases.selectedItems[0].case.processinfo.activeTaskType);
      }else{
        voltmx.print("### frmFollow_onclick_segCases case no docID");
      }
    }
}

function frmFollow_RetrieveAndAssignedCaseARSCallback(result){
  if(result.opstatus === 0 && result.httpStatusCode == 200){
     voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSCallback result: " + JSON.stringify(result));
     //alles ok ga verder
     if(result.ok === "true"){
       voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSCallback OK");
       if(result.code == "NoViolation"){
         voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSCallback noViolation");
         Global.vars.noViolation = true;
       }else{
         Global.vars.noViolation = false;
       }
       try {
          Utility_getOnlineAttachments(Global.vars.claimedDocID, frmFollow_RetrieveAndAssignedCaseARS);
       }catch(e){}
     }else{
       	voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSCallback NOT OK");
       	frmFollow_SP_ErrorHandling(result);
     }
  }else{
    voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSCallback not ok result: " + JSON.stringify(result));
    if(result.ok === "false" && (result.code !== "" || result.code != null) && Global.vars.alreadyClaimed === true){
      voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSCallback NOT OK but already claimed so retrieve the case");
      try{
      	Utility_getOnlineAttachments(Global.vars.claimedDocID, frmFollow_RetrieveAndAssignedCaseARS);
      }catch(err){}
    }else{
      if(Global.vars.alreadyClaimed === true){
        try{
      		Utility_getOnlineAttachments(Global.vars.claimedDocID, frmFollow_RetrieveAndAssignedCaseARS);
      	}catch(err){}
      }else{
       	voltmx.application.dismissLoadingScreen();
        frmFollow_SP_ErrorHandling(result, voltmx.i18n.getLocalizedString("l_claimFailed"));
        Global_resetApp();
        try{
          voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSCallback Global.vars.previousForm: " + Global.vars.previousForm);
          if(Global.vars.previousForm === null){
            frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
            frmFollow.lblLocation.skin = lblFieldInfo;
            voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSCallback frmFollow_RetrieveUnclaimedCases");
            frmFollow_RetrieveUnclaimedCases();
          }
      	}catch(e){}
      }
    }
  }
}

function frmFollow_RetrieveAndAssignedCaseARSErrorCallback(error){
  	voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSErrorCallback: " + JSON.stringify(error));
  	voltmx.application.dismissLoadingScreen();
	if(error.opstatus === 8005){
      	voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSErrorCallback Empty response: " + JSON.stringify(error));
    }
  	voltmx.application.dismissLoadingScreen();
    alert(voltmx.i18n.getLocalizedString("i_fsc0001"));
    Global_resetApp();
    try{
      voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSErrorCallback Global.vars.previousForm: " + Global.vars.previousForm);
      if(Global.vars.previousForm === null){
        frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
        frmFollow.lblLocation.skin = lblFieldInfo;
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSErrorCallback frmFollow_RetrieveUnclaimedCases");
        frmFollow_RetrieveUnclaimedCases();
      }
    }catch(e){}
  	voltmx.print("### frmFollow_RetrieveAndAssignedCaseARSErrorCallback service error");
}

function frmFollow_RetrieveAndAssignedCaseARS(){
  voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS Global.vars.claimedDocID: " + Global.vars.claimedDocID);
  var plateSet = false;
  function service_getAttachmentCallback(result) {
    if(result.opstatus === 0 && result.httpStatusCode == 200){
      if(result.response !== undefined && result.response.length > 0){
        var image = result.response[0].base64;
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS getAttachment image: " +image);
        plateSet = true;
        frmFollow.imgPlate.base64 = image;
        frmFollow.editplate.imgPlatePopup.base64 = image;
      }else{
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS set noPhoto");
      	frmFollow.imgPlate.src = "nophoto.png";
      	frmFollow.editplate.imgPlatePopup.src = "nophoto.png";
      	voltmx.application.dismissLoadingScreen();
      }
    }else{
      voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS set noPhoto");
      frmFollow.imgPlate.src = "nophoto.png";
      frmFollow.editplate.imgPlatePopup.src = "nophoto.png";
      voltmx.application.dismissLoadingScreen();
    }
  }
  function service_getAttachmentErrorCallback (err) {
    voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS getAttachment err: " + JSON.stringify(err));
    voltmx.application.dismissLoadingScreen();
    if(plateSet === false){
      voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS set noPhoto");
      frmFollow.imgPlate.src = "nophoto.png";
      frmFollow.editplate.imgPlatePopup.src = "nophoto.png";
    }
  }
  function couchDBCheckClaimCaseCallback(result){
    if((result != null && voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) || (result != null && result.rawResponse != null)){//ivm gebruik passthrough
      var doc = result.rawResponse; //ivm gebruik passthrough
      if (doc.case != null && doc.case.processinfo != null){
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS couchDBCheckClaimCaseCallback result: " + JSON.stringify(result));
        //var doc = result.doc[0];
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS couchDBCheckClaimCaseCallback Couch Doc: " + JSON.stringify(doc));
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS couchDBCheckClaimCaseCallback attachments: " + JSON.stringify(doc._attachments));
        //frmFollow.mapCases.clear();
        Global.vars.photosLoaded = false;
        Global.vars.claimedDoc = JSON.parse(JSON.stringify(doc));
        Global.vars.originalCaseInfo = JSON.parse(JSON.stringify(doc.case.caseinfo));
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS Global.vars.claimedDoc: " + JSON.stringify(Global.vars.claimedDoc));
        CaseData = doc.case;
        CaseData.caseinfo["appVersion"] = appConfig.appVersion.toString();
		var filenamePlate = "";
        var attachmentIdPlate = "";
        Global.vars.base64MapImage = "";
        for(var i in Global.vars.onlineAttachments){
          var v = Global.vars.onlineAttachments[i];
          if((v.documentType === "Plate" || v.documentType === "AnprPhoto") && plateSet === false && v.documentType !== "SmallMap"){
            filenamePlate = v.fileName;
            attachmentIdPlate = v.attachmentId;
            break;
          }
        }
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS filenamePlate: " + filenamePlate);
        if(filenamePlate !== ""){
          service_getAttachment(Global.vars.claimedDocID, attachmentIdPlate, service_getAttachmentCallback, service_getAttachmentErrorCallback);
        }else{
          voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS set noPhoto");
          frmFollow.imgPlate.src = "nophoto.png";
          frmFollow.editplate.imgPlatePopup.src = "nophoto.png";
        }
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS CaseData.status: " + CaseData.status);
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS CaseData.caseinfo.caseType: " + CaseData.caseinfo.caseType);
        if (CaseData.processinfo.assignment.assignee == Global.vars.gUsername){
          if(voltmx.string.startsWith(CaseData.caseinfo.caseType,"MLD_")){
            CaseData_setUserInformationToCaseInfo();
            Global.vars.backToPreviousForm = "frmFollow";
            frmResume.show();
          }else{
            frmFollow_validateCase();
          }
        } else {
          //voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS CaseData status did not change to claimed: " + CaseData.status);
          voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS CaseData username not assigned: " + CaseData.processinfo.assignment.assignee);
          alert(voltmx.i18n.getLocalizedString("l_claimFailed"));
          frmFollow_dismissLoaderUploadCase();
          voltmx.application.dismissLoadingScreen();
        }
      }else{
        alert(voltmx.i18n.getLocalizedString("l_claimFailed"));
        frmFollow_dismissLoaderUploadCase();
        voltmx.application.dismissLoadingScreen();
      }
    }else{
      alert(voltmx.i18n.getLocalizedString("e_ser0001"));
      voltmx.application.dismissLoadingScreen();
    }
  }
  function couchDBCheckClaimCaseErrorCallback(error){
    voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS couchDBCheckClaimCaseErrorCallback Doc Error: " + JSON.stringify(error));
    voltmx.application.dismissLoadingScreen();
    alert(voltmx.i18n.getLocalizedString("l_caseNotRetrieved"));
    Global_resetApp();
    try{
      voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS couchDBCheckClaimCaseErrorCallback Global.vars.previousForm: " + Global.vars.previousForm);
      if(Global.vars.previousForm === null){
        frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
        frmFollow.lblLocation.skin = lblFieldInfo;
        voltmx.print("### frmFollow_RetrieveAndAssignedCaseARS couchDBCheckClaimCaseErrorCallback frmFollow_RetrieveUnclaimedCases");
        frmFollow_RetrieveUnclaimedCases();
      }
    }catch(e){}
  }
  service_couchDBGetCase(Global.vars.claimedDocID, false, true, couchDBCheckClaimCaseCallback, couchDBCheckClaimCaseErrorCallback);
}

function frmFollow_validateCase(){
  	voltmx.print("### frmFollow_validateCase");
  	if(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense === Global.vars.unknownVehicleCountryLicense.module){
      CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode = 0;
      CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseDesc = voltmx.i18n.getLocalizedString("l_unknown");
    }
  	var validateCase = Validate_caseTrackdown(CaseData);
    if(validateCase.validated === true){
      	voltmx.print("### CaseData validated");
        frmFollow_flcGetNewCaseList_flcRetrieveCases_setVisibility(false);
        frmFollow_flcGetNewCaseList_flcCaseSegment_setVisibility(false);
      	frmFollow.btnRetrieveCases.setEnabled(false);
      	//frmFollow.segCases.setVisibility(false);
        frmFollow_flcGetNewCaseList_setVisibility(false);
      	frmFollow.flcLayout.setEnabled(true);
      	frmFollow_flcFooterCases_setVisibility(false);
      	frmFollow_flcMenuButton_setVisibility(false);
      	frmFollow_btnCancelClaim_setVisibility(true);
      	CaseData.vehicle[Global.vars.gCaseVehiclesIndex].validated = true;
        frmFollow_populateFromExistingCase();
    }else{
      	voltmx.print("### CaseData not validated: " + validateCase.errorMsg);
      	voltmx.application.dismissLoadingScreen();
      	frmFollow_dismissLoaderUploadCase();
      	frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
        frmFollow.lblLocation.skin = lblFieldInfo;
        voltmx.print("### CaseData not validated frmFollow_RetrieveUnclaimedCases");
        frmFollow_RetrieveUnclaimedCases();
      	alert(validateCase.errorMsg);
    }
}

function frmFollow_onclickMap(){
  	voltmx.print("### frmFollow_onclickMap");
  	//do not show list
  	frmFollow_flcGetNewCaseList_flcCaseSegment_setVisibility(false);
  	frmFollow.segCases.removeAll();
  	//show map
  	frmFollow_flcGetNewCaseList_flcCaseMap_setVisibility(true);
  	frmFollow_mapCases_setVisibility(true);
  	frmFollow.btnRetrieveCases.setEnabled(false);
  	//set button list to non inverse
  	frmFollow.imgList.src = "but_but_list_unselect.png";
  	//set button map to inverse
  	frmFollow.imgMap.src = "but_but_map_select.png";
  	//get cases
  	frmFollow_RetrieveUnclaimedCases();
  	//get my location
	//GPS_getCurrentLocation(frmFollow_onclickMap_gpsCallback, frmFollow_GPS_errorCallback);
}

function frmFollow_getLocationForMap(){//deze in get cases functie zetten
  //get my location
  try{
      voltmx.timer.cancel("showMapData");
    }catch(err){}
  voltmx.print("### frmFollow_getLocationForMap");
  //GPS_getCurrentLocation(frmFollow_onclickMap_gpsCallback, frmFollow_GPS_errorCallback);
  voltmx.runOnMainThread(GPS_getCurrentLocation, [frmFollow_onclickMap_gpsCallback, frmFollow_GPS_errorCallback]);
}

function frmFollow_onclickMap_gpsCallback(){
	// Get streets if gpsfix
	var latitude = Global.vars.gLatitude;
    var longitude = Global.vars.gLongitude;
    var myLocation = voltmx.i18n.getLocalizedString("l_myLocation");  
  	if(Global.vars.gLatitude === 0.0 || Global.vars.gLongitude === 0.0){
      latitude = Global.vars.connectedCloudGPS.latitude;
      longitude = Global.vars.connectedCloudGPS.longitude;
      myLocation = voltmx.i18n.getLocalizedString("l_connectedCloud");  
    }
    //frmFollow.mapCases.clear();
    var callOutRichText = "<label style=\"color:#000000\">" + myLocation + "</label>";
    var caselocation = {
      lat : latitude + "", 
      lon : longitude + "",
      name: myLocation, 
      desc: "", 
      image : "empty.png",
      showCallout : false,
      calloutData : {data1:myLocation, data2:callOutRichText, img:"location.png", data3:"", data4:""}
    };  
  	  if(Global.vars.casesMapTable.length > 0 && (Global.vars.casesMapTable[0].name == voltmx.i18n.getLocalizedString("l_myLocation") || Global.vars.casesMapTable[0].name == voltmx.i18n.getLocalizedString("l_connectedCloud"))){
        Global.vars.casesMapTable[0].lat = latitude + "";
        Global.vars.casesMapTable[0].lon = longitude + "";
      }else{
        Global.vars.casesMapTable.splice(0, 0, caselocation);
      }
      voltmx.print("### frmFollow_onclickMap_gpsCallback: " +JSON.stringify(Global.vars.casesMapTable));
      // set map to current location
      //frmFollow.mapCases.widgetDataMapForCallout={lbl:"data1", rtx:"data2", imgWhat:"img", lbl2:"data3", rtx2:"data4"};
      //frmFollow.mapCases.calloutTemplate=flcMapCallout;
      //frmFollow.mapCases.calloutWidth=80;
      if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === false){
  		//frmFollow.mapCases.showCurrentLocation = true;
      }
      //make circle to show own location and first point on the map
      if(Global.vars.casesMapTable[1] !== undefined && Global.vars.gLatitude !== 0.0){
        var x = (Math.max(Number(Global.vars.gLatitude), Number(Global.vars.casesMapTable[1].lat)) - Math.min(Number(Global.vars.gLatitude), Number(Global.vars.casesMapTable[1].lat))) / 2 + Math.min(Number(Global.vars.gLatitude), Number(Global.vars.casesMapTable[1].lat));
        var y = (Math.max(Number(Global.vars.gLongitude), Number(Global.vars.casesMapTable[1].lon)) - Math.min(Number(Global.vars.gLongitude), Number(Global.vars.casesMapTable[1].lon))) / 2 + Math.min(Number(Global.vars.gLongitude), Number(Global.vars.casesMapTable[1].lon));
        var location1 = {lat:Global.vars.gLatitude, lon:Global.vars.gLongitude};
      	var location2 = {lat:Global.vars.casesMapTable[1].lat, lon:Global.vars.casesMapTable[1].lon}; //first destination on map
        var distanceInMeters = 0;
        for(var i in Global.vars.casesMapTable){
          var v = Global.vars.casesMapTable[i];
          if(v.name != voltmx.i18n.getLocalizedString("l_myLocation")){
            var locationPoint = {lat:v.lat ,lon: v.lon};
            var distance = voltmx.map.distanceBetween(location1, locationPoint);
            x = (Math.max(Number(Global.vars.gLatitude), Number(v.lat)) - Math.min(Number(Global.vars.gLatitude), Number(v.lat))) / 2 + Math.min(Number(Global.vars.gLatitude), Number(v.lat));
        	y = (Math.max(Number(Global.vars.gLongitude), Number(v.lon)) - Math.min(Number(Global.vars.gLongitude), Number(v.lon))) / 2 + Math.min(Number(Global.vars.gLongitude), Number(v.lon));
            location2 = {lat:x, lon:y};
            var distanceToMiddle = voltmx.map.distanceBetween(location1, location2);
            voltmx.print("### frmFollow_onclickMap_gpsCallback distance: " + distance);
            v.calloutData.data3 = voltmx.i18n.getLocalizedString("l_distance");
            v.calloutData.data4 = "<label style=\"color:#000000\">" + Math.round(distance) + "m" + "</label>";
            if(distanceToMiddle > distanceInMeters){
              distanceInMeters = distanceToMiddle;
            }
          }
        }
        //frmFollow.mapCases.locationData = Global.vars.casesMapTable;
        voltmx.print("### frmFollow_onclickMap_gpsCallback distance to middle: " + distanceInMeters);
        var Circle= {id:"circleId2",
                      centerLocation :{
                      lat:x,
                      lon:y},
                      navigateAndZoom:true,
                      navigatetoZoom: true,
                      radius:distanceInMeters,
                      circleConfig:{
                      lineColor:"ffffff00",
                      fillColor:"",
                      lineWidth:1},
                      showCenterPin:false
                  };        
        //frmFollow.mapCases.addCircle(Circle);
        voltmx.application.dismissLoadingScreen();
      }else{
        //frmFollow.mapCases.locationData = Global.vars.casesMapTable;
        //frmFollow.mapCases.navigateTo(0, false);
        voltmx.application.dismissLoadingScreen();
      }
}

function frmFollow_onclickList(){
  	voltmx.print("### frmFollow_onclickList");
  	//show list
  	frmFollow_flcGetNewCaseList_flcCaseSegment_setVisibility(true);
  	//frmFollow.segCases.setVisibility(true);
  	frmFollow.btnRetrieveCases.setEnabled(false);
  	//do not show map
  	frmFollow_flcGetNewCaseList_flcCaseMap_setVisibility(false);
  	frmFollow_mapCases_setVisibility(false);
  	//set button list to non inverse
  	frmFollow.imgMap.src = "but_but_map_unselect.png";
  	//set button map to inverse
  	frmFollow.imgList.src = "but_but_list_select.png";
  	//clear map
  	//frmFollow.mapCases.clear();
  	//get cases
  	frmFollow_RetrieveUnclaimedCases();
}

function frmFollow_onclick_mapCasesPin(locationdata){
  	//function that fires after clicking on the callout
  	voltmx.print("### frmFollow_onclick_mapCasesPin item: " + JSON.stringify(locationdata));
  	if(locationdata.name !== voltmx.i18n.getLocalizedString("l_myLocation") && locationdata.category !== "caseClaimedByOther"){
      if(Global.vars.demoModus === true){
        frmFollow_showDemoData();
      }else{
        try {
          voltmx.timer.cancel("setRetrieveCasesTimer");
        }catch(err1){}
        frmFollow_retrieveAndAssignCase(locationdata.docID, locationdata.externalCaseID, locationdata.activeTaskType);
      }
    }
}

function frmFollow_retrieveAndAssignCase(docID, externalCaseID, activeTaskType){
  	voltmx.print("### frmFollow_retrieveAndAssignCase docID: " + docID + ", externalCaseID: " + externalCaseID + ", activeTaskType: " + activeTaskType);
    function couchDBCheckCaseCallback(result){
      if((result != null && voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) || (result != null && result.rawResponse != null)){//ivm gebruik passthrough
        var d = result.rawResponse; //ivm gebruik passthrough
        voltmx.print("### frmFollow_retrieveAndAssignCase couchDBCheckCaseCallback result: " + JSON.stringify(result));
        voltmx.print("### frmFollow_retrieveAndAssignCase couchDBCheckCaseCallback lastTaskProcessed: " + JSON.stringify(d.case.processinfo.lastTaskProcessed));
        voltmx.print("### frmFollow_retrieveAndAssignCase couchDBCheckCaseCallback Doc activeTaskType: " + d.case.processinfo.activeTaskType);
        voltmx.print("### frmFollow_retrieveAndAssignCase couchDBCheckCaseCallback input activeTaskType: " + activeTaskType);
        voltmx.print("### frmFollow_retrieveAndAssignCase couchDBCheckCaseCallback Doc: " + d.case.processinfo.assignment.assignee);
        voltmx.print("### frmFollow_retrieveAndAssignCase couchDBCheckCaseCallback Doc: " + Global.vars.gUsername);
        if (d.case && d.case.processinfo != null){
          var _unhandledTaskTypes = -1;
          _unhandledTaskTypes = Utility_getIndexIfObjWithAttr(Global.vars.unhandledTaskTypes,"taskType",d.case.processinfo.activeTaskType);
          if((d.case.status == "OpvolgenPending" || d.case.status == "OpvolgenClaimed" || _unhandledTaskTypes !== -1) && (d.case.processinfo.assignment.assignee != null && d.case.processinfo.assignment.assignee !== Global.vars.gUsername)){
            voltmx.print("### frmFollow_retrieveAndAssignCase claimed by another, alert and retrieve list");
            frmFollow_flcGetNewCaseList_setVisibility(true);
            frmFollow_flcMenuButton_setVisibility(false);
            frmFollow_btnCancelClaim_setVisibility(false);
            frmFollow_flcGetNewCaseList_flcRetrieveCases_setVisibility(true);
            frmFollow_btnNext_setVisibility(false);
            frmFollow_btnNoAutorization_setVisibility(false);
            frmFollow_btnNoViolation_setVisibility(false);
            frmFollow_flcFooterCases_setVisibility(false);
            voltmx.application.dismissLoadingScreen();
            //alert(voltmx.i18n.getLocalizedString("l_claimedByOtherRetrievingCases"));
            voltmx.ui.Alert(voltmx.i18n.getLocalizedString("l_claimedByOtherRetrievingCases"),
	               	  frmFollow_confirm_claimed,
	               	  "info",
	               	  voltmx.i18n.getLocalizedString("bt_ok"),
	               	  null,
	               	  voltmx.i18n.getLocalizedString("l_info"),
	              	  null);
//             Global_resetApp();
//             try{
//               voltmx.print("### frmFollow_retrieveAndAssignCase Global.vars.previousForm: " + Global.vars.previousForm);
//               if(Global.vars.previousForm === null){
//                 frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
//                 frmFollow.lblLocation.skin = lblFieldInfo;
//                 voltmx.print("### frmFollow_retrieveAndAssignCase frmFollow_RetrieveUnclaimedCases");
//                 frmFollow_RetrieveUnclaimedCases();
//               }
//             }catch(e){}
			// return to frmOverviewTask
              
          } else if(d.case.processinfo.activeTaskType === undefined || d.case.processinfo.activeTaskType === null || d.case.processinfo.activeTaskType !== activeTaskType){
            voltmx.print("### frmFollow_retrieveAndAssignCase not longer available");
            voltmx.application.dismissLoadingScreen();
            //alert(voltmx.i18n.getLocalizedString("l_documentAlreadyProcessedException"));
//             Global_resetApp();
//             try{
//               voltmx.print("### frmFollow_retrieveAndAssignCase Global.vars.previousForm: " + Global.vars.previousForm);
//               if(Global.vars.previousForm === null){
//                 frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
//                 frmFollow.lblLocation.skin = lblFieldInfo;
//                 voltmx.print("### frmFollow_retrieveAndAssignCase frmFollow_RetrieveUnclaimedCases");
//                 frmFollow_RetrieveUnclaimedCases();
//               }
//             }catch(e){}
            // return to frmOverviewTask
              frmFollow_FinishUpload();
          }else{
            voltmx.print("### frmFollow_retrieveAndAssignCase continue to do service call");
            var arsCaseID = externalCaseID;
            var taskType = activeTaskType;
            service_RetrieveAndAssignCaseARS(arsCaseID, taskType, frmFollow_RetrieveAndAssignedCaseARSCallback, frmFollow_RetrieveAndAssignedCaseARSCallback);
            Global.vars.claimedDocID = docID;
          }
        }else{
          alert(voltmx.i18n.getLocalizedString("e_ser0001"));
          voltmx.application.dismissLoadingScreen();
        }
      }else{
          alert(voltmx.i18n.getLocalizedString("e_ser0001"));
          voltmx.application.dismissLoadingScreen();
      }
    }
    function couchDBCheckCaseErrorCallback(error){
      voltmx.print("### frmFollow_retrieveAndAssignCase couchDBCheckCaseErrorCallback Doc Error: " + JSON.stringify(error));
      voltmx.application.dismissLoadingScreen();
      alert(voltmx.i18n.getLocalizedString("l_caseNotRetrieved"));
      Global_resetApp();
      try{
        voltmx.print("### frmFollow_retrieveAndAssignCase couchDBCheckCaseErrorCallback Global.vars.previousForm: " + Global.vars.previousForm);
        if(Global.vars.previousForm === null){
          frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
          frmFollow.lblLocation.skin = lblFieldInfo;
          voltmx.print("### frmFollow_retrieveAndAssignCase couchDBCheckCaseErrorCallback frmFollow_RetrieveUnclaimedCases");
          frmFollow_RetrieveUnclaimedCases();
        }
      }catch(e){}
    }
  	frmFollow_flcMenuButton_setVisibility(true);
    frmFollow_btnCancelClaim_setVisibility(false);
    frmFollow_btnNext_setVisibility(false);
    Utility_resetOnlineAttachments();
    service_couchDBGetCase(docID, false, true, couchDBCheckCaseCallback, couchDBCheckCaseErrorCallback);
}

function frmFollow_confirm_claimed(response){
  voltmx.print("### frmFollow_confirm_claimed");
  if(response){
  	frmFollow_FinishUpload();
  }
}

function frmFollow_onPinClick(mapid, locationdata){
  //function that fires after clicking on the pin
  voltmx.print("### frmFollow_onPinClick mapid: " + mapid);
  if(locationdata != null && locationdata !== undefined){
   	voltmx.print("### frmFollow_onPinClick locationdata: " + JSON.stringify(locationdata));
    var location1 = {lat: Global.vars.gLatitude, lon: Global.vars.gLongitude};
    var location2 = {lat: locationdata.lat, lon: locationdata.lon};
//     var distanceInMeters = voltmx.map.distanceBetween(location1, location2);
//     voltmx.print("### frmFollow_onPinClick distanceInMeters between target and my location: " + distanceInMeters); 
  }
}

function frmFollow_btnRetrieveCases(){
  Global_resetApp();
  try{
    voltmx.print("### frmFollow_btnRetrieveCases Global.vars.previousForm: " + Global.vars.previousForm);
    if(Global.vars.previousForm === null){
      frmFollow.lblLocation.text = voltmx.i18n.getLocalizedString("l_location");
      frmFollow.lblLocation.skin = lblFieldInfo;
      voltmx.print("### frmFollow_btnRetrieveCases frmFollow_RetrieveUnclaimedCases");
      frmFollow_RetrieveUnclaimedCases();
    }
  }catch(e){}
}

function frmFollow_btnCancelClaim(){
  voltmx.print("### frmFollow_btnCancelClaim");
  if (CaseData.caseinfo.id != null){
    frmFollow_showLoaderUploadCase(voltmx.i18n.getLocalizedString("l_send"));
    service_unclaimCase(CaseData.caseinfo.id,frmFollow_unclaimCaseSuccesCallback,frmFollow_unclaimCaseErrorCallback);
  } else {
    frmFollow_FinishUpload();
  }
}
 
function frmFollow_unclaimCaseSuccesCallback(result){
  voltmx.print("### frmFollow_unclaimCaseSuccesCallback result: " + JSON.stringify(result));
  if(result.opstatus === 0 && result.httpStatusCode == 200){
      if(result.ok === "true"){
        voltmx.print("### frmFollow_unclaimCaseSuccesCallback result ok");
        frmFollow_Finish();
      }else{
       	voltmx.print("### frmFollow_unclaimCaseSuccesCallback NOT OK");
        frmFollow_Finish();
      }
  }else if(result.opstatus === 0 && result.httpStatusCode == 500){
    voltmx.print("### frmFollow_unclaimCaseSuccesCallback status code 500 code: " + result.code);
    voltmx.print("### frmFollow_unclaimCaseSuccesCallback document may be changed on server so let go");
    frmFollow_Finish();
  }else{
    voltmx.print("### frmFollow_unclaimCaseSuccesCallback some service error");
    alert(voltmx.i18n.getLocalizedString("i_fsc0001"));
    frmFollow_dismissLoaderUploadCase();
  	voltmx.application.dismissLoadingScreen();
  }
}

function frmFollow_unclaimCaseErrorCallback(error){
  	voltmx.print("### frmFollow_unclaimCaseErrorCallback: " + JSON.stringify(error));
	//alert(voltmx.i18n.getLocalizedString("i_fsc0001"));
  	frmFollow_dismissLoaderUploadCase();
  	voltmx.application.dismissLoadingScreen();
  	frmFollow_FinishUpload();
}

function frmFollow_showDemoDataFiscaal(){
  existingCase_loadDataFiscaal();
   //add timestamp fields
  CaseData.caseinfo.indClaimedByUser = true;
  CaseData.caseinfo.timeClaimedByUser = Utility_getUTCJavascriptDate(null);
  frmFollow_validateCase();
}

function frmFollow_showDemoDataMulder(){
  existingCase_loadDataMulder();
   //add timestamp fields
  CaseData.caseinfo.indClaimedByUser = true;
  CaseData.caseinfo.timeClaimedByUser = Utility_getUTCJavascriptDate(null);
  frmFollow_validateCase();
}

function frmFollow_showDemoDataKlemmen(){
  existingCase_loadDataKlemmen();
   //add timestamp fields
  CaseData.caseinfo.indClaimedByUser = true;
  CaseData.caseinfo.timeClaimedByUser = Utility_getUTCJavascriptDate(null);
  frmFollow_validateCase();
}

function frmFollow_showDemoDataOntklemmen(){
  existingCase_loadDataOntKlemmen();
   //add timestamp fields
  CaseData.caseinfo.indClaimedByUser = true;
  CaseData.caseinfo.timeClaimedByUser = Utility_getUTCJavascriptDate(null);
  frmFollow_validateCase();
}

function frmFollow_showDemoDataOntklemmen(){
  existingCase_loadDataSlepen();
   //add timestamp fields
  CaseData.caseinfo.indClaimedByUser = true;
  CaseData.caseinfo.timeClaimedByUser = Utility_getUTCJavascriptDate(null);
  frmFollow_validateCase();
}

function frmFollow_showRegionInfo() {
    try {
      if(frmFollow.lblSearchQuery.text !== "" && frmFollow.lblSearchQuery.text !== voltmx.i18n.getLocalizedString("l_shift")){
        frmFollow.segRegions.setData(Global.vars.listRegions);
        frmFollow.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmFollow.forceLayout();
        frmFollow.flcShowRegions.isVisible=true;
        frmFollow_showRegionInfo_preAnim();
        frmFollow_showRegionInfo_animationStart();
      }
    } catch (e) {
      voltmx.print("### frmFollow_showRegionInfo error: " + JSON.stringify(e));
    }
}

function frmFollow_showRegionInfo_preAnim() {
  try {
    voltmx.print("### frmFollow_showRegionInfo_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
    //frmFollow.flcDetailRegions.transform = trans1;
    //frmFollow.imgPopupLogoRegions.transform = trans1;
    //frmFollow.flcRegionDetails.transform = trans1;
  } catch (e) {
    voltmx.print("### frmFollow_showRegionInfo_preAnim error: " + JSON.stringify(e));
  }
}

function frmFollow_showRegionInfo_arrangeWidgets() {
  try {
    voltmx.print("### frmFollow_showRegionInfo_arrangeWidgets");
    //popup fields
    frmFollow.imgPopupLogoRegions.isVisible = false;
    frmFollow.flcDetailRegions.isVisible = false;
    frmFollow.flcRegionDetails.isVisible = false;
    frmFollow.lblLineRegions.isVisible = false;
    frmFollow.flcFooterShowRegions.isVisible = false;
    frmFollow.flcShowRegions.isVisible = false;
    frmFollow.flcFooterShowRegions.setEnabled(false);
    frmFollow.flcShowRegions.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showRegionInfo_preAnim error: " + JSON.stringify(e));
  }

}

function frmFollow_showRegionInfo_animationStart(eventobject) {
  try {
    voltmx.print("### frmFollow_showRegionInfo_animationStart");
    frmFollow.flcShowRegions.isVisible = true;
    frmFollow.flcDetailRegions.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmFollow.flcDetailRegions.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmFollow_showRegionInfo_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmFollow_showRegionInfo_animationStart error: " + JSON.stringify(e));
  }
}

function frmFollow_showRegionInfo_animLogo() {
  try {
    voltmx.print("### frmFollow_showRegionInfo_animLogo");
//     var trans = voltmx.ui.makeAffineTransform();
//     trans.scale(1.2, 1.2);
//     frmFollow.imgPopupLogoRegions.animate(
//       voltmx.ui.createAnimation({
//         "100": {
//           "anchorPoint": {
//             "x": 0.5,
//             "y": 0.5
//           },
//           "stepConfig": {
//             "timingFunction": voltmx.anim.EASE
//           },
//           "transform": trans,
//         }
//       }), {
//         "delay": 0,
//         "iterationCount": 1,
//         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
//         "duration": 0.25
//       }, {
//         "animationEnd": function (){
//           frmFollow_showRegionInfo_animOtherWidgets(frmFollow.flcRegionDetails);
//           frmFollow_showRegionInfo_animOtherWidgets(frmFollow.lblLineRegions);
//           frmFollow_showRegionInfo_animLogoBack();
//         }
//       });
    frmFollow_showRegionInfo_animOtherWidgets(frmFollow.flcRegionDetails);
    frmFollow_showRegionInfo_animOtherWidgets(frmFollow.lblLineRegions);
    frmFollow.imgPopupLogoRegions.isVisible = true;
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showRegionInfo_animLogo error: " + JSON.stringify(e));
  }
}


function frmFollow_showRegionInfo_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmFollow_showRegionInfo_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmFollow.flcRegionDetails.isVisible = true;
    frmFollow.lblLineRegions.isVisible = true;
    frmFollow.flcFooterShowRegions.isVisible = true;
    frmFollow.flcFooterShowRegions.setEnabled(true);
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showRegionInfo_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmFollow_showRegionInfo_animLogoBack() {
  try {
    voltmx.print("### frmFollow_showRegionInfo_animLogoBack");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmFollow.imgPopupLogoRegions.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showRegionInfo_animLogoBack error: " + JSON.stringify(e));
  }
}

function frmFollow_hideShowRegionInfo(){
  //activate footer and mainpage
  frmFollow.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmFollow_flcShowRegions_setVisibility(false);
}

function frmFollow_showLoaderUploadCase(title){
  voltmx.application.showLoadingScreen(lblLoader,
                                          voltmx.i18n.getLocalizedString("l_loading") + "...",
                                          "center",
                                          false,
                                          true,
                                      { enablemenukey : true, enablebackkey : true } );
//   if(title != null && title !== ""){
//     frmFollow.lblMsgUpload.text = title;
//   }else{
//     frmFollow.lblMsgUpload.text = voltmx.i18n.getLocalizedString("l_send");
//   }
  frmFollow.imgLoader.src = "empty.png";
  frmFollow_flcUpLoadCase_setVisibility(true);
  frmFollow.flcMainPage.setEnabled(false);
}

function frmFollow_dismissLoaderUploadCase(){
    frmFollow_flcUpLoadCase_setVisibility(false);
    frmFollow.flcMainPage.setEnabled(true);
  	voltmx.application.dismissLoadingScreen();
}

function frmFollow_licenseplateToUpper(){
  if(frmFollow.editplate.txtLicensePlate.text != null){
    frmFollow.editplate.txtLicensePlate.text = frmFollow.editplate.txtLicensePlate.text.toUpperCase();
  }
}

function frmFollow_showTaskTimeLine() {
    try {
      if(CaseData.processinfo.tasks.length > 0){
        var tasks = JSON.parse(JSON.stringify(CaseData.processinfo.tasks));
        var showtasks = [];
        for(var i in tasks){
          var v = tasks[i];
          var time = v.taskCompletedOn === null ? "n/a" : new Date(v.taskCompletedOn);
          v.taskClaimedByName = v.taskClaimedByName === null ? "n/a" : v.taskClaimedByName;
          v.taskOutcomeDescription = v.taskOutcomeDescription === null ? "n/a" : v.taskOutcomeDescription;
          v.taskType = v.taskType === null ? "n/a" : v.taskType;
          v.taskOutcomeExplanation = v.taskOutcomeExplanation === null ? "n/a" : v.taskOutcomeExplanation;
          v.date = v.taskCompletedOn === null ? "n/a" : Utility_getLocaleShortDateString(time);
          v.time = v.taskCompletedOn === null ? "" : Utility_getLocaleShortTimeString(time, true);
          if(v.taskOutcomeDescription != null && v.taskOutcomeDescription !== "n/a"){
            showtasks.push(v);
          }
        }
		//descending, null onderaan
        if(showtasks.length > 0){
          showtasks.sort(function(a, b) {
              return (a.taskCompletedOn===null)-(b.taskCompletedOn===null) || -(a.taskCompletedOn>b.taskCompletedOn)||+(a.taskCompletedOn<b.taskCompletedOn);
          });
        }
        voltmx.print("### frmFollow_showTaskTimeLine showtasks: " + JSON.stringify(showtasks));
        voltmx.print("### frmFollow_showTaskTimeLine CaseData.processinfo.tasks.: " + JSON.stringify(CaseData.processinfo.tasks));
        frmFollow.segTaskTimeLine.removeAll();
        frmFollow.segTaskTimeLine.setData(showtasks);
        frmFollow.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmFollow.forceLayout();
        frmFollow_flcShowTaskTimeLine_setVisibility(true);
        frmFollow_showTaskTimeLine_preAnim();
        frmFollow_showTaskTimeLine_animationStart();
      }
    } catch (e) {
      voltmx.print("### frmFollow_showTaskTimeLine error: " + JSON.stringify(e));
    }
}

function frmFollow_showTaskTimeLine_preAnim() {
  try {
    voltmx.print("### frmFollow_showTaskTimeLine_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
    //frmFollow.flcDetailTaskTimeLine.transform = trans1;
    //frmFollow.imgPopupLogoTaskTimeLine.transform = trans1;
  } catch (e) {
    voltmx.print("### frmFollow_showTaskTimeLine_preAnim error: " + JSON.stringify(e));
  }
}

function frmFollow_showTaskTimeLine_arrangeWidgets() {
  try {
    voltmx.print("### frmFollow_showTaskTimeLine_arrangeWidgets");
    //popup fields
    frmFollow.imgPopupLogoTaskTimeLine.isVisible = false;
    frmFollow.flcDetailTaskTimeLine.isVisible = false;
    frmFollow.lblLineTaskTimeLine.isVisible = false;
    frmFollow.flcFooterTaskTimeLine.isVisible = false;
    frmFollow.flcShowTaskTimeLine.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showTaskTimeLine_preAnim error: " + JSON.stringify(e));
  }

}

function frmFollow_showTaskTimeLine_animationStart(eventobject) {
  try {
    voltmx.print("### frmFollow_showTaskTimeLine_animationStart");
    frmFollow_flcShowTaskTimeLine_setVisibility(true);
    frmFollow.flcDetailTaskTimeLine.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmFollow.flcDetailTaskTimeLine.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmFollow_showTaskTimeLine_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmFollow_showTaskTimeLine_animationStart error: " + JSON.stringify(e));
  }
}

function frmFollow_showTaskTimeLine_animLogo() {
  try {
    voltmx.print("### frmFollow_showTaskTimeLine_animLogo");
//     var trans = voltmx.ui.makeAffineTransform();
//     trans.scale(1.2, 1.2);
//     frmFollow.imgPopupLogoTaskTimeLine.animate(
//       voltmx.ui.createAnimation({
//         "100": {
//           "anchorPoint": {
//             "x": 0.5,
//             "y": 0.5
//           },
//           "stepConfig": {
//             "timingFunction": voltmx.anim.EASE
//           },
//           "transform": trans,
//         }
//       }), {
//         "delay": 0,
//         "iterationCount": 1,
//         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
//         "duration": 0.25
//       }, {
//         "animationEnd": function (){
//           frmFollow_showTaskTimeLine_animOtherWidgets(frmFollow.flcTaskTimeLineDetails);
//           //frmFollow_showTaskTimeLine_animOtherWidgets(frmFollow.lblLineTaskTimeLine);
//           frmFollow_showTaskTimeLine_animLogoBack();
//         }
//       });
    frmFollow_showTaskTimeLine_animOtherWidgets(frmFollow.flcTaskTimeLineDetails);
    frmFollow.imgPopupLogoTaskTimeLine.isVisible = true;
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showTaskTimeLine_animLogo error: " + JSON.stringify(e));
  }
}


function frmFollow_showTaskTimeLine_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmFollow_showTaskTimeLine_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmFollow.flcTaskTimeLineDetails.isVisible = true;
    frmFollow.lblLineTaskTimeLine.isVisible = true;
    frmFollow.flcFooterTaskTimeLine.isVisible = true;
    frmFollow.flcFooterTaskTimeLine.setEnabled(true);
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showTaskTimeLine_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmFollow_showTaskTimeLine_animLogoBack() {
  try {
    voltmx.print("### frmFollow_showTaskTimeLine_animLogoBack");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmFollow.imgPopupLogoTaskTimeLine.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showTaskTimeLine_animLogoBack error: " + JSON.stringify(e));
  }
}

function frmFollow_hideShowTaskTimeLine(){
  //activate footer and mainpage
  frmFollow.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmFollow_flcShowTaskTimeLine_setVisibility(false);
}

// function frmFollow_showLoaderUploadCase(title){
//   if(title != null && title !== ""){
//     frmFollow.lblMsgUpload.text = title;
//   }else{
//     frmFollow.lblMsgUpload.text = voltmx.i18n.getLocalizedString("l_send");
//   }
//   frmFollow_flcUpLoadCase_setVisibility(true);
//   frmFollow.flcMainPage.setEnabled(false);
// }

function frmFollow_GPS_errorCallback(poserr){
  voltmx.print("### GPS frmFollow_GPS_errorCallback: " + JSON.stringify(poserr));
  voltmx.application.dismissLoadingScreen();
  Global.vars.gGpsRunning = false;
  Global.vars.gGpsFix = false;
  Global.vars.gLatitude = 0.0;
  Global.vars.gLongitude = 0.0;
  Global.vars.gAltitude = 0.0;
  Global.vars.gStreet = "";
  Global.vars.gCity = "";
  Global.vars.gMunicipality = "";
  Global.vars.nearbyStreets = [];
  if(Number(Global.vars.gpsTimeoutCount) === 0 && Global.vars.gDeviceInfo.model == "TC55"){
    Global.vars.gpsTimeoutCount = Number(Global.vars.gpsTimeoutCount) + 1;
    Global.vars.useBestProvider = true;
  }else{
    Global.vars.gpsTimeoutCount = 0;
    Global.vars.useBestProvider = false;
  }
  frmFollow_onclickMap_gpsCallback();
}

function frmFollow_setRecheckCaseDateTime(){
	//Set valid dates
	var startdate = new Date();
	startdate.setDate(startdate.getDate()-Global.vars.daysToLookBack);
	voltmx.print("#### frmFollow_setRecheckDateTime startdate: " + startdate); //now.format("m/dd/yy");
	//format the  start date
	var form_date = startdate.getDate();
	var form_month = startdate.getMonth();
	form_month++;
	var form_year = startdate.getFullYear();
	var valid_startdate = [form_date,form_month,form_year];
	voltmx.print("#### frmFollow_setRecheckDateTime valid startdate: " + valid_startdate);
	//format the current date
	var d = new Date();
  	var caseDate = d;
	var curr_date = d.getDate();
	var curr_month = d.getMonth();
	curr_month++;
	var curr_year = d.getFullYear();
	var curr_hour = d.getHours();
	var curr_min = d.getMinutes();
  	var curr_sec = d.getSeconds();
	var caseDateComponents = [curr_date,curr_month,curr_year,curr_hour,curr_min,curr_sec];
	voltmx.print("### frmFollow_setRecheckDateTime caseDate: " + caseDate);
  	voltmx.print("### frmFollow_setRecheckDateTime caseDateComponents: " + caseDateComponents);
  	Global.vars.newTimeSet = false;
  	CaseData_setDateTime(caseDateComponents, frmFollow_CaseData_setDateTimeRecheckCallback);
}

function frmFollow_resetResult(){
  frmFollow_flcParkingCheckResult_setVisibility(false);
  frmFollow.lblParkingCheckStatus.text = "";

}

function frmFollow_CaseData_setDateTimeRecheckCallback(){
  voltmx.print("### frmFollow_CaseData_setDateTimeRecheckCallback Time set to Case: " + JSON.stringify(CaseData.time));
  CaseData.time.savedTime = true;
  frmFollow.lblTime.text = CaseData.time.localeShortDate;
  frmFollow_resetResult();
  service_ParkingCheck(CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identNumber, CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode, CaseData.location.latitude, CaseData.location.longitude, CaseData.location.municipalityCode, CaseData.time.utcDateTime, frmFollow_parkingCheckCallback, frmFollow_parkingCheckErrorcallback);
}

function frmFollow_parkingCheckCallback(result){
  voltmx.print("#### frmFollow_parkingCheckCallback result: " + JSON.stringify(result));
  //change color
  frmFollow.lblParkingCheckStatus.skin = lblFieldInfo;
  //replace alarm bell
  frmFollow.imgParkingCheckStatusLeft.src = "parkingnotvalid.png";
  if(result.opstatus === 0 && result.httpStatusCode == 200){
    CaseData.parking.serviceResult = result.parkingCheckResponse[0];
    if(result.parkingCheckResponse[0].valid === true){
      voltmx.print("### frmFollow_parkingCheckCallback noViolation");
      Global.vars.noViolation = true;
    } else {
      voltmx.print("### frmFollow_parkingCheckCallback Violation");
      Global.vars.noViolation = false;
      if (Global.vars.enableErrorVibrate === true){
        var config = [	
          //{"delay" : 0, "duration" : 200, "amplitude" : 250}, 
          {"delay" : 0, "duration" : 600, "amplitude" : 350},
          {"delay" : 650, "duration" : 50, "amplitude" : 250},
        ];
        voltmx.phone.startVibration (config, false);
      }
      if (Global.vars.enableErrorSound === true){
        frmTrackDown_playSound("wrong");
      }
    }
    frmFollow_setParkingCheckResult();
  } else{
    frmFollow_flcParkingCheckResult_setVisibility(true);
    frmFollow.imgParkingCheckStatusLeft.src = "parkingwarning.png";
  	frmFollow.lblParkingCheckStatus.text = voltmx.i18n.getLocalizedString("l_serverError");
    voltmx.application.dismissLoadingScreen();
  }
  
}

function frmFollow_parkingCheckErrorcallback(error){
  voltmx.print("#### frmFollow_parkingCheckErrorcallback error: " + JSON.stringify(error));
  frmFollow_flcParkingCheckResult_setVisibility(true);
  frmFollow.imgParkingCheckStatusLeft.src = "parkingwarning.png";
  frmFollow.lblParkingCheckStatus.text = voltmx.i18n.getLocalizedString("l_serverError");
  voltmx.application.dismissLoadingScreen();
}

function frmFollow_setParkingCheckResult(){
  frmFollow.lblParkingCheckStatus.skin = lblFieldInfo;
  frmFollow.imgParkingCheckStatusLeft.src = "parkingnotvalid.png";
  var lblstatus  = voltmx.i18n.getLocalizedString("l_parking_invalid");
  if(CaseData.parking.serviceResult.valid === true){
    voltmx.print("### frmFollow_setParkingCheckResult noViolation");
    Global.vars.noViolation = true;
  } else {
    voltmx.print("### frmFollow_setParkingCheckResult Violation");
    Global.vars.noViolation = false;
  }
  if(Global.vars.noViolation === false){
    try {
      if(CaseData.parking.serviceResult !== undefined && CaseData.parking.serviceResult != null){
        voltmx.print("### frmFollow_setParkingCheckResult 13");
        if (CaseData.parking.serviceResult.serviceErrorsDetected !== undefined && CaseData.parking.serviceResult.serviceErrorsDetected === true){
          voltmx.print("### frmFollow_setParkingCheckResult 14");
          var resultText = "";
          for (var i=0; CaseData.parking.serviceResult.responses != null && i < CaseData.parking.serviceResult.responses.length; i++ ){
            voltmx.print("### frmFollow_setParkingCheckResult 14: " + JSON.stringify(CaseData.parking.serviceResult.responses[i]));
            var responseArray = CaseData.parking.serviceResult.responses[i];
            var serviceName = voltmx.i18n.getLocalizedString(responseArray.service);
            if (serviceName === "" || serviceName === null || serviceName === undefined){
              serviceName = responseArray.service;
            }
            if (responseArray.errorCode !== undefined && responseArray.errorCode != null){
              if (responseArray.errorMessage !== undefined && responseArray.errorMessage != null){
                responseArray.errorMessage = voltmx.i18n.getLocalizedString("l_serverError");
              }
              resultText = Utility_geterrorMessage(responseArray.service,responseArray.errorCode,responseArray.errorMessage);
              if (resultText === "" || resultText === null || resultText === undefined){
                resultText = responseArray.errorMessage;
              }
              break;
            } else {
              resultText = voltmx.i18n.getLocalizedString("l_serverError");//
            }
          }
          if (resultText == voltmx.i18n.getLocalizedString("e_serverErrorNPR_5")){
            frmFollow.imgParkingCheckStatusLeft.src = "parkingvalid.png";
          } else {
            frmFollow.imgParkingCheckStatusLeft.src = "parkingfailure.png";
          }
          lblstatus = resultText;
        }
      }
    } catch (error){
      voltmx.print("### frmFollow_setParkingCheckResult 16");
    } 
  }else{
    lblstatus = voltmx.i18n.getLocalizedString("l_hasParkingRight"); //i18n
    frmFollow.imgParkingCheckStatusLeft.src = "parkingvalid.png";
    try {
      if(CaseData.parking.serviceResult !== undefined && CaseData.parking.serviceResult != null){
        voltmx.print("### frmFollow_setParkingCheckResult 12");
        if(CaseData.parking.serviceResult !== undefined && CaseData.parking.serviceResult != null){
          voltmx.print("### frmFollow_setParkingCheckResult 13");
          if (CaseData.parking.serviceResult.locationVerified !== undefined && CaseData.parking.serviceResult.locationVerified === false){
            voltmx.print("### frmFollow_setParkingCheckResult 14");
            frmFollow.imgParkingCheckStatusLeft.src = "parkingwarning.png";
            lblstatus = voltmx.i18n.getLocalizedString("l_validationNeeded");
          } else {
            voltmx.print("### frmFollow_setParkingCheckResult 15");
            frmFollow.imgParkingCheckStatusLeft.src = "parkingvalid.png";
          }
        }
      }
    } catch (error){
      voltmx.print("### frmFollow_setParkingCheckResult 16");

    }
  }
  frmFollow.lblParkingCheckStatus.text = lblstatus;
  frmFollow_populateSegParkingChecks();
} 

function frmFollow_showParkingCheckInfo() {
    try {
        //frmFollow_populateSegParkingChecks();
        frmFollow.flcMainPage.setEnabled(false);
        voltmx.print("### flcMainPage disabled");
        frmFollow.forceLayout();
        frmFollow_flcShowParkingChecks_setVisibility(true);
        frmFollow_showParkingCheckInfo_preAnim();
        frmFollow_showParkingCheckInfo_animationStart();
    } catch (e) {
      alert("Exception showParking");
      voltmx.print("### frmFollow_showParkingCheckInfo error: " + JSON.stringify(e));
    }
}

function frmFollow_showParkingCheckInfo_preAnim() {
  try {
    voltmx.print("### frmFollow_showParkingCheckInfo_preAnim");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.scale(0.1, 0.1);
    var trans2 = voltmx.ui.makeAffineTransform();
    trans2.translate(0, 10);
    //frmFollow.flcDetailsParkingChecks.transform = trans1;
    //frmFollow.imgPopupLogoParkingChecks.transform = trans1;
    //frmFollow.flcParkingCheckDetails.transform = trans1;
  } catch (e) {
    voltmx.print("### frmFollow_showParkingCheckInfo_preAnim error: " + JSON.stringify(e));
  }
}

function frmFollow_showParkingCheckInfo_arrangeWidgets() {
  try {
    voltmx.print("### frmFollow_showParkingCheckInfo_arrangeWidgets");
    //popup fields
    frmFollow.imgPopupLogoParkingChecks.isVisible = false;
    frmFollow.flcDetailsParkingChecks.isVisible = false;
    frmFollow.flcParkingCheckResults.isVisible = false;
    frmFollow.lblLineParkingChecks.isVisible = false;
    frmFollow.flcFooterShowParkingChecks.isVisible = false;
    frmFollow_flcShowParkingChecks_setVisibility(false);
    frmFollow.flcFooterShowParkingChecks.setEnabled(false);
    frmFollow.flcShowParkingChecks.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showParkingCheckInfo_preAnim error: " + JSON.stringify(e));
  }

}

function frmFollow_showParkingCheckInfo_animationStart(eventobject) {
  try {
    voltmx.print("### frmFollow_showParkingCheckInfo_animationStart");
    frmFollow_flcShowParkingChecks_setVisibility(true);
    frmFollow.flcDetailsParkingChecks.isVisible = true;
    var trans100 = voltmx.ui.makeAffineTransform();
    trans100.scale(1, 1);
    frmFollow.flcDetailsParkingChecks.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans100,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": voltmx.runOnMainThread(frmFollow_showParkingCheckInfo_animLogo)
      });
  } catch (e) {
    voltmx.print("### frmFollow_showParkingCheckInfo_animationStart error: " + JSON.stringify(e));
  }
}

function frmFollow_showParkingCheckInfo_animLogo() {
  try {
    voltmx.print("### frmFollow_showParkingCheckInfo_animLogo");
//     var trans = voltmx.ui.makeAffineTransform();
//     trans.scale(1.2, 1.2);
//     frmFollow.imgPopupLogoParkingChecks.animate(
//       voltmx.ui.createAnimation({
//         "100": {
//           "anchorPoint": {
//             "x": 0.5,
//             "y": 0.5
//           },
//           "stepConfig": {
//             "timingFunction": voltmx.anim.EASE
//           },
//           "transform": trans,
//         }
//       }), {
//         "delay": 0,
//         "iterationCount": 1,
//         "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
//         "duration": 0.25
//       }, {
//         "animationEnd": function (){
//           frmFollow_showParkingCheckInfo_animOtherWidgets(frmFollow.flcParkingCheckResults);
//           frmFollow_showParkingCheckInfo_animOtherWidgets(frmFollow.lblLineParkingChecks);
//           frmFollow_showParkingCheckInfo_animLogoBack();
//         }
//       });
    frmFollow_showParkingCheckInfo_animOtherWidgets(frmFollow.flcParkingCheckResults);
    frmFollow_showParkingCheckInfo_animOtherWidgets(frmFollow.lblLineParkingChecks);
    frmFollow.imgPopupLogoParkingChecks.isVisible = true;
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showParkingCheckInfo_animLogo error: " + JSON.stringify(e));
  }
}


function frmFollow_showParkingCheckInfo_animOtherWidgets(widget) {
  try {
    voltmx.print("### frmFollow_showParkingCheckInfo_animOtherWidgets");
    var trans1 = voltmx.ui.makeAffineTransform();
    trans1.translate(1, 1);
    //trans1.translate(1, -10);
    widget.isVisible = true;
    widget.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans1,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.25
      }, {
        "animationEnd": function() {}
      });
    frmFollow.flcParkingCheckResults.isVisible = true;
    // show agents
    frmFollow.flcSegParkingChecks.isVisible = true;
    // hide details
    frmFollow.flcSegParkingCheckDetails.isVisible = false;
    frmFollow.lblLineParkingChecks.isVisible = true;
    frmFollow.flcFooterShowParkingChecks.isVisible = true;
    frmFollow.flcFooterShowParkingChecks.setEnabled(true);
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showParkingCheckInfo_animOtherWidgets error: " + JSON.stringify(e));
  }
}

function frmFollow_showParkingCheckInfo_animLogoBack() {
  try {
    voltmx.print("### frmFollow_showParkingCheckInfo_animLogoBack");
    var trans = voltmx.ui.makeAffineTransform();
    trans.scale(1, 1);
    frmFollow.imgPopupLogoParkingChecks.animate(
      voltmx.ui.createAnimation({
        "100": {
          "anchorPoint": {
            "x": 0.5,
            "y": 0.5
          },
          "stepConfig": {
            "timingFunction": voltmx.anim.EASE
          },
          "transform": trans,
        }
      }), {
        "delay": 0,
        "iterationCount": 1,
        "fillMode": voltmx.anim.FILL_MODE_FORWARDS,
        "duration": 0.15
      }, {
        "animationEnd": function (){}
      });
    frmFollow.forceLayout();
  } catch (e) {
    voltmx.print("### frmFollow_showParkingCheckInfo_animLogoBack error: " + JSON.stringify(e));
  }
}

function frmFollow_hideshowParkingCheckInfo(){
  //activate footer and mainpage
  frmFollow.flcMainPage.setEnabled(true);
  voltmx.print("### flcMainPage enabled");
  frmFollow_flcShowParkingChecks_setVisibility(false);
}

function frmFollow_populateSegParkingChecks(){
  frmFollow.flcSegParkingChecks.isVisible = true;
  voltmx.print("### frmFollow_populateSegParkingChecks");
  var newParkingCheckArray = [];
 	//column level validations
  for (var i=0; CaseData.parking.serviceResult.responses != null && i < CaseData.parking.serviceResult.responses.length; i++ ){
    var responseArray = CaseData.parking.serviceResult.responses[i];
  	var serviceName = voltmx.i18n.getLocalizedString(responseArray.service);
    if (serviceName === "" || serviceName === null || serviceName === undefined){
      serviceName = responseArray.service;
    }
    var resultText = "";
    var imageFile = "";
    if (responseArray.errorCode !== undefined && responseArray.errorCode != null){
      if (responseArray.errorMessage !== undefined && responseArray.errorMessage != null){
        responseArray.errorMessage = voltmx.i18n.getLocalizedString("l_serverError");
      }
      resultText = Utility_geterrorMessage(responseArray.service,responseArray.errorCode,responseArray.errorMessage);
      if (resultText === "" || resultText === null || resultText === undefined){
        resultText = responseArray.errorMessage;
      }
      if (resultText == voltmx.i18n.getLocalizedString("e_serverErrorNPR_5")){
        imageFile = "parkingvalid.png";
      } else {
        imageFile = "parkingfailure.png";
      }
    } else if (responseArray.successful !== undefined && responseArray.successful != null && responseArray.successful === false){
      resultText = voltmx.i18n.getLocalizedString("l_serverError");//
      imageFile = "parkingfailure.png";
    } else {
      resultText = responseArray.validParkingRight === false ? voltmx.i18n.getLocalizedString("l_parking_invalid") : (responseArray.locationVerified === false ? voltmx.i18n.getLocalizedString("l_validationNeeded") : voltmx.i18n.getLocalizedString("l_validParkingRight"));
      imageFile = responseArray.validParkingRight === false ? "parkingnotvalid.png" : (responseArray.locationVerified === false ? "parkingwarning.png" : "parkingvalid.png");	
    }
    voltmx.print("### frmFollow_populateSegParkingChecks serviceName: " + serviceName);
 	voltmx.print("### frmFollow_populateSegParkingChecks responseArray: " + JSON.stringify(responseArray));
 	var addrecord = {
  					name : serviceName,
    				result : resultText,
    				image : imageFile,
    				response : responseArray
      				};
      newParkingCheckArray.push(addrecord);
	  newParkingCheckArray = JSON.parse(JSON.stringify(newParkingCheckArray));
  }
  voltmx.print("### frmFollow_populateSegParkingChecks newParkingCheckArray: " + JSON.stringify(newParkingCheckArray));
  frmFollow.segParkingChecks.removeAll();
  frmFollow.segParkingChecks.setData(newParkingCheckArray);
  frmFollow_flcParkingCheckResult_setVisibility(true);
  frmFollow.flcSegParkingCheckDetails.isVisible = false;
  frmFollow.segParkingCheckDetail.removeAll();
  voltmx.application.dismissLoadingScreen();
  frmFollow_dismissLoaderUploadCase();
}

function frmFollow_onClicksegParkingChecks(){
  voltmx.print("### frmFollow_onClicksegParkingChecks");
  var newParkingCheckDetailArray = [];
  var gFocusedParkingCheck = frmFollow.segParkingChecks.selectedItems;
  voltmx.print("### frmFollow_onClicksegParkingChecks gFocusedParkingCheck: " + JSON.stringify(gFocusedParkingCheck));
  var addrecord = { key : "",
    				value : "",
    				imageLeft : "",
    				imageRight : ""};
  if (gFocusedParkingCheck[0].response !== undefined){
    var response = gFocusedParkingCheck[0].response;
    try {
    	voltmx.print("#### frmFollow_onClicksegParkingChecks response: " + JSON.stringify(response));
        var imgLeft = "";
        var errorMessage = "";
      	if (response.errorCode !== undefined && response.errorCode != null){
          if (response.errorMessage !== undefined && response.errorMessage != null){
            response.errorMessage = "Onbekende fout";
          }
          errorMessage = Utility_geterrorMessage(response.service,response.errorCode,response.errorMessage);
          if (errorMessage === "" || errorMessage === null || errorMessage === undefined){
			errorMessage = response.errorMessage;
          }
      	}
      	for (var i in response){
          voltmx.print("#### frmFollow_onClicksegParkingChecks forloop " + i +": "+ response[i]);
          if (i != "externalResponse"){//} && (response(i) != null && response(i) !== "")){
            if (i == "data"){
              var dataRec = {};
              dataRec = JSON.parse(JSON.stringify(response[i]));
              voltmx.print("#### frmFollow_onClicksegParkingChecks dataRec " + dataRec);
              //if (dataRec !== undefined && dataRec != {} && dataRec.length > 0){
              for (var j in dataRec){
                voltmx.print("#### frmFollow_onClicksegParkingChecks 2nd forloop " + j +": "+ dataRec[j]);
                if (dataRec[j] !== undefined && dataRec[j] != null && dataRec[j].length > 0){
                  if (j != "recordType" && j != "originalResponse" && j != "statusCode" && j != "statusOmschrijving" && j != "transactieType"  && j != "transactieGestart" && j != "eindtijd"  && j != "volgnummer"  && j != "lat"  && j != "lon"){
                    imgLeft = (dataRec[j] === false || dataRec[j] === "false") ? "parkingnotvalid.png" : ((dataRec[j] === true || dataRec[j] === "true") ? "parkingvalid.png": "");
                    voltmx.print("#### frmFollow_onClicksegParkingChecks 2nd forloop " + j +": "+ dataRec[j] + " -- " + imgLeft);
                    addrecord.key = frmTrackDown_translate_parkright(j);
                    addrecord.value = frmTrackDown_translate_parkright_value(j, dataRec[j]);
                    addrecord.imageLeft = imgLeft;
                    addrecord.imageRight = "";
                    newParkingCheckDetailArray.push(addrecord);
                    newParkingCheckDetailArray = JSON.parse(JSON.stringify(newParkingCheckDetailArray));
                  }
                }  
              }
              //}
            } else {
              if (i == "errorMessage"){
                addrecord.key = frmTrackDown_translate_parkright(i);
                addrecord.value = errorMessage;
                addrecord.imageLeft = imgLeft;
                addrecord.imageRight = "";
                newParkingCheckDetailArray.push(addrecord);
                newParkingCheckDetailArray = JSON.parse(JSON.stringify(newParkingCheckDetailArray));
              } else {
                imgLeft = response[i] === false ? "parkingnotvalid.png" : (response[i] === true ? "parkingvalid.png": "");
                addrecord.key = frmTrackDown_translate_parkright(i);
                addrecord.value = frmTrackDown_translate_parkright_value(i, response[i]);
                addrecord.imageLeft = imgLeft;
                addrecord.imageRight = "";
                newParkingCheckDetailArray.push(addrecord);
                newParkingCheckDetailArray = JSON.parse(JSON.stringify(newParkingCheckDetailArray));
              }
            }
          }
        }
    } catch (err){
      alert ("for loop error: " + JSON.stringify(err));
    }
  }
  voltmx.print("### frmFollow_onClicksegParkingChecks newParkingCheckDetailArray: " + JSON.stringify(newParkingCheckDetailArray));
  frmFollow.segParkingCheckDetail.setData(newParkingCheckDetailArray);
  frmFollow.flcSegParkingChecks.isVisible = false;
  frmFollow.flcSegParkingCheckDetails.isVisible = true;
}

function frmFollow_btnCloseCheckDetails(){
	frmFollow.flcSegParkingChecks.isVisible = true;
  	frmFollow.flcSegParkingCheckDetails.isVisible = false;
  	frmFollow.segParkingCheckDetail.removeAll();
}