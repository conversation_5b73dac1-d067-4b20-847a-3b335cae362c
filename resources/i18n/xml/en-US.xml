<Tbl><Array><Nil/></Array><Hash><HashCell><Str value="CaseManagementData"/><Str value="Case Management Data"/></HashCell><HashCell><Str value="ERR_SP_00"/><Str value="Undefined error or custom error"/></HashCell><HashCell><Str value="ERR_SP_01"/><Str value="Authentication failed"/></HashCell><HashCell><Str value="ERR_SP_02"/><Str value="Not authenticated"/></HashCell><HashCell><Str value="ERR_SP_03"/><Str value="Unknown CaseID"/></HashCell><HashCell><Str value="ERR_SP_04"/><Str value="Case not allowed"/></HashCell><HashCell><Str value="ERR_SP_05"/><Str value="Internal Service Error:"/></HashCell><HashCell><Str value="ERR_SP_06"/><Str value="License plate update not allowed"/></HashCell><HashCell><Str value="GetCarInfo"/><Str value="Retrieving car info"/></HashCell><HashCell><Str value="GetParkings"/><Str value="Retrieving parkings"/></HashCell><HashCell><Str value="GetSignals"/><Str value="Retrieving signals"/></HashCell><HashCell><Str value="Instance"/><Str value="Instance"/></HashCell><HashCell><Str value="LabelAanbrengen"/><Str value="Applying label"/></HashCell><HashCell><Str value="Location"/><Str value="Location"/></HashCell><HashCell><Str value="LocationData"/><Str value="Location information"/></HashCell><HashCell><Str value="NotificatieAanbrengen"/><Str value="Apply label"/></HashCell><HashCell><Str value="Offence"/><Str value="Offence"/></HashCell><HashCell><Str value="Person"/><Str value="Person"/></HashCell><HashCell><Str value="Planning"/><Str value="Planning"/></HashCell><HashCell><Str value="RED-ASGN-01"/><Str value="Current assignment"/></HashCell><HashCell><Str value="RED-ASGN-02"/><Str value="No current assignment"/></HashCell><HashCell><Str value="RED-ASGN-03"/><Str value="User already assigned"/></HashCell><HashCell><Str value="RED-ASGN-04"/><Str value="User logged on with other case type, log out first"/></HashCell><HashCell><Str value="RED-ASGN-05"/><Str value="Succesful assignment"/></HashCell><HashCell><Str value="RED-ASGN-06"/><Str value="Assignment failed, no available cloud or user already assigned"/></HashCell><HashCell><Str value="RED-ASGN-07"/><Str value="No available cloud"/></HashCell><HashCell><Str value="RED-ASGN-08"/><Str value="User unassigned from cloud"/></HashCell><HashCell><Str value="RED-ASGN-09"/><Str value="User was assigned, but unassignment failed"/></HashCell><HashCell><Str value="RED-ASGN-10"/><Str value="User was not assigned"/></HashCell><HashCell><Str value="RED-ASGN-11"/><Str value="User switched"/></HashCell><HashCell><Str value="RED-ASGN-12"/><Str value="User switch failed. Did not find two users in two different groups"/></HashCell><HashCell><Str value="ReferenceData"/><Str value="Reference information"/></HashCell><HashCell><Str value="Users"/><Str value="Users"/></HashCell><HashCell><Str value="VEHICLE_SIGNAL"/><Str value="Redora Signal"/></HashCell><HashCell><Str value="Vehicle"/><Str value="Vehicle"/></HashCell><HashCell><Str value="WachtenOpVerwijderen"/><Str value="Waiting on expiration for removal"/></HashCell><HashCell><Str value="WaitingForLocation"/><Str value="Waiting for gps location"/></HashCell><HashCell><Str value="_usesLegalAssistance"/><Str value="Uses legal assistance"/></HashCell><HashCell><Str value="appmode_checklabel"/><Str value="Check label"/></HashCell><HashCell><Str value="appmode_labeloverview"/><Str value="Label overview"/></HashCell><HashCell><Str value="appmode_register"/><Str value="Register"/></HashCell><HashCell><Str value="appmode_registerconcept"/><Str value="Register concept"/></HashCell><HashCell><Str value="appmode_registerlabel"/><Str value="Register label"/></HashCell><HashCell><Str value="bt_add"/><Str value="Add"/></HashCell><HashCell><Str value="bt_again"/><Str value="Again"/></HashCell><HashCell><Str value="bt_back"/><Str value="Back"/></HashCell><HashCell><Str value="bt_cancel"/><Str value="Cancel"/></HashCell><HashCell><Str value="bt_check"/><Str value="Check"/></HashCell><HashCell><Str value="bt_complete"/><Str value="Complete"/></HashCell><HashCell><Str value="bt_continue"/><Str value="Continue"/></HashCell><HashCell><Str value="bt_deselectAll"/><Str value="Deselect all"/></HashCell><HashCell><Str value="bt_directPV"/><Str value="Direct PV"/></HashCell><HashCell><Str value="bt_done"/><Str value="Done"/></HashCell><HashCell><Str value="bt_edit"/><Str value="Edit"/></HashCell><HashCell><Str value="bt_exit"/><Str value="Exit"/></HashCell><HashCell><Str value="bt_info"/><Str value="Info"/></HashCell><HashCell><Str value="bt_lightoff"/><Str value="Light off"/></HashCell><HashCell><Str value="bt_lightoff_once"/><Str value="Light off once"/></HashCell><HashCell><Str value="bt_lighton"/><Str value="Light on"/></HashCell><HashCell><Str value="bt_lighton_once"/><Str value="Light on once"/></HashCell><HashCell><Str value="bt_map"/><Str value="Map"/></HashCell><HashCell><Str value="bt_markOverviewPhoto"/><Str value="Make this photo the overview photo"/></HashCell><HashCell><Str value="bt_new"/><Str value="New"/></HashCell><HashCell><Str value="bt_no"/><Str value="No"/></HashCell><HashCell><Str value="bt_ok"/><Str value="OK"/></HashCell><HashCell><Str value="bt_register"/><Str value="Register"/></HashCell><HashCell><Str value="bt_remove"/><Str value="Remove"/></HashCell><HashCell><Str value="bt_retry"/><Str value="Retry"/></HashCell><HashCell><Str value="bt_selectAll"/><Str value="Select all"/></HashCell><HashCell><Str value="bt_send"/><Str value="Send"/></HashCell><HashCell><Str value="bt_stop"/><Str value="Stop"/></HashCell><HashCell><Str value="bt_unknown"/><Str value="Unknown"/></HashCell><HashCell><Str value="bt_vehicle_scan"/><Str value="Scan"/></HashCell><HashCell><Str value="bt_view"/><Str value="View"/></HashCell><HashCell><Str value="bt_yes"/><Str value="Yes"/></HashCell><HashCell><Str value="camera_permission_dialog_message"/><Str value="To enable photo capture, this app requires access to your camera. Please grant permission by opening the Settings app."/></HashCell><HashCell><Str value="camera_permission_dialog_title"/><Str value="Allow Twyns App to Access Your Camera"/></HashCell><HashCell><Str value="camera_permission_scan_dialog_message"/><Str value="To enable document scanning, this app requires access to your camera. Please grant permission by opening the Settings app."/></HashCell><HashCell><Str value="cancel"/><Str value="Cancel"/></HashCell><HashCell><Str value="drivingCard"/><Str value="Driving licence"/></HashCell><HashCell><Str value="e_area0001"/><Str value="Not an appropriate area"/></HashCell><HashCell><Str value="e_area0002"/><Str value="Something went wrong trying to find the area:"/></HashCell><HashCell><Str value="e_log0001"/><Str value="Login error"/></HashCell><HashCell><Str value="e_log0002"/><Str value="There was a login error looking up the Employee information"/></HashCell><HashCell><Str value="e_log0003"/><Str value="Login error"/></HashCell><HashCell><Str value="e_log0004"/><Str value="Login error, try again please."/></HashCell><HashCell><Str value="e_mand0002"/><Str value="The following mandatory fields are not filled:"/></HashCell><HashCell><Str value="e_mandByLawNotFound"/><Str value="Mandatory bylaw not found"/></HashCell><HashCell><Str value="e_multipleByLaws"/><Str value="Multiple bylaws found, contact your system administrator"/></HashCell><HashCell><Str value="e_noInstanceparameter"/><Str value="Instance parameter not found"/></HashCell><HashCell><Str value="e_prt0001"/><Str value="Printing failed, please check if the printer is connected and try again"/></HashCell><HashCell><Str value="e_select_zone"/><Str value="Select a zone"/></HashCell><HashCell><Str value="e_ser0001"/><Str value="Service call could not be completed successfuly."/></HashCell><HashCell><Str value="e_ser0002"/><Str value="Query cannot be processed, try alternate input"/></HashCell><HashCell><Str value="e_ser0003"/><Str value="Service not available"/></HashCell><HashCell><Str value="e_serverError"/><Str value="Parking check failure"/></HashCell><HashCell><Str value="e_serverErrorNPR_1"/><Str value="Parking check for non-regulated zone"/></HashCell><HashCell><Str value="e_serverErrorNPR_100"/><Str value="Area is not provided "/></HashCell><HashCell><Str value="e_serverErrorNPR_104"/><Str value="Checking organisation has no authorisation to check "/></HashCell><HashCell><Str value="e_serverErrorNPR_302"/><Str value="Technical failure, please contact Service House Parking "/></HashCell><HashCell><Str value="e_serverErrorNPR_5"/><Str value="No PSRight required"/></HashCell><HashCell><Str value="e_serverErrorNPR_999"/><Str value="Technical failure, please contact ICT Servicedesk "/></HashCell><HashCell><Str value="e_sync0001"/><Str value="There was an error during synchronization initialization."/></HashCell><HashCell><Str value="e_sync0002"/><Str value="Synchronization error"/></HashCell><HashCell><Str value="e_sync0003"/><Str value="Not all information is filled out"/></HashCell><HashCell><Str value="e_upgradeError"/><Str value="Error during upgrade, please contact support."/></HashCell><HashCell><Str value="e_userNotFound"/><Str value="Username not found. Please contact support. Application will close."/></HashCell><HashCell><Str value="e_val00004"/><Str value="No streets found"/></HashCell><HashCell><Str value="e_val00005"/><Str value="No areas found"/></HashCell><HashCell><Str value="e_val00006"/><Str value="Refresh your GPS coordinates."/></HashCell><HashCell><Str value="e_val00007"/><Str value="This is not a valid parkin area"/></HashCell><HashCell><Str value="e_val00008"/><Str value="No location selected"/></HashCell><HashCell><Str value="e_val00009"/><Str value="No label scanned"/></HashCell><HashCell><Str value="e_val00010"/><Str value="No photo added"/></HashCell><HashCell><Str value="hdr_default"/><Str value="Default country"/></HashCell><HashCell><Str value="hdr_manual"/><Str value="MANUAL"/></HashCell><HashCell><Str value="hdr_rdw"/><Str value="Vehicle Details"/></HashCell><HashCell><Str value="hdr_scan"/><Str value="SCAN"/></HashCell><HashCell><Str value="hdr_scanRdw"/><Str value="SCAN + Vehicle Details"/></HashCell><HashCell><Str value="i_anpr0001"/><Str value="No license plate detected"/></HashCell><HashCell><Str value="i_anpr0002"/><Str value="No matching licenseplate pattern"/></HashCell><HashCell><Str value="i_confirmsync"/><Str value="Your last synchronization was ~~~, do you want to synchronize now?"/></HashCell><HashCell><Str value="i_connectPrinterFailed"/><Str value="Connection to the printer failed, please check if bluetooth is turned on or choose to add a printer"/></HashCell><HashCell><Str value="i_daysago"/><Str value="days ago"/></HashCell><HashCell><Str value="i_deceased"/><Str value="Person is deceased"/></HashCell><HashCell><Str value="i_declinedLegalAssistance"/><Str value="Suspect has been told he can reclaim his right to legal assistance"/></HashCell><HashCell><Str value="i_emigrate"/><Str value="Person has emigrated"/></HashCell><HashCell><Str value="i_faulted"/><Str value="Faulted"/></HashCell><HashCell><Str value="i_fsc0001"/><Str value="Service invocation error"/></HashCell><HashCell><Str value="i_fsc0002"/><Str value="No vehicle information found"/></HashCell><HashCell><Str value="i_fsc0007"/><Str value="No network available"/></HashCell><HashCell><Str value="i_hoursago"/><Str value="hours ago"/></HashCell><HashCell><Str value="i_interpretationCommunicated"/><Str value="Right to interpretation communicated"/></HashCell><HashCell><Str value="i_interpretationNotCommunicated"/><Str value="Right to interpretation not communicated"/></HashCell><HashCell><Str value="i_interpreter"/><Str value="Id of interpreter"/></HashCell><HashCell><Str value="i_legalAssistanceCommunicated"/><Str value="Right to legal assistance communicated"/></HashCell><HashCell><Str value="i_legalAssistanceNotCommunicated"/><Str value="Right to legal assistance not communicated"/></HashCell><HashCell><Str value="i_noCities"/><Str value="No cities"/></HashCell><HashCell><Str value="i_noMunicipalities"/><Str value="No municipalities"/></HashCell><HashCell><Str value="i_no_person"/><Str value="No person has been filled in"/></HashCell><HashCell><Str value="i_notDeclinedLegalAssistance"/><Str value="Suspect has not been told he can reclaim his right to legal assistance"/></HashCell><HashCell><Str value="i_notUsesThisRight"/><Str value="Suspect did not want to make use of this right"/></HashCell><HashCell><Str value="i_offenceCommunicated"/><Str value="Offence communicated"/></HashCell><HashCell><Str value="i_offenceNotCommunicated"/><Str value="Offence not communicated"/></HashCell><HashCell><Str value="i_personNotFound"/><Str value="Person not found"/></HashCell><HashCell><Str value="i_restart"/><Str value="The application has been idle for more then #idletimeout# minutes,                                                                                                            would you like to restart?"/></HashCell><HashCell><Str value="i_select_street"/><Str value="Select street"/></HashCell><HashCell><Str value="i_syncwarning"/><Str value="Your last synchronization was ~~~, this is more then seven days ago you need to sync now."/></HashCell><HashCell><Str value="i_tooManyPersonRecords"/><Str value="Too many persons found, please fill in more fields to specify"/></HashCell><HashCell><Str value="i_translationLanguage"/><Str value="The hearing took place in the suspect intelligible language, namely"/></HashCell><HashCell><Str value="i_usesThisRight"/><Str value="Suspect wants to make use of this right"/></HashCell><HashCell><Str value="idCard"/><Str value="Identity Card"/></HashCell><HashCell><Str value="l_2Characterinput"/><Str value="Type 2 characters"/></HashCell><HashCell><Str value="l_3Characterinput"/><Str value="Type 3 characters"/></HashCell><HashCell><Str value="l_Diagnostics"/><Str value="Diagnostics"/></HashCell><HashCell><Str value="l_HouseNumberMustBeNumeric"/><Str value="House number must be numeric"/></HashCell><HashCell><Str value="l_NSproduction"/><Str value="NS"/></HashCell><HashCell><Str value="l_NSproductionEducation"/><Str value="NS education"/></HashCell><HashCell><Str value="l_NoPlate"/><Str value="No plate"/></HashCell><HashCell><Str value="l_NoPlates"/><Str value="No plates found"/></HashCell><HashCell><Str value="l_NoPlatesConfirmation"/><Str value="No plates found, do you want to refresh the list?"/></HashCell><HashCell><Str value="l_NoRefreshPlate"/><Str value="Plate not longer in list"/></HashCell><HashCell><Str value="l_OVdemo"/><Str value="OV demo"/></HashCell><HashCell><Str value="l_OffenceCommunicatedNoStatement"/><Str value="Statement offence not communicated"/></HashCell><HashCell><Str value="l_Pin"/><Str value="Pin number"/></HashCell><HashCell><Str value="l_RDWowner"/><Str value="Owner"/></HashCell><HashCell><Str value="l_RDWownerCheck"/><Str value="Vehicle + Owner check"/></HashCell><HashCell><Str value="l_StatementPledge"/><Str value="Pledge"/></HashCell><HashCell><Str value="l_WatchGPSInterval"/><Str value="Number of seconds before route refresh"/></HashCell><HashCell><Str value="l_about"/><Str value="About"/></HashCell><HashCell><Str value="l_acceptance"/><Str value="Acceptance"/></HashCell><HashCell><Str value="l_acceptanceTest"/><Str value="Acceptance test"/></HashCell><HashCell><Str value="l_acceptance_pt"/><Str value="Acceptance Pub. Tp."/></HashCell><HashCell><Str value="l_activeCases"/><Str value="Add to case"/></HashCell><HashCell><Str value="l_addAddressWithoutGPS"/><Str value="Manually entering the address is not possible without GPS"/></HashCell><HashCell><Str value="l_addData"/><Str value="Do you want to complete the case or add some more data?"/></HashCell><HashCell><Str value="l_addLabel"/><Str value="Add label"/></HashCell><HashCell><Str value="l_addTax"/><Str value="Additional costs"/></HashCell><HashCell><Str value="l_addTaxDisplay"/><Str value="Add. costs"/></HashCell><HashCell><Str value="l_additionalAssesment"/><Str value="Additional assesment"/></HashCell><HashCell><Str value="l_address"/><Str value="Address"/></HashCell><HashCell><Str value="l_addressUnderInvestigation"/><Str value="Address under investigation"/></HashCell><HashCell><Str value="l_administrationcosts"/><Str value="Handling fee"/></HashCell><HashCell><Str value="l_all"/><Str value="All"/></HashCell><HashCell><Str value="l_allowed"/><Str value="Allowed"/></HashCell><HashCell><Str value="l_amountDue"/><Str value="Amount due"/></HashCell><HashCell><Str value="l_amountSanction"/><Str value="Sanction amount"/></HashCell><HashCell><Str value="l_applyLabel"/><Str value="Apply label"/></HashCell><HashCell><Str value="l_applyfilter"/><Str value="Apply filter"/></HashCell><HashCell><Str value="l_area"/><Str value="Area"/></HashCell><HashCell><Str value="l_areas"/><Str value="Areas"/></HashCell><HashCell><Str value="l_askAdministrator"/><Str value="Ask administrator"/></HashCell><HashCell><Str value="l_assessmentNumber"/><Str value="Assessment number"/></HashCell><HashCell><Str value="l_assessor"/><Str value="Assessor"/></HashCell><HashCell><Str value="l_assignedArea"/><Str value="Assigned area"/></HashCell><HashCell><Str value="l_attempt"/><Str value="Attempt"/></HashCell><HashCell><Str value="l_authenticating_user"/><Str value="Authenticating user"/></HashCell><HashCell><Str value="l_auto_restart_enabled"/><Str value="Auto Restart Enabled"/></HashCell><HashCell><Str value="l_auto_scan_time"/><Str value="Auto Scan Time"/></HashCell><HashCell><Str value="l_averages"/><Str value="Averages"/></HashCell><HashCell><Str value="l_batches"/><Str value="Batches"/></HashCell><HashCell><Str value="l_birthdate"/><Str value="Birthdate"/></HashCell><HashCell><Str value="l_branchNumber"/><Str value="Branchn umber"/></HashCell><HashCell><Str value="l_brand"/><Str value="Brand"/></HashCell><HashCell><Str value="l_brandtype"/><Str value="Type"/></HashCell><HashCell><Str value="l_building"/><Str value="Building"/></HashCell><HashCell><Str value="l_building100chars"/><Str value="Additional building info (100 chars)"/></HashCell><HashCell><Str value="l_bus"/><Str value="Bus"/></HashCell><HashCell><Str value="l_by"/><Str value="By"/></HashCell><HashCell><Str value="l_calculatedPrice"/><Str value="Calculated price"/></HashCell><HashCell><Str value="l_cancel"/><Str value="Cancel"/></HashCell><HashCell><Str value="l_cannotRegisterYourself"/><Str value="You cannot register yourself"/></HashCell><HashCell><Str value="l_cannotUseAddress"/><Str value=" This address is not in an enforcement location and therefore cannot be used. Do you want to continue anyway?"/></HashCell><HashCell><Str value="l_car"/><Str value="Car"/></HashCell><HashCell><Str value="l_cardNumber"/><Str value="card number"/></HashCell><HashCell><Str value="l_cardcheck"/><Str value="Card check"/></HashCell><HashCell><Str value="l_cards"/><Str value="Cards"/></HashCell><HashCell><Str value="l_case"/><Str value="Case"/></HashCell><HashCell><Str value="l_caseAndTaskTypeNotFound"/><Str value="There was no CaseType and/or TaskType found please contact the system administrator."/></HashCell><HashCell><Str value="l_caseNoLongerOnServer"/><Str value="Case no longer available on server, you will be taken back to the beginning"/></HashCell><HashCell><Str value="l_caseNotFound"/><Str value="Case not found"/></HashCell><HashCell><Str value="l_caseNotRetrieved"/><Str value="Something went wrong retrieving the case, please try again"/></HashCell><HashCell><Str value="l_caseOnlineFailedOutboxQuestion"/><Str value="Unable to update case online, case will be put into the outbox. Do you want to try again or click done and try to send it later?"/></HashCell><HashCell><Str value="l_caseOnlineFailedSetToOutbox"/><Str value="Unable to update case online, case will be put into the outbox."/></HashCell><HashCell><Str value="l_caseType"/><Str value="Case type"/></HashCell><HashCell><Str value="l_caseUpdateFailed"/><Str value="Case update failed, please try again"/></HashCell><HashCell><Str value="l_caseWillBeClosed"/><Str value="This case will be closed."/></HashCell><HashCell><Str value="l_case_not_opened"/><Str value="Case can not be opened"/></HashCell><HashCell><Str value="l_cases"/><Str value="Cases"/></HashCell><HashCell><Str value="l_casesToBeUpdated"/><Str value="To be updated"/></HashCell><HashCell><Str value="l_cashAmount"/><Str value="Cash amount"/></HashCell><HashCell><Str value="l_changeAddress"/><Str value="Change address"/></HashCell><HashCell><Str value="l_changeCaseTo"/><Str value="Do you want to change the case to:"/></HashCell><HashCell><Str value="l_changeEnvironment"/><Str value="If you change the environment all data will be reset and you will have to log in again, do you want to do this?"/></HashCell><HashCell><Str value="l_changeFilter"/><Str value="Change filter"/></HashCell><HashCell><Str value="l_changeHouseNumber"/><Str value="Change house number"/></HashCell><HashCell><Str value="l_changeInstance"/><Str value="If you change the instance all data will be reset, do you want to do this?"/></HashCell><HashCell><Str value="l_changePosition"/><Str value="Change position"/></HashCell><HashCell><Str value="l_changeTheme"/><Str value="Change theme"/></HashCell><HashCell><Str value="l_changeTravelMode"/><Str value="Change travel mode"/></HashCell><HashCell><Str value="l_channel"/><Str value="Channel"/></HashCell><HashCell><Str value="l_check"/><Str value="Check"/></HashCell><HashCell><Str value="l_checkBounds"/><Str value="Value must be between #lower# and #upper#"/></HashCell><HashCell><Str value="l_checkBoundsValue"/><Str value="#value# is not a valid number"/></HashCell><HashCell><Str value="l_checkLicensePlate"/><Str value="Check license plate"/></HashCell><HashCell><Str value="l_checkPin"/><Str value="Type in current pin"/></HashCell><HashCell><Str value="l_checkTime"/><Str value="Time checked"/></HashCell><HashCell><Str value="l_checkUserDatabase"/><Str value="Checking user in database ..."/></HashCell><HashCell><Str value="l_checkUserPV"/><Str value="Checking user PV ..."/></HashCell><HashCell><Str value="l_check_types"/><Str value="Check Types"/></HashCell><HashCell><Str value="l_checkcard"/><Str value="Check card"/></HashCell><HashCell><Str value="l_checked"/><Str value="Checked"/></HashCell><HashCell><Str value="l_checkedOut"/><Str value="Checked out"/></HashCell><HashCell><Str value="l_checkedOutFollow"/><Str value="Chekced out for follow up"/></HashCell><HashCell><Str value="l_checking_service"/><Str value="Checking service access"/></HashCell><HashCell><Str value="l_checklocation"/><Str value="Check location"/></HashCell><HashCell><Str value="l_checkout"/><Str value="Check out"/></HashCell><HashCell><Str value="l_checks"/><Str value="Checks"/></HashCell><HashCell><Str value="l_checkvehicle"/><Str value="Check vehicle"/></HashCell><HashCell><Str value="l_choose"/><Str value="make a choice"/></HashCell><HashCell><Str value="l_chooseCity"/><Str value="Choose City"/></HashCell><HashCell><Str value="l_chooseHost"/><Str value="Please select a host location."/></HashCell><HashCell><Str value="l_chooseMunicipality"/><Str value="Choose Municipality"/></HashCell><HashCell><Str value="l_chooseOneCheckType"/><Str value="Choose at least one checktype"/></HashCell><HashCell><Str value="l_chooseTaskOutcome"/><Str value="Choose a task outcome"/></HashCell><HashCell><Str value="l_chooseTeam"/><Str value="Choose team"/></HashCell><HashCell><Str value="l_chooseZone"/><Str value="Choose Zone"/></HashCell><HashCell><Str value="l_choosearea"/><Str value="Choose area"/></HashCell><HashCell><Str value="l_chooselabel"/><Str value="Choose label"/></HashCell><HashCell><Str value="l_choosestreet"/><Str value="Choose street"/></HashCell><HashCell><Str value="l_choosetask"/><Str value="Choose task"/></HashCell><HashCell><Str value="l_choosevalue"/><Str value="Choose value"/></HashCell><HashCell><Str value="l_chosenZone"/><Str value="Chosen zone"/></HashCell><HashCell><Str value="l_chosenaddress"/><Str value="Chosen address"/></HashCell><HashCell><Str value="l_city"/><Str value="City"/></HashCell><HashCell><Str value="l_city36chars"/><Str value="City (36 chars)"/></HashCell><HashCell><Str value="l_cityNotOffenceCity"/><Str value="The found location is not in the enforcement area. Please choose another street or try again. Found location:"/></HashCell><HashCell><Str value="l_claimFailed"/><Str value="Something went wrong claiming the case, please try again"/></HashCell><HashCell><Str value="l_claimedByMe"/><Str value="Claimed by me"/></HashCell><HashCell><Str value="l_claimedByOther"/><Str value="Claimed by other"/></HashCell><HashCell><Str value="l_claimedByOtherRetrievingCases"/><Str value="This case is claimed by someone else, refreshing case list. Please try selecting another case."/></HashCell><HashCell><Str value="l_clamp"/><Str value="Clamp"/></HashCell><HashCell><Str value="l_clampNumber"/><Str value="Clamp number"/></HashCell><HashCell><Str value="l_clampNumberNotFound"/><Str value="Clamp number not found"/></HashCell><HashCell><Str value="l_clampTime"/><Str value="Clamp time"/></HashCell><HashCell><Str value="l_clickToAddPhotos"/><Str value="Click here to add photos"/></HashCell><HashCell><Str value="l_close"/><Str value="Close"/></HashCell><HashCell><Str value="l_closePDF"/><Str value="Close PDF"/></HashCell><HashCell><Str value="l_cocnumber"/><Str value="CoC number"/></HashCell><HashCell><Str value="l_code"/><Str value="Code"/></HashCell><HashCell><Str value="l_color"/><Str value="Color"/></HashCell><HashCell><Str value="l_concepts"/><Str value="Concepts"/></HashCell><HashCell><Str value="l_confirm_removelist"/><Str value="Do you want to delete this list?"/></HashCell><HashCell><Str value="l_connectiondescription"/><Str value="Connection description"/></HashCell><HashCell><Str value="l_continueOffline"/><Str value="Continue offline"/></HashCell><HashCell><Str value="l_continueWithoutConnection"/><Str value="Continue without connection"/></HashCell><HashCell><Str value="l_coordinates"/><Str value="GPS"/></HashCell><HashCell><Str value="l_countries"/><Str value="Countries"/></HashCell><HashCell><Str value="l_country"/><Str value="Country"/></HashCell><HashCell><Str value="l_countryPlateUnknownChange"/><Str value="Country of the licenseplate is unknown, are you sure you want to continue?"/></HashCell><HashCell><Str value="l_countryUnknown"/><Str value="Unknown"/></HashCell><HashCell><Str value="l_countryUnknownCode"/><Str value="UNK"/></HashCell><HashCell><Str value="l_countryadress"/><Str value="Country address"/></HashCell><HashCell><Str value="l_countryoforigin"/><Str value="Country of origin"/></HashCell><HashCell><Str value="l_createPin"/><Str value="Create a pin number"/></HashCell><HashCell><Str value="l_currentLocation"/><Str value="Current location"/></HashCell><HashCell><Str value="l_currentPosition"/><Str value="Current Position"/></HashCell><HashCell><Str value="l_currentValidity"/><Str value="Current validity"/></HashCell><HashCell><Str value="l_current_task"/><Str value="Current task"/></HashCell><HashCell><Str value="l_dashesOnly"/><Str value="It is not allowed to add anything other than dashes"/></HashCell><HashCell><Str value="l_date"/><Str value="Date"/></HashCell><HashCell><Str value="l_dateTimeFuture"/><Str value="Date and time are too far in the future"/></HashCell><HashCell><Str value="l_dateTimePast"/><Str value="Date and time are too far in the past"/></HashCell><HashCell><Str value="l_datetime"/><Str value="Date and time"/></HashCell><HashCell><Str value="l_daysMonth"/><Str value="This month has no ~~ days"/></HashCell><HashCell><Str value="l_declinedLegalAssistance"/><Str value="Reclaim right legal assistance"/></HashCell><HashCell><Str value="l_description"/><Str value="Description"/></HashCell><HashCell><Str value="l_details"/><Str value="Details"/></HashCell><HashCell><Str value="l_development"/><Str value="Development"/></HashCell><HashCell><Str value="l_digit_count"/><Str value="Please enter a 5 or 9 digit number"/></HashCell><HashCell><Str value="l_directPerfectview"/><Str value="Direct PerfectView"/></HashCell><HashCell><Str value="l_disabled_parking"/><Str value="Disabled Parking"/></HashCell><HashCell><Str value="l_discard"/><Str value="Discard"/></HashCell><HashCell><Str value="l_distance"/><Str value="Distance"/></HashCell><HashCell><Str value="l_doNotChangePlate"/><Str value="Please do not change the number of the license plate, only set the dashes ( - ) in the correct place"/></HashCell><HashCell><Str value="l_doNotHaveFingerprints"/><Str value="You have not registered any fingerprints on your device"/></HashCell><HashCell><Str value="l_doNotHaveLogOnCode"/><Str value="You have not set a verification code on your device"/></HashCell><HashCell><Str value="l_doYouWantToPasteData"/><Str value="Do you want to paste the data:"/></HashCell><HashCell><Str value="l_documentAlreadyProcessedException"/><Str value="Case already processed and no longer available"/></HashCell><HashCell><Str value="l_documentNotFoundException"/><Str value="Case not found"/></HashCell><HashCell><Str value="l_duplicate"/><Str value="Duplicate"/></HashCell><HashCell><Str value="l_duplicateScan"/><Str value="Second scan of the same vehicle in short time"/></HashCell><HashCell><Str value="l_dutch"/><Str value="Dutch"/></HashCell><HashCell><Str value="l_editAddress"/><Str value="Edit address"/></HashCell><HashCell><Str value="l_editData"/><Str value="Contact your system manager or edit the data"/></HashCell><HashCell><Str value="l_editExistingOptionsVariables"/><Str value="Do you want to view the existing text or enter a new one?"/></HashCell><HashCell><Str value="l_editIdentification"/><Str value="Edit identification"/></HashCell><HashCell><Str value="l_editKindOfViolation"/><Str value="Edit violation"/></HashCell><HashCell><Str value="l_editLicensePlate"/><Str value="Edit license plate"/></HashCell><HashCell><Str value="l_editTime"/><Str value="Edit time"/></HashCell><HashCell><Str value="l_email"/><Str value="E-mail address"/></HashCell><HashCell><Str value="l_emmDataInCorrect"/><Str value="The data to synchronize have not been registered correctly in EMM, contact support please."/></HashCell><HashCell><Str value="l_emmNotCorrect"/><Str value="The data comming from EMM is not correct please contact your system manager"/></HashCell><HashCell><Str value="l_employeeNumberPrint"/><Str value="Employee no."/></HashCell><HashCell><Str value="l_emptyPrinterMac"/><Str value="00:00:00:00:00:00"/></HashCell><HashCell><Str value="l_emptyPrinterName"/><Str value="None"/></HashCell><HashCell><Str value="l_end"/><Str value="End"/></HashCell><HashCell><Str value="l_endOfList"/><Str value="End of list"/></HashCell><HashCell><Str value="l_endTime"/><Str value="Time ended"/></HashCell><HashCell><Str value="l_enforcementObject"/><Str value="Legal entity"/></HashCell><HashCell><Str value="l_english"/><Str value="English"/></HashCell><HashCell><Str value="l_enter5DigitPin"/><Str value="Enter a 5-digit pin"/></HashCell><HashCell><Str value="l_enterNumber"/><Str value="Enter a number"/></HashCell><HashCell><Str value="l_enterPin"/><Str value="Enter a pin"/></HashCell><HashCell><Str value="l_enterUsernameFirst"/><Str value="Enter username first before environment list can be activated!"/></HashCell><HashCell><Str value="l_enteraddress"/><Str value="No address found, enter address"/></HashCell><HashCell><Str value="l_environment"/><Str value="Environment"/></HashCell><HashCell><Str value="l_environmentListActivated"/><Str value="Environments list is now activated!"/></HashCell><HashCell><Str value="l_environmentalClassification"/><Str value="Environmental Classification"/></HashCell><HashCell><Str value="l_environmentalClassificationPrefix"/><Str value="Euro"/></HashCell><HashCell><Str value="l_error"/><Str value="Error"/></HashCell><HashCell><Str value="l_errorCode"/><Str value="Error code"/></HashCell><HashCell><Str value="l_errorDuringUpgrade"/><Str value="Error during upgrade,                                                                                                                                           please contact support"/></HashCell><HashCell><Str value="l_errorUserCheck"/><Str value="Error while checking/creating user,                                                                                                                             please try again or edit the data"/></HashCell><HashCell><Str value="l_errored"/><Str value="Errored"/></HashCell><HashCell><Str value="l_europeanVehicleType"/><Str value="European Vehicle Type"/></HashCell><HashCell><Str value="l_execByPartner"/><Str value="Interrogated by Partner"/></HashCell><HashCell><Str value="l_executing_anpr"/><Str value="Executing ANPR"/></HashCell><HashCell><Str value="l_exemption"/><Str value="Exemption"/></HashCell><HashCell><Str value="l_exemptionstartdate"/><Str value="Exemption start date"/></HashCell><HashCell><Str value="l_existingOptionsVariables"/><Str value="Do you want to change the existing text?"/></HashCell><HashCell><Str value="l_exit"/><Str value="Exit"/></HashCell><HashCell><Str value="l_exitApplication"/><Str value="App will be closed"/></HashCell><HashCell><Str value="l_exp_date"/><Str value="Expires on"/></HashCell><HashCell><Str value="l_explanation"/><Str value="Explanation"/></HashCell><HashCell><Str value="l_externalcollector"/><Str value="External collector"/></HashCell><HashCell><Str value="l_extraInformation"/><Str value="Extra information"/></HashCell><HashCell><Str value="l_favorites"/><Str value="Favorits"/></HashCell><HashCell><Str value="l_fillStreetCity"/><Str value="Please fill in a street and a city"/></HashCell><HashCell><Str value="l_filter"/><Str value="Filter"/></HashCell><HashCell><Str value="l_fineWithdrawn"/><Str value="You have chosen 'Cautie: No', this means that the fine will be withdrawn is this correct?"/></HashCell><HashCell><Str value="l_finished"/><Str value="Finished"/></HashCell><HashCell><Str value="l_firstName"/><Str value="First name"/></HashCell><HashCell><Str value="l_flash_auto"/><Str value="Flash auto"/></HashCell><HashCell><Str value="l_flash_off"/><Str value="Flash off"/></HashCell><HashCell><Str value="l_flash_on"/><Str value="Flash on"/></HashCell><HashCell><Str value="l_folderNotFound"/><Str value="No folders found"/></HashCell><HashCell><Str value="l_follow"/><Str value="Follow up"/></HashCell><HashCell><Str value="l_followUp"/><Str value="Follow up"/></HashCell><HashCell><Str value="l_found"/><Str value="Found"/></HashCell><HashCell><Str value="l_foundHistoricCase"/><Str value="Already written ticket found"/></HashCell><HashCell><Str value="l_fuel"/><Str value="Fuel"/></HashCell><HashCell><Str value="l_fullName"/><Str value="Full name"/></HashCell><HashCell><Str value="l_fullname"/><Str value="Full name"/></HashCell><HashCell><Str value="l_gender"/><Str value="Gender"/></HashCell><HashCell><Str value="l_georgia"/><Str value="Georgia"/></HashCell><HashCell><Str value="l_germanPlateDash"/><Str value="This is a german license plate, please set dashes ( - ) in the correct place"/></HashCell><HashCell><Str value="l_germany"/><Str value="Germany"/></HashCell><HashCell><Str value="l_getDataFailed"/><Str value="Sorry, loading the data has failed."/></HashCell><HashCell><Str value="l_getParkRegistrations"/><Str value="Getting parking registrations..."/></HashCell><HashCell><Str value="l_getPreviousFines"/><Str value="Get previous issued fines"/></HashCell><HashCell><Str value="l_getZonesTerminals"/><Str value="Getting zones and terminals..."/></HashCell><HashCell><Str value="l_give5NumberPin"/><Str value="You must enter a 5-digit PIN"/></HashCell><HashCell><Str value="l_giveValidParkingRight"/><Str value="Are you sure you want to give out a valid parking right?"/></HashCell><HashCell><Str value="l_gps_no_fix"/><Str value="No GPS Fix found. You can manually enter your location."/></HashCell><HashCell><Str value="l_gps_refresh"/><Str value="Refreshing GPS location ..."/></HashCell><HashCell><Str value="l_handle"/><Str value="Handle"/></HashCell><HashCell><Str value="l_handling"/><Str value="Handling"/></HashCell><HashCell><Str value="l_hasParkingRight"/><Str value="Has parkingRight"/></HashCell><HashCell><Str value="l_hasValidParkingRight"/><Str value="A valid parking right is given"/></HashCell><HashCell><Str value="l_haveToUpdate"/><Str value="This version #currentversion# of the app is no longer supported please update your app to #newversion#. You will be redirected to the download page."/></HashCell><HashCell><Str value="l_haveToUpdateMDM"/><Str value="This version #currentversion# of the app is no longer supported please update your app to #newversion#. Ask your administrator to update the app."/></HashCell><HashCell><Str value="l_haveToUpdateStoreMDM"/><Str value="This version #currentversion# of the app is no longer supported please update your app to #newversion#. Download the app from the appstore or ask your administrator to release the new version if the app is pushed to your device."/></HashCell><HashCell><Str value="l_hectometerMarkersNearby"/><Str value="Hectometer makers nearby"/></HashCell><HashCell><Str value="l_history"/><Str value="History"/></HashCell><HashCell><Str value="l_hostlocation"/><Str value="Host location"/></HashCell><HashCell><Str value="l_hours"/><Str value="Hours"/></HashCell><HashCell><Str value="l_houseNumberAdd"/><Str value="House number addition"/></HashCell><HashCell><Str value="l_houseNumberAdd7chars"/><Str value="House number addition (7 chars)"/></HashCell><HashCell><Str value="l_houseNumberAddition"/><Str value="House number addition"/></HashCell><HashCell><Str value="l_houseNumberAddition_short"/><Str value="add..."/></HashCell><HashCell><Str value="l_houseletter"/><Str value="House letter"/></HashCell><HashCell><Str value="l_houseletter_short"/><Str value="letter"/></HashCell><HashCell><Str value="l_housenumber"/><Str value="House number"/></HashCell><HashCell><Str value="l_housenumber5chars"/><Str value="House number (5 chars)"/></HashCell><HashCell><Str value="l_housenumber_short"/><Str value="number"/></HashCell><HashCell><Str value="l_imageGallery"/><Str value="Image gallery"/></HashCell><HashCell><Str value="l_info"/><Str value="Information"/></HashCell><HashCell><Str value="l_initDBFailed"/><Str value="The loading of the necessary data failed. The app will be cleaned and you will have to log in again."/></HashCell><HashCell><Str value="l_initialize"/><Str value="Initialize"/></HashCell><HashCell><Str value="l_initialize_text"/><Str value="Your device is not initialized to use this service. Please enter your username to register this device. When support activated your account you can start using the application."/></HashCell><HashCell><Str value="l_initialize_title"/><Str value="Initialize"/></HashCell><HashCell><Str value="l_initials"/><Str value="Initials"/></HashCell><HashCell><Str value="l_inserted"/><Str value="Inserted"/></HashCell><HashCell><Str value="l_inspectionExpirationDate"/><Str value="Inspection Expiration Date"/></HashCell><HashCell><Str value="l_instance"/><Str value="Instance"/></HashCell><HashCell><Str value="l_insuranceDate"/><Str value="Insurance Date"/></HashCell><HashCell><Str value="l_internalRemark"/><Str value="Internal remark"/></HashCell><HashCell><Str value="l_interpreterCommunicated"/><Str value="Right to us a translator communicated"/></HashCell><HashCell><Str value="l_interpreterNR"/><Str value="Interperter number"/></HashCell><HashCell><Str value="l_invalid"/><Str value="Invalid"/></HashCell><HashCell><Str value="l_invalidLoginCredentails"/><Str value="The login details do not match, you must log in again. The data will be erased."/></HashCell><HashCell><Str value="l_isNotFilled"/><Str value="is not filled"/></HashCell><HashCell><Str value="l_keepLocation"/><Str value="Keep location"/></HashCell><HashCell><Str value="l_kind"/><Str value="Kind"/></HashCell><HashCell><Str value="l_kindOfHandling"/><Str value="Kind of handling"/></HashCell><HashCell><Str value="l_kindOfLabel"/><Str value="Kind of label"/></HashCell><HashCell><Str value="l_kindOfRoad"/><Str value="Kind of road"/></HashCell><HashCell><Str value="l_kindOfVehicle"/><Str value="Kind of vehicle"/></HashCell><HashCell><Str value="l_label"/><Str value="Label"/></HashCell><HashCell><Str value="l_labelCheck"/><Str value="Check label"/></HashCell><HashCell><Str value="l_labelExists"/><Str value="This label already exists in the system"/></HashCell><HashCell><Str value="l_labelOverview"/><Str value="Label overview"/></HashCell><HashCell><Str value="l_labelPhoto"/><Str value="Label photo"/></HashCell><HashCell><Str value="l_lastChosen"/><Str value="Last chosen"/></HashCell><HashCell><Str value="l_lastChosenOutcome"/><Str value="Last chosen outcome"/></HashCell><HashCell><Str value="l_lastsync"/><Str value="Last sync"/></HashCell><HashCell><Str value="l_legalAssistCommunicated"/><Str value="Right to use legal assistance communicated"/></HashCell><HashCell><Str value="l_legalAssistance"/><Str value="Right to assistance communicated"/></HashCell><HashCell><Str value="l_legalForm"/><Str value="Legal form"/></HashCell><HashCell><Str value="l_licensePlateCountryUnknown"/><Str value="Country of the licenseplate is unknown"/></HashCell><HashCell><Str value="l_licenseplate"/><Str value="License plate"/></HashCell><HashCell><Str value="l_licenseplateCountries"/><Str value="License plate Countries"/></HashCell><HashCell><Str value="l_list"/><Str value="List"/></HashCell><HashCell><Str value="l_listNotAvailable"/><Str value=" No changes possible, list is not available (anymore)"/></HashCell><HashCell><Str value="l_loadLicenseplate"/><Str value="Processing License plate"/></HashCell><HashCell><Str value="l_loadPhoto"/><Str value="Processing photo"/></HashCell><HashCell><Str value="l_load_map"/><Str value="Load map"/></HashCell><HashCell><Str value="l_loading"/><Str value="Loading"/></HashCell><HashCell><Str value="l_loadingParam"/><Str value="Loading parameters"/></HashCell><HashCell><Str value="l_location"/><Str value="Location"/></HashCell><HashCell><Str value="l_locationFound"/><Str value="Location found"/></HashCell><HashCell><Str value="l_locationVerified"/><Str value="Location used"/></HashCell><HashCell><Str value="l_logOffDate"/><Str value="Logoff date"/></HashCell><HashCell><Str value="l_logOffTime"/><Str value="Logoff time"/></HashCell><HashCell><Str value="l_logOn"/><Str value="Log in"/></HashCell><HashCell><Str value="l_logOnDate"/><Str value="Logon date"/></HashCell><HashCell><Str value="l_logOnDuration"/><Str value="Logon duration"/></HashCell><HashCell><Str value="l_logOnStatus"/><Str value="Logon status"/></HashCell><HashCell><Str value="l_logOnTime"/><Str value="Logon time"/></HashCell><HashCell><Str value="l_logOut"/><Str value="Log out"/></HashCell><HashCell><Str value="l_loggingOut"/><Str value="Logging out"/></HashCell><HashCell><Str value="l_login"/><Str value="Log in"/></HashCell><HashCell><Str value="l_loginAccount"/><Str value="Log in with your account"/></HashCell><HashCell><Str value="l_loginAuthorisation"/><Str value="You are not authorized to log in"/></HashCell><HashCell><Str value="l_loginInstance"/><Str value="Choose your instance"/></HashCell><HashCell><Str value="l_loginName"/><Str value="Login"/></HashCell><HashCell><Str value="l_loginNoFunctions"/><Str value="You have no functions / roles for this application. Your current roles are: "/></HashCell><HashCell><Str value="l_loginNoOfficerId"/><Str value="You cannot log in, officer identification is missing. Contact system administration."/></HashCell><HashCell><Str value="l_loginRegister"/><Str value="Enter your login details to register."/></HashCell><HashCell><Str value="l_loginServer"/><Str value="Choose the environment to sign up for"/></HashCell><HashCell><Str value="l_loginText1"/><Str value="You log in to the"/></HashCell><HashCell><Str value="l_loginText2"/><Str value="environment as"/></HashCell><HashCell><Str value="l_loginUserIncomplete"/><Str value="Your login details are not complete, contact your administrator"/></HashCell><HashCell><Str value="l_loginUserIncorrect"/><Str value="You are trying to log in as a different user than the one the app was installed with. Reset the user or log in as the correct user."/></HashCell><HashCell><Str value="l_loginUserNotFound"/><Str value="The user was not found in the database, contact your administrator"/></HashCell><HashCell><Str value="l_loginWelcome"/><Str value=" Welcome to Twyns."/></HashCell><HashCell><Str value="l_logoff"/><Str value="Log off"/></HashCell><HashCell><Str value="l_mandatoryBrand"/><Str value="Brand is mandatory, please fill in the brand"/></HashCell><HashCell><Str value="l_mandatoryClampNumber"/><Str value="Clamp number is mandatory"/></HashCell><HashCell><Str value="l_mandatoryLocation"/><Str value="The location data is not complete."/></HashCell><HashCell><Str value="l_mandatoryNHACountryLicense"/><Str value="Country of the licenseplate cannot be unknown"/></HashCell><HashCell><Str value="l_mandatoryOptions"/><Str value="It is mandatory to fill in the options"/></HashCell><HashCell><Str value="l_mandatoryPhoto"/><Str value="You need to take a photo"/></HashCell><HashCell><Str value="l_mandatoryPhotoClamp"/><Str value="You need to take a photo before you clamp the vehicle and a photo after you clamp the vehicle"/></HashCell><HashCell><Str value="l_mandatoryPhotoReceipt"/><Str value="Please take a photo of the receipt"/></HashCell><HashCell><Str value="l_mandatoryPhotoUnclamp"/><Str value="You need to take a photo before you unclamp the vehicle and a photo after you unclamp the vehicle"/></HashCell><HashCell><Str value="l_mandatoryRegionCodeFrance"/><Str value="For France region code is mandatory"/></HashCell><HashCell><Str value="l_mandatoryScanUnit"/><Str value="If queue is enabled, choice of scan unit is mandatory."/></HashCell><HashCell><Str value="l_mandatoryStatement"/><Str value="Statement is mandatory when a person is added"/></HashCell><HashCell><Str value="l_mandatoryVehicle"/><Str value="The vehicle data is not complete."/></HashCell><HashCell><Str value="l_manual"/><Str value="Manual"/></HashCell><HashCell><Str value="l_manualLocation"/><Str value="Manual"/></HashCell><HashCell><Str value="l_map"/><Str value="Map"/></HashCell><HashCell><Str value="l_markPhotoReceipt"/><Str value="Please mark a photo as receipt"/></HashCell><HashCell><Str value="l_mastercode"/><Str value="Master code"/></HashCell><HashCell><Str value="l_max6Photos"/><Str value="You can send a maximum of 6 photos"/></HashCell><HashCell><Str value="l_max7Photos"/><Str value="You can send a maximum of 7 photos"/></HashCell><HashCell><Str value="l_max8Photos"/><Str value="You can send a maximum of 8 photos"/></HashCell><HashCell><Str value="l_maxPhotosTaken"/><Str value="You can't take more than #maximum# photos"/></HashCell><HashCell><Str value="l_maxdatetime"/><Str value="Time exceeds current date and time"/></HashCell><HashCell><Str value="l_mediaGalleryError"/><Str value="Media gallery error: "/></HashCell><HashCell><Str value="l_member"/><Str value="Member"/></HashCell><HashCell><Str value="l_menu"/><Str value="Menu"/></HashCell><HashCell><Str value="l_menuSettings"/><Str value="Settings"/></HashCell><HashCell><Str value="l_minPhotosMandatory"/><Str value="You need to take minimal #minimum# photos"/></HashCell><HashCell><Str value="l_minmaxPhotosMandatory"/><Str value="You need to take minimal #minimum# but not more than #maximum# photos"/></HashCell><HashCell><Str value="l_minutes"/><Str value="Minutes"/></HashCell><HashCell><Str value="l_moreThen1Area"/><Str value="More then one area was found. Please contact your system manager"/></HashCell><HashCell><Str value="l_moreThenOnePersonFound"/><Str value="More then one person found"/></HashCell><HashCell><Str value="l_motorcycle"/><Str value="Motorcycle"/></HashCell><HashCell><Str value="l_multiUserDevice"/><Str value="Device used for multiple users"/></HashCell><HashCell><Str value="l_multimedia"/><Str value="Multimedia"/></HashCell><HashCell><Str value="l_multipleFiltersSet"/><Str value="Multiple filters applied"/></HashCell><HashCell><Str value="l_multipleZipcodes"/><Str value="Multiple zipcodes found, specify by zipcode and/or housenumber"/></HashCell><HashCell><Str value="l_municipality"/><Str value="Municipality"/></HashCell><HashCell><Str value="l_municipalityOfBirth"/><Str value="Municipality of birth"/></HashCell><HashCell><Str value="l_mustTakePicures"/><Str value="You still need to take photos"/></HashCell><HashCell><Str value="l_myLocation"/><Str value="My location"/></HashCell><HashCell><Str value="l_name"/><Str value="Name"/></HashCell><HashCell><Str value="l_nameTaxpayer"/><Str value="Name taxpayer"/></HashCell><HashCell><Str value="l_nan"/><Str value="Not a number"/></HashCell><HashCell><Str value="l_nationality"/><Str value="Nationality"/></HashCell><HashCell><Str value="l_nearbyAddresses"/><Str value="Nearby addresses"/></HashCell><HashCell><Str value="l_nearbyDevicesPermission"/><Str value="To access real Bluetooth devices, you need to enable 'Nearby Devices' permission for this app through Android settings."/></HashCell><HashCell><Str value="l_nearbyStreets"/><Str value="Streets nearby"/></HashCell><HashCell><Str value="l_nearestHectometerMarker"/><Str value="Nearest hectometer marker"/></HashCell><HashCell><Str value="l_newest_ontop"/><Str value="Newest on top"/></HashCell><HashCell><Str value="l_nex25"/><Str value="Next 25"/></HashCell><HashCell><Str value="l_next"/><Str value="Next"/></HashCell><HashCell><Str value="l_nha"/><Str value="Parking ticket"/></HashCell><HashCell><Str value="l_no"/><Str value="No"/></HashCell><HashCell><Str value="l_noActiveCloudFor"/><Str value="At this moment there is no active cluster for:"/></HashCell><HashCell><Str value="l_noAreaOrMultipleArea"/><Str value="No or multiple areas found"/></HashCell><HashCell><Str value="l_noAuthorization"/><Str value="You don't have any authorizations"/></HashCell><HashCell><Str value="l_noBrand"/><Str value="Brand unknown"/></HashCell><HashCell><Str value="l_noCardTypeAvailable"/><Str value="No card type available, contact the administrator."/></HashCell><HashCell><Str value="l_noCase"/><Str value="No case"/></HashCell><HashCell><Str value="l_noCaseLoaded"/><Str value="There is no case loaded right now. Please try to retrieve a new one."/></HashCell><HashCell><Str value="l_noCaseTypesFound"/><Str value="No case types found"/></HashCell><HashCell><Str value="l_noCases"/><Str value="No cases"/></HashCell><HashCell><Str value="l_noChangeAvailable"/><Str value="No change allowed, no recheck available."/></HashCell><HashCell><Str value="l_noConnection"/><Str value="Sorry, retrieving the data failed. Please try again later."/></HashCell><HashCell><Str value="l_noCoordinates"/><Str value="No coördinates available"/></HashCell><HashCell><Str value="l_noCoordinatesNearbyStreets"/><Str value="No coördinates to find nearby streets"/></HashCell><HashCell><Str value="l_noCountriesFound"/><Str value="No countries found"/></HashCell><HashCell><Str value="l_noDocument"/><Str value="[none]"/></HashCell><HashCell><Str value="l_noDossierPVCasesFound"/><Str value="No open official reports found"/></HashCell><HashCell><Str value="l_noExemption"/><Str value="No exemption"/></HashCell><HashCell><Str value="l_noFilterSet"/><Str value="No filter applied"/></HashCell><HashCell><Str value="l_noInstances"/><Str value="The user has no instance, please contact your supervisor"/></HashCell><HashCell><Str value="l_noLocation"/><Str value="No location"/></HashCell><HashCell><Str value="l_noName"/><Str value="You have not specified a name"/></HashCell><HashCell><Str value="l_noNetworkClose"/><Str value="No network available, the application will be closed"/></HashCell><HashCell><Str value="l_noNumberCopied"/><Str value="No number has been copied, please try again"/></HashCell><HashCell><Str value="l_noOffence"/><Str value="No offence"/></HashCell><HashCell><Str value="l_noOffencesFound"/><Str value="No offences found"/></HashCell><HashCell><Str value="l_noOpenTasks"/><Str value="No open tasks"/></HashCell><HashCell><Str value="l_noOutcome"/><Str value="No outcome"/></HashCell><HashCell><Str value="l_noPersonRegistered"/><Str value="No person is registered"/></HashCell><HashCell><Str value="l_noPhotosTaken"/><Str value="You have not taken any pictures yet"/></HashCell><HashCell><Str value="l_noPin"/><Str value="There is no pin"/></HashCell><HashCell><Str value="l_noPledge"/><Str value="No pledge statement"/></HashCell><HashCell><Str value="l_noPreviousFines"/><Str value="No previous issued fines"/></HashCell><HashCell><Str value="l_noReferenceDataRefresh"/><Str value="You are proceeding without updating the data. If you want to update the data, close the application and restart"/></HashCell><HashCell><Str value="l_noServerConnectionRestart"/><Str value="Server connection failed, please restart the application"/></HashCell><HashCell><Str value="l_noSignal"/><Str value="No signals"/></HashCell><HashCell><Str value="l_noStreetFound"/><Str value="No street found"/></HashCell><HashCell><Str value="l_noStreetInBAG"/><Str value="The street found through GPS does not exist in the local database."/></HashCell><HashCell><Str value="l_noTerminalsLocation"/><Str value="No terminals found on this location"/></HashCell><HashCell><Str value="l_noTicketTypes"/><Str value="No tickettypes available, please try to synchronize data again"/></HashCell><HashCell><Str value="l_noValidDate"/><Str value="This is not a valid date"/></HashCell><HashCell><Str value="l_noValidPlace"/><Str value="Is not a valid enforcement city"/></HashCell><HashCell><Str value="l_noValidPlate"/><Str value="No or invalid plate"/></HashCell><HashCell><Str value="l_noViolation"/><Str value="No violation"/></HashCell><HashCell><Str value="l_noZipcode"/><Str value="This is not a valid zipcode"/></HashCell><HashCell><Str value="l_no_data_found"/><Str value="No Data Found"/></HashCell><HashCell><Str value="l_no_favorites"/><Str value="No favorite offences are available for this combination of entered data"/></HashCell><HashCell><Str value="l_no_media"/><Str value="No media"/></HashCell><HashCell><Str value="l_no_menu_authorization"/><Str value="You do not have any menu authorizations, contact your administrator."/></HashCell><HashCell><Str value="l_no_notes"/><Str value="No extra information available"/></HashCell><HashCell><Str value="l_no_recent"/><Str value="No recently chosen offences are available for this combination of entered data"/></HashCell><HashCell><Str value="l_noaddressfound"/><Str value="No adress found"/></HashCell><HashCell><Str value="l_notAnSSN"/><Str value="The given number is not a valid SSN"/></HashCell><HashCell><Str value="l_notAvailable"/><Str value="Not available"/></HashCell><HashCell><Str value="l_notRegistered"/><Str value="Not registered"/></HashCell><HashCell><Str value="l_notSameLocation"/><Str value="This is not the same as the current location:"/></HashCell><HashCell><Str value="l_notValidZipCode"/><Str value="This is not a valid zipcode"/></HashCell><HashCell><Str value="l_not_activated"/><Str value="Your device has not been activated to use this service. Please contact support to get your username/device combination activated."/></HashCell><HashCell><Str value="l_not_all_fields_filled"/><Str value="Not all requiered fields are filled."/></HashCell><HashCell><Str value="l_not_set"/><Str value="Not set"/></HashCell><HashCell><Str value="l_notariff"/><Str value="No tariff"/></HashCell><HashCell><Str value="l_notariff_star"/><Str value="*"/></HashCell><HashCell><Str value="l_notfound"/><Str value="Not found"/></HashCell><HashCell><Str value="l_nothingFound"/><Str value="Nothing found"/></HashCell><HashCell><Str value="l_nozonesassigned"/><Str value="No zones assigned to current instance"/></HashCell><HashCell><Str value="l_nr"/><Str value="Num"/></HashCell><HashCell><Str value="l_number"/><Str value="Number"/></HashCell><HashCell><Str value="l_numberm2"/><Str value="Number m2"/></HashCell><HashCell><Str value="l_objectdata"/><Str value="Object data"/></HashCell><HashCell><Str value="l_objectnumber"/><Str value="Objectnumber"/></HashCell><HashCell><Str value="l_objectsFound"/><Str value="Objects found"/></HashCell><HashCell><Str value="l_objectsyncCasemanagement"/><Str value="Case management data"/></HashCell><HashCell><Str value="l_objectsyncGlobal"/><Str value="Global data"/></HashCell><HashCell><Str value="l_objectsyncHectometermarker"/><Str value="Hectometer marker data"/></HashCell><HashCell><Str value="l_objectsyncInstance"/><Str value="Instance data"/></HashCell><HashCell><Str value="l_objectsyncKilometermarker"/><Str value="Kilometer marker data"/></HashCell><HashCell><Str value="l_objectsyncLocation"/><Str value="Location data"/></HashCell><HashCell><Str value="l_objectsyncOffence"/><Str value="Offence data"/></HashCell><HashCell><Str value="l_objectsyncPerson"/><Str value="Person data"/></HashCell><HashCell><Str value="l_objectsyncPlanning"/><Str value="Planning data"/></HashCell><HashCell><Str value="l_objectsyncReference"/><Str value="Reference data"/></HashCell><HashCell><Str value="l_objectsyncScanUnit"/><Str value="Scanunit data"/></HashCell><HashCell><Str value="l_objectsyncTheme"/><Str value="Theme data"/></HashCell><HashCell><Str value="l_objectsyncVehicle"/><Str value="Vehicle data"/></HashCell><HashCell><Str value="l_observation"/><Str value="Observation"/></HashCell><HashCell><Str value="l_observationTitel"/><Str value="Observation"/></HashCell><HashCell><Str value="l_offence"/><Str value="Offence"/></HashCell><HashCell><Str value="l_offenceCategory"/><Str value="Offence category"/></HashCell><HashCell><Str value="l_offenceCommunicated"/><Str value="Offence communicated"/></HashCell><HashCell><Str value="l_officerNotFound"/><Str value="Officer not found"/></HashCell><HashCell><Str value="l_officerNotFoundMuni"/><Str value="Officer not found due to missing municipality"/></HashCell><HashCell><Str value="l_officerNumber"/><Str value="Officer number"/></HashCell><HashCell><Str value="l_officerNumberPrint"/><Str value="Officer no."/></HashCell><HashCell><Str value="l_officer_findings"/><Str value="Findings (not printed)"/></HashCell><HashCell><Str value="l_officer_observation"/><Str value="Observation"/></HashCell><HashCell><Str value="l_oldest_ontop"/><Str value="Oldest on top"/></HashCell><HashCell><Str value="l_onStreetParkingCheck"/><Str value="On street parking check"/></HashCell><HashCell><Str value="l_onstreetpayment"/><Str value="Payed on street"/></HashCell><HashCell><Str value="l_open"/><Str value="Open"/></HashCell><HashCell><Str value="l_openCase"/><Str value="Open case"/></HashCell><HashCell><Str value="l_openPDF"/><Str value="Open PDF"/></HashCell><HashCell><Str value="l_openTasks"/><Str value="Open tasks"/></HashCell><HashCell><Str value="l_options"/><Str value="Options"/></HashCell><HashCell><Str value="l_optionsVariables"/><Str value="Options and variables"/></HashCell><HashCell><Str value="l_oranje"/><Str value="Hinderlijk aanwezig bij evenement"/></HashCell><HashCell><Str value="l_other"/><Str value="Other"/></HashCell><HashCell><Str value="l_otherSignals"/><Str value="other signals"/></HashCell><HashCell><Str value="l_outOfMemory"/><Str value="The memory threshold has been exceeded during scanning. The app will close after you have clicked OK."/></HashCell><HashCell><Str value="l_outbox"/><Str value="Outbox"/></HashCell><HashCell><Str value="l_outcome"/><Str value="Outcome"/></HashCell><HashCell><Str value="l_overview"/><Str value="Overview"/></HashCell><HashCell><Str value="l_overviewLabel"/><Str value="Label overview"/></HashCell><HashCell><Str value="l_overviewPhoto"/><Str value="Overview photo"/></HashCell><HashCell><Str value="l_overviewTask"/><Str value="Task overview"/></HashCell><HashCell><Str value="l_ownStatement"/><Str value="Own statement"/></HashCell><HashCell><Str value="l_ownerRegistrationDate"/><Str value="Owner Registration Date"/></HashCell><HashCell><Str value="l_ownerRegistrationDatePrint"/><Str value="Registration Date"/></HashCell><HashCell><Str value="l_paars"/><Str value="Langstaander"/></HashCell><HashCell><Str value="l_paidParkingZone"/><Str value="Parking zone"/></HashCell><HashCell><Str value="l_parking"/><Str value="Parking"/></HashCell><HashCell><Str value="l_parkingCardNumber"/><Str value="parkingcard number"/></HashCell><HashCell><Str value="l_parkingCardOutcome"/><Str value="Parkingcard outcome"/></HashCell><HashCell><Str value="l_parkingPenalty"/><Str value="Parking penalty"/></HashCell><HashCell><Str value="l_parkingServicefailed"/><Str value="Parking service call failed"/></HashCell><HashCell><Str value="l_parking_expired"/><Str value="Parking Expired"/></HashCell><HashCell><Str value="l_parking_invalid"/><Str value="No valid parking"/></HashCell><HashCell><Str value="l_parking_unavailable_end"/><Str value="Try again later"/></HashCell><HashCell><Str value="l_parking_unavailable_start"/><Str value="Search unavailable"/></HashCell><HashCell><Str value="l_parking_valid"/><Str value="Valid"/></HashCell><HashCell><Str value="l_parkingcard"/><Str value="Parkingcard"/></HashCell><HashCell><Str value="l_parkingcheck"/><Str value="Parking check"/></HashCell><HashCell><Str value="l_password"/><Str value="Password"/></HashCell><HashCell><Str value="l_paste"/><Str value="Paste"/></HashCell><HashCell><Str value="l_pedestrian"/><Str value="Pedestrian"/></HashCell><HashCell><Str value="l_permit"/><Str value="Permit"/></HashCell><HashCell><Str value="l_permitName"/><Str value="Permit name"/></HashCell><HashCell><Str value="l_permitTypeDesc"/><Str value="Permit type"/></HashCell><HashCell><Str value="l_permit_invalid"/><Str value="Permit Invalid"/></HashCell><HashCell><Str value="l_permit_valid"/><Str value="Permit Valid"/></HashCell><HashCell><Str value="l_permitcheck"/><Str value="Permit Check"/></HashCell><HashCell><Str value="l_permitnumber"/><Str value="Permitnumber"/></HashCell><HashCell><Str value="l_person"/><Str value="Person"/></HashCell><HashCell><Str value="l_personData"/><Str value="Person data"/></HashCell><HashCell><Str value="l_personUnderInvestigation"/><Str value="Person under investigation"/></HashCell><HashCell><Str value="l_photo"/><Str value="Photo"/></HashCell><HashCell><Str value="l_photoNotFound"/><Str value="Photo not found, take a new photo to be surePhoto not found, take a new photo to be sure"/></HashCell><HashCell><Str value="l_photoProcessingFailed"/><Str value="Scan cannot be processed, please try again"/></HashCell><HashCell><Str value="l_photoSelectFailed"/><Str value="The selected image is not a valid type"/></HashCell><HashCell><Str value="l_photos"/><Str value="Photos"/></HashCell><HashCell><Str value="l_pinNotCorrect"/><Str value="Pin is not correct"/></HashCell><HashCell><Str value="l_pinNotUsable"/><Str value="This kind of pin number is not allowed"/></HashCell><HashCell><Str value="l_pinsDoNotMatch"/><Str value="PINs do not match, please try again"/></HashCell><HashCell><Str value="l_placeofEstablishment"/><Str value="Place of establishment"/></HashCell><HashCell><Str value="l_placeofresidency"/><Str value="Place of residency"/></HashCell><HashCell><Str value="l_plateNotValid"/><Str value="Not a valid plate"/></HashCell><HashCell><Str value="l_plate_format"/><Str value="Please enter capitals and numbers only and a maximum length of 12"/></HashCell><HashCell><Str value="l_platesFound"/><Str value="Plates found"/></HashCell><HashCell><Str value="l_pledge"/><Str value="Pledge"/></HashCell><HashCell><Str value="l_policeofficeremail"/><Str value="Police officer email"/></HashCell><HashCell><Str value="l_preferences"/><Str value="Preferences"/></HashCell><HashCell><Str value="l_prefix"/><Str value="Prefix"/></HashCell><HashCell><Str value="l_previousFines"/><Str value="Previous issued fines"/></HashCell><HashCell><Str value="l_priceOneHourPark"/><Str value="Tariff"/></HashCell><HashCell><Str value="l_print"/><Str value="Print"/></HashCell><HashCell><Str value="l_printAmountPayed"/><Str value="#totalamount# paid, including #handlingfee# handling fee."/></HashCell><HashCell><Str value="l_printTestLabel"/><Str value="Print test label"/></HashCell><HashCell><Str value="l_print_observation"/><Str value="Observation"/></HashCell><HashCell><Str value="l_printerType"/><Str value="Type of printer"/></HashCell><HashCell><Str value="l_printing"/><Str value="Printing"/></HashCell><HashCell><Str value="l_process"/><Str value="Process"/></HashCell><HashCell><Str value="l_processing"/><Str value="Processing ..."/></HashCell><HashCell><Str value="l_production"/><Str value="Production"/></HashCell><HashCell><Str value="l_production_pt"/><Str value="Production Pub. Tp."/></HashCell><HashCell><Str value="l_prohibitions"/><Str value="Prohibitions"/></HashCell><HashCell><Str value="l_provider"/><Str value="Provider"/></HashCell><HashCell><Str value="l_publishedBy"/><Str value="Published by"/></HashCell><HashCell><Str value="l_putOnLabel"/><Str value="Apply label"/></HashCell><HashCell><Str value="l_pvFailed"/><Str value="Please let your system manager know that PerfectView is not available."/></HashCell><HashCell><Str value="l_pvId"/><Str value="Perfectview identification"/></HashCell><HashCell><Str value="l_pvNotAvailable"/><Str value="On this moment Perfectview is not available. Please contact your system manager, you can continue or stop"/></HashCell><HashCell><Str value="l_questions"/><Str value="Questions"/></HashCell><HashCell><Str value="l_queueLengthTimerOn"/><Str value="Queue on"/></HashCell><HashCell><Str value="l_radius"/><Str value="Radius"/></HashCell><HashCell><Str value="l_receipt"/><Str value="Receipt"/></HashCell><HashCell><Str value="l_receiptNumber"/><Str value="Receipt number"/></HashCell><HashCell><Str value="l_receivedAt"/><Str value="Received at"/></HashCell><HashCell><Str value="l_recheck"/><Str value="Recheck"/></HashCell><HashCell><Str value="l_reformatDateFailed"/><Str value="Reformat date failed"/></HashCell><HashCell><Str value="l_refresh"/><Str value="Refresh"/></HashCell><HashCell><Str value="l_refreshGPS"/><Str value="GPS"/></HashCell><HashCell><Str value="l_refreshLocation"/><Str value="Refresh"/></HashCell><HashCell><Str value="l_refreshTime"/><Str value="Refresh Time (min)"/></HashCell><HashCell><Str value="l_reg_date"/><Str value="Registered on"/></HashCell><HashCell><Str value="l_regionCode"/><Str value="Regioncode"/></HashCell><HashCell><Str value="l_register"/><Str value="Register"/></HashCell><HashCell><Str value="l_register_error"/><Str value="Error registering device"/></HashCell><HashCell><Str value="l_register_success"/><Str value="Device registered successfuly."/></HashCell><HashCell><Str value="l_registerconcept"/><Str value="Register concept"/></HashCell><HashCell><Str value="l_registeredByOrganization"/><Str value="Registered by"/></HashCell><HashCell><Str value="l_registering_device"/><Str value="Registering device"/></HashCell><HashCell><Str value="l_registrationDate"/><Str value="Registration date"/></HashCell><HashCell><Str value="l_registrationDatePrint"/><Str value="Registration date"/></HashCell><HashCell><Str value="l_registrationDateTime"/><Str value="Registration date time"/></HashCell><HashCell><Str value="l_registrationNotFound"/><Str value="Registration is not (yet) found in the system"/></HashCell><HashCell><Str value="l_registrationTitle"/><Str value="Registration title"/></HashCell><HashCell><Str value="l_regulatedTimeConsumption"/><Str value="Time consumption"/></HashCell><HashCell><Str value="l_regulation"/><Str value="Regulation"/></HashCell><HashCell><Str value="l_relatedcase"/><Str value="Folder"/></HashCell><HashCell><Str value="l_remark"/><Str value="Remark"/></HashCell><HashCell><Str value="l_removalFailed"/><Str value="Removal has failed:"/></HashCell><HashCell><Str value="l_remove"/><Str value="Remove"/></HashCell><HashCell><Str value="l_removePhoto"/><Str value="Remove photo"/></HashCell><HashCell><Str value="l_removeReg"/><Str value="Remove registration"/></HashCell><HashCell><Str value="l_removed"/><Str value="Removed"/></HashCell><HashCell><Str value="l_removefilter"/><Str value="Remove filter"/></HashCell><HashCell><Str value="l_reported"/><Str value="Reported"/></HashCell><HashCell><Str value="l_rescan"/><Str value="Rescan"/></HashCell><HashCell><Str value="l_resendPhotos"/><Str value="Resending photos ..."/></HashCell><HashCell><Str value="l_resendThumbnail"/><Str value="Resending thumbnails ..."/></HashCell><HashCell><Str value="l_reset"/><Str value="Reset"/></HashCell><HashCell><Str value="l_resetAfter5Tries"/><Str value="You have tried 5 times, application will now be reset"/></HashCell><HashCell><Str value="l_resetDBandReload"/><Str value="Are you sure you want to reset the database and reload all data?"/></HashCell><HashCell><Str value="l_resetDatabase"/><Str value="Reset database"/></HashCell><HashCell><Str value="l_resetPin"/><Str value="Reset pin"/></HashCell><HashCell><Str value="l_resetUser"/><Str value="Reset user"/></HashCell><HashCell><Str value="l_resetUserPrompt"/><Str value="Do you want to log out and reset the app?"/></HashCell><HashCell><Str value="l_restoreLayout"/><Str value="Restore layout"/></HashCell><HashCell><Str value="l_result"/><Str value="Result"/></HashCell><HashCell><Str value="l_resume"/><Str value="Resume"/></HashCell><HashCell><Str value="l_resyncFailed"/><Str value="Loading the necessery data has failed,if you choose to reset the app will be cleaned and you will have to log in again."/></HashCell><HashCell><Str value="l_retrieve"/><Str value="Retrieve"/></HashCell><HashCell><Str value="l_retrieveNewCase"/><Str value="Retrieve new case"/></HashCell><HashCell><Str value="l_retrievingNewCases"/><Str value="Retrieving cases"/></HashCell><HashCell><Str value="l_retryServerconnection"/><Str value="Connection to the server has failed, check your device connnetions. Do you want to retry?"/></HashCell><HashCell><Str value="l_rightType"/><Str value="Right type"/></HashCell><HashCell><Str value="l_rightTypeDetail"/><Str value="Right type detail"/></HashCell><HashCell><Str value="l_rightsNotReadConfirmation"/><Str value="You have chosen 'Cautie: No', is this correct?"/></HashCell><HashCell><Str value="l_roadsigns"/><Str value="Roadsigns"/></HashCell><HashCell><Str value="l_route"/><Str value="Route"/></HashCell><HashCell><Str value="l_routeInfo"/><Str value="Route information"/></HashCell><HashCell><Str value="l_rows"/><Str value="Rows"/></HashCell><HashCell><Str value="l_sameAddress"/><Str value="Do you want to use this same address as location?"/></HashCell><HashCell><Str value="l_sanction"/><Str value="Sanction"/></HashCell><HashCell><Str value="l_save"/><Str value="Save"/></HashCell><HashCell><Str value="l_saveAndExit"/><Str value="Save case and start a new one"/></HashCell><HashCell><Str value="l_saveConceptCase"/><Str value="Do you want to save the changes to your concept?"/></HashCell><HashCell><Str value="l_saveRegisterCase"/><Str value="Do you want to save your new case as a concept?"/></HashCell><HashCell><Str value="l_save_data"/><Str value="Save data"/></HashCell><HashCell><Str value="l_saved"/><Str value="Saved"/></HashCell><HashCell><Str value="l_sbiCode"/><Str value="SBI code"/></HashCell><HashCell><Str value="l_sbiCodeDescription"/><Str value="SBI description"/></HashCell><HashCell><Str value="l_scan"/><Str value="Scan"/></HashCell><HashCell><Str value="l_scanEnableAutoRestart"/><Str value="Scan enable auto restart"/></HashCell><HashCell><Str value="l_scanFailure"/><Str value="Scan can't be opened, check if it is installed and functioning."/></HashCell><HashCell><Str value="l_scan_delay"/><Str value="Scan delay (seconds)"/></HashCell><HashCell><Str value="l_scancountry"/><Str value="Scan country"/></HashCell><HashCell><Str value="l_scanner_light"/><Str value="Scanner light setting"/></HashCell><HashCell><Str value="l_sealbagNumber"/><Str value="Sealbag number"/></HashCell><HashCell><Str value="l_search"/><Str value="Search"/></HashCell><HashCell><Str value="l_searchAddress"/><Str value="Search address"/></HashCell><HashCell><Str value="l_searchExact"/><Str value="Search exact (number+letter)"/></HashCell><HashCell><Str value="l_searchLetter"/><Str value="Search letter"/></HashCell><HashCell><Str value="l_searchNumber"/><Str value="Search number"/></HashCell><HashCell><Str value="l_searchPlatesBy"/><Str value="Search plates by"/></HashCell><HashCell><Str value="l_search_numberorsurname"/><Str value="Search number or surname"/></HashCell><HashCell><Str value="l_search_printers"/><Str value="Searching for printers"/></HashCell><HashCell><Str value="l_searchstreetandestablishmentplace"/><Str value="Search street and city"/></HashCell><HashCell><Str value="l_secondOfficer"/><Str value="Second officer"/></HashCell><HashCell><Str value="l_section"/><Str value="Section"/></HashCell><HashCell><Str value="l_sector"/><Str value="Sector"/></HashCell><HashCell><Str value="l_sectorDesc"/><Str value="Sector description"/></HashCell><HashCell><Str value="l_select"/><Str value="Select"/></HashCell><HashCell><Str value="l_selectFolder"/><Str value="Select folder"/></HashCell><HashCell><Str value="l_selectPrinter"/><Str value="Select printer"/></HashCell><HashCell><Str value="l_selectTeam"/><Str value="Select team"/></HashCell><HashCell><Str value="l_selectall"/><Str value="Select all"/></HashCell><HashCell><Str value="l_selectedPrinter"/><Str value="Selected printer"/></HashCell><HashCell><Str value="l_selectscancountry"/><Str value="Select a scan country"/></HashCell><HashCell><Str value="l_send"/><Str value="Send..."/></HashCell><HashCell><Str value="l_send1LabelPhoto"/><Str value="You must at least send 1 label photo"/></HashCell><HashCell><Str value="l_send1RemovalPhoto"/><Str value="You must at least send 1 removal photo"/></HashCell><HashCell><Str value="l_sendPhotos"/><Str value="Sending photos ..."/></HashCell><HashCell><Str value="l_sendRegistration"/><Str value="Send registration ..."/></HashCell><HashCell><Str value="l_sendTaskData"/><Str value="Sending taskdata ..."/></HashCell><HashCell><Str value="l_server"/><Str value="Server"/></HashCell><HashCell><Str value="l_serverError"/><Str value="Internal server error"/></HashCell><HashCell><Str value="l_serviceDidNotRespond"/><Str value="The service did not respond in a timely fashion"/></HashCell><HashCell><Str value="l_serviceError"/><Str value="The service has errored"/></HashCell><HashCell><Str value="l_serviceErrorRetrieveList"/><Str value="Service has errored, list will be retrieved"/></HashCell><HashCell><Str value="l_service_show_time"/><Str value="Show service debug info (seconds)"/></HashCell><HashCell><Str value="l_setCurrentTime"/><Str value="Current time"/></HashCell><HashCell><Str value="l_setReceipt"/><Str value="Set receipt"/></HashCell><HashCell><Str value="l_settings"/><Str value="Settings"/></HashCell><HashCell><Str value="l_shift"/><Str value="Shift"/></HashCell><HashCell><Str value="l_shift_A"/><Str value="Evening shift"/></HashCell><HashCell><Str value="l_shift_D"/><Str value="Day shift"/></HashCell><HashCell><Str value="l_shift_Z"/><Str value="Sunday or holiday shift"/></HashCell><HashCell><Str value="l_signal"/><Str value="Signal"/></HashCell><HashCell><Str value="l_signalServicefailed"/><Str value="Signal service call failed"/></HashCell><HashCell><Str value="l_socialsecuritynr"/><Str value="Socialsecuritynumber"/></HashCell><HashCell><Str value="l_start"/><Str value="Start"/></HashCell><HashCell><Str value="l_startApp"/><Str value="Start application..."/></HashCell><HashCell><Str value="l_startDate"/><Str value="Date started"/></HashCell><HashCell><Str value="l_startGrip"/><Str value="ENFORCEMENT"/></HashCell><HashCell><Str value="l_startTime"/><Str value="Time started"/></HashCell><HashCell><Str value="l_started"/><Str value="Started"/></HashCell><HashCell><Str value="l_state"/><Str value="State"/></HashCell><HashCell><Str value="l_state100chars"/><Str value="State (100 chars)"/></HashCell><HashCell><Str value="l_statement"/><Str value="Statement"/></HashCell><HashCell><Str value="l_statementRecordedBy"/><Str value="Statement recorded by"/></HashCell><HashCell><Str value="l_statistics"/><Str value="Statistics"/></HashCell><HashCell><Str value="l_status"/><Str value="Status"/></HashCell><HashCell><Str value="l_statusClaimed"/><Str value="Claimed"/></HashCell><HashCell><Str value="l_statusControl_F"/><Str value="Claimed Fiscal"/></HashCell><HashCell><Str value="l_statusControl_K"/><Str value="Claimed clamp"/></HashCell><HashCell><Str value="l_statusControl_L"/><Str value="Claimed Mulder"/></HashCell><HashCell><Str value="l_statusControl_O"/><Str value="Claimed unclamp"/></HashCell><HashCell><Str value="l_status_unknown"/><Str value="Status Unknown"/></HashCell><HashCell><Str value="l_street"/><Str value="Street"/></HashCell><HashCell><Str value="l_street24chars"/><Str value="Street (24 chars)"/></HashCell><HashCell><Str value="l_streetAtNumber"/><Str value="Street and number (approximately)"/></HashCell><HashCell><Str value="l_streetNotDetermined"/><Str value="Street could not be determined, check loacation."/></HashCell><HashCell><Str value="l_streetnumber"/><Str value="Street number"/></HashCell><HashCell><Str value="l_streetpropositions"/><Str value="Street propositions"/></HashCell><HashCell><Str value="l_subjectnumber"/><Str value="Subjectnumber"/></HashCell><HashCell><Str value="l_successful"/><Str value="Successful"/></HashCell><HashCell><Str value="l_surname"/><Str value="Surname"/></HashCell><HashCell><Str value="l_swipe"/><Str value="By moving your finger from right to left you will be able to switch between checks"/></HashCell><HashCell><Str value="l_syncError"/><Str value="Synchronization error"/></HashCell><HashCell><Str value="l_syncLast"/><Str value="Last synchronization"/></HashCell><HashCell><Str value="l_sync_end"/><Str value="End time"/></HashCell><HashCell><Str value="l_sync_finished"/><Str value="Synchronization finished successfully"/></HashCell><HashCell><Str value="l_sync_start"/><Str value="Start time"/></HashCell><HashCell><Str value="l_sync_status"/><Str value="Synchronization status"/></HashCell><HashCell><Str value="l_synchronization"/><Str value="Synchronization"/></HashCell><HashCell><Str value="l_synchronize"/><Str value="Synchronize"/></HashCell><HashCell><Str value="l_syncinit"/><Str value="Initializing Synchronisation"/></HashCell><HashCell><Str value="l_tariff"/><Str value="Tariff"/></HashCell><HashCell><Str value="l_tariffgroup"/><Str value="Tariff group"/></HashCell><HashCell><Str value="l_task"/><Str value="Task"/></HashCell><HashCell><Str value="l_taskFault"/><Str value="Something went wrong during processing of the task"/></HashCell><HashCell><Str value="l_taskoutcomeNotFound"/><Str value="Task outcome not found"/></HashCell><HashCell><Str value="l_tasks"/><Str value="Tasks"/></HashCell><HashCell><Str value="l_tax"/><Str value="Amount parkingtax"/></HashCell><HashCell><Str value="l_taxi"/><Str value="Taxi"/></HashCell><HashCell><Str value="l_team"/><Str value="Team"/></HashCell><HashCell><Str value="l_templateName"/><Str value="Template"/></HashCell><HashCell><Str value="l_tenant"/><Str value="Tenant"/></HashCell><HashCell><Str value="l_terminal"/><Str value="Terminal"/></HashCell><HashCell><Str value="l_test"/><Str value="Test"/></HashCell><HashCell><Str value="l_text"/><Str value="Text"/></HashCell><HashCell><Str value="l_thirdOfficer"/><Str value="Third officer"/></HashCell><HashCell><Str value="l_ticketType"/><Str value="Ticket type"/></HashCell><HashCell><Str value="l_ticket_nr"/><Str value="Ticket #"/></HashCell><HashCell><Str value="l_time"/><Str value="Time"/></HashCell><HashCell><Str value="l_timeArrived"/><Str value="Time arrived"/></HashCell><HashCell><Str value="l_timeOutException"/><Str value="The service has timed out"/></HashCell><HashCell><Str value="l_timeRestriction"/><Str value="Time may not be 0:00"/></HashCell><HashCell><Str value="l_timeScanned"/><Str value="Time scanned"/></HashCell><HashCell><Str value="l_timeToArrive"/><Str value="Time to arrive"/></HashCell><HashCell><Str value="l_tokenExpired"/><Str value="Your login token has expired please login again. "/></HashCell><HashCell><Str value="l_totalDue"/><Str value="Total amount"/></HashCell><HashCell><Str value="l_touchIdFail"/><Str value="Signing in trough TOUCH ID was unsuccesful"/></HashCell><HashCell><Str value="l_touchIdSucces"/><Str value="Signing in trough TOUCH ID was succesful"/></HashCell><HashCell><Str value="l_touchIdWillNotUse"/><Str value="Are You sure you will not use TOUCH ID?"/></HashCell><HashCell><Str value="l_tow"/><Str value="Tow"/></HashCell><HashCell><Str value="l_trackDown"/><Str value="Track down"/></HashCell><HashCell><Str value="l_trackDownDesc"/><Str value="Track down and check a vehicle"/></HashCell><HashCell><Str value="l_translationLanguage"/><Str value="Translation language"/></HashCell><HashCell><Str value="l_transpondercardcode"/><Str value="Transpondercardcode"/></HashCell><HashCell><Str value="l_truck"/><Str value="Truck"/></HashCell><HashCell><Str value="l_twoPhotosMandatory"/><Str value="You need to take minimal two photos"/></HashCell><HashCell><Str value="l_type"/><Str value="Type"/></HashCell><HashCell><Str value="l_typeCode"/><Str value="Type code"/></HashCell><HashCell><Str value="l_typeTextHere"/><Str value="Type your text here"/></HashCell><HashCell><Str value="l_unClamp"/><Str value="Unclamp"/></HashCell><HashCell><Str value="l_unclaimed"/><Str value="Unclaimed by user"/></HashCell><HashCell><Str value="l_unclampRequestSetOn"/><Str value="Unclamp request set on"/></HashCell><HashCell><Str value="l_unknown"/><Str value="Unknown"/></HashCell><HashCell><Str value="l_unpair"/><Str value="Unpair"/></HashCell><HashCell><Str value="l_updateAvailable"/><Str value="Version #newversion# of the app is available. Do you want to download this new version?"/></HashCell><HashCell><Str value="l_updateAvailableMDM"/><Str value="Version #newversion# of the app is available. Remind your administrator to release this new version."/></HashCell><HashCell><Str value="l_updateAvailableStoreMDM"/><Str value="Version #newversion# of the app is available. Download the app from the appstore or remind your administrator to release the new version if the app is pushed to your device. Do you want to download this new version?"/></HashCell><HashCell><Str value="l_uploadCaseFailedRetry"/><Str value="Upload case failed, please try again or save and start a new case."/></HashCell><HashCell><Str value="l_usage"/><Str value="Usage"/></HashCell><HashCell><Str value="l_user"/><Str value="User"/></HashCell><HashCell><Str value="l_userMustBeEmail"/><Str value="Username has to be an email address"/></HashCell><HashCell><Str value="l_userNotInInstance"/><Str value="User not in current instance, please select another user"/></HashCell><HashCell><Str value="l_userPVNotExists"/><Str value="User is not found in PerfectView, please contact your manager. You can continue as an unknown user or stop"/></HashCell><HashCell><Str value="l_username"/><Str value="Username"/></HashCell><HashCell><Str value="l_usesInterpreter"/><Str value="Uses an interpreter"/></HashCell><HashCell><Str value="l_usesLegalAssistance"/><Str value="Uses legal assistance"/></HashCell><HashCell><Str value="l_valid"/><Str value="Valid"/></HashCell><HashCell><Str value="l_validFrom"/><Str value="Valid from"/></HashCell><HashCell><Str value="l_validParkingRight"/><Str value="Valid parkingright"/></HashCell><HashCell><Str value="l_validTo"/><Str value="Valid to"/></HashCell><HashCell><Str value="l_validateVehicleMessage"/><Str value="Vehicle not fully filled"/></HashCell><HashCell><Str value="l_validateVehicleOr"/><Str value=" or "/></HashCell><HashCell><Str value="l_validationNeeded"/><Str value="Validation needed"/></HashCell><HashCell><Str value="l_validityMessage"/><Str value="Outcome"/></HashCell><HashCell><Str value="l_validparkingtime"/><Str value="Valid parking time"/></HashCell><HashCell><Str value="l_van"/><Str value="Van"/></HashCell><HashCell><Str value="l_variables"/><Str value="Variables"/></HashCell><HashCell><Str value="l_vehicle"/><Str value="Vehicle"/></HashCell><HashCell><Str value="l_vehicleDetails"/><Str value="Vehicle details"/></HashCell><HashCell><Str value="l_vehicleInformation"/><Str value="Vehicle information"/></HashCell><HashCell><Str value="l_vehicleServicefailed"/><Str value="Vehicle service call failed"/></HashCell><HashCell><Str value="l_vehicleTypeGroup"/><Str value="Vehicle type group"/></HashCell><HashCell><Str value="l_vehicle_invalid"/><Str value="No vehicle information found"/></HashCell><HashCell><Str value="l_vehicle_law"/><Str value="Vehicle"/></HashCell><HashCell><Str value="l_vehicletype"/><Str value="Vehicle type"/></HashCell><HashCell><Str value="l_verifyAddress"/><Str value="Is this address correct?"/></HashCell><HashCell><Str value="l_verifyPin"/><Str value="Verify pin"/></HashCell><HashCell><Str value="l_verifyTouchId"/><Str value="Verify TOUCH ID"/></HashCell><HashCell><Str value="l_version"/><Str value="Version"/></HashCell><HashCell><Str value="l_vessel"/><Str value="Vessel"/></HashCell><HashCell><Str value="l_wait"/><Str value="Wait"/></HashCell><HashCell><Str value="l_wantToLogOn"/><Str value="Do you want to log on to a cluster?"/></HashCell><HashCell><Str value="l_wantToRemoveCase"/><Str value="Do you want to remove the case?"/></HashCell><HashCell><Str value="l_wantToRemovePhoto"/><Str value="Are you sure you want to remove the photo?"/></HashCell><HashCell><Str value="l_wantToSaveContinueOrTryAgain"/><Str value="Unable to create case online, do you want to continue offline or try again?"/></HashCell><HashCell><Str value="l_wantToTryAgain"/><Str value="Do you want to try again?"/></HashCell><HashCell><Str value="l_wantToUsePin"/><Str value="Do you want to use a pin number to log in to the application?"/></HashCell><HashCell><Str value="l_wantToUseTouchId"/><Str value="Do you want to use TOUCH ID to log in?"/></HashCell><HashCell><Str value="l_wanted"/><Str value="Wanted"/></HashCell><HashCell><Str value="l_warning"/><Str value="Warning"/></HashCell><HashCell><Str value="l_warningMessage"/><Str value="“Building info” &amp; “State” should not be used if you’re planning to write a Combibon"/></HashCell><HashCell><Str value="l_withdraw"/><Str value="Withdraw"/></HashCell><HashCell><Str value="l_writeExtraTicket"/><Str value="Extra ticket"/></HashCell><HashCell><Str value="l_xTerminalsLocation"/><Str value=" Terminals found on this location"/></HashCell><HashCell><Str value="l_yearofbirth"/><Str value="Year of birth"/></HashCell><HashCell><Str value="l_yes"/><Str value="Yes"/></HashCell><HashCell><Str value="l_zipCode"/><Str value="Zipcode"/></HashCell><HashCell><Str value="l_zipCode12chars"/><Str value="Zipcode (12 chars)"/></HashCell><HashCell><Str value="l_zoneCode"/><Str value="Zone code"/></HashCell><HashCell><Str value="l_zoneDesc"/><Str value="Zone Description"/></HashCell><HashCell><Str value="noCaution"/><Str value="No caution"/></HashCell><HashCell><Str value="open_settings"/><Str value="Open Settings"/></HashCell><HashCell><Str value="otherDocuments"/><Str value="Other Documents"/></HashCell><HashCell><Str value="passport"/><Str value="Passport"/></HashCell><HashCell><Str value="pl_banDurationDays"/><Str value="Duration of ban in days"/></HashCell><HashCell><Str value="pl_banDurationMonths"/><Str value="Duration of ban in months"/></HashCell><HashCell><Str value="pl_banDurationWeeks"/><Str value="Duration of ban in weeks"/></HashCell><HashCell><Str value="pl_casetime"/><Str value="Case time"/></HashCell><HashCell><Str value="pl_char_limit"/><Str value="Character limit reached"/></HashCell><HashCell><Str value="pl_checkCard"/><Str value="Check card"/></HashCell><HashCell><Str value="pl_chooseNearbyStreets"/><Str value="Or choose one of the streets in the area:"/></HashCell><HashCell><Str value="pl_goto"/><Str value=" Go to"/></HashCell><HashCell><Str value="pl_identification"/><Str value="Identification"/></HashCell><HashCell><Str value="pl_interior"/><Str value="Interior"/></HashCell><HashCell><Str value="pl_internalRemark"/><Str value="Internal remark"/></HashCell><HashCell><Str value="pl_letter"/><Str value="Letters"/></HashCell><HashCell><Str value="pl_locationDescription"/><Str value="Location description"/></HashCell><HashCell><Str value="pl_locationSpec"/><Str value="Location specification"/></HashCell><HashCell><Str value="pl_location_indication"/><Str value="Location indication"/></HashCell><HashCell><Str value="pl_minimal2input"/><Str value="Enter the minimal of 2 characters"/></HashCell><HashCell><Str value="pl_minimal3input"/><Str value="Type a least 3 characters"/></HashCell><HashCell><Str value="pl_minimalInput"/><Str value="Enter the minimal of 3 characters"/></HashCell><HashCell><Str value="pl_minimalStreetinput"/><Str value="Enter a minimal of 3 characters for Street"/></HashCell><HashCell><Str value="pl_notepad"/><Str value="Notepad"/></HashCell><HashCell><Str value="pl_number"/><Str value="Numbers"/></HashCell><HashCell><Str value="pl_onStreetGPS"/><Str value="Found on the street with GPS"/></HashCell><HashCell><Str value="pl_paymentMethod"/><Str value="payment method"/></HashCell><HashCell><Str value="pl_questionnaires"/><Str value="Questionnaires"/></HashCell><HashCell><Str value="pl_scanUnit"/><Str value="Scan unit"/></HashCell><HashCell><Str value="pl_tax"/><Str value="Parkingtax"/></HashCell><HashCell><Str value="pl_total"/><Str value="Total"/></HashCell><HashCell><Str value="pl_towed"/><Str value="Towed"/></HashCell><HashCell><Str value="pl_translate"/><Str value="Translate"/></HashCell><HashCell><Str value="pleaseWait"/><Str value="Please wait..."/></HashCell><HashCell><Str value="scanner_app_not_installed"/><Str value="To scan documents the Document Scanner app is required."/></HashCell><HashCell><Str value="selectDocumentType"/><Str value="Select Document Type"/></HashCell></Hash></Tbl>