"CaseManagementData"="CaseManagementData";
"ERR_SP_00"="ERR_SP_00";
"ERR_SP_01"="ERR_SP_01";
"ERR_SP_02"="ERR_SP_02";
"ERR_SP_03"="ERR_SP_03";
"ERR_SP_04"="ERR_SP_04";
"ERR_SP_05"="ERR_SP_05";
"ERR_SP_06"="ERR_SP_06";
"GetCarInfo"="GetCarInfo";
"GetParkings"="GetParkings";
"GetSignals"="GetSignals";
"Instance"="Instance";
"LabelAanbrengen"="LabelAanbrengen";
"Location"="Location";
"LocationData"="LocationData";
"NotificatieAanbrengen"="NotificatieAanbrengen";
"Offence"="Offence";
"Person"="Person";
"Planning"="Planning";
"RED-ASGN-01"="RED-ASGN-01";
"RED-ASGN-02"="RED-ASGN-02";
"RED-ASGN-03"="RED-ASGN-03";
"RED-ASGN-04"="RED-ASGN-04";
"RED-ASGN-05"="RED-ASGN-05";
"RED-ASGN-06"="RED-ASGN-06";
"RED-ASGN-07"="RED-ASGN-07";
"RED-ASGN-08"="RED-ASGN-08";
"RED-ASGN-09"="RED-ASGN-09";
"RED-ASGN-10"="RED-ASGN-10";
"RED-ASGN-11"="RED-ASGN-11";
"RED-ASGN-12"="RED-ASGN-12";
"ReferenceData"="ReferenceData";
"Users"="Users";
"VEHICLE_SIGNAL"="VEHICLE_SIGNAL";
"Vehicle"="Vehicle";
"WachtenOpVerwijderen"="WachtenOpVerwijderen";
"WaitingForLocation"="WaitingForLocation";
"_usesLegalAssistance"="_usesLegalAssistance";
"appmode_checklabel"="appmode_checklabel";
"appmode_labeloverview"="appmode_labeloverview";
"appmode_register"="appmode_register";
"appmode_registerconcept"="appmode_registerconcept";
"appmode_registerlabel"="appmode_registerlabel";
"bt_add"="bt_add";
"bt_again"="bt_again";
"bt_back"="bt_back";
"bt_cancel"="bt_cancel";
"bt_check"="bt_check";
"bt_complete"="bt_complete";
"bt_continue"="bt_continue";
"bt_deselectAll"="bt_deselectAll";
"bt_directPV"="bt_directPV";
"bt_done"="bt_done";
"bt_edit"="bt_edit";
"bt_exit"="bt_exit";
"bt_info"="bt_info";
"bt_lightoff"="bt_lightoff";
"bt_lightoff_once"="bt_lightoff_once";
"bt_lighton"="bt_lighton";
"bt_lighton_once"="bt_lighton_once";
"bt_map"="bt_map";
"bt_markOverviewPhoto"="bt_markOverviewPhoto";
"bt_new"="bt_new";
"bt_no"="bt_no";
"bt_ok"="bt_ok";
"bt_register"="bt_register";
"bt_remove"="bt_remove";
"bt_retry"="bt_retry";
"bt_selectAll"="bt_selectAll";
"bt_send"="bt_send";
"bt_stop"="bt_stop";
"bt_unknown"="bt_unknown";
"bt_vehicle_scan"="bt_vehicle_scan";
"bt_view"="bt_view";
"bt_yes"="bt_yes";
"camera_permission_dialog_message"="To enable photo capture, this app requires access to your camera. Please grant permission by opening the Settings app.";
"camera_permission_dialog_title"="Allow Twyns App to Access Your Camera";
"camera_permission_scan_dialog_message"="To enable document scanning, this app requires access to your camera. Please grant permission by opening the Settings app.";
"cancel"="Cancel";
"drivingCard"="Driving licence";
"e_area0001"="e_area0001";
"e_area0002"="e_area0002";
"e_log0001"="e_log0001";
"e_log0002"="e_log0002";
"e_log0003"="e_log0003";
"e_log0004"="e_log0004";
"e_mand0002"="e_mand0002";
"e_mandByLawNotFound"="e_mandByLawNotFound";
"e_multipleByLaws"="e_multipleByLaws";
"e_noInstanceparameter"="e_noInstanceparameter";
"e_prt0001"="e_prt0001";
"e_select_zone"="e_select_zone";
"e_ser0001"="e_ser0001";
"e_ser0002"="e_ser0002";
"e_ser0003"="e_ser0003";
"e_serverError"="e_serverError";
"e_serverErrorNPR_1"="e_serverErrorNPR_1";
"e_serverErrorNPR_100"="e_serverErrorNPR_100";
"e_serverErrorNPR_104"="e_serverErrorNPR_104";
"e_serverErrorNPR_302"="e_serverErrorNPR_302";
"e_serverErrorNPR_5"="e_serverErrorNPR_5";
"e_serverErrorNPR_999"="e_serverErrorNPR_999";
"e_sync0001"="e_sync0001";
"e_sync0002"="e_sync0002";
"e_sync0003"="e_sync0003";
"e_upgradeError"="e_upgradeError";
"e_userNotFound"="e_userNotFound";
"e_val00004"="e_val00004";
"e_val00005"="e_val00005";
"e_val00006"="e_val00006";
"e_val00007"="e_val00007";
"e_val00008"="e_val00008";
"e_val00009"="e_val00009";
"e_val00010"="e_val00010";
"hdr_default"="hdr_default";
"hdr_manual"="hdr_manual";
"hdr_rdw"="hdr_rdw";
"hdr_scan"="hdr_scan";
"hdr_scanRdw"="hdr_scanRdw";
"i_anpr0001"="i_anpr0001";
"i_anpr0002"="i_anpr0002";
"i_confirmsync"="i_confirmsync";
"i_connectPrinterFailed"="i_connectPrinterFailed";
"i_daysago"="i_daysago";
"i_deceased"="i_deceased";
"i_declinedLegalAssistance"="i_declinedLegalAssistance";
"i_emigrate"="i_emigrate";
"i_faulted"="i_faulted";
"i_fsc0001"="i_fsc0001";
"i_fsc0002"="i_fsc0002";
"i_fsc0007"="i_fsc0007";
"i_hoursago"="i_hoursago";
"i_interpretationCommunicated"="i_interpretationCommunicated";
"i_interpretationNotCommunicated"="i_interpretationNotCommunicated";
"i_interpreter"="i_interpreter";
"i_legalAssistanceCommunicated"="i_legalAssistanceCommunicated";
"i_legalAssistanceNotCommunicated"="i_legalAssistanceNotCommunicated";
"i_noCities"="i_noCities";
"i_noMunicipalities"="i_noMunicipalities";
"i_no_person"="i_no_person";
"i_notDeclinedLegalAssistance"="i_notDeclinedLegalAssistance";
"i_notUsesThisRight"="i_notUsesThisRight";
"i_offenceCommunicated"="i_offenceCommunicated";
"i_offenceNotCommunicated"="i_offenceNotCommunicated";
"i_personNotFound"="i_personNotFound";
"i_restart"="i_restart";
"i_select_street"="i_select_street";
"i_syncwarning"="i_syncwarning";
"i_tooManyPersonRecords"="i_tooManyPersonRecords";
"i_translationLanguage"="i_translationLanguage";
"i_usesThisRight"="i_usesThisRight";
"idCard"="Identity Card";
"l_2Characterinput"="l_2Characterinput";
"l_3Characterinput"="l_3Characterinput";
"l_Diagnostics"="l_Diagnostics";
"l_HouseNumberMustBeNumeric"="l_HouseNumberMustBeNumeric";
"l_NSproduction"="l_NSproduction";
"l_NSproductionEducation"="l_NSproductionEducation";
"l_NoPlate"="l_NoPlate";
"l_NoPlates"="l_NoPlates";
"l_NoPlatesConfirmation"="l_NoPlatesConfirmation";
"l_NoRefreshPlate"="l_NoRefreshPlate";
"l_OVdemo"="l_OVdemo";
"l_OffenceCommunicatedNoStatement"="l_OffenceCommunicatedNoStatement";
"l_Pin"="l_Pin";
"l_RDWowner"="l_RDWowner";
"l_RDWownerCheck"="l_RDWownerCheck";
"l_StatementPledge"="l_StatementPledge";
"l_WatchGPSInterval"="l_WatchGPSInterval";
"l_about"="l_about";
"l_acceptance"="l_acceptance";
"l_acceptanceTest"="l_acceptanceTest";
"l_acceptance_pt"="l_acceptance_pt";
"l_activeCases"="l_activeCases";
"l_addAddressWithoutGPS"="l_addAddressWithoutGPS";
"l_addData"="l_addData";
"l_addLabel"="l_addLabel";
"l_addTax"="l_addTax";
"l_addTaxDisplay"="l_addTaxDisplay";
"l_additionalAssesment"="l_additionalAssesment";
"l_address"="l_address";
"l_addressUnderInvestigation"="l_addressUnderInvestigation";
"l_administrationcosts"="l_administrationcosts";
"l_all"="l_all";
"l_allowed"="l_allowed";
"l_amountDue"="l_amountDue";
"l_amountSanction"="l_amountSanction";
"l_applyLabel"="l_applyLabel";
"l_applyfilter"="l_applyfilter";
"l_area"="l_area";
"l_areas"="l_areas";
"l_askAdministrator"="l_askAdministrator";
"l_assessmentNumber"="l_assessmentNumber";
"l_assessor"="l_assessor";
"l_assignedArea"="l_assignedArea";
"l_attempt"="l_attempt";
"l_authenticating_user"="l_authenticating_user";
"l_auto_restart_enabled"="l_auto_restart_enabled";
"l_auto_scan_time"="l_auto_scan_time";
"l_averages"="l_averages";
"l_batches"="l_batches";
"l_birthdate"="l_birthdate";
"l_branchNumber"="l_branchNumber";
"l_brand"="l_brand";
"l_brandtype"="l_brandtype";
"l_building"="l_building";
"l_building100chars"="l_building100chars";
"l_bus"="l_bus";
"l_by"="l_by";
"l_calculatedPrice"="l_calculatedPrice";
"l_cancel"="l_cancel";
"l_cannotRegisterYourself"="l_cannotRegisterYourself";
"l_cannotUseAddress"="l_cannotUseAddress";
"l_car"="l_car";
"l_cardNumber"="l_cardNumber";
"l_cardcheck"="l_cardcheck";
"l_cards"="l_cards";
"l_case"="l_case";
"l_caseAndTaskTypeNotFound"="l_caseAndTaskTypeNotFound";
"l_caseNoLongerOnServer"="l_caseNoLongerOnServer";
"l_caseNotFound"="l_caseNotFound";
"l_caseNotRetrieved"="l_caseNotRetrieved";
"l_caseOnlineFailedOutboxQuestion"="l_caseOnlineFailedOutboxQuestion";
"l_caseOnlineFailedSetToOutbox"="l_caseOnlineFailedSetToOutbox";
"l_caseType"="l_caseType";
"l_caseUpdateFailed"="l_caseUpdateFailed";
"l_caseWillBeClosed"="l_caseWillBeClosed";
"l_case_not_opened"="l_case_not_opened";
"l_cases"="l_cases";
"l_casesToBeUpdated"="l_casesToBeUpdated";
"l_cashAmount"="l_cashAmount";
"l_changeAddress"="l_changeAddress";
"l_changeCaseTo"="l_changeCaseTo";
"l_changeEnvironment"="l_changeEnvironment";
"l_changeFilter"="l_changeFilter";
"l_changeHouseNumber"="l_changeHouseNumber";
"l_changeInstance"="l_changeInstance";
"l_changePosition"="l_changePosition";
"l_changeTheme"="l_changeTheme";
"l_changeTravelMode"="l_changeTravelMode";
"l_channel"="l_channel";
"l_check"="l_check";
"l_checkBounds"="l_checkBounds";
"l_checkBoundsValue"="l_checkBoundsValue";
"l_checkLicensePlate"="l_checkLicensePlate";
"l_checkPin"="l_checkPin";
"l_checkTime"="l_checkTime";
"l_checkUserDatabase"="l_checkUserDatabase";
"l_checkUserPV"="l_checkUserPV";
"l_check_types"="l_check_types";
"l_checkcard"="l_checkcard";
"l_checked"="l_checked";
"l_checkedOut"="l_checkedOut";
"l_checkedOutFollow"="l_checkedOutFollow";
"l_checking_service"="l_checking_service";
"l_checklocation"="l_checklocation";
"l_checkout"="l_checkout";
"l_checks"="l_checks";
"l_checkvehicle"="l_checkvehicle";
"l_choose"="l_choose";
"l_chooseCity"="l_chooseCity";
"l_chooseHost"="l_chooseHost";
"l_chooseMunicipality"="l_chooseMunicipality";
"l_chooseOneCheckType"="l_chooseOneCheckType";
"l_chooseTaskOutcome"="l_chooseTaskOutcome";
"l_chooseTeam"="l_chooseTeam";
"l_chooseZone"="l_chooseZone";
"l_choosearea"="l_choosearea";
"l_chooselabel"="l_chooselabel";
"l_choosestreet"="l_choosestreet";
"l_choosetask"="l_choosetask";
"l_choosevalue"="l_choosevalue";
"l_chosenZone"="l_chosenZone";
"l_chosenaddress"="l_chosenaddress";
"l_city"="l_city";
"l_city36chars"="l_city36chars";
"l_cityNotOffenceCity"="l_cityNotOffenceCity";
"l_claimFailed"="l_claimFailed";
"l_claimedByMe"="l_claimedByMe";
"l_claimedByOther"="l_claimedByOther";
"l_claimedByOtherRetrievingCases"="l_claimedByOtherRetrievingCases";
"l_clamp"="l_clamp";
"l_clampNumber"="l_clampNumber";
"l_clampNumberNotFound"="l_clampNumberNotFound";
"l_clampTime"="l_clampTime";
"l_clickToAddPhotos"="l_clickToAddPhotos";
"l_close"="l_close";
"l_closePDF"="l_closePDF";
"l_cocnumber"="l_cocnumber";
"l_code"="l_code";
"l_color"="l_color";
"l_concepts"="l_concepts";
"l_confirm_removelist"="l_confirm_removelist";
"l_connectiondescription"="l_connectiondescription";
"l_continueOffline"="l_continueOffline";
"l_continueWithoutConnection"="l_continueWithoutConnection";
"l_coordinates"="l_coordinates";
"l_countries"="l_countries";
"l_country"="l_country";
"l_countryPlateUnknownChange"="l_countryPlateUnknownChange";
"l_countryUnknown"="l_countryUnknown";
"l_countryUnknownCode"="l_countryUnknownCode";
"l_countryadress"="l_countryadress";
"l_countryoforigin"="l_countryoforigin";
"l_createPin"="l_createPin";
"l_currentLocation"="l_currentLocation";
"l_currentPosition"="l_currentPosition";
"l_currentValidity"="l_currentValidity";
"l_current_task"="l_current_task";
"l_dashesOnly"="l_dashesOnly";
"l_date"="l_date";
"l_dateTimeFuture"="l_dateTimeFuture";
"l_dateTimePast"="l_dateTimePast";
"l_datetime"="l_datetime";
"l_daysMonth"="l_daysMonth";
"l_declinedLegalAssistance"="l_declinedLegalAssistance";
"l_description"="l_description";
"l_details"="l_details";
"l_development"="l_development";
"l_digit_count"="l_digit_count";
"l_directPerfectview"="l_directPerfectview";
"l_disabled_parking"="l_disabled_parking";
"l_discard"="l_discard";
"l_distance"="l_distance";
"l_doNotChangePlate"="l_doNotChangePlate";
"l_doNotHaveFingerprints"="l_doNotHaveFingerprints";
"l_doNotHaveLogOnCode"="l_doNotHaveLogOnCode";
"l_doYouWantToPasteData"="l_doYouWantToPasteData";
"l_documentAlreadyProcessedException"="l_documentAlreadyProcessedException";
"l_documentNotFoundException"="l_documentNotFoundException";
"l_duplicate"="l_duplicate";
"l_duplicateScan"="l_duplicateScan";
"l_dutch"="l_dutch";
"l_editAddress"="l_editAddress";
"l_editData"="l_editData";
"l_editExistingOptionsVariables"="l_editExistingOptionsVariables";
"l_editIdentification"="l_editIdentification";
"l_editKindOfViolation"="l_editKindOfViolation";
"l_editLicensePlate"="l_editLicensePlate";
"l_editTime"="l_editTime";
"l_email"="l_email";
"l_emmDataInCorrect"="l_emmDataInCorrect";
"l_emmNotCorrect"="l_emmNotCorrect";
"l_employeeNumberPrint"="l_employeeNumberPrint";
"l_emptyPrinterMac"="l_emptyPrinterMac";
"l_emptyPrinterName"="l_emptyPrinterName";
"l_end"="l_end";
"l_endOfList"="l_endOfList";
"l_endTime"="l_endTime";
"l_enforcementObject"="l_enforcementObject";
"l_english"="l_english";
"l_enter5DigitPin"="l_enter5DigitPin";
"l_enterNumber"="l_enterNumber";
"l_enterPin"="l_enterPin";
"l_enterUsernameFirst"="l_enterUsernameFirst";
"l_enteraddress"="l_enteraddress";
"l_environment"="l_environment";
"l_environmentListActivated"="l_environmentListActivated";
"l_environmentalClassification"="l_environmentalClassification";
"l_environmentalClassificationPrefix"="l_environmentalClassificationPrefix";
"l_error"="l_error";
"l_errorCode"="l_errorCode";
"l_errorDuringUpgrade"="l_errorDuringUpgrade";
"l_errorUserCheck"="l_errorUserCheck";
"l_errored"="l_errored";
"l_europeanVehicleType"="l_europeanVehicleType";
"l_execByPartner"="l_execByPartner";
"l_executing_anpr"="l_executing_anpr";
"l_exemption"="l_exemption";
"l_exemptionstartdate"="l_exemptionstartdate";
"l_existingOptionsVariables"="l_existingOptionsVariables";
"l_exit"="l_exit";
"l_exitApplication"="l_exitApplication";
"l_exp_date"="l_exp_date";
"l_explanation"="l_explanation";
"l_externalcollector"="l_externalcollector";
"l_extraInformation"="l_extraInformation";
"l_favorites"="l_favorites";
"l_fillStreetCity"="l_fillStreetCity";
"l_filter"="l_filter";
"l_fineWithdrawn"="l_fineWithdrawn";
"l_finished"="l_finished";
"l_firstName"="l_firstname";
"l_flash_auto"="l_flash_auto";
"l_flash_off"="l_flash_off";
"l_flash_on"="l_flash_on";
"l_folderNotFound"="l_folderNotFound";
"l_follow"="l_follow";
"l_followUp"="l_followUp";
"l_found"="l_found";
"l_foundHistoricCase"="l_foundHistoricCase";
"l_fuel"="l_fuel";
"l_fullName"="l_fullName";
"l_fullname"="l_fullname";
"l_gender"="l_gender";
"l_georgia"="l_georgia";
"l_germanPlateDash"="l_germanPlateDash";
"l_germany"="l_germany";
"l_getDataFailed"="l_getDataFailed";
"l_getParkRegistrations"="l_getParkRegistrations";
"l_getPreviousFines"="l_getPreviousFines";
"l_getZonesTerminals"="l_getZonesTerminals";
"l_give5NumberPin"="l_give5NumberPin";
"l_giveValidParkingRight"="l_giveValidParkingRight";
"l_gps_no_fix"="l_gps_no_fix";
"l_gps_refresh"="l_gps_refresh";
"l_handle"="l_handle";
"l_handling"="l_handling";
"l_hasParkingRight"="l_hasParkingRight";
"l_hasValidParkingRight"="l_hasValidParkingRight";
"l_haveToUpdate"="l_haveToUpdate";
"l_haveToUpdateMDM"="l_haveToUpdateMDM";
"l_haveToUpdateStoreMDM"="l_haveToUpdateStoreMDM";
"l_hectometerMarkersNearby"="l_hectometerMarkersNearby";
"l_history"="l_history";
"l_hostlocation"="l_hostlocation";
"l_hours"="l_hours";
"l_houseNumberAdd"="l_houseNumberAdd";
"l_houseNumberAdd7chars"="l_houseNumberAdd7chars";
"l_houseNumberAddition"="l_houseNumberAddition";
"l_houseNumberAddition_short"="l_houseNumberAddition_short";
"l_houseletter"="l_houseletter";
"l_houseletter_short"="l_houseletter_short";
"l_housenumber"="l_housenumber";
"l_housenumber5chars"="l_housenumber5chars";
"l_housenumber_short"="l_housenumber_short";
"l_imageGallery"="l_imageGallery";
"l_info"="l_info";
"l_initDBFailed"="l_initDBFailed";
"l_initialize"="l_initialize";
"l_initialize_text"="l_initialize_text";
"l_initialize_title"="l_initialize_title";
"l_initials"="l_initials";
"l_inserted"="l_inserted";
"l_inspectionExpirationDate"="l_inspectionExpirationDate";
"l_instance"="l_instance";
"l_insuranceDate"="l_insuranceDate";
"l_internalRemark"="l_internalRemark";
"l_interpreterCommunicated"="l_interpreterCommunicated";
"l_interpreterNR"="l_interpreterNR";
"l_invalid"="l_invalid";
"l_invalidLoginCredentails"="l_invalidLoginCredentails";
"l_isNotFilled"="l_isNotFilled";
"l_keepLocation"="l_keepLocation";
"l_kind"="l_kind";
"l_kindOfHandling"="l_kindOfHandling";
"l_kindOfLabel"="l_kindOfLabel";
"l_kindOfRoad"="l_kindOfRoad";
"l_kindOfVehicle"="l_kindOfVehicle";
"l_label"="l_label";
"l_labelCheck"="l_labelCheck";
"l_labelExists"="l_labelExists";
"l_labelOverview"="l_labelOverview";
"l_labelPhoto"="l_labelPhoto";
"l_lastChosen"="l_lastChosen";
"l_lastChosenOutcome"="l_lastChosenOutcome";
"l_lastsync"="l_lastsync";
"l_legalAssistCommunicated"="l_legalAssistCommunicated";
"l_legalAssistance"="l_legalAssistance";
"l_legalForm"="l_legalForm";
"l_licensePlateCountryUnknown"="l_licensePlateCountryUnknown";
"l_licenseplate"="l_licenseplate";
"l_licenseplateCountries"="l_licenseplateCountries";
"l_list"="l_list";
"l_listNotAvailable"="l_listNotAvailable";
"l_loadLicenseplate"="l_loadLicenseplate";
"l_loadPhoto"="l_loadPhoto";
"l_load_map"="l_load_map";
"l_loading"="l_loading";
"l_loadingParam"="l_loadingParam";
"l_location"="l_location";
"l_locationFound"="l_locationFound";
"l_locationVerified"="l_locationVerified";
"l_logOffDate"="l_logOffDate";
"l_logOffTime"="l_logOffTime";
"l_logOn"="l_logOn";
"l_logOnDate"="l_logOnDate";
"l_logOnDuration"="l_logOnDuration";
"l_logOnStatus"="l_logOnStatus";
"l_logOnTime"="l_logOnTime";
"l_logOut"="l_logOut";
"l_loggingOut"="l_loggingOut";
"l_login"="l_login";
"l_loginAccount"="l_loginAccount";
"l_loginAuthorisation"="l_loginAuthorisation";
"l_loginInstance"="l_loginInstance";
"l_loginName"="l_loginName";
"l_loginNoFunctions"="l_loginNoFunctions";
"l_loginNoOfficerId"="l_loginNoOfficerId";
"l_loginRegister"="l_loginRegister";
"l_loginServer"="l_loginServer";
"l_loginText1"="l_loginText1";
"l_loginText2"="l_loginText2";
"l_loginUserIncomplete"="l_loginUserIncomplete";
"l_loginUserIncorrect"="l_loginUserIncorrect";
"l_loginUserNotFound"="l_loginUserNotFound";
"l_loginWelcome"="l_loginWelcome";
"l_logoff"="l_logoff";
"l_mandatoryBrand"="l_mandatoryBrand";
"l_mandatoryClampNumber"="l_mandatoryClampNumber";
"l_mandatoryLocation"="l_mandatoryLocation";
"l_mandatoryNHACountryLicense"="l_mandatoryNHACountryLicense";
"l_mandatoryOptions"="l_mandatoryOptions";
"l_mandatoryPhoto"="l_mandatoryPhoto";
"l_mandatoryPhotoClamp"="l_mandatoryPhotoClamp";
"l_mandatoryPhotoReceipt"="l_mandatoryPhotoReceipt";
"l_mandatoryPhotoUnclamp"="l_mandatoryPhotoUnclamp";
"l_mandatoryRegionCodeFrance"="l_mandatoryRegionCodeFrance";
"l_mandatoryScanUnit"="l_mandatoryScanUnit";
"l_mandatoryStatement"="l_mandatoryStatement";
"l_mandatoryVehicle"="l_mandatoryVehicle";
"l_manual"="l_manual";
"l_manualLocation"="l_manualLocation";
"l_map"="l_map";
"l_markPhotoReceipt"="l_markPhotoReceipt";
"l_mastercode"="l_mastercode";
"l_max6Photos"="l_max6Photos";
"l_max7Photos"="l_max7Photos";
"l_max8Photos"="l_max8Photos";
"l_maxPhotosTaken"="l_maxPhotosTaken";
"l_maxdatetime"="l_maxdatetime";
"l_mediaGalleryError"="l_mediaGalleryError";
"l_member"="l_member";
"l_menu"="l_menu";
"l_menuSettings"="l_menuSettings";
"l_minPhotosMandatory"="l_minPhotosMandatory";
"l_minmaxPhotosMandatory"="l_minmaxPhotosMandatory";
"l_minutes"="l_minutes";
"l_moreThen1Area"="l_moreThen1Area";
"l_moreThenOnePersonFound"="l_moreThenOnePersonFound";
"l_motorcycle"="l_motorcycle";
"l_multiUserDevice"="l_multiUserDevice";
"l_multimedia"="l_multimedia";
"l_multipleFiltersSet"="l_multipleFiltersSet";
"l_multipleZipcodes"="l_multipleZipcodes";
"l_municipality"="l_municipality";
"l_municipalityOfBirth"="l_municipalityOfBirth";
"l_mustTakePicures"="l_mustTakePicures";
"l_myLocation"="l_myLocation";
"l_name"="l_name";
"l_nameTaxpayer"="l_nameTaxpayer";
"l_nan"="l_nan";
"l_nationality"="l_nationality";
"l_nearbyAddresses"="l_nearbyAddresses";
"l_nearbyDevicesPermission"="l_nearbyDevicesPermission";
"l_nearbyStreets"="l_nearbyStreets";
"l_nearestHectometerMarker"="l_nearestHectometerMarker";
"l_newest_ontop"="l_newest_above";
"l_nex25"="l_nex25";
"l_next"="l_next";
"l_nha"="l_nha";
"l_no"="l_no";
"l_noActiveCloudFor"="l_noActiveCloudFor";
"l_noAreaOrMultipleArea"="l_noAreaOrMultipleArea";
"l_noAuthorization"="l_noAuthorization";
"l_noBrand"="l_noBrand";
"l_noCardTypeAvailable"="l_noCardTypeAvailable";
"l_noCase"="l_noCase";
"l_noCaseLoaded"="l_noCaseLoaded";
"l_noCaseTypesFound"="l_noCaseTypesFound";
"l_noCases"="l_noCases";
"l_noChangeAvailable"="l_noChangeAvailable";
"l_noConnection"="l_noConnection";
"l_noCoordinates"="l_noCoordinates";
"l_noCoordinatesNearbyStreets"="l_noCoordinatesNearbyStreets";
"l_noCountriesFound"="l_noCountriesFound";
"l_noDocument"="l_noDocument";
"l_noDossierPVCasesFound"="l_noDossierPVCasesFound";
"l_noExemption"="l_noExemption";
"l_noFilterSet"="l_noFilterSet";
"l_noInstances"="l_noInstances";
"l_noLocation"="l_noLocation";
"l_noName"="l_noName";
"l_noNetworkClose"="l_noNetworkClose";
"l_noNumberCopied"="l_noNumberCopied";
"l_noOffence"="l_noOffence";
"l_noOffencesFound"="l_noOffencesFound";
"l_noOpenTasks"="l_noOpenTasks";
"l_noOutcome"="l_noOutcome";
"l_noPersonRegistered"="l_noPersonRegistered";
"l_noPhotosTaken"="l_noPhotosTaken";
"l_noPin"="l_noPin";
"l_noPledge"="l_noPledge";
"l_noPreviousFines"="l_noPreviousFines";
"l_noReferenceDataRefresh"="l_noReferenceDataRefresh";
"l_noServerConnectionRestart"="l_noServerConnectionRestart";
"l_noSignal"="l_noSignal";
"l_noStreetFound"="l_noStreetFound";
"l_noStreetInBAG"="l_noStreetInBAG";
"l_noTerminalsLocation"="l_noTerminalsLocation";
"l_noTicketTypes"="l_noTicketTypes";
"l_noValidDate"="l_noValidDate";
"l_noValidPlace"="l_noValidPlace";
"l_noValidPlate"="l_noValidPlate";
"l_noViolation"="l_noViolation";
"l_noZipcode"="l_noZipcode";
"l_no_data_found"="l_no_data_found";
"l_no_favorites"="l_no_favorites";
"l_no_media"="l_no_media";
"l_no_menu_authorization"="l_no_menu_authorization";
"l_no_notes"="l_no_notes";
"l_no_recent"="l_no_recent";
"l_noaddressfound"="l_noaddressfound";
"l_notAnSSN"="l_notAnSSN";
"l_notAvailable"="l_notAvailable";
"l_notRegistered"="l_notRegistered";
"l_notSameLocation"="l_notSameLocation";
"l_notValidZipCode"="l_notValidZipCode";
"l_not_activated"="l_not_activated";
"l_not_all_fields_filled"="l_not_all_fields_filled";
"l_not_set"="l_not_set";
"l_notariff"="l_notariff";
"l_notariff_star"="l_notariff_star";
"l_notfound"="l_notfound";
"l_nothingFound"="l_nothingFound";
"l_nozonesassigned"="l_nozonesassigned";
"l_nr"="l_nr";
"l_number"="l_number";
"l_numberm2"="l_numberm2";
"l_objectdata"="l_objectdata";
"l_objectnumber"="l_objectnumber";
"l_objectsFound"="l_objectsFound";
"l_objectsyncCasemanagement"="l_objectsyncCasemanagement";
"l_objectsyncGlobal"="l_objectsyncGlobal";
"l_objectsyncHectometermarker"="l_objectsyncHectometermarker";
"l_objectsyncInstance"="l_objectsyncInstance";
"l_objectsyncKilometermarker"="l_objectsyncKilometermarker";
"l_objectsyncLocation"="l_objectsyncLocation";
"l_objectsyncOffence"="l_objectsyncOffence";
"l_objectsyncPerson"="l_objectsyncPerson";
"l_objectsyncPlanning"="l_objectsyncPlanning";
"l_objectsyncReference"="l_objectsyncReference";
"l_objectsyncScanUnit"="l_objectsyncScanUnit";
"l_objectsyncTheme"="l_objectsyncTheme";
"l_objectsyncVehicle"="l_objectsyncVehicle";
"l_observation"="l_observation";
"l_observationTitel"="l_observationTitel";
"l_offence"="l_offence";
"l_offenceCategory"="l_offenceCategory";
"l_offenceCommunicated"="l_offenceCommunicated";
"l_officerNotFound"="l_officerNotFound";
"l_officerNotFoundMuni"="l_officerNotFoundMuni";
"l_officerNumber"="l_officerNumber";
"l_officerNumberPrint"="l_officerNumberPrint";
"l_officer_findings"="l_officer_findings";
"l_officer_observation"="l_officer_observation";
"l_oldest_ontop"="l_oldest_ontop";
"l_onStreetParkingCheck"="l_onStreetParkingCheck";
"l_onstreetpayment"="l_onstreetpayment";
"l_open"="l_open";
"l_openCase"="l_openCase";
"l_openPDF"="l_openPDF";
"l_openTasks"="l_openTasks";
"l_options"="l_options";
"l_optionsVariables"="l_optionsVariables";
"l_oranje"="l_oranje";
"l_other"="l_other";
"l_otherSignals"="l_otherSignals";
"l_outOfMemory"="l_outOfMemory";
"l_outbox"="l_outbox";
"l_outcome"="l_outcome";
"l_overview"="l_overview";
"l_overviewLabel"="l_overviewLabel";
"l_overviewPhoto"="l_overviewPhoto";
"l_overviewTask"="l_overviewTask";
"l_ownStatement"="l_ownStatement";
"l_ownerRegistrationDate"="l_ownerRegistrationDate";
"l_ownerRegistrationDatePrint"="l_ownerRegistrationDatePrint";
"l_paars"="l_paars";
"l_paidParkingZone"="l_paidParkingZone";
"l_parking"="l_parking";
"l_parkingCardNumber"="l_parkingCardNumber";
"l_parkingCardOutcome"="l_parkingCardOutcome";
"l_parkingPenalty"="l_parkingPenalty";
"l_parkingServicefailed"="l_parkingServicefailed";
"l_parking_expired"="l_parking_expired";
"l_parking_invalid"="l_parking_invalid";
"l_parking_unavailable_end"="l_parking_unavailable_end";
"l_parking_unavailable_start"="l_parking_unavailable_start";
"l_parking_valid"="l_parking_valid";
"l_parkingcard"="l_parkingcard";
"l_parkingcheck"="l_parkingCheck";
"l_password"="l_password";
"l_paste"="l_paste";
"l_pedestrian"="l_pedestrian";
"l_permit"="l_permit";
"l_permitName"="l_permitName";
"l_permitTypeDesc"="l_permitTypeDesc";
"l_permit_invalid"="l_permit_invalid";
"l_permit_valid"="l_permit_valid";
"l_permitcheck"="l_permitcheck";
"l_permitnumber"="l_permitnumber";
"l_person"="l_person";
"l_personData"="l_persondata";
"l_personUnderInvestigation"="l_personUnderInvestigation";
"l_photo"="l_photo";
"l_photoNotFound"="l_photoNotFound";
"l_photoProcessingFailed"="l_photoProcessingFailed";
"l_photoSelectFailed"="l_photoSelectFailed";
"l_photos"="l_photos";
"l_pinNotCorrect"="l_pinNotCorrect";
"l_pinNotUsable"="l_pinNotUsable";
"l_pinsDoNotMatch"="l_pinsDoNotMatch";
"l_placeofEstablishment"="l_placeofEstablishment";
"l_placeofresidency"="l_placeofresidency";
"l_plateNotValid"="l_plateNotValid";
"l_plate_format"="l_plate_format";
"l_platesFound"="l_platesFound";
"l_pledge"="l_pledge";
"l_policeofficeremail"="l_policeofficeremail";
"l_preferences"="l_preferences";
"l_prefix"="l_prefix";
"l_previousFines"="l_previousFines";
"l_priceOneHourPark"="l_priceOneHourPark";
"l_print"="l_print";
"l_printAmountPayed"="l_printAmountPayed";
"l_printTestLabel"="l_printTestLabel";
"l_print_observation"="l_print_observation";
"l_printerType"="l_printerType";
"l_printing"="l_printing";
"l_process"="l_process";
"l_processing"="l_processing";
"l_production"="l_production";
"l_production_pt"="l_production_pt";
"l_prohibitions"="l_prohibitions";
"l_provider"="l_provider";
"l_publishedBy"="l_publishedBy";
"l_putOnLabel"="l_putOnLabel";
"l_pvFailed"="l_pvFailed";
"l_pvId"="l_pvId";
"l_pvNotAvailable"="l_pvNotAvailable";
"l_questions"="l_questions";
"l_queueLengthTimerOn"="l_queueLengthTimerOn";
"l_radius"="l_radius";
"l_receipt"="l_receipt";
"l_receiptNumber"="l_receiptNumber";
"l_receivedAt"="l_receivedAt";
"l_recheck"="l_recheck";
"l_reformatDateFailed"="l_reformatDateFailed";
"l_refresh"="l_refresh";
"l_refreshGPS"="l_refreshGPS";
"l_refreshLocation"="l_refreshLocation";
"l_refreshTime"="l_refreshTime";
"l_reg_date"="l_reg_date";
"l_regionCode"="l_regionCode";
"l_register"="l_register";
"l_register_error"="l_register_error";
"l_register_success"="l_register_success";
"l_registerconcept"="l_registerconcept";
"l_registeredByOrganization"="l_registeredByOrganization";
"l_registering_device"="l_registering_device";
"l_registrationDate"="l_registrationDate";
"l_registrationDatePrint"="l_registrationDatePrint";
"l_registrationDateTime"="l_registrationDateTime";
"l_registrationNotFound"="l_registrationNotFound";
"l_registrationTitle"="l_registrationTitle";
"l_regulatedTimeConsumption"="l_regulatedTimeConsumption";
"l_regulation"="l_regulation";
"l_relatedcase"="l_relatedcase";
"l_remark"="l_remark";
"l_removalFailed"="l_removalFailed";
"l_remove"="l_remove";
"l_removePhoto"="l_removePhoto";
"l_removeReg"="l_removeReg";
"l_removed"="l_removed";
"l_removefilter"="l_removefilter";
"l_reported"="l_reported";
"l_rescan"="l_rescan";
"l_resendPhotos"="l_resendPhotos";
"l_resendThumbnail"="l_resendThumbnail";
"l_reset"="l_reset";
"l_resetAfter5Tries"="l_resetAfter5Tries";
"l_resetDBandReload"="l_resetDBandReload";
"l_resetDatabase"="l_resetDatabase";
"l_resetPin"="l_resetPin";
"l_resetUser"="l_resetUser";
"l_resetUserPrompt"="l_resetUserPrompt";
"l_restoreLayout"="l_restoreLayout";
"l_result"="l_result";
"l_resume"="l_resume";
"l_resyncFailed"="l_resyncFailed";
"l_retrieve"="l_retrieve";
"l_retrieveNewCase"="l_retrieveNewCase";
"l_retrievingNewCases"="l_retrievingNewCases";
"l_retryServerconnection"="l_retryServerconnection";
"l_rightType"="l_rightType";
"l_rightTypeDetail"="l_rightTypeDetail";
"l_rightsNotReadConfirmation"="l_rightsNotReadConfirmation";
"l_roadsigns"="l_roadsigns";
"l_route"="l_route";
"l_routeInfo"="l_routeInfo";
"l_rows"="l_rows";
"l_sameAddress"="l_sameAddress";
"l_sanction"="l_sanction";
"l_save"="l_save";
"l_saveAndExit"="l_saveAndExit";
"l_saveConceptCase"="l_saveConceptCase";
"l_saveRegisterCase"="l_saveRegisterCase";
"l_save_data"="l_save_data";
"l_saved"="l_saved";
"l_sbiCode"="l_sbiCode";
"l_sbiCodeDescription"="l_sbiCodeDescription";
"l_scan"="l_scan";
"l_scanEnableAutoRestart"="l_scanEnableAutoRestart";
"l_scanFailure"="l_scanFailure";
"l_scan_delay"="l_scan_delay";
"l_scancountry"="l_scancountry";
"l_scanner_light"="l_scanner_light";
"l_sealbagNumber"="l_sealbagNumber";
"l_search"="l_search";
"l_searchAddress"="l_searchAddress";
"l_searchExact"="l_searchExact";
"l_searchLetter"="l_searchLetter";
"l_searchNumber"="l_searchNumber";
"l_searchPlatesBy"="l_searchPlatesBy";
"l_search_numberorsurname"="l_search_numberorsurname";
"l_search_printers"="l_search_printers";
"l_searchstreetandestablishmentplace"="l_searchstreetandestablishmentplace";
"l_secondOfficer"="l_secondOfficer";
"l_section"="l_section";
"l_sector"="l_sector";
"l_sectorDesc"="l_sectorDesc";
"l_select"="l_select";
"l_selectFolder"="l_selectFolder";
"l_selectPrinter"="l_selectPrinter";
"l_selectTeam"="l_selectTeam";
"l_selectall"="l_selectall";
"l_selectedPrinter"="l_selectedPrinter";
"l_selectscancountry"="l_selectscancountry";
"l_send"="l_send";
"l_send1LabelPhoto"="l_send1LabelPhoto";
"l_send1RemovalPhoto"="l_send1RemovalPhoto";
"l_sendPhotos"="l_sendPhotos";
"l_sendRegistration"="l_sendRegistration";
"l_sendTaskData"="l_sendTaskData";
"l_server"="l_server";
"l_serverError"="l_serverError";
"l_serviceDidNotRespond"="l_serviceDidNotRespond";
"l_serviceError"="l_serviceError";
"l_serviceErrorRetrieveList"="l_serviceErrorRetrieveList";
"l_service_show_time"="l_service_show_time";
"l_setCurrentTime"="l_setCurrentTime";
"l_setReceipt"="l_setReceipt";
"l_settings"="l_settings";
"l_shift"="l_shift";
"l_shift_A"="l_shift_A";
"l_shift_D"="l_shift_D";
"l_shift_Z"="l_shift_Z";
"l_signal"="l_signal";
"l_signalServicefailed"="l_signalServicefailed";
"l_socialsecuritynr"="l_socialsecuritynr";
"l_start"="l_start";
"l_startApp"="l_startApp";
"l_startDate"="l_startDate";
"l_startGrip"="l_startGrip";
"l_startTime"="l_startTime";
"l_started"="l_started";
"l_state"="l_state";
"l_state100chars"="l_state100chars";
"l_statement"="l_statement";
"l_statementRecordedBy"="l_statementRecordedBy";
"l_statistics"="l_statistics";
"l_status"="l_status";
"l_statusClaimed"="l_statusClaimed";
"l_statusControl_F"="l_statusControl_F";
"l_statusControl_K"="l_statusControl_K";
"l_statusControl_L"="l_statusControl_L";
"l_statusControl_O"="l_statusControl_O";
"l_status_unknown"="l_status_unknown";
"l_street"="l_street";
"l_street24chars"="l_street24chars";
"l_streetAtNumber"="l_streetAtNumber";
"l_streetNotDetermined"="l_streetNotDetermined";
"l_streetnumber"="l_streetnumber";
"l_streetpropositions"="l_streetpropositions";
"l_subjectnumber"="l_subjectnumber";
"l_successful"="l_successful";
"l_surname"="l_surname";
"l_swipe"="l_swipe";
"l_syncError"="l_syncError";
"l_syncLast"="l_syncLast";
"l_sync_end"="l_sync_end";
"l_sync_finished"="l_sync_finished";
"l_sync_start"="l_sync_start";
"l_sync_status"="l_sync_status";
"l_synchronization"="l_synchronization";
"l_synchronize"="l_synchronize";
"l_syncinit"="l_syncinit";
"l_tariff"="l_tariff";
"l_tariffgroup"="l_tariffgroup";
"l_task"="l_task";
"l_taskFault"="l_taskFault";
"l_taskoutcomeNotFound"="l_taskoutcomeNotFound";
"l_tasks"="l_tasks";
"l_tax"="l_tax";
"l_taxi"="l_taxi";
"l_team"="l_team";
"l_templateName"="l_templateName";
"l_tenant"="l_tenant";
"l_terminal"="l_terminal";
"l_test"="l_test";
"l_text"="l_text";
"l_thirdOfficer"="l_thirdOfficer";
"l_ticketType"="l_ticketType";
"l_ticket_nr"="l_ticket_nr";
"l_time"="l_time";
"l_timeArrived"="l_timeArrived";
"l_timeOutException"="l_timeOutException";
"l_timeRestriction"="l_timeRestriction";
"l_timeScanned"="l_timeScanned";
"l_timeToArrive"="l_timeToArrive";
"l_tokenExpired"="l_tokenExpired";
"l_totalDue"="l_totalDue";
"l_touchIdFail"="l_touchIdFail";
"l_touchIdSucces"="l_touchIdSucces";
"l_touchIdWillNotUse"="l_touchIdWillNotUse";
"l_tow"="l_tow";
"l_trackDown"="l_trackDown";
"l_trackDownDesc"="l_trackDownDesc";
"l_translationLanguage"="l_translationLanguage";
"l_transpondercardcode"="l_transpondercardcode";
"l_truck"="l_truck";
"l_twoPhotosMandatory"="l_twoPhotosMandatory";
"l_type"="l_type";
"l_typeCode"="l_typeCode";
"l_typeTextHere"="l_typeTextHere";
"l_unClamp"="l_unClamp";
"l_unclaimed"="l_unclaimed";
"l_unclampRequestSetOn"="l_unclampRequestSetOn";
"l_unknown"="l_unknown";
"l_unpair"="l_unpair";
"l_updateAvailable"="l_updateAvailable";
"l_updateAvailableMDM"="l_updateAvailableMDM";
"l_updateAvailableStoreMDM"="l_updateAvailableStoreMDM";
"l_uploadCaseFailedRetry"="l_uploadCaseFailedRetry";
"l_usage"="l_usage";
"l_user"="l_user";
"l_userMustBeEmail"="l_userMustBeEmail";
"l_userNotInInstance"="l_userNotInInstance";
"l_userPVNotExists"="l_userPVNotExists";
"l_username"="l_username";
"l_usesInterpreter"="l_usesInterpreter";
"l_usesLegalAssistance"="l_usesLegalAssistance";
"l_valid"="l_valid";
"l_validFrom"="l_validFrom";
"l_validParkingRight"="l_validParkingRight";
"l_validTo"="l_validTo";
"l_validateVehicleMessage"="l_validateVehicleMessage";
"l_validateVehicleOr"="l_validateVehicleOr";
"l_validationNeeded"="l_validationNeeded";
"l_validityMessage"="l_validityMessage";
"l_validparkingtime"="l_validparkingtime";
"l_van"="l_van";
"l_variables"="l_variables";
"l_vehicle"="l_vehicle";
"l_vehicleDetails"="l_vehicleDetails";
"l_vehicleInformation"="l_vehicleInformation";
"l_vehicleServicefailed"="l_vehicleServicefailed";
"l_vehicleTypeGroup"="l_vehicleTypeGroup";
"l_vehicle_invalid"="l_vehicle_invalid";
"l_vehicle_law"="l_vehicle_law";
"l_vehicletype"="l_vehicletype";
"l_verifyAddress"="l_verifyAddress";
"l_verifyPin"="l_verifyPin";
"l_verifyTouchId"="l_verifyTouchId";
"l_version"="l_version";
"l_vessel"="l_vessel";
"l_wait"="l_wait";
"l_wantToLogOn"="l_wantToLogOn";
"l_wantToRemoveCase"="l_wantToRemoveCase";
"l_wantToRemovePhoto"="l_wantToRemovePhoto";
"l_wantToSaveContinueOrTryAgain"="l_wantToSaveContinueOrTryAgain";
"l_wantToTryAgain"="l_wantToTryAgain";
"l_wantToUsePin"="l_wantToUsePin";
"l_wantToUseTouchId"="l_wantToUseTouchId";
"l_wanted"="l_wanted";
"l_warning"="l_warning";
"l_warningMessage"="l_warningMessage";
"l_withdraw"="l_withdraw";
"l_writeExtraTicket"="l_writeExtraTicket";
"l_xTerminalsLocation"="l_xTerminalsLocation";
"l_yearofbirth"="l_yearofbirth";
"l_yes"="l_yes";
"l_zipCode"="l_zipCode";
"l_zipCode12chars"="l_zipCode12chars";
"l_zoneCode"="l_zoneCode";
"l_zoneDesc"="l_zoneDesc";
"noCaution"="No caution";
"open_settings"="Open Settings";
"otherDocuments"="Other Documents";
"passport"="Passport";
"pl_banDurationDays"="pl_banDurationDays";
"pl_banDurationMonths"="pl_banDurationMonths";
"pl_banDurationWeeks"="pl_banDurationWeeks";
"pl_casetime"="pl_casetime";
"pl_char_limit"="pl_char_limit";
"pl_checkCard"="pl_checkCard";
"pl_chooseNearbyStreets"="pl_chooseNearbyStreets";
"pl_goto"="pl_goto";
"pl_identification"="pl_identification";
"pl_interior"="Interior";
"pl_internalRemark"="pl_internalRemark";
"pl_letter"="pl_letter";
"pl_locationDescription"="pl_locationDescription";
"pl_locationSpec"="pl_locationSpec";
"pl_location_indication"="pl_location_indication";
"pl_minimal2input"="pl_minimal2input";
"pl_minimal3input"="pl_minimal3input";
"pl_minimalInput"="pl_minimalInput";
"pl_minimalStreetinput"="pl_minimalStreetinput";
"pl_notepad"="pl_notepad";
"pl_number"="pl_number";
"pl_onStreetGPS"="pl_onStreetGPS";
"pl_paymentMethod"="pl_paymentMethod";
"pl_questionnaires"="pl_questionnaires";
"pl_scanUnit"="pl_scanUnit";
"pl_tax"="pl_tax";
"pl_total"="pl_total";
"pl_towed"="pl_towed";
"pl_translate"="pl_translate";
"pleaseWait"="Please wait...";
"scanner_app_not_installed"="scanner_app_not_installed";
"selectDocumentType"="selectDocumentType";
