"CaseManagementData"="Case Management Data";
"ERR_SP_00"="Undefined error or custom error";
"ERR_SP_01"="Authentication failed";
"ERR_SP_02"="Not authenticated";
"ERR_SP_03"="Unknown CaseID";
"ERR_SP_04"="Case not allowed";
"ERR_SP_05"="Internal Service Error";
"ERR_SP_06"="License plate update not allowed";
"GetCarInfo"="Retrieving car info";
"GetParkings"="Retrieving parkings";
"GetSignals"="Retrieving signals";
"Instance"="Instance";
"LabelAanbrengen"="Applying label";
"Location"="موقع";
"LocationData"="Location information";
"NotificatieAanbrengen"="Apply label";
"Offence"="Offence";
"Person"="Person";
"Planning"="Planning";
"RED-ASGN-01"="المهمة الحالية";
"RED-ASGN-02"="لا توجد مهمة حالية";
"RED-ASGN-03"="User already assigned";
"RED-ASGN-04"="User logged on with other case type, log out first";
"RED-ASGN-05"="مهمة ناجحة";
"RED-ASGN-06"="Assignment failed, no available cloud or user already assigned";
"RED-ASGN-07"="الخدمة السحابية غير متوفرة";
"RED-ASGN-08"="User unassigned from cloud";
"RED-ASGN-09"="User was assigned, but unassignment failed";
"RED-ASGN-10"="User was not assigned";
"RED-ASGN-11"="User switched";
"RED-ASGN-12"="User switch failed. Did not find two users in two different groups";
"ReferenceData"="Reference information";
"Users"="Users";
"VEHICLE_SIGNAL"="Redora Signal";
"Vehicle"="مركبة";
"WachtenOpVerwijderen"="Waiting on expiration for removal";
"WaitingForLocation"="Waiting for gps location";
"_usesLegalAssistance"="Uses legal assistance";
"appmode_checklabel"="Check label";
"appmode_labeloverview"="Label overview";
"appmode_register"="Register";
"appmode_registerconcept"="Register concept";
"appmode_registerlabel"="Register label";
"bt_add"="Add";
"bt_again"="Again";
"bt_back"="Back";
"bt_cancel"="Cancel";
"bt_check"="Check";
"bt_complete"="Complete";
"bt_continue"="Continue";
"bt_deselectAll"="Deselect all";
"bt_directPV"="Direct PV";
"bt_done"="Done";
"bt_edit"="تعديل";
"bt_exit"="خروج";
"bt_info"="Info";
"bt_lightoff"="Light off";
"bt_lightoff_once"="Light off once";
"bt_lighton"="Light on";
"bt_lighton_once"="Light on once";
"bt_map"="Map";
"bt_markOverviewPhoto"="Make this photo the overview photo";
"bt_new"="New";
"bt_no"="No";
"bt_ok"="OK";
"bt_register"="Register";
"bt_remove"="إزالة";
"bt_retry"="Retry";
"bt_selectAll"="Select all";
"bt_send"="إرسال";
"bt_stop"="Stop";
"bt_unknown"="Unknown";
"bt_vehicle_scan"="Scan";
"bt_view"="View";
"bt_yes"="Yes";
"camera_permission_dialog_message"="To enable photo capture, this app requires access to your camera. Please grant permission by opening the Settings app.";
"camera_permission_dialog_title"="Allow Twyns App to Access Your Camera";
"camera_permission_scan_dialog_message"="To enable document scanning, this app requires access to your camera. Please grant permission by opening the Settings app.";
"cancel"="Cancel";
"drivingCard"="Driving licence";
"e_area0001"="Not an appropriate area";
"e_area0002"="Something went wrong trying to find the area:";
"e_log0001"="خطأ في تسجيل الدخول";
"e_log0002"="There was a login error looking up the Employee information";
"e_log0003"="خطأ في تسجيل الدخول";
"e_log0004"="Login error, try again please.";
"e_mand0002"="The following mandatory fields are not filled:";
"e_mandByLawNotFound"="Mandatory bylaw not found";
"e_multipleByLaws"="Multiple bylaws found, contact your system administrator";
"e_noInstanceparameter"="Instance parameter not found";
"e_prt0001"="Printing failed, please check if the printer is connected and try again";
"e_select_zone"="Select a zone";
"e_ser0001"="Service call could not be completed successfuly.";
"e_ser0002"="Query cannot be processed, try alternate input";
"e_ser0003"="Service not available";
"e_serverError"="Parking check failure";
"e_serverErrorNPR_1"="Parking check for non-regulated zone";
"e_serverErrorNPR_100"="Area is not provided ";
"e_serverErrorNPR_104"="Checking organisation has no authorisation to check ";
"e_serverErrorNPR_302"="Technical failure, please contact Service House Parking ";
"e_serverErrorNPR_5"="No PSRight required";
"e_serverErrorNPR_999"="Technical failure, please contact ICT Servicedesk ";
"e_sync0001"="There was an error during synchronization initialization.";
"e_sync0002"="Synchronization error";
"e_sync0003"="Not all information is filled out";
"e_upgradeError"="Error during upgrade, please contact support.";
"e_userNotFound"="Username not found. Please contact support. Application will close.";
"e_val00004"="No streets found";
"e_val00005"="No areas found";
"e_val00006"="Refresh your GPS coordinates.";
"e_val00007"="This is not a valid parkin area";
"e_val00008"="No location selected";
"e_val00009"="No label scanned";
"e_val00010"="No photo added";
"hdr_default"="Default country";
"hdr_manual"="MANUAL";
"hdr_rdw"="Vehicle Details";
"hdr_scan"="SCAN";
"hdr_scanRdw"="SCAN + Vehicle Details";
"i_anpr0001"="No license plate detected";
"i_anpr0002"="No matching licenseplate pattern";
"i_confirmsync"="Your last synchronization was ~~~, do you want to synchronize now?";
"i_connectPrinterFailed"="Connection to the printer failed, please check if bluetooth is turned on or choose to add a printer";
"i_daysago"="days ago";
"i_deceased"="Person is deceased";
"i_declinedLegalAssistance"="Suspect has been told he can reclaim his right to legal assistance";
"i_emigrate"="Person has emigrated";
"i_faulted"="Faulted";
"i_fsc0001"="Service invocation error";
"i_fsc0002"="No vehicle information found";
"i_fsc0007"="No network available";
"i_hoursago"="hours ago";
"i_interpretationCommunicated"="Right to interpretation communicated";
"i_interpretationNotCommunicated"="Right to interpretation not communicated";
"i_interpreter"="Id of interpreter";
"i_legalAssistanceCommunicated"="Right to legal assistance communicated";
"i_legalAssistanceNotCommunicated"="Right to legal assistance not communicated";
"i_noCities"="No cities";
"i_noMunicipalities"="No municipalities";
"i_no_person"="No person has been filled in";
"i_notDeclinedLegalAssistance"="Suspect has not been told he can reclaim his right to legal assistance";
"i_notUsesThisRight"="Suspect did not want to make use of this right";
"i_offenceCommunicated"="Offence communicated";
"i_offenceNotCommunicated"="Offence not communicated";
"i_personNotFound"="Person not found";
"i_restart"="The application has been idle for more then #idletimeout# minutes,                                                                                                            would you like to restart?";
"i_select_street"="Select street";
"i_syncwarning"="Your last synchronization was ~~~, this is more then seven days ago you need to sync now.";
"i_tooManyPersonRecords"="Too many persons found, please fill in more fields to specify";
"i_translationLanguage"="The hearing took place in the suspect intelligible language, namely";
"i_usesThisRight"="Suspect wants to make use of this right";
"idCard"="Identity Card";
"l_2Characterinput"="Type 2 characters";
"l_3Characterinput"="Type 3 characters";
"l_Diagnostics"="Diagnostics";
"l_HouseNumberMustBeNumeric"="House number must be numeric";
"l_NSproduction"="NS";
"l_NSproductionEducation"="NS education";
"l_NoPlate"="No plate";
"l_NoPlates"="No plates found";
"l_NoPlatesConfirmation"="No plates found, do you want to refresh the list?";
"l_NoRefreshPlate"="لوحة لم تعد في القائمة";
"l_OVdemo"="OV demo";
"l_OffenceCommunicatedNoStatement"="Statement offence not communicated";
"l_Pin"="Pin number";
"l_RDWowner"="Owner";
"l_RDWownerCheck"="Vehicle + Owner check";
"l_StatementPledge"="Pledge";
"l_WatchGPSInterval"="Number of seconds before route refresh";
"l_about"="About";
"l_acceptance"="Acceptance";
"l_acceptanceTest"="Acceptance test";
"l_acceptance_pt"="Acceptance Pub. Tp.";
"l_activeCases"="Add to case";
"l_addAddressWithoutGPS"="Manually entering the address is not possible without GPS";
"l_addData"="Do you want to complete the case or add some more data?";
"l_addLabel"="Add label";
"l_addTax"="Additional costs";
"l_addTaxDisplay"="Add. costs";
"l_additionalAssesment"="Additional assesment";
"l_address"="Address";
"l_addressUnderInvestigation"="Address under investigation";
"l_administrationcosts"="Handling fee";
"l_all"="All";
"l_allowed"="Allowed";
"l_amountDue"="Amount due";
"l_amountSanction"="Sanction amount";
"l_applyLabel"="Apply label";
"l_applyfilter"="Apply filter";
"l_area"="Area";
"l_areas"="Areas";
"l_askAdministrator"="Ask administrator";
"l_assessmentNumber"="Assessment number";
"l_assessor"="Assessor";
"l_assignedArea"="Assigned area";
"l_attempt"="Attempt";
"l_authenticating_user"="Authenticating user";
"l_auto_restart_enabled"="Auto Restart Enabled";
"l_auto_scan_time"="Auto Scan Time";
"l_averages"="Averages";
"l_batches"="Batches";
"l_birthdate"="Birthdate";
"l_branchNumber"="Branch number";
"l_brand"="العلامة التجارية / نوع المركبة";
"l_brandtype"="Type";
"l_building"="Additional building info";
"l_building100chars"="Additional building info (100 chars)";
"l_bus"="Bus";
"l_by"="By";
"l_calculatedPrice"="Calculated price";
"l_cancel"="Cancel";
"l_cannotRegisterYourself"="You cannot register yourself";
"l_cannotUseAddress"=" This address is not in an enforcement location and therefore cannot be used. Do you want to continue anyway?";
"l_car"="Car";
"l_cardNumber"="card number";
"l_cardcheck"="Card check";
"l_cards"="Cards";
"l_case"="Case";
"l_caseAndTaskTypeNotFound"="There was no CaseType and/or TaskType found please contact the system administrator.";
"l_caseNoLongerOnServer"="Case no longer available on server, you will be taken back to the beginning";
"l_caseNotFound"="Case not found";
"l_caseNotRetrieved"="Something went wrong retrieving the case, please try again";
"l_caseOnlineFailedOutboxQuestion"="Unable to update case online, case will be put into the outbox. Do you want to try again or click done and try to send it later?";
"l_caseOnlineFailedSetToOutbox"="Unable to update case online, case will be put into the outbox.";
"l_caseType"="نوع الحالة";
"l_caseUpdateFailed"="Case update failed, please try again";
"l_caseWillBeClosed"="This case will be closed.";
"l_case_not_opened"="Case can not be opened";
"l_cases"="Cases";
"l_casesToBeUpdated"="To be updated";
"l_cashAmount"="Cash amount";
"l_changeAddress"="Change address";
"l_changeCaseTo"="Do you want to change the case to:";
"l_changeEnvironment"="If you change the environment all data will be reset and you will have to log in again, do you want to do this?";
"l_changeFilter"="Change filter";
"l_changeHouseNumber"="Change house number";
"l_changeInstance"="If you change the instance all data will be reset, do you want to do this?";
"l_changePosition"="Change position";
"l_changeTheme"="Change theme";
"l_changeTravelMode"="Change travel mode";
"l_channel"="Channel";
"l_check"="Check";
"l_checkBounds"="Value must be between #lower# and #upper#";
"l_checkBoundsValue"="#value# is not a valid number";
"l_checkLicensePlate"="Check license plate";
"l_checkPin"="قم بإدخال رقم التعريف الشخصي";
"l_checkTime"="التحقق من الوقت";
"l_checkUserDatabase"="Checking user in database ...";
"l_checkUserPV"="Checking user PV ...";
"l_check_types"="Check Types";
"l_checkcard"="Check card";
"l_checked"="Checked";
"l_checkedOut"="Checked out";
"l_checkedOutFollow"="Chekced out for follow up";
"l_checking_service"="Checking service access";
"l_checklocation"="تحقق من الموقع";
"l_checkout"="التحقق من";
"l_checks"="Checks";
"l_checkvehicle"="Check vehicle";
"l_choose"="make a choice";
"l_chooseCity"="Choose City";
"l_chooseHost"="Please select a host location.";
"l_chooseMunicipality"="Choose Municipality";
"l_chooseOneCheckType"="Choose at least one checktype";
"l_chooseTaskOutcome"="Choose a task outcome";
"l_chooseTeam"="Choose team";
"l_chooseZone"="Choose Zone";
"l_choosearea"="Choose area";
"l_chooselabel"="Choose label";
"l_choosestreet"="Choose street";
"l_choosetask"="Choose task";
"l_choosevalue"="Choose value";
"l_chosenZone"="Chosen zone";
"l_chosenaddress"="Chosen address";
"l_city"="City";
"l_city36chars"="City (36 chars)";
"l_cityNotOffenceCity"="الموقع الذي تم العثور عليه ليس في منطقة التنفيذ. يرجى إختيار شارع أخر أو المحاولة مرة أخرى. تم العثور على الموقع:";
"l_claimFailed"="Something went wrong claiming the case, please try again";
"l_claimedByMe"="Claimed by me";
"l_claimedByOther"="Claimed by other";
"l_claimedByOtherRetrievingCases"="هذه الحالة تم طلبها من قبل شخص أخر، قم بتحديث قائمة الحالات. يرجى محالولة إختيار حالة أخرى.";
"l_clamp"="Clamp";
"l_clampNumber"="Clamp number";
"l_clampNumberNotFound"="Clamp number not found";
"l_clampTime"="Clamp time";
"l_clickToAddPhotos"="Click here to add photos";
"l_close"="Close";
"l_closePDF"="Close PDF";
"l_cocnumber"="CoC number";
"l_code"="Code";
"l_color"="Color";
"l_concepts"="Concepts";
"l_confirm_removelist"="Do you want to delete this list?";
"l_connectiondescription"="Connection description";
"l_continueOffline"="Continue offline";
"l_continueWithoutConnection"="Continue without connection";
"l_coordinates"="GPS";
"l_countries"="Countries";
"l_country"="Country";
"l_countryPlateUnknownChange"="Country of the licenseplate is unknown, are you sure you want to continue?";
"l_countryUnknown"="Unknown";
"l_countryUnknownCode"="UNK";
"l_countryadress"="Country address";
"l_countryoforigin"="Country of origin";
"l_createPin"="إنشاء رقم التعريف الشخصي";
"l_currentLocation"="الموقع الحالي";
"l_currentPosition"="Current Position";
"l_currentValidity"="Current validity";
"l_current_task"="Current task";
"l_dashesOnly"="It is not allowed to add anything other than dashes";
"l_date"="Date";
"l_dateTimeFuture"="Date and time are too far in the future";
"l_dateTimePast"="Date and time are too far in the past";
"l_datetime"="Date and time";
"l_daysMonth"="This month has no ~~ days";
"l_declinedLegalAssistance"="Reclaim right legal assistance";
"l_description"="الوصف";
"l_details"="Details";
"l_development"="Development";
"l_digit_count"="Please enter a 5 or 9 digit number";
"l_directPerfectview"="Direct PerfectView";
"l_disabled_parking"="Disabled Parking";
"l_discard"="Discard";
"l_distance"="Distance";
"l_doNotChangePlate"="Please do not change the number of the license plate, only set the dashes ( - ) in the correct place";
"l_doNotHaveFingerprints"="You have not registered any fingerprints on your device";
"l_doNotHaveLogOnCode"="You have not set a verification code on your device";
"l_doYouWantToPasteData"="Do you want to paste the data:";
"l_documentAlreadyProcessedException"="Case already processed and no longer available";
"l_documentNotFoundException"="Case not found";
"l_duplicate"="Duplicate";
"l_duplicateScan"="Second scan of the same vehicle in short time";
"l_dutch"="Dutch";
"l_editAddress"="Edit address";
"l_editData"="Contact your system manager or edit the data";
"l_editExistingOptionsVariables"="Do you want to view the existing text or enter a new one?";
"l_editIdentification"="Edit identification";
"l_editKindOfViolation"="Edit violation";
"l_editLicensePlate"="Edit license plate";
"l_editTime"="Edit time";
"l_email"="E-mail address";
"l_emmDataInCorrect"="The data to synchronize have not been registered correctly in EMM, contact support please.";
"l_emmNotCorrect"="The data comming from EMM is not correct please contact your system manager";
"l_employeeNumberPrint"="Employee no.";
"l_emptyPrinterMac"="00:00:00:00:00:00";
"l_emptyPrinterName"="None";
"l_end"="End";
"l_endOfList"="End of list";
"l_endTime"="Time ended";
"l_enforcementObject"="Legal entity";
"l_english"="English";
"l_enter5DigitPin"="Enter a 5-digit pin";
"l_enterNumber"="Enter a number";
"l_enterPin"="أدخل رقم التعريف الشخصي";
"l_enterUsernameFirst"="Enter username first before environment list can be activated!";
"l_enteraddress"="No address found, enter address";
"l_environment"="Environment";
"l_environmentListActivated"="Environments list is now activated!";
"l_environmentalClassification"="Environmental Classification";
"l_environmentalClassificationPrefix"="Euro";
"l_error"="Error";
"l_errorCode"="Error code";
"l_errorDuringUpgrade"="Error during upgrade,                                                                                                                                           please contact support";
"l_errorUserCheck"="Error while checking/creating user,                                                                                                                             please try again or edit the data";
"l_errored"="Errored";
"l_europeanVehicleType"="European Vehicle Type";
"l_execByPartner"="Interrogated by Partner";
"l_executing_anpr"="Executing ANPR";
"l_exemption"="Exemption";
"l_exemptionstartdate"="Exemption start date";
"l_existingOptionsVariables"="Do you want to change the existing text?";
"l_exit"="Exit";
"l_exitApplication"="App will be closed";
"l_exp_date"="Expires on";
"l_explanation"="Explanation";
"l_externalcollector"="External collector";
"l_extraInformation"="Extra information";
"l_favorites"="Favorits";
"l_fillStreetCity"="Please fill in a street and a city";
"l_filter"="Filter";
"l_fineWithdrawn"="You have chosen 'Cautie: No', this means that the fine will be withdrawn is this correct?";
"l_finished"="Finished";
"l_firstName"="First name";
"l_flash_auto"="Flash auto";
"l_flash_off"="Flash off";
"l_flash_on"="Flash on";
"l_folderNotFound"="No folders found";
"l_follow"="متابعة";
"l_followUp"="متابعة";
"l_found"="Found";
"l_foundHistoricCase"="Already written ticket found";
"l_fuel"="Fuel";
"l_fullName"="Full name";
"l_fullname"="Full name";
"l_gender"="Gender";
"l_georgia"="Georgia";
"l_germanPlateDash"="This is a german license plate, please set dashes ( - ) in the correct place";
"l_germany"="Germany";
"l_getDataFailed"="Sorry, loading the data has failed.";
"l_getParkRegistrations"="Getting parking registrations...";
"l_getPreviousFines"="Get previous issued fines";
"l_getZonesTerminals"="Getting zones and terminals...";
"l_give5NumberPin"="You must enter a 5-digit PIN";
"l_giveValidParkingRight"="Are you sure you want to give out a valid parking right?";
"l_gps_no_fix"="No GPS Fix found. You can manually enter your location.";
"l_gps_refresh"="إعادة تحديث تغطية الموقع";
"l_handle"="معالجة / تعامل";
"l_handling"="Handling";
"l_hasParkingRight"="Has parkingRight";
"l_hasValidParkingRight"="A valid parking right is given";
"l_haveToUpdate"="This version #currentversion# of the app is no longer supported please update your app to #newversion#. You will be redirected to the download page.";
"l_haveToUpdateMDM"="This version #currentversion# of the app is no longer supported please update your app to #newversion#. Ask your administrator to update the app.";
"l_haveToUpdateStoreMDM"="This version #currentversion# of the app is no longer supported please update your app to #newversion#. Download the app from the appstore or ask your administrator to release the new version if the app is pushed to your device.";
"l_hectometerMarkersNearby"="Hectometer makers nearby";
"l_history"="History";
"l_hostlocation"="Host location";
"l_hours"="Hours";
"l_houseNumberAdd"="House number addition";
"l_houseNumberAdd7chars"="House number addition (7 chars)";
"l_houseNumberAddition"="House number addition";
"l_houseNumberAddition_short"="add...";
"l_houseletter"="House letter";
"l_houseletter_short"="letter";
"l_housenumber"="House number";
"l_housenumber5chars"="House number (5 chars)";
"l_housenumber_short"="number";
"l_imageGallery"="Image gallery";
"l_info"="Information";
"l_initDBFailed"="The loading of the necessary data failed. The app will be cleaned and you will have to log in again.";
"l_initialize"="Initialize";
"l_initialize_text"="Your device is not initialized to use this service. Please enter your username to register this device. When support activated your account you can start using the application.";
"l_initialize_title"="Initialize";
"l_initials"="Initials";
"l_inserted"="Inserted";
"l_inspectionExpirationDate"="Inspection Expiration Date";
"l_instance"="Instance";
"l_insuranceDate"="Insurance Date";
"l_internalRemark"="Internal remark";
"l_interpreterCommunicated"="Right to us a translator communicated";
"l_interpreterNR"="Interperter number";
"l_invalid"="Invalid";
"l_invalidLoginCredentails"="The login details do not match, you must log in again. The data will be erased.";
"l_isNotFilled"="is not filled";
"l_keepLocation"="Keep location";
"l_kind"="Kind";
"l_kindOfHandling"="نوع المعالجة / نوع التعامل";
"l_kindOfLabel"="Kind of label";
"l_kindOfRoad"="Kind of road";
"l_kindOfVehicle"="Kind of vehicle";
"l_label"="Label";
"l_labelCheck"="Check label";
"l_labelExists"="This label already exists in the system";
"l_labelOverview"="Label overview";
"l_labelPhoto"="Label photo";
"l_lastChosen"="Last chosen";
"l_lastChosenOutcome"="Last chosen outcome";
"l_lastsync"="Last sync";
"l_legalAssistCommunicated"="Right to use legal assistance communicated";
"l_legalAssistance"="Right to assistance communicated";
"l_legalForm"="Legal form";
"l_licensePlateCountryUnknown"="Country of the licenseplate is unknown";
"l_licenseplate"="License plate";
"l_licenseplateCountries"="License plate Countries";
"l_list"="قائمة";
"l_listNotAvailable"=" No changes possible, list is not available (anymore)";
"l_loadLicenseplate"="Processing License plate";
"l_loadPhoto"="Processing photo";
"l_load_map"="Load map";
"l_loading"="Loading";
"l_loadingParam"="Loading parameters";
"l_location"="الموقع ";
"l_locationFound"="Location found";
"l_locationVerified"="Location used";
"l_logOffDate"="Logoff date";
"l_logOffTime"="Logoff time";
"l_logOn"="Log in";
"l_logOnDate"="Logon date";
"l_logOnDuration"="Logon duration";
"l_logOnStatus"="Logon status";
"l_logOnTime"="Logon time";
"l_logOut"="Log out";
"l_loggingOut"="Logging out";
"l_login"="تسجيل دخول";
"l_loginAccount"="Log in with your account";
"l_loginAuthorisation"="You are not authorized to log in";
"l_loginInstance"="Choose your instance";
"l_loginName"="Login";
"l_loginNoFunctions"="You have no functions / roles for this application. Your current roles are: ";
"l_loginNoOfficerId"="You cannot log in, officer identification is missing. Contact system administration.";
"l_loginRegister"="Enter your login details to register.";
"l_loginServer"="Choose the environment to sign up for";
"l_loginText1"="You log in to the";
"l_loginText2"="environment as";
"l_loginUserIncomplete"="Your login details are not complete, contact your administrator";
"l_loginUserIncorrect"="You are trying to log in as a different user than the one the app was installed with. Reset the user or log in as the correct user.";
"l_loginUserNotFound"="The user was not found in the database, contact your administrator";
"l_loginWelcome"="Welcome to Twyns.";
"l_logoff"="تسجيل خروج";
"l_mandatoryBrand"="العلامة التجارية إلزامية، يرجى ملء العلامة التجارية.";
"l_mandatoryClampNumber"="Clamp number is mandatory";
"l_mandatoryLocation"="The location data is not complete.";
"l_mandatoryNHACountryLicense"="Country of the licenseplate cannot be unknown";
"l_mandatoryOptions"="It is mandatory to fill in the options";
"l_mandatoryPhoto"="You need to take a photo";
"l_mandatoryPhotoClamp"="You need to take a photo before you clamp the vehicle and a photo after you clamp the vehicle";
"l_mandatoryPhotoReceipt"="Please take a photo of the receipt";
"l_mandatoryPhotoUnclamp"="You need to take a photo before you unclamp the vehicle and a photo after you unclamp the vehicle";
"l_mandatoryRegionCodeFrance"="For France region code is mandatory";
"l_mandatoryScanUnit"="If queue is enabled, choice of scan unit is mandatory.";
"l_mandatoryStatement"="Statement is mandatory when a person is added";
"l_mandatoryVehicle"="The vehicle data is not complete.";
"l_manual"="Manual";
"l_manualLocation"="Manual";
"l_map"="Map";
"l_markPhotoReceipt"="Please mark a photo as receipt";
"l_mastercode"="Master code";
"l_max6Photos"="You can send a maximum of 6 photos";
"l_max7Photos"="You can send a maximum of 7 photos";
"l_max8Photos"="You can send a maximum of 8 photos";
"l_maxPhotosTaken"="You can't take more than #maximum# photos";
"l_maxdatetime"="Time exceeds current date and time";
"l_mediaGalleryError"="Media gallery error: ";
"l_member"="Member";
"l_menu"="Menu";
"l_menuSettings"="Settings";
"l_minPhotosMandatory"="You need to take minimal #minimum# photos";
"l_minmaxPhotosMandatory"="You need to take minimal #minimum# but not more than #maximum# photos";
"l_minutes"="Minutes";
"l_moreThen1Area"="More then one area was found. Please contact your system manager";
"l_moreThenOnePersonFound"="More then one person found";
"l_motorcycle"="Motorcycle";
"l_multiUserDevice"="Device used for multiple users";
"l_multimedia"="Multimedia";
"l_multipleFiltersSet"="Multiple filters applied";
"l_multipleZipcodes"="Multiple zipcodes found, specify by zipcode and/or housenumber";
"l_municipality"="Municipality";
"l_municipalityOfBirth"="Municipality of birth";
"l_mustTakePicures"="You still need to take photos";
"l_myLocation"="My location";
"l_name"="Name";
"l_nameTaxpayer"="Name taxpayer";
"l_nan"="Not a number";
"l_nationality"="Nationality";
"l_nearbyAddresses"="Nearby addresses";
"l_nearbyDevicesPermission"="To access real Bluetooth devices, you need to enable 'Nearby Devices' permission for this app through Android settings.";
"l_nearbyStreets"="Streets nearby";
"l_nearestHectometerMarker"="Nearest hectometer marker";
"l_newest_ontop"="Newest on top";
"l_nex25"="Next 25";
"l_next"="التالي";
"l_nha"="Parking ticket";
"l_no"="No";
"l_noActiveCloudFor"="At this moment there is no active cluster for:";
"l_noAreaOrMultipleArea"="No or multiple areas found";
"l_noAuthorization"="You don't have any authorizations";
"l_noBrand"="Brand unknown";
"l_noCardTypeAvailable"="No card type available, contact the administrator.";
"l_noCase"="No case";
"l_noCaseLoaded"="There is no case loaded right now.";
"l_noCaseTypesFound"="No case types found";
"l_noCases"="No cases";
"l_noChangeAvailable"="No change allowed, no recheck available.";
"l_noConnection"="Sorry, retrieving the data failed. Please try again later.";
"l_noCoordinates"="No coördinates available";
"l_noCoordinatesNearbyStreets"="No coördinates to find nearby streets";
"l_noCountriesFound"="No countries found";
"l_noDocument"="[none]";
"l_noDossierPVCasesFound"="No open official reports found";
"l_noExemption"="No exemption";
"l_noFilterSet"="No filter applied";
"l_noInstances"="The user has no instance, please contact your supervisor";
"l_noLocation"="No location";
"l_noName"="You have not specified a name";
"l_noNetworkClose"="No network available, the application will be closed";
"l_noNumberCopied"="No number has been copied, please try again";
"l_noOffence"="No offence";
"l_noOffencesFound"="No offences found";
"l_noOpenTasks"="No open tasks";
"l_noOutcome"="No outcome";
"l_noPersonRegistered"="No person is registered";
"l_noPhotosTaken"="You have not taken any pictures yet";
"l_noPin"="There is no pin";
"l_noPledge"="No pledge statement";
"l_noPreviousFines"="No previous issued fines";
"l_noReferenceDataRefresh"="You are proceeding without updating the data. If you want to update the data, close the application and restart";
"l_noServerConnectionRestart"="Server connection failed, please restart the application";
"l_noSignal"="No signals";
"l_noStreetFound"="No street found";
"l_noStreetInBAG"="The street found through GPS does not exist in the local database.";
"l_noTerminalsLocation"="No terminals found on this location";
"l_noTicketTypes"="No tickettypes available, please try to synchronize data again";
"l_noValidDate"="This is not a valid date";
"l_noValidPlace"="Is not a valid enforcement city";
"l_noValidPlate"="No or invalid plate";
"l_noViolation"="No violation";
"l_noZipcode"="This is not a valid zipcode";
"l_no_data_found"="No Data Found";
"l_no_favorites"="No favorite offences are available for this combination of entered data";
"l_no_media"="No media";
"l_no_menu_authorization"="You do not have any menu authorizations, contact your administrator.";
"l_no_notes"="No extra information available";
"l_no_recent"="No recently chosen offences are available for this combination of entered data";
"l_noaddressfound"="No adress found";
"l_notAnSSN"="The given number is not a valid SSN";
"l_notAvailable"="Not available";
"l_notRegistered"="Not registered";
"l_notSameLocation"="This is not the same as the current location:";
"l_notValidZipCode"="This is not a valid zipcode";
"l_not_activated"="Your device has not been activated to use this service. Please contact support to get your username/device combination activated.";
"l_not_all_fields_filled"="Not all requiered fields are filled.";
"l_not_set"="Not set";
"l_notariff"="No tariff";
"l_notariff_star"="*";
"l_notfound"="Not found";
"l_nothingFound"="Nothing found";
"l_nozonesassigned"="No zones assigned to current instance";
"l_nr"="Num";
"l_number"="Number";
"l_numberm2"="Number m2";
"l_objectdata"="Object data";
"l_objectnumber"="Objectnumber";
"l_objectsFound"="Objects found";
"l_objectsyncCasemanagement"="Case management data";
"l_objectsyncGlobal"="Global data";
"l_objectsyncHectometermarker"="Hectometer marker data";
"l_objectsyncInstance"="Instance data";
"l_objectsyncKilometermarker"="Kilometer marker data";
"l_objectsyncLocation"="Location data";
"l_objectsyncOffence"="Offence data";
"l_objectsyncPerson"="Person data";
"l_objectsyncPlanning"="Planning data";
"l_objectsyncReference"="Reference data";
"l_objectsyncScanUnit"="Scanunit data";
"l_objectsyncTheme"="Theme data";
"l_objectsyncVehicle"="Vehicle data";
"l_observation"="Observation";
"l_observationTitel"="Observation";
"l_offence"="Offence";
"l_offenceCategory"="Offence category";
"l_offenceCommunicated"="Offence communicated";
"l_officerNotFound"="Officer not found";
"l_officerNotFoundMuni"="Officer not found due to missing municipality";
"l_officerNumber"="Officer number";
"l_officerNumberPrint"="Officer no.";
"l_officer_findings"="Findings (not printed)";
"l_officer_observation"="Observation";
"l_oldest_ontop"="Oldest on top";
"l_onStreetParkingCheck"="On street parking check";
"l_onstreetpayment"="Payed on street";
"l_open"="إفتح";
"l_openCase"="Open case";
"l_openPDF"="Open PDF";
"l_openTasks"="Open tasks";
"l_options"="Options";
"l_optionsVariables"="Options and variables";
"l_oranje"="Hinderlijk aanwezig bij evenement";
"l_other"="أخرى";
"l_otherSignals"="other signals";
"l_outOfMemory"="The memory threshold has been exceeded during scanning. The app will close after you have clicked OK.";
"l_outbox"="Outbox";
"l_outcome"="Outcome";
"l_overview"="Overview";
"l_overviewLabel"="Label overview";
"l_overviewPhoto"="Overview photo";
"l_overviewTask"="Task overview";
"l_ownStatement"="Own statement";
"l_ownerRegistrationDate"="Owner Registration Date";
"l_ownerRegistrationDatePrint"="Registration Date";
"l_paars"="Langstaander";
"l_paidParkingZone"="Parking zone";
"l_parking"="Parking";
"l_parkingCardNumber"="parkingcard number";
"l_parkingCardOutcome"="Parkingcard outcome";
"l_parkingPenalty"="Parking penalty";
"l_parkingServicefailed"="Parking service call failed";
"l_parking_expired"="Parking Expired";
"l_parking_invalid"="موقف غير ساري المفعول";
"l_parking_unavailable_end"="Try again later";
"l_parking_unavailable_start"="Search unavailable";
"l_parking_valid"="Valid";
"l_parkingcard"="Parkingcard";
"l_parkingcheck"="Parking check";
"l_password"="Password";
"l_paste"="Paste";
"l_pedestrian"="Pedestrian";
"l_permit"="Permit";
"l_permitName"="Permit name";
"l_permitTypeDesc"="Permit type";
"l_permit_invalid"="Permit Invalid";
"l_permit_valid"="Permit Valid";
"l_permitcheck"="Permit Check";
"l_permitnumber"="Permitnumber";
"l_person"="Person";
"l_personData"="Person data";
"l_personUnderInvestigation"="Person under investigation";
"l_photo"="Photo";
"l_photoNotFound"="Photo not found, take a new photo to be sure";
"l_photoProcessingFailed"="Scan cannot be processed, please try again";
"l_photoSelectFailed"="The selected image is not a valid type";
"l_photos"="Photos";
"l_pinNotCorrect"="رقم التعريف الشخصي غير صحيح";
"l_pinNotUsable"="This kind of pin number is not allowed";
"l_pinsDoNotMatch"="PINs do not match, please try again";
"l_placeofEstablishment"="Place of establishment";
"l_placeofresidency"="Place of residency";
"l_plateNotValid"="Not a valid plate";
"l_plate_format"="Please enter capitals and numbers only and a maximum length of 12";
"l_platesFound"="Plates found";
"l_pledge"="Pledge";
"l_policeofficeremail"="Police officer email";
"l_preferences"="Preferences";
"l_prefix"="اختصار";
"l_previousFines"="Previous issued fines";
"l_priceOneHourPark"="Tariff";
"l_print"="Print";
"l_printAmountPayed"="#totalamount# paid, including #handlingfee# handling fee.";
"l_printTestLabel"="Print test label";
"l_print_observation"="Observation";
"l_printerType"="Type of printer";
"l_printing"="Printing";
"l_process"="معالجة";
"l_processing"="Processing ...";
"l_production"="Production";
"l_production_pt"="Production Pub. Tp.";
"l_prohibitions"="Prohibitions";
"l_provider"="Provider";
"l_publishedBy"="Published by";
"l_putOnLabel"="Apply label";
"l_pvFailed"="Please let your system manager know that PerfectView is not available.";
"l_pvId"="Perfectview identification";
"l_pvNotAvailable"="On this moment Perfectview is not available. Please contact your system manager, you can continue or stop";
"l_questions"="Questions";
"l_queueLengthTimerOn"="Queue on";
"l_radius"="Radius";
"l_receipt"="Receipt";
"l_receiptNumber"="Receipt number";
"l_receivedAt"="Received at";
"l_recheck"="Recheck";
"l_reformatDateFailed"="Reformat date failed";
"l_refresh"="تحديث";
"l_refreshGPS"="GPS";
"l_refreshLocation"="تحديث";
"l_refreshTime"="Refresh Time (min)";
"l_reg_date"="Registered on";
"l_regionCode"="Regioncode";
"l_register"="Register";
"l_register_error"="Error registering device";
"l_register_success"="Device registered successfuly.";
"l_registerconcept"="Register concept";
"l_registeredByOrganization"="Registered by";
"l_registering_device"="Registering device";
"l_registrationDate"="Registration date";
"l_registrationDatePrint"="Registration date";
"l_registrationDateTime"="Registration date time";
"l_registrationNotFound"="Registration is not (yet) found in the system";
"l_registrationTitle"="Registration title";
"l_regulatedTimeConsumption"="Time consumption";
"l_regulation"="Regulation";
"l_relatedcase"="Folder";
"l_remark"="Remark";
"l_removalFailed"="Removal has failed:";
"l_remove"="إزالة";
"l_removePhoto"="إزالة الصورة";
"l_removeReg"="Remove registration";
"l_removed"="إزالة";
"l_removefilter"="Remove filter";
"l_reported"="Reported";
"l_rescan"="Rescan";
"l_resendPhotos"="Resending photos ...";
"l_resendThumbnail"="Resending thumbnails ...";
"l_reset"="Reset";
"l_resetAfter5Tries"="You have tried 5 times, application will now be reset";
"l_resetDBandReload"="Are you sure you want to reset the database and reload all data?";
"l_resetDatabase"="Reset database";
"l_resetPin"="إعادة تعيين رقم التعريف الشخصي";
"l_resetUser"="Reset user";
"l_resetUserPrompt"="Do you want to log out and reset the app?";
"l_restoreLayout"="Restore layout";
"l_result"="Result";
"l_resume"="إستئناف / إستئنف";
"l_resyncFailed"="Loading the necessery data has failed,if you choose to reset the app will be cleaned and you will have to log in again.";
"l_retrieve"="Retrieve";
"l_retrieveNewCase"="Retrieve new case";
"l_retrievingNewCases"="إسترداد الحالات / إسترجاع الحالات";
"l_retryServerconnection"="Connection to the server has failed, check your device connnetions. Do you want to retry?";
"l_rightType"="Right type";
"l_rightTypeDetail"="Right type detail";
"l_rightsNotReadConfirmation"="You have chosen 'Cautie: No', is this correct?";
"l_roadsigns"="Roadsigns";
"l_route"="Route";
"l_routeInfo"="Route information";
"l_rows"="Rows";
"l_sameAddress"="Do you want to use this same address as location?";
"l_sanction"="Sanction";
"l_save"="Save";
"l_saveAndExit"="Save case and start a new one";
"l_saveConceptCase"="Do you want to save the changes to your concept?";
"l_saveRegisterCase"="Do you want to save your new case as a concept?";
"l_save_data"="Save data";
"l_saved"="Saved";
"l_sbiCode"="SBI code";
"l_sbiCodeDescription"="SBI description";
"l_scan"="Scan";
"l_scanEnableAutoRestart"="Scan enable auto restart";
"l_scanFailure"="Scan can't be opened, check if it is installed and functioning.";
"l_scan_delay"="Scan delay (seconds)";
"l_scancountry"="Scan country";
"l_scanner_light"="Scanner light setting";
"l_sealbagNumber"="Sealbag number";
"l_search"="Search";
"l_searchAddress"="Search address";
"l_searchExact"="Search exact (number+letter)";
"l_searchLetter"="Search letter";
"l_searchNumber"="Search number";
"l_searchPlatesBy"="Search plates by";
"l_search_numberorsurname"="Search number or surname";
"l_search_printers"="Searching for printers";
"l_searchstreetandestablishmentplace"="Search street and city";
"l_secondOfficer"="Second officer";
"l_section"="Section";
"l_sector"="Sector";
"l_sectorDesc"="Sector description";
"l_select"="Select";
"l_selectFolder"="Select folder";
"l_selectPrinter"="Select printer";
"l_selectTeam"="Select team";
"l_selectall"="Select all";
"l_selectedPrinter"="Selected printer";
"l_selectscancountry"="Select a scan country";
"l_send"="Send...";
"l_send1LabelPhoto"="You must at least send 1 label photo";
"l_send1RemovalPhoto"="You must at least send 1 removal photo";
"l_sendPhotos"="Sending photos ...";
"l_sendRegistration"="Send registration ...";
"l_sendTaskData"="Sending taskdata ...";
"l_server"="Server";
"l_serverError"="Internal server error";
"l_serviceDidNotRespond"="The service did not respond in a timely fashion";
"l_serviceError"="The service has errored";
"l_serviceErrorRetrieveList"="Service has errored, list will be retrieved";
"l_service_show_time"="Show service debug info (seconds)";
"l_setCurrentTime"="Current time";
"l_setReceipt"="Set receipt";
"l_settings"="Settings";
"l_shift"="Shift";
"l_shift_A"="Evening shift";
"l_shift_D"="Day shift";
"l_shift_Z"="Sunday or holiday shift";
"l_signal"="Signal";
"l_signalServicefailed"="Signal service call failed";
"l_socialsecuritynr"="Socialsecuritynumber";
"l_start"="Start";
"l_startApp"="Start application...";
"l_startDate"="Date started";
"l_startGrip"="ENFORCEMENT";
"l_startTime"="Time started";
"l_started"="Started";
"l_state"="State";
"l_state100chars"="State (100 chars)";
"l_statement"="Statement";
"l_statementRecordedBy"="Statement recorded by";
"l_statistics"="Statistics";
"l_status"="الحالة";
"l_statusClaimed"="Claimed";
"l_statusControl_F"="مطالبات مالية";
"l_statusControl_K"="Claimed clamp";
"l_statusControl_L"="Claimed Mulder";
"l_statusControl_O"="Claimed unclamp";
"l_status_unknown"="Status Unknown";
"l_street"="Street";
"l_street24chars"="Street (24 chars)";
"l_streetAtNumber"="Street and number (approximately)";
"l_streetNotDetermined"="Street could not be determined, check loacation";
"l_streetnumber"="Street number";
"l_streetpropositions"="Street propositions";
"l_subjectnumber"="Subjectnumber";
"l_successful"="Successful";
"l_surname"="Surname";
"l_swipe"="By moving your finger from right to left you will be able to switch between checks";
"l_syncError"="Synchronization error";
"l_syncLast"="Last synchronization";
"l_sync_end"="End time";
"l_sync_finished"="Synchronization finished successfully";
"l_sync_start"="Start time";
"l_sync_status"="Synchronization status";
"l_synchronization"="Synchronization";
"l_synchronize"="Synchronize";
"l_syncinit"="Initializing Synchronisation";
"l_tariff"="Tariff";
"l_tariffgroup"="Tariff group";
"l_task"="Task";
"l_taskFault"="Something went wrong during processing of the task";
"l_taskoutcomeNotFound"="Task outcome not found";
"l_tasks"="Tasks";
"l_tax"="Amount parkingtax";
"l_taxi"="Taxi";
"l_team"="Team";
"l_templateName"="Template";
"l_tenant"="Tenant";
"l_terminal"="Terminal";
"l_test"="Test";
"l_text"="Text";
"l_thirdOfficer"="Third officer";
"l_ticketType"="Ticket type";
"l_ticket_nr"="Ticket #";
"l_time"="Time";
"l_timeArrived"="Time arrived";
"l_timeOutException"="The service has timed out";
"l_timeRestriction"="Time may not be 0:00";
"l_timeScanned"="وقت المسح";
"l_timeToArrive"="Time to arrive";
"l_tokenExpired"="Your login token has expired please login again. ";
"l_totalDue"="Total amount";
"l_touchIdFail"="Signing in trough TOUCH ID was unsuccesful";
"l_touchIdSucces"="Signing in trough TOUCH ID was succesful";
"l_touchIdWillNotUse"="Are You sure you will not use TOUCH ID?";
"l_tow"="Tow";
"l_trackDown"="Track down";
"l_trackDownDesc"="Track down and check a vehicle";
"l_translationLanguage"="Translation language";
"l_transpondercardcode"="Transpondercardcode";
"l_truck"="Truck";
"l_twoPhotosMandatory"="You need to take minimal two photos";
"l_type"="Type";
"l_typeCode"="Type code";
"l_typeTextHere"="Type your text here";
"l_unClamp"="Unclamp";
"l_unclaimed"="Unclaimed by user";
"l_unclampRequestSetOn"="Unclamp request set on";
"l_unknown"="Unknown";
"l_unpair"="Unpair";
"l_updateAvailable"="Version #newversion# of the app is available. Do you want to download this new version?";
"l_updateAvailableMDM"="Version #newversion# of the app is available. Remind your administrator to release this new version.";
"l_updateAvailableStoreMDM"="Version #newversion# of the app is available. Download the app from the appstore or remind your administrator to release the new version if the app is pushed to your device. Do you want to download this new version?";
"l_uploadCaseFailedRetry"="Upload case failed, please try again or save and start a new case.";
"l_usage"="Usage";
"l_user"="User";
"l_userMustBeEmail"="Username has to be an email address";
"l_userNotInInstance"="User not in current instance, please select another user";
"l_userPVNotExists"="User is not found in PerfectView, please contact your manager. You can continue as an unknown user or stop";
"l_username"="Username";
"l_usesInterpreter"="Uses an interpreter";
"l_usesLegalAssistance"="Uses legal assistance";
"l_valid"="Valid";
"l_validFrom"="Valid from";
"l_validParkingRight"="Valid parkingright";
"l_validTo"="Valid to";
"l_validateVehicleMessage"="Vehicle not fully filled";
"l_validateVehicleOr"=" or ";
"l_validationNeeded"="Validation needed";
"l_validityMessage"="Outcome";
"l_validparkingtime"="Valid parking time";
"l_van"="Van";
"l_variables"="Variables";
"l_vehicle"="مركبة";
"l_vehicleDetails"="Vehicle details";
"l_vehicleInformation"="Vehicle information";
"l_vehicleServicefailed"="Vehicle service call failed";
"l_vehicleTypeGroup"="Vehicle type group";
"l_vehicle_invalid"="No vehicle information found";
"l_vehicle_law"="Vehicle";
"l_vehicletype"="Vehicle type";
"l_verifyAddress"="Is this address correct?";
"l_verifyPin"="تحقق من رقم التعريف الشخصي";
"l_verifyTouchId"="Verify TOUCH ID";
"l_version"="Version";
"l_vessel"="Vessel";
"l_wait"="Wait";
"l_wantToLogOn"="هل تريد الإنضمام إلى المجموعة؟";
"l_wantToRemoveCase"="هل تريد إزالة الحالة؟";
"l_wantToRemovePhoto"="هل أنت متأكد بأنك تريد إزالة الصورة؟";
"l_wantToSaveContinueOrTryAgain"="Unable to create case online, do you want to continue offline or try again?";
"l_wantToTryAgain"="Do you want to try again?";
"l_wantToUsePin"="Do you want to use a pin number to log in to the application?";
"l_wantToUseTouchId"="Do you want to use TOUCH ID to log in?";
"l_wanted"="Wanted";
"l_warning"="Warning";
"l_warningMessage"="“Building info” & “State” should not be used if you’re planning to write a Combibon";
"l_withdraw"="Withdraw";
"l_writeExtraTicket"="Extra ticket";
"l_xTerminalsLocation"="Terminals found on this location";
"l_yearofbirth"="Year of birth";
"l_yes"="Yes";
"l_zipCode"="Zipcode";
"l_zipCode12chars"="Zipcode (12 chars)";
"l_zoneCode"="Zone code";
"l_zoneDesc"="Zone Description";
"noCaution"="No caution";
"open_settings"="Open Settings";
"otherDocuments"="Other Documents";
"passport"="Passport";
"pl_banDurationDays"="Duration of ban in days";
"pl_banDurationMonths"="Duration of ban in months";
"pl_banDurationWeeks"="Duration of ban in weeks";
"pl_casetime"="Case time";
"pl_char_limit"="Character limit reached";
"pl_checkCard"="Check card";
"pl_chooseNearbyStreets"="Or choose one of the streets in the area:";
"pl_goto"="Go to";
"pl_identification"="Identification";
"pl_interior"="Interior";
"pl_internalRemark"="Internal remark";
"pl_letter"="Letters";
"pl_locationDescription"="Location description";
"pl_locationSpec"="Location specification";
"pl_location_indication"="Location indication";
"pl_minimal2input"="Enter the minimal of 2 characters";
"pl_minimal3input"="Type a least 3 characters";
"pl_minimalInput"="Enter the minimal of 3 characters";
"pl_minimalStreetinput"="Enter a minimal of 3 characters for Street";
"pl_notepad"="Notepad";
"pl_number"="Numbers";
"pl_onStreetGPS"="Found on the street with GPS";
"pl_paymentMethod"="payment method";
"pl_questionnaires"="Questionnaires";
"pl_scanUnit"="Scan unit";
"pl_tax"="Parkingtax";
"pl_total"="Total";
"pl_towed"="Towed";
"pl_translate"="Translate";
"pleaseWait"="Please wait...";
"scanner_app_not_installed"="To scan documents the Document Scanner app is required.";
"selectDocumentType"="Select Document Type";
