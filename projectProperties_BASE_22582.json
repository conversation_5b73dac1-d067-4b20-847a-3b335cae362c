{"@class": "com.kony.gen.appprops.KVizAppProps", "Appoinments": "true", "Contacts": "true", "DisableApplicationScreenshot": "false", "DisableApplicationScreenshotandwear": "false", "Documents_Library_Access": "true", "Enterprise_Authentication": "true", "Home_or_Work_Networking": "true", "ID_CAP_APPOINTMENTS": "true", "ID_CAP_CAMERA": "false", "ID_CAP_CONTACTS": "true", "ID_CAP_GAMERSERVICES": "true", "ID_CAP_IDENTITY_DEVICE": "true", "ID_CAP_IDENTITY_USER": "true", "ID_CAP_ISV_CAMERA": "true", "ID_CAP_LOCATION": "true", "ID_CAP_MEDIALIB": "true", "ID_CAP_MICROPHONE": "true", "ID_CAP_NETWORKING": "true", "ID_CAP_PHONEDIALER": "true", "ID_CAP_PUSH_NOTIFICATION": "true", "ID_CAP_SENSORS": "true", "ID_CAP_WEBBROWSERCOMPONENT": "true", "ID_HW_FRONTCAMERA": "false", "Internet(Client)": "true", "Internet(Client_AND_Server)": "true", "KonyHTTPPublicKeyPinning": "false", "KonyPay": "false", "Location": "true", "Microphone": "true", "Music_Library": "true", "Pictures_Library_Access": "true", "Proximity": "true", "Removable_Storage": "true", "SELECTED_THEME": "default", "Server side mobile web": "false", "Shared_User_Certificate": "true", "Shared_User_Certificates": "true", "Text_Messaging": "true", "UseOpenSSLLibrary": "true", "UseOpenSSLLibraryandwear": "false", "UseSQLCipherFIPS": "true", "UseSQLCipherFIPSandwear": "false", "Videos_Library_Access": "true", "Webcam": "true", "_sys_use_consumer_push": "false", "access_internet": "true", "access_location_services": "false", "access_phone": "false", "access_pimdomain_calendars": "false", "access_pimdomain_contacts": "false", "access_pimdomain_messages": "false", "access_shared": "false", "access_sms_mms": "false", "adaptivewebmanifestjson": "", "addWatchBackgroundListenerToReceiveDataPayload": "false", "addWatchBackgroundListenerToReceiveDataPayloadAndWear": "false", "addWatchBackgroundListenerToReceiveMessagePayload": "false", "addWatchBackgroundListenerToReceiveMessagePayloadAndWear": "false", "allJoyn": "false", "allowselfsignedcertificate": "true", "andmaxsdkkey": "None", "andminsdkkey": "8.1 (27)", "androidInstallLocation": "Auto", "androidInstallLocnPref": "false", "androidUsesmap": "true", "androidUsesvideo": "false", "android_allowselfsignedcertificates": "None", "androidloadindicatorkey": "true", "androidmapkey": "", "androidmapkey2": "AIzaSyBXPDbaxdEVTkMosUMkWP_KsrARrh12B08", "androidorientation": "<PERSON><PERSON>", "androidscreenanydensity": "true", "androidscreenextralarge": "true", "androidscreenlarge": "true", "androidscreennormal": "true", "androidscreenresizeable": "false", "androidscreensmall": "true", "androidtabUsesmap": "false", "androidtabUsesvideo": "false", "androidwear_allowselfsignedcertificates": "None", "androidwearosUsesmap": "false", "androidwearosUsesvideo": "false", "andtargetsdkkey": "14.0 (34)", "andwearapppackagekey": "com.konylabs.Redline", "andwearappversioncode": "1", "andwearmaxsdkkey": "None", "andwearminsdkkey": "7.1 (25)", "andweartargetsdkkey": "7.1 (25)", "antialiaseddrawing": "false", "apmappload": "false", "apmapptransition": "false", "apmcrash": "true", "apmerrorunhandledexception": "true", "apmformEntry": "false", "apmformExit": "false", "apmgestureswipe": "false", "apmorientationchange": "false", "apmservicerequest": "false", "apmserviceresponse": "true", "apmtouchorclick": "false", "appidkey": "Redline", "appiniteventkey": "None", "applepushfunc": "None", "apploadeventkey": "None", "applogokey": "0=startandroidredlineacc.png", "appmenu": {"desktop": {"data": [{"icon": {"default": "option1.png"}, "id": "appmenuitemid1", "kuid": "5fadbfde39f44868a07660dbba865f21", "title": "Item 1"}, {"icon": {"default": "option2.png"}, "id": "appmenuitemid2", "kuid": "69d4dbdd61eb4b91b3375f358c6a16d5", "title": "Item 2"}, {"icon": {"default": "option3.png"}, "id": "appmenuitemid3", "kuid": "082e2c68a30e4db1bed46c56c06c225f", "title": "Item 3"}, {"icon": {"default": "option4.png"}, "id": "appmenuitemid4", "kuid": "ca757fe1ed194dcd8ab381b56571af2e", "title": "Item 4"}]}, "mobile": {"data": [{"i18nkey": "l_getBicycleData", "icon": {"default": "option1.png"}, "id": "appmenuitemid1", "kuid": "f8c5b15409894b1290e306dc59ae609f", "menuPSPs": {"android": {"showAsFlag": 0}}, "title": "Item 1"}, {"i18nkey": "l_getBicycleData", "icon": {"default": "option2.png"}, "id": "appmenuitemid2", "kuid": "563d91d1711f4f81b5283743f2ed86ff", "menuPSPs": {"android": {"showAsFlag": 0}}, "title": "Item 2"}, {"i18nkey": "l_getBicycleData", "icon": {"default": "option3.png"}, "id": "appmenuitemid3", "kuid": "c6b67b00a4474bf1b23aba9bef3acf10", "menuPSPs": {"android": {"showAsFlag": 0}}, "title": "Item 3"}, {"i18nkey": "l_getBicycleData", "icon": {"default": "option4.png"}, "id": "appmenuitemid4", "kuid": "c5649592a93947ada94de512216c8619", "menuPSPs": {"android": {"showAsFlag": 0}}, "title": "Item 4"}]}, "osPlatform": "", "osVersion": "iOS7", "tablet": {"data": [{"i18nkey": "l_getBicycleData", "icon": {"default": "option1.png"}, "id": "appmenuitemid1", "kuid": "3a2490d432b74a7495b4b2bce3ff1178", "menuPSPs": {"android": {"showAsFlag": 0}, "windows8": {"appbarButtonStyle": "No Style", "position": "BOTTOM_LEFT"}}, "title": "Item 1"}, {"i18nkey": "l_getBicycleData", "icon": {"default": "option2.png"}, "id": "appmenuitemid2", "kuid": "e65acbb7a4034198b77d269db20b59ae", "menuPSPs": {"android": {"showAsFlag": 0}, "windows8": {"appbarButtonStyle": "No Style", "position": "BOTTOM_LEFT"}}, "title": "Item 2"}, {"i18nkey": "l_getBicycleData", "icon": {"default": "option3.png"}, "id": "appmenuitemid3", "kuid": "6412a85c13a444049a3bb85cb3961dc3", "menuPSPs": {"android": {"showAsFlag": 0}, "windows8": {"appbarButtonStyle": "No Style", "position": "BOTTOM_LEFT"}}, "title": "Item 3"}, {"i18nkey": "l_getBicycleData", "icon": {"default": "option4.png"}, "id": "appmenuitemid4", "kuid": "3414a2636f3d4adb91463ae3173ad35f", "menuPSPs": {"android": {"showAsFlag": 0}, "windows8": {"appbarButtonStyle": "No Style", "position": "BOTTOM_LEFT"}}, "title": "Item 4"}]}, "wType": "Appmenu"}, "appnamekey": "Twyns Test", "appointments": "false", "apppackagekey": "com.twyns.TWYNSTest", "apptitle": "", "appversioncode": "260000", "appversionkey": "2.6.0", "backgroundMediaPlayback": "false", "backgroundmodes": "<PERSON>tch", "backward_compatibility_mode": "true", "baseTarget": "_blank", "bb10author": "", "bb10authorid": "", "bb10buildid": "", "bb10description": "", "bb10keystorepassword": "", "bb10orientationmode": "<PERSON><PERSON><PERSON>", "bb10packageid": "", "bb10theme": "<PERSON><PERSON><PERSON>", "bb10urlscheme": "", "bb10versionnumber": "", "bbserviceidentifierpattern": "", "bbservicemenuitemlabel": "", "bgcolorkey": "225,105,85", "blockedChatMessages": "false", "bluetooth": "false", "build": "release", "buildExclusion": {}, "buildWarCompatability": "false", "build_gradle_attrs_prefix": "buildscript {\n    repositories {\n        google()\n        mavenCentral()\n    }\n    dependencies {\n     classpath 'com.google.gms:google-services:4.4.2'\n    }\n}\napply plugin: 'com.android.application'\napply plugin: 'com.google.gms.google-services'", "build_gradle_attrs_prefix_andwear": "", "build_gradle_attrs_suffix": "allprojects {\n    repositories {\n        // Adding here because <PERSON> cannot access AndroidADAL feed.\n        maven {\n            url 'https://pkgs.dev.azure.com/MicrosoftDeviceSDK/DuoSDK-Public/_packaging/Duo-SDK-Feed/maven/v1'\n            name 'Duo-SDK-Feed'\n        }\n        google()\n        mavenCentral()\n   }\n}\ndependencies {\n    // Import the Firebase BoM\n    implementation platform(\"com.google.firebase:firebase-bom:33.5.1\")\n    // Add the dependency for Google Analytics\n\timplementation 'com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava'\n    implementation('com.google.firebase:firebase-analytics') {\n        exclude group: 'com.google.guava'\n    }\n    implementation 'androidx.biometric:biometric:1.1.0'\n    implementation 'com.google.android.gms:play-services-vision:20.1.3'\n    implementation 'com.android.support:appcompat-v7:27.1.1'\n    implementation 'com.android.support.constraint:constraint-layout:1.1.3'\napi 'com.android.support:appcompat-v7:27.1.1'\n    api 'com.android.support:design:27.1.1'\n\tapi 'com.github.barteksc:pdfium-android:1.9.0'\n    modules {\n        module(\"com.google.guava:listenablefuture\") {\n            replacedBy(\"com.google.guava:guava\", \"listenablefuture is part of guava\")\n        }\n    }\n}\ndependencies.implementation 'com.android.support.constraint:constraint-layout:1.1.2'\ndependencies.implementation('com.microsoft.aad:adal:1.16.3') {\n         exclude group: 'com.android.support'\n exclude group: 'com.google.code.gson'\n    }\nandroid { defaultConfig { renderscriptSupportModeEnabled false}\nlintOptions {\n        checkReleaseBuilds false\n    }\n }", "build_gradle_attrs_suffix_andwear": "", "buttonAsLabel": true, "camerasettings": "", "cancelicon": "", "category": "app", "certs_key_store_pwd": "kds/0eUc+i3BqFqSkpCk4g==", "certs_key_store_type": "JKS", "certs_ssl_algorithm": "TLS", "certs_trust_store_pwd": "kds/0eUc+i3BqFqSkpCk4g==", "certs_trust_store_type": "JKS", "chat": "false", "cloud_account_guid": "36169ec1-b5d0-4905-aac9-0ce6bed0d8c8", "cloud_account_id": "*********", "cloud_config_ipaddress_list": "", "cloud_env_GUID": "ce35754d-03d2-4818-94b3-14f1afca2eb4", "cloud_env_name": "<PERSON>", "cloud_http_redirect": "false", "cloud_selected_ipaddress_list": "", "cloud_url": "https://dev8.redora.com/admin", "codeGeneration": "false", "contacts": "false", "cordovaModified": false, "cordovaVersionInfo": {"useGlobalCordova": false}, "createdVizVersion": "9.2.64", "createdgaversion": "9.2.64", "currentgaversion": "9.5.51", "customfcmservice": "", "customgcmbroadcastreceiver": "", "deeplinkkey": "None", "defaultZIndex": "1", "defaultlocalekey": "en", "defaulttheme": "REDORA", "desktopwebaligenmentoption": "left", "desktopwebbasefontsize": "16", "desktopwebfavico": "", "desktopwebmanifestjson": "", "desktopwebnoscriptmessage": "To use this site, first enable your browser's JavaScript support and then refresh this page.", "desktopwebtitle": "", "desktopwebwidthoption": "Percentage", "desktopwebwidthval": "100", "developmentMethod": "enterprise", "developmentMethodLocalBuild": "enterprise", "deviceexception": "Unknown Error Occurred <br/> Contact System Admin", "devicemsg": "This device is currently not supported", "disablehttpskey": "false", "enableAwssdk": "false", "enableCordova": false, "enableIdForAutomation": true, "enableOfflineObjects": "false", "enableOfflineObjectsDW": "false", "enableOfflineObjectsSpa": "false", "enableProgressiveWeb": false, "enablePushNotificationsDW": false, "enableResponsive": false, "enableServiceWorker": false, "enableServiceWorkerHelper": false, "enableViewportZooming": false, "enableWindowsOfflineObjectsForWebApp": false, "enableWindowsRuntimeForWebApp": false, "enableactionbar": false, "enableasyncdesktopweb": false, "enableasyncspa": false, "enableautomaticeventcap": "true", "enablecharts2d3diphone": "true", "enablefcmpushnotifications": "false", "enablefileupload": "false", "enablefileuploadandwear": "false", "enablefocushtml5iphonbb": "false", "enablelocalnotifications": "false", "enablelocalnotificationsandroidwear": "false", "enablepushnotifications": "false", "enablesearchbar": "false", "enablesearchbarandwear": "false", "enableseo": "false", "enableserverevents": false, "enablesso": false, "enablexfsdw": false, "enablexfsspa": false, "encryptionofsqlite": true, "enterpriseAuthentication": "false", "es5build": false, "exceptionalert": "true", "extendBottom": "false", "extendTop": "false", "fabric_runtime_app_version": "Linked", "fbxfolderpathkey": "", "fcmsenderid": "", "fgcolorkey": "255,255,255", "generateAppBundle": false, "generateJavaCodeForForms": "false", "genericexceptionalert": "false", "gradle_properties_attrs": "org.gradle.jvmargs=-Xmx2560M", "gradle_properties_attrs_andwear": "", "hiddenAppGroups": {}, "hostkey": "main", "hostkey_andwear": "", "htmlheadscripttagsdesktopweb": "", "htmlheadscripttagsspa": "", "hybdridcontextkey": "", "hybridaddrkey": "", "hybridhttpsportkey": "", "hybridportkey": "", "iOSMobileProvision": "", "iOSP12FilePath": "", "iOSP12Password": "", "ide_appEvents": {"mobile": {"appservice": "AS_AppEvents_gfc962826da34888abad67df0e5b3e7c", "postappinit": "AS_AppEvents_a8aa03af0b8e4ffb9970b171e3347360", "preappinit": "AS_AppEvents_e98e7ba2a707448c94cf5e2402dcb623"}}, "imagewhendownloading": "", "initialRotationPreferenceKey": "landscape", "inputaccessoryviewtype": "nextprevtoolbar", "internetClient": "false", "internetClientServer": "false", "iosBuildFormat": "kar", "iosUsesvideo": "false", "ioscheckboxcorrection": "true", "iosdeployment_targetversionkey": "14.0", "iospopuptransitioncorrection": "true", "iosrespectimagesizecorrection": "true", "iostextboxpaddingcorrection": "true", "ipadUsesvideo": "false", "ipad_appicon_1x_76": "startiosredline76.png", "ipad_appicon_1x_76_dark": "", "ipad_appicon_1x_76_tinted": "", "ipad_appicon_2x_68": "", "ipad_appicon_2x_68_dark": "", "ipad_appicon_2x_68_tinted": "", "ipad_appicon_2x_76": "startiosredline152.png", "ipad_appicon_2x_76_dark": "", "ipad_appicon_2x_76_tinted": "", "ipad_notificationicon_1x_20": "", "ipad_notificationicon_1x_20_dark": "", "ipad_notificationicon_1x_20_tinted": "", "ipad_notificationicon_2x_20": "", "ipad_notificationicon_2x_20_dark": "", "ipad_notificationicon_2x_20_tinted": "", "ipad_notificationicon_2x_38": "", "ipad_notificationicon_2x_38_dark": "", "ipad_notificationicon_2x_38_tinted": "", "ipad_proappicon_2x_83Point5": "startiosredline167.png", "ipad_proappicon_2x_83Point5_dark": "", "ipad_proappicon_2x_83Point5_tinted": "", "ipad_settingsicon_1x_29": "", "ipad_settingsicon_1x_29_dark": "", "ipad_settingsicon_1x_29_tinted": "", "ipad_settingsicon_2x_29": "", "ipad_settingsicon_2x_29_dark": "", "ipad_settingsicon_2x_29_tinted": "", "ipad_spotlighticon_1x_40": "", "ipad_spotlighticon_1x_40_dark": "", "ipad_spotlighticon_1x_40_tinted": "", "ipad_spotlighticon_2x_40": "", "ipad_spotlighticon_2x_40_dark": "", "ipad_spotlighticon_2x_40_tinted": "", "ipadlandscapemodekey": "true", "ipadlaunchmodekey": "Portrait", "ipadportraitmodekey": "true", "iphone_appicon_2x_60": "startiosredline120.png", "iphone_appicon_2x_60_dark": "", "iphone_appicon_2x_60_tinted": "", "iphone_appicon_2x_64": "", "iphone_appicon_2x_64_dark": "", "iphone_appicon_2x_64_tinted": "", "iphone_appicon_3x_60": "startiosredline180.png", "iphone_appicon_3x_60_dark": "", "iphone_appicon_3x_60_tinted": "", "iphone_appicon_3x_64": "", "iphone_appicon_3x_64_dark": "", "iphone_appicon_3x_64_tinted": "", "iphone_notificationicon_2x_20": "", "iphone_notificationicon_2x_20_dark": "", "iphone_notificationicon_2x_20_tinted": "", "iphone_notificationicon_2x_38": "", "iphone_notificationicon_2x_38_dark": "", "iphone_notificationicon_2x_38_tinted": "", "iphone_notificationicon_3x_20": "", "iphone_notificationicon_3x_20_dark": "", "iphone_notificationicon_3x_20_tinted": "", "iphone_notificationicon_3x_38": "", "iphone_notificationicon_3x_38_dark": "", "iphone_notificationicon_3x_38_tinted": "", "iphone_spotlighticon_2x_29": "", "iphone_spotlighticon_2x_29_dark": "", "iphone_spotlighticon_2x_29_tinted": "", "iphone_spotlighticon_2x_40": "", "iphone_spotlighticon_2x_40_dark": "", "iphone_spotlighticon_2x_40_tinted": "", "iphone_spotlighticon_3x_29": "", "iphone_spotlighticon_3x_29_dark": "", "iphone_spotlighticon_3x_29_tinted": "", "iphone_spotlighticon_3x_40": "", "iphone_spotlighticon_3x_40_dark": "", "iphone_spotlighticon_3x_40_tinted": "", "iphonebundleidentifierkey": "com.twyns.TWYNSTest", "iphonebundleversionkey": "2.6.0", "iphoneipad_appstoreicon_1x_1024": "startiosredline1024.png", "iphoneipad_appstoreicon_1x_1024_dark": "", "iphoneipad_appstoreicon_1x_1024_tinted": "", "iphoneipadglossyeffectkey": "false", "iphoneloadindicatorkey": "true", "iphoneurlscheme": "", "iphonewebshrtcutkey": "", "isA11yConfigEnabled": false, "isDeveloperMode": "true", "isDpScaleFactor": true, "isEASAppUpgradable": false, "isI18nConfigEnabled": true, "isI18nLayoutConfigEnabled": false, "isLibraryBuild": "false", "isNewAppWithRespectToAndroidPackageName": "true", "isNewAppWithRespectToAndroidWearPackageName": "true", "isScreenShotDisabledForTestReports": false, "islocalserverurl": "true", "keyAlias": "redora", "keyAliasandwear": "", "keyPassword": "Red0r@", "keyPasswordandwear": "", "keyStoreFilePath": "certificates/android/redora_release_key.keystore", "keyStoreFilePathandwear": "", "keyStorePassword": "Red0r@", "keyStorePasswordandwear": "", "keychainPassword": "", "kfAppInfo": {"002f08c4-af06-40cc-a491-be7fad036c68": {"appName": "REDLINE", "baseId": "41dd845d-0817-436e-aaf3-2b3113ac7145", "id": "41dd845d-0817-436e-aaf3-2b3113ac7145", "version": "1.0"}, "2438fd4c-d9ef-4deb-96e8-2cb2b931b92d": {"appName": "REDLINE", "baseId": "23fbd544-4f52-478e-a07c-e244a6301d0f", "id": "23fbd544-4f52-478e-a07c-e244a6301d0f", "version": "1.0"}, "36169ec1-b5d0-4905-aac9-0ce6bed0d8c8": {"appName": "REDLINE", "baseId": "4e6f9c52-e5ef-4ea0-8d9b-c61b8aee1972", "id": "4e6f9c52-e5ef-4ea0-8d9b-c61b8aee1972", "version": "1.0"}, "4c845bcf-c7e2-432e-abb4-f58719982860": {"baseId": "45b1161a-bcd3-49af-8594-c90dab783bb1", "id": "45b1161a-bcd3-49af-8594-c90dab783bb1", "version": "1.0"}, "54633202-3133-4024-977d-07e4e48e759c": {"appName": "REDLINE", "baseId": "9190590b-101d-45d2-8526-49a55a5251e2", "id": "9190590b-101d-45d2-8526-49a55a5251e2", "version": "1.0"}, "64701e58-8f4b-4098-9275-0c8d958cb28f": {"appName": "REDLINE", "baseId": "59f5e5cf-f9b7-40ba-9b49-9448ff79c874", "id": "59f5e5cf-f9b7-40ba-9b49-9448ff79c874", "version": "1.0"}, "6751168a-0fae-4161-84dd-27acead36c40": {"appName": "REDLINE2", "baseId": "ee6fa6a1-7b1c-425b-9b30-67e8de68d10f", "id": "ee6fa6a1-7b1c-425b-9b30-67e8de68d10f", "version": "1.0"}, "6f60d7da-3fd5-4709-b572-b8cc16819953": {"appName": "REDLINE PROD (NS)", "baseId": "7db1e3ae-0b0d-4780-9ff3-57cd35d952cf", "id": "7db1e3ae-0b0d-4780-9ff3-57cd35d952cf", "version": "1.0"}, "8bd938f7-f312-4d3b-b024-9604fa3f151e": {"appName": "REDLINE", "baseId": "1b75e800-ce71-4372-bb3a-138fb1917e4d", "id": "1b75e800-ce71-4372-bb3a-138fb1917e4d", "version": "1.0"}, "ef472c1c-2c2f-4318-830b-59f3b54bb111": {"appName": "REDLINE", "baseId": "0c1dea88-f968-4cef-98cc-e81abc078084", "id": "0c1dea88-f968-4cef-98cc-e81abc078084", "version": "1.0"}}, "konyVizVersion": "9.5.51", "lastmodifiedtime": "1652362981882", "location": "false", "log_level": "ERROR", "mangoapptitlekey": ",,,,,,", "mapkey": "AIzaSyBXPDbaxdEVTkMosUMkWP_KsrARrh12B08", "marginsincludedinwidgetcontainerweight": "true", "mastersProperties": {"masterVersion": "2.0.0"}, "metatags": "", "microphone": "false", "musicLibrary": "false", "mwaddrkey": "169.254.80.80", "mwcontextkey": "", "mwhttpsportkey": "443", "mwportkey": "80", "objects3D": "false", "organizationidsso": "", "passwordkey": "kds/0eUc+i3BqFqSkpCk4g==", "pasteboardType": "2", "pasteboardtype": "systemlevel", "pathkey": "", "pathkey_andwear": "", "pathpatternkey": "", "pathpatternkey_andwear": "", "pathprefixkey": "", "pathprefixkey_andwear": "", "permissions": {"android": {"ACCESS_CHECKIN_PROPERTIES": "true", "ACCESS_COARSE_LOCATION": "true", "ACCESS_FINE_LOCATION": "true", "ACCESS_LOCATION_EXTRA_COMMANDS": "true", "ACCESS_MOCK_LOCATION": "false", "ACCESS_NETWORK_STATE": "true", "ACCESS_SURFACE_FLINGER": "false", "ACCESS_WIFI_STATE": "true", "ACCOUNT_MANAGER": "false", "AUTHENTICATE_ACCOUNTS": "false", "BATTERY_STATS": "false", "BIND_APPWIDGET": "false", "BIND_DEVICE_ADMIN": "false", "BIND_INPUT_METHOD": "false", "BIND_REMOTEVIEWS": "false", "BIND_WALLPAPER": "false", "BLUETOOTH": "true", "BLUETOOTH_ADMIN": "true", "BRICK": "false", "BROADCAST_PACKAGE_REMOVED": "false", "BROADCAST_SMS": "false", "BROADCAST_STICKY": "false", "BROADCAST_WAP_PUSH": "false", "CALL_PHONE": "false", "CALL_PRIVILEGED": "false", "CAMERA": "true", "CHANGE_COMPONENT_ENABLED_STATE": "false", "CHANGE_CONFIGURATION": "false", "CHANGE_NETWORK_STATE": "false", "CHANGE_WIFI_MULTICAST_STATE": "false", "CHANGE_WIFI_STATE": "false", "CLEAR_APP_CACHE": "true", "CLEAR_APP_USER_DATA": "false", "CONTROL_LOCATION_UPDATES": "false", "DELETE_CACHE_FILES": "true", "DELETE_PACKAGES": "false", "DEVICE_POWER": "false", "DIAGNOSTIC": "true", "DISABLE_KEYGUARD": "false", "DUMP": "false", "EXPAND_STATUS_BAR": "false", "FACTORY_TEST": "false", "FLASHLIGHT": "false", "FORCE_BACK": "false", "GET_ACCOUNTS": "true", "GET_PACKAGE_SIZE": "false", "GET_TASKS": "false", "GLOBAL_SEARCH": "false", "HARDWARE_TEST": "false", "INJECT_EVENTS": "false", "INSTALL_LOCATION_PROVIDER": "false", "INSTALL_PACKAGES": "false", "INTERNAL_SYSTEM_WINDOW": "false", "INTERNET": "false", "KILL_BACKGROUND_PROCESSES": "false", "MANAGE_ACCOUNTS": "true", "MANAGE_APP_TOKENS": "false", "MASTER_CLEAR": "false", "MODIFY_AUDIO_SETTINGS": "false", "MODIFY_PHONE_STATE": "false", "MOUNT_FORMAT_FILESYSTEMS": "false", "MOUNT_UNMOUNT_FILESYSTEMS": "false", "NFC": "true", "PERSISTENT_ACTIVITY": "false", "POST_NOTIFICATIONS": "false", "PROCESS_OUTGOING_CALLS": "false", "READ_CALENDAR": "false", "READ_CONTACTS": "false", "READ_FRAME_BUFFER": "false", "READ_HISTORY_BOOKMARKS": "false", "READ_INPUT_STATE": "false", "READ_LOGS": "false", "READ_PHONE_STATE": "false", "READ_SMS": "false", "READ_SYNC_SETTINGS": "false", "READ_SYNC_STATS": "false", "REBOOT": "false", "RECEIVE_BOOT_COMPLETED": "false", "RECEIVE_MMS": "false", "RECEIVE_SMS": "false", "RECEIVE_WAP_PUSH": "false", "RECORD_AUDIO": "false", "REORDER_TASKS": "false", "RESTART_PACKAGES": "false", "SEND_SMS": "false", "SET_ACTIVITY_WATCHER": "false", "SET_ALARM": "false", "SET_ALWAYS_FINISH": "false", "SET_ANIMATION_SCALE": "true", "SET_DEBUG_APP": "false", "SET_ORIENTATION": "false", "SET_PREFERRED_APPLICATIONS": "false", "SET_PROCESS_LIMIT": "false", "SET_TIME": "false", "SET_TIME_ZONE": "false", "SET_WALLPAPER": "false", "SET_WALLPAPER_HINTS": "false", "SIGNAL_PERSISTENT_PROCESSES": "false", "STATUS_BAR": "true", "SUBSCRIBED_FEEDS_READ": "false", "SUBSCRIBED_FEEDS_WRITE": "false", "SYSTEM_ALERT_WINDOW": "false", "UPDATE_DEVICE_STATS": "false", "USE_CREDENTIALS": "false", "USE_FINGERPRINT": "false", "USE_SIP": "false", "VIBRATE": "true", "WAKE_LOCK": "false", "WRITE_APN_SETTINGS": "false", "WRITE_CALENDAR": "false", "WRITE_CONTACTS": "false", "WRITE_EXTERNAL_STORAGE": "true", "WRITE_GSERVICES": "false", "WRITE_HISTORY_BOOKMARKS": "false", "WRITE_SECURE_SETTINGS": "false", "WRITE_SETTINGS": "false", "WRITE_SMS": "false", "WRITE_SYNC_SETTINGS": "false"}, "androidwear": {"ACCESS_CHECKIN_PROPERTIES": "false", "ACCESS_COARSE_LOCATION": "false", "ACCESS_FINE_LOCATION": "false", "ACCESS_LOCATION_EXTRA_COMMANDS": "false", "ACCESS_MOCK_LOCATION": "false", "ACCESS_NETWORK_STATE": "true", "ACCESS_SURFACE_FLINGER": "false", "ACCESS_WIFI_STATE": "false", "ACCOUNT_MANAGER": "false", "AUTHENTICATE_ACCOUNTS": "false", "BATTERY_STATS": "false", "BIND_APPWIDGET": "false", "BIND_DEVICE_ADMIN": "false", "BIND_INPUT_METHOD": "false", "BIND_REMOTEVIEWS": "false", "BIND_WALLPAPER": "false", "BLUETOOTH": "false", "BLUETOOTH_ADMIN": "false", "BRICK": "false", "BROADCAST_PACKAGE_REMOVED": "false", "BROADCAST_SMS": "false", "BROADCAST_STICKY": "false", "BROADCAST_WAP_PUSH": "false", "CALL_PHONE": "false", "CALL_PRIVILEGED": "false", "CAMERA": "false", "CHANGE_COMPONENT_ENABLED_STATE": "false", "CHANGE_CONFIGURATION": "false", "CHANGE_NETWORK_STATE": "false", "CHANGE_WIFI_MULTICAST_STATE": "false", "CHANGE_WIFI_STATE": "false", "CLEAR_APP_CACHE": "false", "CLEAR_APP_USER_DATA": "false", "CONTROL_LOCATION_UPDATES": "false", "DELETE_CACHE_FILES": "false", "DELETE_PACKAGES": "false", "DEVICE_POWER": "false", "DIAGNOSTIC": "false", "DISABLE_KEYGUARD": "false", "DUMP": "false", "EXPAND_STATUS_BAR": "false", "FACTORY_TEST": "false", "FLASHLIGHT": "false", "FORCE_BACK": "false", "GET_ACCOUNTS": "false", "GET_PACKAGE_SIZE": "false", "GET_TASKS": "false", "GLOBAL_SEARCH": "false", "HARDWARE_TEST": "false", "INJECT_EVENTS": "false", "INSTALL_LOCATION_PROVIDER": "false", "INSTALL_PACKAGES": "false", "INTERNAL_SYSTEM_WINDOW": "false", "INTERNET": "true", "KILL_BACKGROUND_PROCESSES": "false", "MANAGE_ACCOUNTS": "false", "MANAGE_APP_TOKENS": "false", "MASTER_CLEAR": "false", "MODIFY_AUDIO_SETTINGS": "false", "MODIFY_PHONE_STATE": "false", "MOUNT_FORMAT_FILESYSTEMS": "false", "MOUNT_UNMOUNT_FILESYSTEMS": "false", "NFC": "false", "PERSISTENT_ACTIVITY": "false", "POST_NOTIFICATIONS": "false", "PROCESS_OUTGOING_CALLS": "false", "READ_CALENDAR": "false", "READ_CONTACTS": "false", "READ_FRAME_BUFFER": "false", "READ_HISTORY_BOOKMARKS": "false", "READ_INPUT_STATE": "false", "READ_LOGS": "false", "READ_PHONE_STATE": "true", "READ_SMS": "false", "READ_SYNC_SETTINGS": "false", "READ_SYNC_STATS": "false", "REBOOT": "false", "RECEIVE_BOOT_COMPLETED": "false", "RECEIVE_MMS": "false", "RECEIVE_SMS": "false", "RECEIVE_WAP_PUSH": "false", "RECORD_AUDIO": "false", "REORDER_TASKS": "false", "RESTART_PACKAGES": "false", "SEND_SMS": "false", "SET_ACTIVITY_WATCHER": "false", "SET_ALARM": "false", "SET_ALWAYS_FINISH": "false", "SET_ANIMATION_SCALE": "false", "SET_DEBUG_APP": "false", "SET_ORIENTATION": "false", "SET_PREFERRED_APPLICATIONS": "false", "SET_PROCESS_LIMIT": "false", "SET_TIME": "false", "SET_TIME_ZONE": "false", "SET_WALLPAPER": "false", "SET_WALLPAPER_HINTS": "false", "SIGNAL_PERSISTENT_PROCESSES": "false", "STATUS_BAR": "false", "SUBSCRIBED_FEEDS_READ": "false", "SUBSCRIBED_FEEDS_WRITE": "false", "SYSTEM_ALERT_WINDOW": "false", "UPDATE_DEVICE_STATS": "false", "USE_CREDENTIALS": "false", "USE_FINGERPRINT": "false", "USE_SIP": "false", "VIBRATE": "false", "WAKE_LOCK": "false", "WRITE_APN_SETTINGS": "false", "WRITE_CALENDAR": "false", "WRITE_CONTACTS": "false", "WRITE_EXTERNAL_STORAGE": "false", "WRITE_GSERVICES": "false", "WRITE_HISTORY_BOOKMARKS": "false", "WRITE_SECURE_SETTINGS": "false", "WRITE_SETTINGS": "false", "WRITE_SMS": "false", "WRITE_SYNC_SETTINGS": "false"}}, "phantompath": "", "phoneCall": "false", "picturesLibrary": "false", "portkey": "", "portkey_andwear": "", "post_notification": "false", "previewshowpassword": "false", "privateNetworkClientServer": "false", "project.type": "js", "project_version": "sprint45", "protectedModePrivateKey": "certificates/_encryptionkeys/private_key.pem", "protectedModePublicKey": "certificates/_encryptionkeys/public_key.dat", "protectedmodeenabled_android": "false", "protectedmodeenabled_androidwear": "false", "protectedmodeenabled_ios": "false", "prototype": {}, "proximity": "false", "pushcertificatetype": "", "pwaUrl": "", "rcringtonedatakey": "", "read_device_identifying_information": "false", "referenceWidth": "320", "registerForDeepLink": {"androidwear": false, "desktop": false, "mobile": false, "tablet": false, "watch": false}, "remoteSystem": "false", "removableStorage": "false", "schemekey": "twyns", "schemekey_andwear": "", "secondarytilefunctionkey": "None", "seoconfigfilepath": "", "serviceworkerfile": "", "serviceworkerhelperfile": "", "sessionmanager": "Http Session Manager", "settings_gradle_attrs": "", "settings_gradle_attrs_andwear": "", "settingsicon": "", "sharedUserCertificates": "false", "sitemapxmlpath": "", "spaipadbasefontsize": "17", "spamanifestjson": "", "spawindows320basefontsize": "12", "spawindows480basefontsize": "12", "spawindowstabbasefontsize": "12", "splashlogokey": "", "startupformkey": "p2kwiet104807508927405", "staticmapwidgetkey": "AIzaSyBY8IHGBZAMWNK2DoA43YR3ONJXloQqCZk", "statusBarHidden": "false", "statusBarStyle": "STATUS_BAR_STYLE_DEFAULT", "studiovizproject": "true", "support32bit": "false", "support32bitandwear": "true", "support64bitandwear": "false", "supportX86Devicesandwear": "false", "support_arm_32bit": false, "support_arm_64bit": true, "support_x86_32bit": false, "support_x86_64bit": false, "supportedorientations": ["portrait"], "swiftversionkey": "None", "tabletBreakPointInInches": "6", "tags": {"android": {"activity_child_tags": "", "activityattrs": "", "andapplicationattrs": "tools:replace=\"android:allowBackup\" android:allowBackup =\"false\"\nandroid:largeHeap=\"true\"\nandroid:extractNativeLibs=\"true\"", "andapplicationtags": "", "andmanifesttags": "<uses-permission android:name=\"android.permission.READ_MEDIA_AUDIO\"/>\n\n<queries>\n        <package android:name=\"com.twyns.documentscanner\" />\n        <package android:name=\"com.twyns.documentscanner.test\" />\n    </queries>", "manifest_tag_attrs": ""}, "androidwear": {"activity_child_tags_andwear": "", "activityattrs_andwear": "", "andwearapplicationattrs": "", "andwearapplicationtags": "", "andwearmanifesttags": "", "manifest_tag_attrs_andwear": ""}}, "tcandroid240basefontsize": "10", "tcandroid320basefontsize": "12", "tcandroid360basefontsize": "13", "tcandroid400basefontsize": "14", "tcandroid440basefontsize": "15", "tcandroid480basefontsize": "16", "tcandroid640basefontsize": "19", "tcbb240basefontsize": "12", "tcbb320basefontsize": "12", "tcbb360basefontsize": "12", "tcbb400basefontsize": "12", "tcbb440basefontsize": "12", "tcbb480basefontsize": "12", "tcimportedjsfileskey": "", "tciphonebasefontsize": "12", "tcnoscriptmessage": "To use this site, first enable your browser's JavaScript support and then refresh this page.", "tcphoneformatindicator": "false", "tcrequiresGPSfuctionality": "false", "teamIdentity": "", "thinwebcontextkey": "", "tileUpdateRecurrenceKey": "", "universallinksupport": "", "urlschemanameorder": "Scheme,Host", "urlschemanameorder_andwear": "", "useWatchCommunicationApis": "false", "useWatchCommunicationApisAndWear": "false", "use_camera": "false", "usegoogleplaylocationservices": "true", "usegoogleplaylocationservicesandwear": "false", "userAccountInformation": "false", "userNotificationListener": "false", "useridkey": "", "usevizactions": "false", "vendornamekey": "Redora", "videosLibrary": "false", "voipCall": "false", "wap_android240_fontsize": "10", "wap_android320_fontsize": "12", "wap_android360_fontsize": "13", "wap_android400_fontsize": "14", "wap_android440_fontsize": "15", "wap_android480_fontsize": "16", "wap_android640_fontsize": "19", "wap_android_fontsize": "10", "wap_bb240_fontsize": "12", "wap_bb320_fontsize": "12", "wap_bb360_fontsize": "12", "wap_bb400_fontsize": "12", "wap_bb440_fontsize": "12", "wap_bb480_fontsize": "12", "wap_ip_fontsize": "12", "wap_spaandroidtab_fontsize": "17", "wap_spaandroidtabhdpi_fontsize": "16", "wap_spaandroidtabldpi_fontsize": "17", "wap_spaandroidtabmdpi_fontsize": "14", "wap_spaandroidtabxhdpi_fontsize": "18", "wap_spaipad_fontsize": "17", "wap_spawindowstab_fontsize": "12", "wap_winphone320_fontsize": "12", "wap_winphone480_fontsize": "12", "watchos_companionSettings2x29": "", "watchos_companionSettings3x29": "", "watchos_connectivityKey": "false", "watchos_connectivity_JSModuleName": "", "watchos_connectivity_SwiftModuleName": "", "watchos_homeScreen40": "", "watchos_homescreenicon_38": "", "watchos_homescreenicon_42": "", "watchos_homescreenicon_44": "", "watchos_longlooknotificationicon_38": "", "watchos_longlooknotificationicon_42": "", "watchos_notificationcentericon_38": "", "watchos_notificationcentericon_42": "", "watchos_shortlooknotificationicon_38": "", "watchos_shortlooknotificationicon_42": "", "watchos_shortlooknotificationicon_44": "", "watchos_target_versionkey": "None", "watchos_watchMarketing1x": "", "webLibraryMode": "js", "webcam": "false", "win10bgagentclassname": "", "win10bgagentnamespace": "", "win10bgagentpath": "", "win10bgagenttaskname": "", "win10pfxkey": "", "win6xj2meonlysupported": "false", "win8applogokey": "", "win8badgelogokey": "", "win8bgagentclassname": "", "win8bgagentnamespace": "", "win8bgagentpath": "", "win8bgagenttaskname": "", "win8certificatekey": "", "win8descriptionkey": "Sample Test Application", "win8displaynamekey": "REDLINE", "win8enablecrashlogkey": "false", "win8lockscreennotificationskey": "Not set", "win8packagedisplaynamekey": "REDLINE", "win8packagelogokey": "", "win8packagenamekey": "Redline", "win8packagepublichernamekey": "<PERSON><PERSON>", "win8publisherdisplaynamekey": "KonyLabs", "win8pvkkey": "", "win8shortnamekey": "Bicycles", "win8shownamekey": "Not set", "win8smalllogokey": "", "win8toastcapablekey": "Not set", "win8versionbuildkey": "0", "win8versionmajorkey": "1", "win8versionminorkey": "0", "win8versionrevisionkey": "0", "win8widelogokey": "", "winDisableApplicationScreenshot": "false", "windesktop_allowselfsignedcertificate": "None", "windesktop_disableapplicationminimize": "false", "windesktop_enableBackwardCompatibility": "false", "windows10guid": "", "windowskioskguid": "", "windowsloadindicatorkey": "true", "windowsmapkey": "", "winenablecrashlogkey": "false", "winmangotiletitlekey": "BicycleReg", "winmobileguidkey": "", "winmobiletileimagekey": "", "winphn10bgagentclassname": "", "winphn10bgagentnamespace": "", "winphn10bgagentpath": "", "winphn10bgagenttaskname": "", "winphn81bgagentclassname": "", "winphn81bgagentnamespace": "", "winphn81bgagentpath": "", "winphone_allowselfsignedcertificate": "None", "winphoneurlscheme": "", "winsmoothscrollkey": "true", "wintab_allowselfsignedcertificate": "None", "wintaburlscheme": "", "wintilebackcolornamekey": "128,128,128", "wintileforegroundnamekey": "Light", "wp10allJoyn": "false", "wp10applogokey": "", "wp10appointments": "false", "wp10backgroundMediaPlayback": "false", "wp10badgelogokey": "", "wp10blockedChatMessages": "false", "wp10bluetooth": "false", "wp10chat": "false", "wp10codeGeneration": "false", "wp10contacts": "false", "wp10descriptionkey": "Sample Test Application", "wp10displayname": "REDLINE", "wp10enablecrashlogkey": "false", "wp10enterpriseAuthentication": "false", "wp10initialRotationPreferenceKey": "landscape", "wp10internetClient": "false", "wp10internetClientServer": "false", "wp10location": "false", "wp10lockscreennotificationskey": "Not set", "wp10microphone": "false", "wp10musicLibrary": "false", "wp10objects3D": "false", "wp10packagedisplaynamekey": "CityManagement", "wp10packagelogokey": "", "wp10packagenamekey": "Sensor", "wp10packagepublichernamekey": "KonyLabs", "wp10pfxkey": "", "wp10phoneCall": "false", "wp10picturesLibrary": "false", "wp10privateNetworkClientServer": "false", "wp10proximity": "false", "wp10publisherdisplaynamekey": "", "wp10remoteSystem": "false", "wp10removableStorage": "false", "wp10sharedUserCertificates": "false", "wp10shortnamekey": "CityManagement", "wp10shownamekey": "Not set", "wp10smalllogokey": "", "wp10tileUpdateRecurrenceKey": "", "wp10tilebackcolornamekey": "128,128,128", "wp10tileforegroundnamekey": "Light", "wp10toastcapablekey": "Not set", "wp10userAccountInformation": "false", "wp10userNotificationListener": "false", "wp10versionbuildkey": "0", "wp10versionmajorkey": "1", "wp10versionminorkey": "0", "wp10versionrevisionkey": "0", "wp10videosLibrary": "false", "wp10voipCall": "false", "wp10webcam": "false", "wp10widelogokey": "", "wp81sdescriptionkey": "Sample Test Application", "wp81sdisplaynamekey": "REDLINE", "wp81slockscreennotificationskey": "Not set", "wp81spackagedisplaynamekey": "Bicycles", "wp81spackagenamekey": "BicycleReg", "wp81spackagepublichernamekey": "<PERSON><PERSON>", "wp81spublisherdisplaynamekey": "KonyLabs", "wp81stoastcapablekey": "Not set", "wp8cycle1key": "", "wp8cycle2key": "", "wp8cycle3key": "", "wp8cycle4key": "", "wp8cycle5key": "", "wp8cycle6key": "", "wp8cycle7key": "", "wp8cycle8key": "", "wp8cycle9key": "", "wp8cyclesmallkey": "", "wp8doNotInstalllowerMemory": "false", "wp8fliplargekey": "", "wp8flipmediumkey": "", "wp8flipsmallkey": "", "wp8iconcountnotificationkey": "false", "wp8iconicmediumkey": "", "wp8iconicsmallkey": "", "wp8iconimagekey": "", "wp8lockscreenbackgroundkey": "false", "wp8packagepublisheridkey": "", "wp8packageversionkey": "", "wp8supportlargetileskey": "false", "wp8textnotificationkey": "false", "wp8tilestemplatekey": "FLIP_TILE", "wp8tilestitlekey": "", "xhtmlfavicokey": ""}